/*******************************************************************************
 * @file      scheduler.h
 * <AUTHOR>
 * @version   V1.0
 * @date      2025-07-18
 * @brief     简单任务调度器的头文件
 * @note      此文件为任务调度器模块提供了外部接口声明。任何需要使用
 * 该调度器的模块，都应包含此头文件来调用其初始化和运行函数。
 *******************************************************************************/

#ifndef SCHEDULER_H
#define SCHEDULER_H

// 包含系统级头文件，以获取必要的类型定义或宏，如 u8, u32 等。
#include "bsp_system.h"

/*******************************************************************************
 * 外部函数声明
 *******************************************************************************/

/**
 * @brief  调度器初始化函数声明
 * @see    scheduler.c 文件中的具体实现
 */
void scheduler_init(void);

/**
 * @brief  调度器主运行函数声明
 * @see    scheduler.c 文件中的具体实现
 */
void scheduler_run(void);

#endif /* SCHEDULER_H */
