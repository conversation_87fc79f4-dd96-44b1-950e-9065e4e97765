{"name": "zuolan_STM32", "target": "zuolan_STM32", "toolchain": "AC5", "toolchainLocation": "D:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.21.1\\res\\data\\models\\win32/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 12, "rootDir": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "dumpPath": "build\\zuolan_STM32", "outDir": "build\\zuolan_STM32", "ram": 262144, "rom": 1048576, "incDirs": ["../Core/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../Drivers/CMSIS/Include", "../Middlewares/ST/ARM/DSP/Inc", "../MY_Algorithms/Inc", "../MY_Communication/Inc", "../MY_Hardware_Drivers/Inc", "../MY_Utilities/Inc", "../MY_APP", ".cmsis/include", "../MDK-ARM/RTE/_zuolan_STM32"], "libDirs": [], "defines": ["USE_HAL_DRIVER", "STM32F429xx", "ARM_MATH_CM4"], "sourceList": ["../Core/Src/fmc.c", "../Core/Src/gpio.c", "../Core/Src/main.c", "../Core/Src/stm32f4xx_hal_msp.c", "../Core/Src/stm32f4xx_it.c", "../Core/Src/system_stm32f4xx.c", "../Core/Src/usart.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sram.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c", "../MDK-ARM/startup_stm32f429xx.s", "../MY_APP/scheduler.c", "../MY_Algorithms/Src/my_fft.c", "../MY_Algorithms/Src/my_filter.c", "../MY_Algorithms/Src/phase_measure.c", "../MY_Communication/Src/my_hmi.c", "../MY_Communication/Src/my_usart.c", "../MY_Communication/Src/my_usart_pack.c", "../MY_Hardware_Drivers/Src/AD9959.c", "../MY_Hardware_Drivers/Src/ad_measure.c", "../MY_Hardware_Drivers/Src/da_output.c", "../MY_Hardware_Drivers/Src/freq_measure.c", "../MY_Utilities/Src/cmd_to_fun.c", "../Middlewares/ST/ARM/DSP/Lib/arm_cortexM4lf_math.lib"], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "axf to elf", "command": "axf2elf -d \"D:\\Keil_v5\\ARM\\ARMCC\" -i \"${outDir}\\zuolan_STM32.axf\" -o \"${outDir}\\zuolan_STM32.elf\" > \"${outDir}\\axf2elf.log\""}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "target": "cortex-m4-sp"}, "c/cpp-compiler": {"optimization": "level-3", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "link-scatter": ["\"e:/MCU/FPGA+32 DEMO/STM32/EIDE/build/zuolan_STM32/zuolan_STM32.sct\""]}}, "env": {"KEIL_OUTPUT_DIR": "zuolan_STM32", "KEIL_OUTPUT_NAME": "STM32", "workspaceFolder": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "workspaceFolderBasename": "EIDE", "OutDir": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE\\build\\zuolan_STM32", "OutDirRoot": "build", "OutDirBase": "build\\zuolan_STM32", "ProjectName": "zuolan_STM32", "ConfigName": "zuolan_STM32", "ProjectRoot": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "ExecutableName": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE\\build\\zuolan_STM32\\zuolan_STM32", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.21.1\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.0.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "ToolchainRoot": "D:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}