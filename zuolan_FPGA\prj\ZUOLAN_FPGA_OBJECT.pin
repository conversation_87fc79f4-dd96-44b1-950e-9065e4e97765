 -- Copyright (C) 2018  Intel Corporation. All rights reserved.
 -- Your use of Intel Corporation's design tools, logic functions 
 -- and other software and tools, and its AMPP partner logic 
 -- functions, and any output files from any of the foregoing 
 -- (including device programming or simulation files), and any 
 -- associated documentation or information are expressly subject 
 -- to the terms and conditions of the Intel Program License 
 -- Subscription Agreement, the Intel Quartus Prime License Agreement,
 -- the Intel FPGA IP License Agreement, or other applicable license
 -- agreement, including, without limitation, that your use is for
 -- the sole purpose of programming logic devices manufactured by
 -- Intel and sold by Intel or its authorized distributors.  Please
 -- refer to the applicable agreement for further details.
 -- 
 -- This is a Quartus Prime output file. It is for reporting purposes only, and is
 -- not intended for use as a Quartus Prime input file. This file cannot be used
 -- to make Quartus Prime pin assignments - for instructions on how to make pin
 -- assignments, please see Quartus Prime help.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- NC            : No Connect. This pin has no internal connection to the device.
 -- DNU           : Do Not Use. This pin MUST NOT be connected.
 -- VCCINT        : Dedicated power pin, which MUST be connected to VCC  (1.2V).
 -- VCCIO         : Dedicated power pin, which MUST be connected to VCC
 --                 of its bank.
 --                  Bank 1:       2.5V
 --                  Bank 2:       2.5V
 --                  Bank 3:       2.5V
 --                  Bank 4:       2.5V
 --                  Bank 5:       2.5V
 --                  Bank 6:       2.5V
 --                  Bank 7:       2.5V
 --                  Bank 8:       2.5V
 -- GND           : Dedicated ground pin. Dedicated GND pins MUST be connected to GND.
 --                  It can also be used to report unused dedicated pins. The connection
 --                  on the board for unused dedicated pins depends on whether this will
 --                  be used in a future design. One example is device migration. When
 --                  using device migration, refer to the device pin-tables. If it is a
 --                  GND pin in the pin table or if it will not be used in a future design
 --                  for another purpose the it MUST be connected to GND. If it is an unused
 --                  dedicated pin, then it can be connected to a valid signal on the board
 --                  (low, high, or toggling) if that signal is required for a different
 --                  revision of the design.
 -- GND+          : Unused input pin. It can also be used to report unused dual-purpose pins.
 --                  This pin should be connected to GND. It may also be connected  to a
 --                  valid signal  on the board  (low, high, or toggling)  if that signal
 --                  is required for a different revision of the design.
 -- GND*          : Unused  I/O  pin. Connect each pin marked GND* directly to GND
 --                  or leave it unconnected.
 -- RESERVED      : Unused I/O pin, which MUST be left unconnected.
 -- RESERVED_INPUT    : Pin is tri-stated and should be connected to the board.
 -- RESERVED_INPUT_WITH_WEAK_PULLUP    : Pin is tri-stated with internal weak pull-up resistor.
 -- RESERVED_INPUT_WITH_BUS_HOLD       : Pin is tri-stated with bus-hold circuitry.
 -- RESERVED_OUTPUT_DRIVEN_HIGH        : Pin is output driven high.
 ---------------------------------------------------------------------------------



 ---------------------------------------------------------------------------------
 -- Pin directions (input, output or bidir) are based on device operating in user mode.
 ---------------------------------------------------------------------------------

Quartus Prime Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition
CHIP  "ZUOLAN_FPGA_OBJECT"  ASSIGNED TO AN: EP4CE10F17C8

Pin Name/Usage               : Location  : Dir.   : I/O Standard      : Voltage : I/O Bank  : User Assignment
-------------------------------------------------------------------------------------------------------------
VCCIO8                       : A1        : power  :                   : 2.5V    : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A2        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A3        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A4        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A5        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A6        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A7        :        :                   :         : 8         :                
DA2_OUT[2]                   : A8        : output : 2.5 V             :         : 8         : Y              
DA2_OUT[6]                   : A9        : output : 2.5 V             :         : 7         : Y              
DA2_OUT[10]                  : A10       : output : 2.5 V             :         : 7         : Y              
DA2_OUTCLK                   : A11       : output : 2.5 V             :         : 7         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : A12       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A13       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A14       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : A15       :        :                   :         : 7         :                
VCCIO7                       : A16       : power  :                   : 2.5V    : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B1        :        :                   :         : 1         :                
GND                          : B2        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B3        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B4        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B5        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B6        :        :                   :         : 8         :                
DA2_OUT[0]                   : B7        : output : 2.5 V             :         : 8         : Y              
DA2_OUT[4]                   : B8        : output : 2.5 V             :         : 8         : Y              
DA2_OUT[8]                   : B9        : output : 2.5 V             :         : 7         : Y              
DA2_OUT[12]                  : B10       : output : 2.5 V             :         : 7         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : B11       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B12       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B13       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B14       :        :                   :         : 7         :                
GND                          : B15       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : B16       :        :                   :         : 6         :                
~ALTERA_ASDO_DATA1~ / RESERVED_INPUT_WITH_WEAK_PULLUP : C1        : input  : 2.5 V             :         : 1         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : C2        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C3        :        :                   :         : 8         :                
VCCIO8                       : C4        : power  :                   : 2.5V    : 8         :                
GND                          : C5        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C6        :        :                   :         : 8         :                
VCCIO8                       : C7        : power  :                   : 2.5V    : 8         :                
DA2_OUT[3]                   : C8        : output : 2.5 V             :         : 8         : Y              
DA2_OUT[13]                  : C9        : output : 2.5 V             :         : 7         : Y              
VCCIO7                       : C10       : power  :                   : 2.5V    : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C11       :        :                   :         : 7         :                
GND                          : C12       : gnd    :                   :         :           :                
VCCIO7                       : C13       : power  :                   : 2.5V    : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : C14       :        :                   :         : 7         :                
AD1_OUTCLK                   : C15       : output : 2.5 V             :         : 6         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : C16       :        :                   :         : 6         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D1        :        :                   :         : 1         :                
~ALTERA_FLASH_nCE_nCSO~ / RESERVED_INPUT_WITH_WEAK_PULLUP : D2        : input  : 2.5 V             :         : 1         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : D3        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D4        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D5        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D6        :        :                   :         : 8         :                
GND                          : D7        : gnd    :                   :         :           :                
DA2_OUT[1]                   : D8        : output : 2.5 V             :         : 8         : Y              
DA2_OUT[11]                  : D9        : output : 2.5 V             :         : 7         : Y              
GND                          : D10       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D11       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D12       :        :                   :         : 7         :                
VCCD_PLL2                    : D13       : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : D14       :        :                   :         : 7         :                
AD1_INPUT_CLK                : D15       : input  : 2.5 V             :         : 6         : Y              
AD2_INPUT_CLK                : D16       : input  : 2.5 V             :         : 6         : Y              
CLK                          : E1        : input  : 2.5 V             :         : 1         : Y              
GND                          : E2        : gnd    :                   :         :           :                
VCCIO1                       : E3        : power  :                   : 2.5V    : 1         :                
GND                          : E4        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E5        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E6        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E7        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E8        :        :                   :         : 8         :                
DA2_OUT[9]                   : E9        : output : 2.5 V             :         : 7         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : E10       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : E11       :        :                   :         : 7         :                
GNDA2                        : E12       : gnd    :                   :         :           :                
GND                          : E13       : gnd    :                   :         :           :                
VCCIO6                       : E14       : power  :                   : 2.5V    : 6         :                
GND+                         : E15       :        :                   :         : 6         :                
RST                          : E16       : input  : 2.5 V             :         : 6         : Y              
FPGA_CS_NEL                  : F1        : input  : 2.5 V             :         : 1         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : F2        :        :                   :         : 1         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F3        :        :                   :         : 1         :                
nSTATUS                      : F4        :        :                   :         : 1         :                
FPGA_NL_NADV                 : F5        : input  : 2.5 V             :         : 1         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : F6        :        :                   :         : 8         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F7        :        :                   :         : 8         :                
DA2_OUT[5]                   : F8        : output : 2.5 V             :         : 8         : Y              
DA2_OUT[7]                   : F9        : output : 2.5 V             :         : 7         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : F10       :        :                   :         : 7         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : F11       :        :                   :         : 7         :                
VCCA2                        : F12       : power  :                   : 2.5V    :           :                
AD1_INPUT[10]                : F13       : input  : 2.5 V             :         : 6         : Y              
AD1_INPUT[8]                 : F14       : input  : 2.5 V             :         : 6         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : F15       :        :                   :         : 6         :                
~ALTERA_nCEO~ / RESERVED_OUTPUT_OPEN_DRAIN : F16       : output : 2.5 V             :         : 6         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : G1        :        :                   :         : 1         :                
FPGA_WR_NWE                  : G2        : input  : 2.5 V             :         : 1         : Y              
VCCIO1                       : G3        : power  :                   : 2.5V    : 1         :                
GND                          : G4        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : G5        :        :                   :         : 1         :                
VCCINT                       : G6        : power  :                   : 1.2V    :           :                
VCCINT                       : G7        : power  :                   : 1.2V    :           :                
VCCINT                       : G8        : power  :                   : 1.2V    :           :                
VCCINT                       : G9        : power  :                   : 1.2V    :           :                
VCCINT                       : G10       : power  :                   : 1.2V    :           :                
AD1_INPUT[6]                 : G11       : input  : 2.5 V             :         : 6         : Y              
MSEL2                        : G12       :        :                   :         : 6         :                
GND                          : G13       : gnd    :                   :         :           :                
VCCIO6                       : G14       : power  :                   : 2.5V    : 6         :                
AD1_INPUT[11]                : G15       : input  : 2.5 V             :         : 6         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : G16       :        :                   :         : 6         :                
~ALTERA_DCLK~                : H1        : output : 2.5 V             :         : 1         : N              
~ALTERA_DATA0~ / RESERVED_INPUT_WITH_WEAK_PULLUP : H2        : input  : 2.5 V             :         : 1         : N              
TCK                          : H3        : input  :                   :         : 1         :                
TDI                          : H4        : input  :                   :         : 1         :                
nCONFIG                      : H5        :        :                   :         : 1         :                
VCCINT                       : H6        : power  :                   : 1.2V    :           :                
GND                          : H7        : gnd    :                   :         :           :                
GND                          : H8        : gnd    :                   :         :           :                
GND                          : H9        : gnd    :                   :         :           :                
GND                          : H10       : gnd    :                   :         :           :                
VCCINT                       : H11       : power  :                   : 1.2V    :           :                
MSEL1                        : H12       :        :                   :         : 6         :                
MSEL0                        : H13       :        :                   :         : 6         :                
CONF_DONE                    : H14       :        :                   :         : 6         :                
GND                          : H15       : gnd    :                   :         :           :                
GND                          : H16       : gnd    :                   :         :           :                
FPGA_RD_NOE                  : J1        : input  : 2.5 V             :         : 2         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : J2        :        :                   :         : 2         :                
nCE                          : J3        :        :                   :         : 1         :                
TDO                          : J4        : output :                   :         : 1         :                
TMS                          : J5        : input  :                   :         : 1         :                
FPGA_DB[3]                   : J6        : bidir  : 2.5 V             :         : 2         : Y              
GND                          : J7        : gnd    :                   :         :           :                
GND                          : J8        : gnd    :                   :         :           :                
GND                          : J9        : gnd    :                   :         :           :                
GND                          : J10       : gnd    :                   :         :           :                
AD1_INPUT[4]                 : J11       : input  : 2.5 V             :         : 5         : Y              
AD1_INPUT[0]                 : J12       : input  : 2.5 V             :         : 5         : Y              
AD2_OUTCLK                   : J13       : output : 2.5 V             :         : 5         : Y              
AD2_INPUT[10]                : J14       : input  : 2.5 V             :         : 5         : Y              
AD1_INPUT[9]                 : J15       : input  : 2.5 V             :         : 5         : Y              
AD1_INPUT[7]                 : J16       : input  : 2.5 V             :         : 5         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : K1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K2        :        :                   :         : 2         :                
VCCIO2                       : K3        : power  :                   : 2.5V    : 2         :                
GND                          : K4        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K5        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K6        :        :                   :         : 2         :                
VCCINT                       : K7        : power  :                   : 1.2V    :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : K8        :        :                   :         : 3         :                
DA1_OUT[3]                   : K9        : output : 2.5 V             :         : 4         : N              
DA1_OUT[12]                  : K10       : output : 2.5 V             :         : 4         : N              
AD1_INPUT[2]                 : K11       : input  : 2.5 V             :         : 5         : Y              
AD2_INPUT[8]                 : K12       : input  : 2.5 V             :         : 5         : Y              
GND                          : K13       : gnd    :                   :         :           :                
VCCIO5                       : K14       : power  :                   : 2.5V    : 5         :                
AD2_INPUT[6]                 : K15       : input  : 2.5 V             :         : 5         : Y              
AD1_INPUT[5]                 : K16       : input  : 2.5 V             :         : 5         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : L1        :        :                   :         : 2         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L2        :        :                   :         : 2         :                
DA1_OUTCLK                   : L3        : output : 2.5 V             :         : 2         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : L4        :        :                   :         : 2         :                
VCCA1                        : L5        : power  :                   : 2.5V    :           :                
FPGA_DB[15]                  : L6        : bidir  : 2.5 V             :         : 2         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : L7        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : L8        :        :                   :         : 3         :                
DA1_OUT[0]                   : L9        : output : 2.5 V             :         : 4         : N              
DA1_OUT[1]                   : L10       : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : L11       :        :                   :         : 4         :                
AD2_INPUT[4]                 : L12       : input  : 2.5 V             :         : 5         : Y              
AD2_INPUT[2]                 : L13       : input  : 2.5 V             :         : 5         : Y              
AD2_INPUT[3]                 : L14       : input  : 2.5 V             :         : 5         : Y              
AD1_INPUT[1]                 : L15       : input  : 2.5 V             :         : 5         : Y              
AD1_INPUT[3]                 : L16       : input  : 2.5 V             :         : 5         : Y              
GND+                         : M1        :        :                   :         : 2         :                
GND+                         : M2        :        :                   :         : 2         :                
VCCIO2                       : M3        : power  :                   : 2.5V    : 2         :                
GND                          : M4        : gnd    :                   :         :           :                
GNDA1                        : M5        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : M6        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : M7        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : M8        :        :                   :         : 3         :                
DA1_OUT[7]                   : M9        : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : M10       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : M11       :        :                   :         : 4         :                
AD2_INPUT[1]                 : M12       : input  : 2.5 V             :         : 5         : Y              
GND                          : M13       : gnd    :                   :         :           :                
VCCIO5                       : M14       : power  :                   : 2.5V    : 5         :                
GND+                         : M15       :        :                   :         : 5         :                
GND+                         : M16       :        :                   :         : 5         :                
FPGA_DB[0]                   : N1        : bidir  : 2.5 V             :         : 2         : Y              
FPGA_DB[1]                   : N2        : bidir  : 2.5 V             :         : 2         : Y              
FPGA_DB[6]                   : N3        : bidir  : 2.5 V             :         : 3         : Y              
VCCD_PLL1                    : N4        : power  :                   : 1.2V    :           :                
FPGA_DB[14]                  : N5        : bidir  : 2.5 V             :         : 3         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : N6        :        :                   :         : 3         :                
GND                          : N7        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N8        :        :                   :         : 3         :                
DA1_OUT[8]                   : N9        : output : 2.5 V             :         : 4         : N              
GND                          : N10       : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N11       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N12       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : N13       :        :                   :         : 5         :                
AD2_INPUT[0]                 : N14       : input  : 2.5 V             :         : 5         : Y              
AD2_INPUT[11]                : N15       : input  : 2.5 V             :         : 5         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : N16       :        :                   :         : 5         :                
FPGA_DB[2]                   : P1        : bidir  : 2.5 V             :         : 2         : Y              
FPGA_DB[4]                   : P2        : bidir  : 2.5 V             :         : 2         : Y              
FPGA_DB[7]                   : P3        : bidir  : 2.5 V             :         : 3         : Y              
VCCIO3                       : P4        : power  :                   : 2.5V    : 3         :                
GND                          : P5        : gnd    :                   :         :           :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P6        :        :                   :         : 3         :                
VCCIO3                       : P7        : power  :                   : 2.5V    : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P8        :        :                   :         : 3         :                
DA1_OUT[10]                  : P9        : output : 2.5 V             :         : 4         : N              
VCCIO4                       : P10       : power  :                   : 2.5V    : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P11       :        :                   :         : 4         :                
GND                          : P12       : gnd    :                   :         :           :                
VCCIO4                       : P13       : power  :                   : 2.5V    : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : P14       :        :                   :         : 4         :                
AD2_INPUT[7]                 : P15       : input  : 2.5 V             :         : 5         : Y              
AD2_INPUT[9]                 : P16       : input  : 2.5 V             :         : 5         : Y              
FPGA_DB[5]                   : R1        : bidir  : 2.5 V             :         : 2         : Y              
GND                          : R2        : gnd    :                   :         :           :                
FPGA_DB[9]                   : R3        : bidir  : 2.5 V             :         : 3         : Y              
FPGA_DB[11]                  : R4        : bidir  : 2.5 V             :         : 3         : Y              
FPGA_DB[13]                  : R5        : bidir  : 2.5 V             :         : 3         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : R6        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R7        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R8        :        :                   :         : 3         :                
DA1_OUT[5]                   : R9        : output : 2.5 V             :         : 4         : N              
DA1_OUT[9]                   : R10       : output : 2.5 V             :         : 4         : N              
DA1_OUT[13]                  : R11       : output : 2.5 V             :         : 4         : N              
DA1_OUT[4]                   : R12       : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : R13       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : R14       :        :                   :         : 4         :                
GND                          : R15       : gnd    :                   :         :           :                
AD2_INPUT[5]                 : R16       : input  : 2.5 V             :         : 5         : Y              
VCCIO3                       : T1        : power  :                   : 2.5V    : 3         :                
FPGA_DB[8]                   : T2        : bidir  : 2.5 V             :         : 3         : Y              
FPGA_DB[10]                  : T3        : bidir  : 2.5 V             :         : 3         : Y              
FPGA_DB[12]                  : T4        : bidir  : 2.5 V             :         : 3         : Y              
RESERVED_INPUT_WITH_WEAK_PULLUP : T5        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T6        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T7        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T8        :        :                   :         : 3         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T9        :        :                   :         : 4         :                
DA1_OUT[6]                   : T10       : output : 2.5 V             :         : 4         : N              
DA1_OUT[11]                  : T11       : output : 2.5 V             :         : 4         : N              
DA1_OUT[2]                   : T12       : output : 2.5 V             :         : 4         : N              
RESERVED_INPUT_WITH_WEAK_PULLUP : T13       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T14       :        :                   :         : 4         :                
RESERVED_INPUT_WITH_WEAK_PULLUP : T15       :        :                   :         : 4         :                
VCCIO4                       : T16       : power  :                   : 2.5V    : 4         :                
