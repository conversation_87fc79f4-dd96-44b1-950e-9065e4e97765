Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to fmc.o(.text.MX_FMC_Init) for MX_FMC_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(.text.main) refers to dac.o(.text.MX_DAC_Init) for MX_DAC_Init
    main.o(.text.main) refers to usart.o(.bss.huart1) for huart1
    main.o(.text.main) refers to my_usart.o(.bss..L_MergedGlobals) for rxTemp1
    main.o(.text.main) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(.text.main) refers to usart.o(.bss.huart2) for huart2
    main.o(.text.main) refers to usart.o(.bss.huart3) for huart3
    main.o(.text.main) refers to cmd_to_fun.o(.text.CTRL_INIT) for CTRL_INIT
    main.o(.text.main) refers to da_output.o(.text.DA_Init) for DA_Init
    main.o(.text.main) refers to da_output.o(.text.DA_Apply_Settings) for DA_Apply_Settings
    main.o(.text.main) refers to app_pid.o(.text.PID_Init) for PID_Init
    main.o(.text.main) refers to my_fft.o(.text.fft_init) for fft_init
    main.o(.text.main) refers to ad9959.o(.text.AD9959_Init) for AD9959_Init
    main.o(.text.main) refers to ad9959.o(.text.AD9959_Modulation_Init) for AD9959_Modulation_Init
    main.o(.text.main) refers to ad9959.o(.text.AD9959_Set_ASK) for AD9959_Set_ASK
    main.o(.text.main) refers to my_usart.o(.text.my_printf) for my_printf
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) for HAL_PWREx_EnableOverDrive
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for HAL_RCC_EnableCSS
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dac.o(.text.MX_DAC_Init) refers to dac.o(.bss.hdac) for hdac
    dac.o(.text.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) for HAL_DAC_Init
    dac.o(.text.MX_DAC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    dac.o(.text.MX_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel) for HAL_DAC_ConfigChannel
    dac.o(.ARM.exidx.text.MX_DAC_Init) refers to dac.o(.text.MX_DAC_Init) for [Anonymous Symbol]
    dac.o(.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    dac.o(.ARM.exidx.text.HAL_DAC_MspInit) refers to dac.o(.text.HAL_DAC_MspInit) for [Anonymous Symbol]
    dac.o(.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit) refers to dac.o(.text.HAL_DAC_MspDeInit) for [Anonymous Symbol]
    fmc.o(.text.MX_FMC_Init) refers to fmc.o(.bss.hsram2) for hsram2
    fmc.o(.text.MX_FMC_Init) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) for HAL_SRAM_Init
    fmc.o(.text.MX_FMC_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    fmc.o(.ARM.exidx.text.MX_FMC_Init) refers to fmc.o(.text.MX_FMC_Init) for [Anonymous Symbol]
    fmc.o(.text.HAL_SRAM_MspInit) refers to fmc.o(.bss.FMC_Initialized) for FMC_Initialized
    fmc.o(.text.HAL_SRAM_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    fmc.o(.ARM.exidx.text.HAL_SRAM_MspInit) refers to fmc.o(.text.HAL_SRAM_MspInit) for [Anonymous Symbol]
    fmc.o(.text.HAL_SRAM_MspDeInit) refers to fmc.o(.bss.FMC_DeInitialized) for FMC_DeInitialized
    fmc.o(.text.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    fmc.o(.ARM.exidx.text.HAL_SRAM_MspDeInit) refers to fmc.o(.text.HAL_SRAM_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART3_UART_Init) refers to usart.o(.bss.huart3) for huart3
    usart.o(.text.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART3_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART3_UART_Init) refers to usart.o(.text.MX_USART3_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.NMI_Handler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for HAL_RCC_NMI_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to usart.o(.bss.huart3) for huart3
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    ad_measure.o(.ARM.exidx.text.findMinMax) refers to ad_measure.o(.text.findMinMax) for [Anonymous Symbol]
    ad_measure.o(.text.setSamplingFrequency) refers to cmd_to_fun.o(.text.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(.text.setSamplingFrequency) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad_measure.o(.ARM.exidx.text.setSamplingFrequency) refers to ad_measure.o(.text.setSamplingFrequency) for [Anonymous Symbol]
    ad_measure.o(.text.readFIFOData) refers to cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(.text.readFIFOData) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad_measure.o(.text.readFIFOData) refers to cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(.ARM.exidx.text.readFIFOData) refers to ad_measure.o(.text.readFIFOData) for [Anonymous Symbol]
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FREQ_SET) for AD_FREQ_SET
    ad_measure.o(.text.vpp_adc_parallel) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE) for AD_FIFO_WRITE_ENABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_DISABLE) for AD_FIFO_WRITE_DISABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE) for AD_FIFO_READ_ENABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data1_f) for fifo_data1_f
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data1) for fifo_data1
    ad_measure.o(.text.vpp_adc_parallel) refers to cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE) for AD_FIFO_READ_DISABLE
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data2_f) for fifo_data2_f
    ad_measure.o(.text.vpp_adc_parallel) refers to ad_measure.o(.bss.fifo_data2) for fifo_data2
    ad_measure.o(.ARM.exidx.text.vpp_adc_parallel) refers to ad_measure.o(.text.vpp_adc_parallel) for [Anonymous Symbol]
    ad_measure.o(.text.ad_proc) refers to ad_measure.o(.text.vpp_adc_parallel) for vpp_adc_parallel
    ad_measure.o(.ARM.exidx.text.ad_proc) refers to ad_measure.o(.text.ad_proc) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Reset) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Reset) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Reset) refers to ad9959.o(.text.AD9959_Reset) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_RST_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_RST_L) refers to ad9959.o(.text.AD9959_RST_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_RST_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_RST_H) refers to ad9959.o(.text.AD9959_RST_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_IO_Update) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_IO_Update) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_IO_Update) refers to ad9959.o(.text.AD9959_IO_Update) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_UP_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_UP_L) refers to ad9959.o(.text.AD9959_UP_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_UP_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_UP_H) refers to ad9959.o(.text.AD9959_UP_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.text.AD9959_Init) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Init) refers to ad9959.o(.text.AD9959_Init) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_CS_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_CS_H) refers to ad9959.o(.text.AD9959_CS_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SCK_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SCK_L) refers to ad9959.o(.text.AD9959_SCK_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_PDC_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_PDC_L) refers to ad9959.o(.text.AD9959_PDC_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Select_Channel) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Select_Channel) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Select_Channel) refers to ad9959.o(.text.AD9959_Select_Channel) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Freq) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_Freq) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_Freq) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_Freq) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Freq) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Set_Freq) refers to ad9959.o(.text.AD9959_Set_Freq) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Phase) refers to f2d.o(.text) for __aeabi_f2d
    ad9959.o(.text.AD9959_Set_Phase) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_Phase) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9959.o(.text.AD9959_Set_Phase) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Phase) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Set_Phase) refers to ad9959.o(.text.AD9959_Set_Phase) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Amp) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Amp) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Set_Amp) refers to ad9959.o(.text.AD9959_Set_Amp) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Single_Output) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Single_Output) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Single_Output) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Single_Output) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Single_Output) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Single_Output) refers to f2d.o(.text) for __aeabi_f2d
    ad9959.o(.text.AD9959_Single_Output) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9959.o(.ARM.exidx.text.AD9959_Single_Output) refers to ad9959.o(.text.AD9959_Single_Output) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Linear_Sweep) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Linear_Sweep) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Set_Linear_Sweep) refers to ad9959.o(.text.AD9959_Set_Linear_Sweep) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_LSRR) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Write_LSRR) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Write_LSRR) refers to ad9959.o(.text.AD9959_Write_LSRR) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_RDW) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Write_RDW) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Write_RDW) refers to ad9959.o(.text.AD9959_Write_RDW) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_FDW) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Write_FDW) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Write_FDW) refers to ad9959.o(.text.AD9959_Write_FDW) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Freq_Sweep) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Freq_Sweep) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Set_Freq_Sweep) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_Freq_Sweep) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_Freq_Sweep) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_Freq_Sweep) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Set_Freq_Sweep) refers to ad9959.o(.text.AD9959_Set_Freq_Sweep) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Amp_Sweep) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Amp_Sweep) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Set_Amp_Sweep) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_Amp_Sweep) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_Amp_Sweep) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_Amp_Sweep) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Set_Amp_Sweep) refers to ad9959.o(.text.AD9959_Set_Amp_Sweep) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_Phase_Sweep) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_Phase_Sweep) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Set_Phase_Sweep) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_Phase_Sweep) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_Phase_Sweep) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_Phase_Sweep) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Set_Phase_Sweep) refers to ad9959.o(.text.AD9959_Set_Phase_Sweep) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Modulation_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Modulation_Init) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Modulation_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Modulation_Init) refers to ad9959.o(.text.AD9959_Modulation_Init) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_Profile_Freq) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Write_Profile_Freq) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Write_Profile_Freq) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Write_Profile_Freq) refers to ad9959.o(.rodata.Reg_Len) for Reg_Len
    ad9959.o(.text.AD9959_Write_Profile_Freq) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Write_Profile_Freq) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Write_Profile_Freq) refers to ad9959.o(.text.AD9959_Write_Profile_Freq) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_Profile_Amp) refers to ad9959.o(.rodata.Reg_Len) for Reg_Len
    ad9959.o(.text.AD9959_Write_Profile_Amp) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Write_Profile_Amp) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Write_Profile_Amp) refers to ad9959.o(.text.AD9959_Write_Profile_Amp) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_Profile_Phase) refers to ad9959.o(.rodata.Reg_Len) for Reg_Len
    ad9959.o(.text.AD9959_Write_Profile_Phase) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Write_Profile_Phase) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.ARM.exidx.text.AD9959_Write_Profile_Phase) refers to ad9959.o(.text.AD9959_Write_Profile_Phase) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_FSK) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_FSK) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Set_FSK) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_FSK) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_FSK) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_FSK) refers to ad9959.o(.text.AD9959_Write_Profile_Freq) for AD9959_Write_Profile_Freq
    ad9959.o(.text.AD9959_Set_FSK) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Set_FSK) refers to ad9959.o(.text.AD9959_Set_FSK) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_ASK) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_ASK) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Set_ASK) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_ASK) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_ASK) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_ASK) refers to ad9959.o(.rodata.Reg_Len) for Reg_Len
    ad9959.o(.text.AD9959_Set_ASK) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Set_ASK) refers to ad9959.o(.text.AD9959_Set_ASK) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Set_PSK) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Set_PSK) refers to ad9959.o(.text.AD9959_Write_Byte_S) for AD9959_Write_Byte_S
    ad9959.o(.text.AD9959_Set_PSK) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(.text.AD9959_Set_PSK) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(.text.AD9959_Set_PSK) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(.text.AD9959_Set_PSK) refers to f2d.o(.text) for __aeabi_f2d
    ad9959.o(.text.AD9959_Set_PSK) refers to dfixi.o(.text) for __aeabi_d2iz
    ad9959.o(.text.AD9959_Set_PSK) refers to ad9959.o(.rodata.Reg_Len) for Reg_Len
    ad9959.o(.text.AD9959_Set_PSK) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.ARM.exidx.text.AD9959_Set_PSK) refers to ad9959.o(.text.AD9959_Set_PSK) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to usart.o(.bss.huart1) for huart1
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to my_usart.o(.text.my_printf) for my_printf
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to ad9959.o(.text.AD9959_Modulation_Init) for AD9959_Modulation_Init
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to ad9959.o(.data.AD9959_Start_ASK_Transmission.ask_amplitudes) for AD9959_Start_ASK_Transmission.ask_amplitudes
    ad9959.o(.text.AD9959_Start_ASK_Transmission) refers to ad9959.o(.text.AD9959_Set_ASK) for AD9959_Set_ASK
    ad9959.o(.ARM.exidx.text.AD9959_Start_ASK_Transmission) refers to ad9959.o(.text.AD9959_Start_ASK_Transmission) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Stop_ASK_Transmission) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.text.AD9959_Stop_ASK_Transmission) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Stop_ASK_Transmission) refers to usart.o(.bss.huart1) for huart1
    ad9959.o(.text.AD9959_Stop_ASK_Transmission) refers to my_usart.o(.text.my_printf) for my_printf
    ad9959.o(.ARM.exidx.text.AD9959_Stop_ASK_Transmission) refers to ad9959.o(.text.AD9959_Stop_ASK_Transmission) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P2_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P2_L) refers to ad9959.o(.text.AD9959_P2_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to usart.o(.bss.huart1) for huart1
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to my_usart.o(.text.my_printf) for my_printf
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to ad9959.o(.text.AD9959_Modulation_Init) for AD9959_Modulation_Init
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to ad9959.o(.data.AD9959_Start_FSK_Transmission.fsk_frequencies) for AD9959_Start_FSK_Transmission.fsk_frequencies
    ad9959.o(.text.AD9959_Start_FSK_Transmission) refers to ad9959.o(.text.AD9959_Set_FSK) for AD9959_Set_FSK
    ad9959.o(.ARM.exidx.text.AD9959_Start_FSK_Transmission) refers to ad9959.o(.text.AD9959_Start_FSK_Transmission) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Stop_FSK_Transmission) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.text.AD9959_Stop_FSK_Transmission) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_Stop_FSK_Transmission) refers to usart.o(.bss.huart1) for huart1
    ad9959.o(.text.AD9959_Stop_FSK_Transmission) refers to my_usart.o(.text.my_printf) for my_printf
    ad9959.o(.ARM.exidx.text.AD9959_Stop_FSK_Transmission) refers to ad9959.o(.text.AD9959_Stop_FSK_Transmission) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P3_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P3_L) refers to ad9959.o(.text.AD9959_P3_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to ad9959.o(.rodata.cst4) for ask_test_pattern
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to usart.o(.bss.huart1) for huart1
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to my_usart.o(.text.my_printf) for my_printf
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to ad9959.o(.rodata.str1.1) for .L.str.7
    ad9959.o(.text.AD9959_Modulation_State_Update) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_Modulation_State_Update) refers to ad9959.o(.text.AD9959_Modulation_State_Update) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P2_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P2_H) refers to ad9959.o(.text.AD9959_P2_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P3_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P3_H) refers to ad9959.o(.text.AD9959_P3_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Is_ASK_Active) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.ARM.exidx.text.AD9959_Is_ASK_Active) refers to ad9959.o(.text.AD9959_Is_ASK_Active) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Is_FSK_Active) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.ARM.exidx.text.AD9959_Is_FSK_Active) refers to ad9959.o(.text.AD9959_Is_FSK_Active) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_proc) refers to ad9959.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ad9959.o(.text.AD9959_proc) refers to ad9959.o(.data.pid_vin) for pid_vin
    ad9959.o(.text.AD9959_proc) refers to ad9959.o(.text.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(.text.AD9959_proc) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.text.AD9959_proc) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    ad9959.o(.text.AD9959_proc) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    ad9959.o(.ARM.exidx.text.AD9959_proc) refers to ad9959.o(.text.AD9959_proc) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P0_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P0_H) refers to ad9959.o(.text.AD9959_P0_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P0_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P0_L) refers to ad9959.o(.text.AD9959_P0_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P1_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P1_H) refers to ad9959.o(.text.AD9959_P1_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_P1_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_P1_L) refers to ad9959.o(.text.AD9959_P1_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_PDC_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_PDC_H) refers to ad9959.o(.text.AD9959_PDC_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_CS_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_CS_L) refers to ad9959.o(.text.AD9959_CS_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SCK_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SCK_H) refers to ad9959.o(.text.AD9959_SCK_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO0_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO0_H) refers to ad9959.o(.text.AD9959_SDIO0_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO0_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO0_L) refers to ad9959.o(.text.AD9959_SDIO0_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO1_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO1_H) refers to ad9959.o(.text.AD9959_SDIO1_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO1_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO1_L) refers to ad9959.o(.text.AD9959_SDIO1_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO2_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO2_H) refers to ad9959.o(.text.AD9959_SDIO2_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO2_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO2_L) refers to ad9959.o(.text.AD9959_SDIO2_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO3_H) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO3_H) refers to ad9959.o(.text.AD9959_SDIO3_H) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_SDIO3_L) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_SDIO3_L) refers to ad9959.o(.text.AD9959_SDIO3_L) for [Anonymous Symbol]
    ad9959.o(.text.AD9959_Write_Byte_S) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(.ARM.exidx.text.AD9959_Write_Byte_S) refers to ad9959.o(.text.AD9959_Write_Byte_S) for [Anonymous Symbol]
    da_output.o(.text.DA_Init) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.text.DA_Init) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(.text.DA_Init) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(.text.DA_Init) refers to cmd_to_fun.o(.text.DA_FPGA_START) for DA_FPGA_START
    da_output.o(.ARM.exidx.text.DA_Init) refers to da_output.o(.text.DA_Init) for [Anonymous Symbol]
    da_output.o(.text.DA_SetConfig) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_SetConfig) refers to da_output.o(.text.DA_SetConfig) for [Anonymous Symbol]
    da_output.o(.text.DA_Apply_Settings) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(.text.DA_Apply_Settings) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.text.DA_Apply_Settings) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(.text.DA_Apply_Settings) refers to cmd_to_fun.o(.text.DA_FPGA_START) for DA_FPGA_START
    da_output.o(.ARM.exidx.text.DA_Apply_Settings) refers to da_output.o(.text.DA_Apply_Settings) for [Anonymous Symbol]
    da_output.o(.text.DA_SetFREQ) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_SetFREQ) refers to da_output.o(.text.DA_SetFREQ) for [Anonymous Symbol]
    da_output.o(.text.DA_GetFREQ) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_GetFREQ) refers to da_output.o(.text.DA_GetFREQ) for [Anonymous Symbol]
    da_output.o(.text.DA_SetAmp) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_SetAmp) refers to da_output.o(.text.DA_SetAmp) for [Anonymous Symbol]
    da_output.o(.text.DA_GetAmp) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_GetAmp) refers to da_output.o(.text.DA_GetAmp) for [Anonymous Symbol]
    da_output.o(.text.DA_SetPhase) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_SetPhase) refers to da_output.o(.text.DA_SetPhase) for [Anonymous Symbol]
    da_output.o(.text.DA_GetPhase) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_GetPhase) refers to da_output.o(.text.DA_GetPhase) for [Anonymous Symbol]
    da_output.o(.text.DA_SetWaveform) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_SetWaveform) refers to da_output.o(.text.DA_SetWaveform) for [Anonymous Symbol]
    da_output.o(.text.DA_GetWaveform) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.ARM.exidx.text.DA_GetWaveform) refers to da_output.o(.text.DA_GetWaveform) for [Anonymous Symbol]
    da_output.o(.text.wave_test) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    da_output.o(.text.wave_test) refers to da_output.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    da_output.o(.text.wave_test) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for DA_FPGA_STOP
    da_output.o(.text.wave_test) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    da_output.o(.text.wave_test) refers to cmd_to_fun.o(.text.DA_FPGA_START) for DA_FPGA_START
    da_output.o(.ARM.exidx.text.wave_test) refers to da_output.o(.text.wave_test) for [Anonymous Symbol]
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_ENABLE) for AD_FREQ_CLR_ENABLE
    freq_measure.o(.text.fre_measure) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_DISABLE) for AD_FREQ_CLR_DISABLE
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_START) for AD_FREQ_START
    freq_measure.o(.text.fre_measure) refers to cmd_to_fun.o(.text.AD_FREQ_STOP) for AD_FREQ_STOP
    freq_measure.o(.ARM.exidx.text.fre_measure) refers to freq_measure.o(.text.fre_measure) for [Anonymous Symbol]
    freq_measure.o(.text.fre_measure_ad1) refers to freq_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    freq_measure.o(.text.fre_measure_ad1) refers to freq_measure.o(.text.fre_measure) for fre_measure
    freq_measure.o(.ARM.exidx.text.fre_measure_ad1) refers to freq_measure.o(.text.fre_measure_ad1) for [Anonymous Symbol]
    freq_measure.o(.text.fre_measure_ad2) refers to freq_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    freq_measure.o(.text.fre_measure_ad2) refers to freq_measure.o(.text.fre_measure) for fre_measure
    freq_measure.o(.ARM.exidx.text.fre_measure_ad2) refers to freq_measure.o(.text.fre_measure_ad2) for [Anonymous Symbol]
    freq_measure.o(.text.freq_proc) refers to freq_measure.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    freq_measure.o(.text.freq_proc) refers to freq_measure.o(.text.fre_measure) for fre_measure
    freq_measure.o(.text.freq_proc) refers to f2d.o(.text) for __aeabi_f2d
    freq_measure.o(.text.freq_proc) refers to usart.o(.bss.huart3) for huart3
    freq_measure.o(.text.freq_proc) refers to my_usart.o(.text.my_printf) for my_printf
    freq_measure.o(.ARM.exidx.text.freq_proc) refers to freq_measure.o(.text.freq_proc) for [Anonymous Symbol]
    key_app.o(.text.key_proc) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_app.o(.text.key_proc) refers to key_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    key_app.o(.text.key_proc) refers to key_app.o(.text.key3_action_cycle_phase) for key3_action_cycle_phase
    key_app.o(.text.key_proc) refers to usart.o(.bss.huart3) for huart3
    key_app.o(.text.key_proc) refers to my_usart.o(.text.my_printf) for my_printf
    key_app.o(.text.key_proc) refers to ad_measure.o(.bss.fifo_data1_f) for fifo_data1_f
    key_app.o(.text.key_proc) refers to my_fft.o(.text.calculate_fft_spectrum) for calculate_fft_spectrum
    key_app.o(.text.key_proc) refers to my_fft.o(.text.output_fft_spectrum) for output_fft_spectrum
    key_app.o(.ARM.exidx.text.key_proc) refers to key_app.o(.text.key_proc) for [Anonymous Symbol]
    key_app.o(.text.set_current_ad_frequency) refers to key_app.o(.data.current_ad_freq) for current_ad_freq
    key_app.o(.ARM.exidx.text.set_current_ad_frequency) refers to key_app.o(.text.set_current_ad_frequency) for [Anonymous Symbol]
    key_app.o(.text.get_current_ad_frequency) refers to key_app.o(.data.current_ad_freq) for current_ad_freq
    key_app.o(.ARM.exidx.text.get_current_ad_frequency) refers to key_app.o(.text.get_current_ad_frequency) for [Anonymous Symbol]
    key_app.o(.text.key3_action_cycle_phase) refers to key_app.o(.data.key3_action_cycle_phase.current_mode) for key3_action_cycle_phase.current_mode
    key_app.o(.text.key3_action_cycle_phase) refers to key_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    key_app.o(.text.key3_action_cycle_phase) refers to da_output.o(.text.DA_SetPhase) for DA_SetPhase
    key_app.o(.text.key3_action_cycle_phase) refers to da_output.o(.text.DA_Apply_Settings) for DA_Apply_Settings
    key_app.o(.text.key3_action_cycle_phase) refers to usart.o(.bss.huart3) for huart3
    key_app.o(.text.key3_action_cycle_phase) refers to key_app.o(.rodata.str1.1) for .L.str.4
    key_app.o(.text.key3_action_cycle_phase) refers to my_usart.o(.text.my_printf) for my_printf
    key_app.o(.text.key3_action_cycle_phase) refers to key_app.o(.rodata.key3_action_cycle_phase.amp_values) for key3_action_cycle_phase.amp_values
    key_app.o(.text.key3_action_cycle_phase) refers to da_output.o(.text.DA_SetAmp) for DA_SetAmp
    key_app.o(.text.key3_action_cycle_phase) refers to da_output.o(.text.DA_SetWaveform) for DA_SetWaveform
    key_app.o(.text.key3_action_cycle_phase) refers to da_output.o(.text.DA_SetFREQ) for DA_SetFREQ
    key_app.o(.text.key3_action_cycle_phase) refers to dfltui.o(.text) for __aeabi_ui2d
    key_app.o(.ARM.exidx.text.key3_action_cycle_phase) refers to key_app.o(.text.key3_action_cycle_phase) for [Anonymous Symbol]
    my_fft.o(.text.generate_hanning_window) refers to my_fft.o(.bss.window_buffer) for window_buffer
    my_fft.o(.text.generate_hanning_window) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(.ARM.exidx.text.generate_hanning_window) refers to my_fft.o(.text.generate_hanning_window) for [Anonymous Symbol]
    my_fft.o(.text.fft_init) refers to my_fft.o(.bss.fft_instance) for fft_instance
    my_fft.o(.text.fft_init) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for arm_cfft_radix4_init_f32
    my_fft.o(.text.fft_init) refers to my_fft.o(.bss.window_buffer) for window_buffer
    my_fft.o(.text.fft_init) refers to arm_cos_f32.o(.text.arm_cos_f32) for arm_cos_f32
    my_fft.o(.ARM.exidx.text.fft_init) refers to my_fft.o(.text.fft_init) for [Anonymous Symbol]
    my_fft.o(.text.calculate_fft_spectrum) refers to my_fft.o(.bss.fft_input_buffer) for fft_input_buffer
    my_fft.o(.text.calculate_fft_spectrum) refers to memseta.o(.text) for __aeabi_memclr4
    my_fft.o(.text.calculate_fft_spectrum) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.text.calculate_fft_spectrum) refers to my_fft.o(.bss.fft_instance) for fft_instance
    my_fft.o(.text.calculate_fft_spectrum) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for arm_cfft_radix4_f32
    my_fft.o(.text.calculate_fft_spectrum) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for arm_cmplx_mag_f32
    my_fft.o(.ARM.exidx.text.calculate_fft_spectrum) refers to my_fft.o(.text.calculate_fft_spectrum) for [Anonymous Symbol]
    my_fft.o(.text.output_fft_spectrum) refers to key_app.o(.text.get_current_ad_frequency) for get_current_ad_frequency
    my_fft.o(.text.output_fft_spectrum) refers to usart.o(.bss.huart3) for huart3
    my_fft.o(.text.output_fft_spectrum) refers to my_usart.o(.text.my_printf) for my_printf
    my_fft.o(.text.output_fft_spectrum) refers to f2d.o(.text) for __aeabi_f2d
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.rodata.str1.1) for .L.str.5
    my_fft.o(.text.output_fft_spectrum) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.text.calculate_thd) for calculate_thd
    my_fft.o(.text.output_fft_spectrum) refers to my_fft.o(.text.calculate_sinad) for calculate_sinad
    my_fft.o(.ARM.exidx.text.output_fft_spectrum) refers to my_fft.o(.text.output_fft_spectrum) for [Anonymous Symbol]
    my_fft.o(.text.get_precise_peak_frequency) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.ARM.exidx.text.get_precise_peak_frequency) refers to my_fft.o(.text.get_precise_peak_frequency) for [Anonymous Symbol]
    my_fft.o(.text.round_to_nearest_k) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    my_fft.o(.ARM.exidx.text.round_to_nearest_k) refers to my_fft.o(.text.round_to_nearest_k) for [Anonymous Symbol]
    my_fft.o(.text.calculate_thd) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.ARM.exidx.text.calculate_thd) refers to my_fft.o(.text.calculate_thd) for [Anonymous Symbol]
    my_fft.o(.text.calculate_thd_n) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.ARM.exidx.text.calculate_thd_n) refers to my_fft.o(.text.calculate_thd_n) for [Anonymous Symbol]
    my_fft.o(.text.calculate_sinad) refers to my_fft.o(.bss.fft_magnitude) for fft_magnitude
    my_fft.o(.text.calculate_sinad) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    my_fft.o(.ARM.exidx.text.calculate_sinad) refers to my_fft.o(.text.calculate_sinad) for [Anonymous Symbol]
    my_filter.o(.text.arm_fir_f32_lp) refers to my_filter.o(.rodata.B) for B
    my_filter.o(.text.arm_fir_f32_lp) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for arm_fir_init_f32
    my_filter.o(.ARM.exidx.text.arm_fir_f32_lp) refers to my_filter.o(.text.arm_fir_f32_lp) for [Anonymous Symbol]
    phase_measure.o(.text.calculate_phase_diff) refers to phase_measure.o(.bss.phase_diff) for phase_diff
    phase_measure.o(.ARM.exidx.text.calculate_phase_diff) refers to phase_measure.o(.text.calculate_phase_diff) for [Anonymous Symbol]
    kalman.o(.ARM.exidx.text.Kalman_init) refers to kalman.o(.text.Kalman_init) for [Anonymous Symbol]
    kalman.o(.ARM.exidx.text.kalman_filter) refers to kalman.o(.text.kalman_filter) for [Anonymous Symbol]
    kalman.o(.text.kalman) refers to kalman.o(.bss.current) for current
    kalman.o(.text.kalman) refers to kalman.o(.data.last) for last
    kalman.o(.text.kalman) refers to kalman.o(.bss.state) for state
    kalman.o(.ARM.exidx.text.kalman) refers to kalman.o(.text.kalman) for [Anonymous Symbol]
    kalman.o(.text.kalman_thd) refers to kalman.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    kalman.o(.text.kalman_thd) refers to kalman.o(.data.last1) for last1
    kalman.o(.ARM.exidx.text.kalman_thd) refers to kalman.o(.text.kalman_thd) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Send_String) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Send_String) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Send_String) refers to my_hmi.o(.text.HMI_Send_String) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Send_Int) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Send_Int) refers to my_hmi.o(.rodata.str1.1) for .L.str.1
    my_hmi.o(.text.HMI_Send_Int) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Send_Int) refers to my_hmi.o(.text.HMI_Send_Int) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Send_Float) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Send_Float) refers to f2d.o(.text) for __aeabi_f2d
    my_hmi.o(.text.HMI_Send_Float) refers to dflti.o(.text) for __aeabi_i2d
    my_hmi.o(.text.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(.text.HMI_Send_Float) refers to dmul.o(.text) for __aeabi_dmul
    my_hmi.o(.text.HMI_Send_Float) refers to dfixi.o(.text) for __aeabi_d2iz
    my_hmi.o(.text.HMI_Send_Float) refers to my_hmi.o(.rodata.str1.1) for .L.str.1
    my_hmi.o(.text.HMI_Send_Float) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Send_Float) refers to my_hmi.o(.text.HMI_Send_Float) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Wave_Clear) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Wave_Clear) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Wave_Clear) refers to my_hmi.o(.text.HMI_Wave_Clear) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Write_Wave_Low) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Write_Wave_Low) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Low) refers to my_hmi.o(.text.HMI_Write_Wave_Low) for [Anonymous Symbol]
    my_hmi.o(.text.HMI_Write_Wave_Fast) refers to memcpya.o(.text) for __aeabi_memcpy4
    my_hmi.o(.text.HMI_Write_Wave_Fast) refers to my_usart.o(.text.my_printf) for my_printf
    my_hmi.o(.text.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Fast) refers to my_hmi.o(.text.HMI_Write_Wave_Fast) for [Anonymous Symbol]
    my_usart.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    my_usart.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(.ARM.exidx.text.my_printf) refers to my_usart.o(.text.my_printf) for [Anonymous Symbol]
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss.rxBuffer1) for rxBuffer1
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart1) for huart1
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss.rxBuffer3) for rxBuffer3
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart3) for huart3
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss.rxBuffer2) for rxBuffer2
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to my_usart_pack.o(.text.ParseFrame) for ParseFrame
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to memseta.o(.text) for __aeabi_memclr4
    my_usart.o(.text.HAL_UART_RxCpltCallback) refers to usart.o(.bss.huart2) for huart2
    my_usart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to my_usart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    my_usart_pack.o(.text.SetParseTemplate) refers to my_usart_pack.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart_pack.o(.text.SetParseTemplate) refers to memcpya.o(.text) for __aeabi_memcpy
    my_usart_pack.o(.ARM.exidx.text.SetParseTemplate) refers to my_usart_pack.o(.text.SetParseTemplate) for [Anonymous Symbol]
    my_usart_pack.o(.text.ParseFrame) refers to my_usart_pack.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart_pack.o(.ARM.exidx.text.ParseFrame) refers to my_usart_pack.o(.text.ParseFrame) for [Anonymous Symbol]
    my_usart_pack.o(.text.PrepareFrame) refers to my_usart_pack.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    my_usart_pack.o(.ARM.exidx.text.PrepareFrame) refers to my_usart_pack.o(.text.PrepareFrame) for [Anonymous Symbol]
    my_usart_pack.o(.text.SendFrame) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(.ARM.exidx.text.SendFrame) refers to my_usart_pack.o(.text.SendFrame) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.CTRL_INIT) refers to cmd_to_fun.o(.text.CTRL_INIT) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_START) refers to cmd_to_fun.o(.text.DA_FPGA_START) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_STOP) refers to cmd_to_fun.o(.text.DA_FPGA_STOP) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_ENABLE) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_ENABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_DISABLE) refers to cmd_to_fun.o(.text.AD_FREQ_CLR_DISABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_START) refers to cmd_to_fun.o(.text.AD_FREQ_START) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_STOP) refers to cmd_to_fun.o(.text.AD_FREQ_STOP) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_SET) refers to cmd_to_fun.o(.text.AD_FREQ_SET) for [Anonymous Symbol]
    cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE) refers to cmd_to_fun.o(.rodata..Lswitch.table.AD_FIFO_WRITE_ENABLE) for .Lswitch.table.AD_FIFO_WRITE_ENABLE
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_ENABLE) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_DISABLE) refers to cmd_to_fun.o(.text.AD_FIFO_WRITE_DISABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_ENABLE) refers to cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE) for [Anonymous Symbol]
    cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_DISABLE) refers to cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE) for [Anonymous Symbol]
    scheduler.o(.text.uart_proc) refers to ad_measure.o(.bss..L_MergedGlobals) for vol_amp2
    scheduler.o(.text.uart_proc) refers to f2d.o(.text) for __aeabi_f2d
    scheduler.o(.text.uart_proc) refers to app_pid.o(.data.output) for output
    scheduler.o(.text.uart_proc) refers to usart.o(.bss.huart1) for huart1
    scheduler.o(.text.uart_proc) refers to my_usart.o(.text.my_printf) for my_printf
    scheduler.o(.ARM.exidx.text.uart_proc) refers to scheduler.o(.text.uart_proc) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for scheduler_task
    scheduler.o(.text.scheduler_run) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to key_app.o(.text.key_proc) for key_proc
    app_pid.o(.text.PID_Init) refers to app_pid.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    app_pid.o(.ARM.exidx.text.PID_Init) refers to app_pid.o(.text.PID_Init) for [Anonymous Symbol]
    app_pid.o(.ARM.exidx.text.increment_pid_ctrl) refers to app_pid.o(.text.increment_pid_ctrl) for [Anonymous Symbol]
    app_pid.o(.text.Pid_Proc) refers to ad_measure.o(.bss..L_MergedGlobals) for vol_amp2
    app_pid.o(.text.Pid_Proc) refers to app_pid.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    app_pid.o(.text.Pid_Proc) refers to app_pid.o(.data.output) for output
    app_pid.o(.text.Pid_Proc) refers to ad9959.o(.data.pid_vin) for pid_vin
    app_pid.o(.ARM.exidx.text.Pid_Proc) refers to app_pid.o(.text.Pid_Proc) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) refers to dac.o(.text.HAL_DAC_MspInit) for HAL_DAC_MspInit
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Init) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit) refers to dac.o(.text.HAL_DAC_MspDeInit) for HAL_DAC_MspDeInit
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DeInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) for DAC_DMAConvCpltCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) for DAC_DMAHalfConvCpltCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) for DAC_DMAErrorCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) for DAC_DMAConvCpltCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) for DAC_DMAHalfConvCpltCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) for DAC_DMAErrorCh1
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start_DMA) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1) for HAL_DAC_ConvCpltCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1) for HAL_DAC_ConvHalfCpltCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1) for HAL_DAC_ErrorCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAErrorCh1) refers to stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop_DMA) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2) for HAL_DACEx_DMAUnderrunCallbackCh2
    stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1) for HAL_DAC_DMAUnderrunCallbackCh1
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_IRQHandler) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DMAUnderrunCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_SetValue) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvCpltCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvHalfCpltCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ErrorCallbackCh1) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetValue) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConfigChannel) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetState) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetError) refers to stm32f4xx_hal_dac.o(.text.HAL_DAC_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStart) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStart) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStop) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStop) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_TriangleWaveGenerate) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_TriangleWaveGenerate) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_NoiseWaveGenerate) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_NoiseWaveGenerate) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualSetValue) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualSetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvCpltCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvHalfCpltCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ErrorCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DMAUnderrunCallbackCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualGetValue) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualGetValue) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2) for HAL_DACEx_ConvCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2) for HAL_DACEx_ConvHalfCpltCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2) for [Anonymous Symbol]
    stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2) for HAL_DACEx_ErrorCallbackCh2
    stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAErrorCh2) refers to stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_GetBank2WRP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_GetBank2WRP) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterUnderDriveSTOPMode) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterUnderDriveSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickPrio) for uwTickPrio
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data.uwTickFreq) for uwTickFreq
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableMemorySwappingBank) refers to stm32f4xx_hal.o(.text.HAL_EnableMemorySwappingBank) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableMemorySwappingBank) refers to stm32f4xx_hal.o(.text.HAL_DisableMemorySwappingBank) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Extended_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_GetECC) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_IOSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_IOSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SendCommand) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_ProgramRefreshRate) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SetAutoRefreshNumber) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_GetModeStatus) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to fmc.o(.text.HAL_SRAM_MspInit) for HAL_SRAM_MspInit
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init) for FMC_NORSRAM_Init
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init) for FMC_NORSRAM_Timing_Init
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init) for FMC_NORSRAM_Extended_Timing_Init
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Init) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) refers to fmc.o(.text.HAL_SRAM_MspDeInit) for HAL_SRAM_MspDeInit
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit) for FMC_NORSRAM_DeInit
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DeInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspDeInit) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferCpltCallback) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferErrorCallback) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_8b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_8b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_16b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_16b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_32b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_32b) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback) for HAL_SRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback) for HAL_SRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_DMA) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable) for FMC_NORSRAM_WriteOperation_Enable
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Enable) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable) for FMC_NORSRAM_WriteOperation_Disable
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Disable) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_GetState) refers to stm32f4xx_hal_sram.o(.text.HAL_SRAM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to my_usart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to my_usart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    arm_cos_f32.o(.text.arm_cos_f32) refers to arm_common_tables.o(.rodata.sinTable_f32) for sinTable_f32
    arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32) refers to arm_cos_f32.o(.text.arm_cos_f32) for [Anonymous Symbol]
    arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32) refers to arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32) for [Anonymous Symbol]
    arm_fir_init_f32.o(.text.arm_fir_init_f32) refers to memseta.o(.text) for __aeabi_memclr4
    arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32) refers to arm_fir_init_f32.o(.text.arm_fir_init_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for arm_radix4_butterfly_inverse_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for arm_radix4_butterfly_f32
    arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for arm_bitreversal_f32
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32) refers to arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32) for [Anonymous Symbol]
    arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32) refers to arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32) for [Anonymous Symbol]
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.twiddleCoef_4096) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) refers to arm_common_tables.o(.rodata.armBitRevTable) for armBitRevTable
    arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32) refers to arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32) refers to arm_bitreversal.o(.text.arm_bitreversal_f32) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31) refers to arm_bitreversal.o(.text.arm_bitreversal_q31) for [Anonymous Symbol]
    arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15) refers to arm_bitreversal.o(.text.arm_bitreversal_q15) for [Anonymous Symbol]
    log10f.o(i.__hardfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    log10f.o(i.__hardfp_log10f) refers to errno.o(i.__set_errno) for __set_errno
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    log10f.o(i.__hardfp_log10f) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    log10f.o(i.__hardfp_log10f) refers to log10f.o(.constdata) for .constdata
    log10f.o(i.__softfp_log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.__softfp_log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(i.log10f) refers (Special) to iusefp.o(.text) for __I$use$fp
    log10f.o(i.log10f) refers to log10f.o(i.__hardfp_log10f) for __hardfp_log10f
    log10f.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to errno.o(i.__set_errno) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(.text) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflti.o(.text) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(.text) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to dadd.o(.text) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to dscalb.o(.text) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to iusefp.o(.text) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    frnd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429xx.o(HEAP), (512 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dac.o(.text), (0 bytes).
    Removing dac.o(.ARM.exidx.text.MX_DAC_Init), (8 bytes).
    Removing dac.o(.ARM.exidx.text.HAL_DAC_MspInit), (8 bytes).
    Removing dac.o(.text.HAL_DAC_MspDeInit), (42 bytes).
    Removing dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing fmc.o(.text), (0 bytes).
    Removing fmc.o(.ARM.exidx.text.MX_FMC_Init), (8 bytes).
    Removing fmc.o(.ARM.exidx.text.HAL_SRAM_MspInit), (8 bytes).
    Removing fmc.o(.text.HAL_SRAM_MspDeInit), (94 bytes).
    Removing fmc.o(.ARM.exidx.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing fmc.o(.bss.FMC_DeInitialized), (1 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART3_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (166 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing ad_measure.o(.text), (0 bytes).
    Removing ad_measure.o(.text.findMinMax), (272 bytes).
    Removing ad_measure.o(.ARM.exidx.text.findMinMax), (8 bytes).
    Removing ad_measure.o(.text.setSamplingFrequency), (172 bytes).
    Removing ad_measure.o(.ARM.exidx.text.setSamplingFrequency), (8 bytes).
    Removing ad_measure.o(.text.readFIFOData), (1108 bytes).
    Removing ad_measure.o(.ARM.exidx.text.readFIFOData), (8 bytes).
    Removing ad_measure.o(.text.vpp_adc_parallel), (1564 bytes).
    Removing ad_measure.o(.ARM.exidx.text.vpp_adc_parallel), (8 bytes).
    Removing ad_measure.o(.text.ad_proc), (24 bytes).
    Removing ad_measure.o(.ARM.exidx.text.ad_proc), (8 bytes).
    Removing ad_measure.o(.bss.fifo_data1), (2048 bytes).
    Removing ad_measure.o(.bss.fifo_data2), (2048 bytes).
    Removing ad_measure.o(.bss.fifo_data2_f), (4096 bytes).
    Removing ad_measure.o(.bss..L_MergedGlobals), (16 bytes).
    Removing ad9959.o(.text), (0 bytes).
    Removing ad9959.o(.text.AD9959_Reset), (50 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Reset), (8 bytes).
    Removing ad9959.o(.text.AD9959_RST_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_RST_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_RST_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_RST_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_IO_Update), (62 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_IO_Update), (8 bytes).
    Removing ad9959.o(.text.AD9959_UP_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_UP_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_UP_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_UP_H), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Init), (8 bytes).
    Removing ad9959.o(.text.AD9959_CS_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_CS_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_SCK_L), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SCK_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_PDC_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_PDC_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_Select_Channel), (52 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Select_Channel), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Freq), (104 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Freq), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Phase), (96 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Phase), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Amp), (76 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Amp), (8 bytes).
    Removing ad9959.o(.text.AD9959_Single_Output), (304 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Single_Output), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Linear_Sweep), (110 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Linear_Sweep), (8 bytes).
    Removing ad9959.o(.text.AD9959_Write_LSRR), (60 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_LSRR), (8 bytes).
    Removing ad9959.o(.text.AD9959_Write_RDW), (74 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_RDW), (8 bytes).
    Removing ad9959.o(.text.AD9959_Write_FDW), (74 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_FDW), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Freq_Sweep), (544 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Freq_Sweep), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Amp_Sweep), (536 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Amp_Sweep), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_Phase_Sweep), (592 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_Phase_Sweep), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Modulation_Init), (8 bytes).
    Removing ad9959.o(.text.AD9959_Write_Profile_Freq), (136 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_Profile_Freq), (8 bytes).
    Removing ad9959.o(.text.AD9959_Write_Profile_Amp), (116 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_Profile_Amp), (8 bytes).
    Removing ad9959.o(.text.AD9959_Write_Profile_Phase), (104 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_Profile_Phase), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_FSK), (312 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_FSK), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_ASK), (8 bytes).
    Removing ad9959.o(.text.AD9959_Set_PSK), (424 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Set_PSK), (8 bytes).
    Removing ad9959.o(.text.AD9959_Start_ASK_Transmission), (172 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Start_ASK_Transmission), (8 bytes).
    Removing ad9959.o(.text.AD9959_Stop_ASK_Transmission), (80 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Stop_ASK_Transmission), (8 bytes).
    Removing ad9959.o(.text.AD9959_P2_L), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P2_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_Start_FSK_Transmission), (164 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Start_FSK_Transmission), (8 bytes).
    Removing ad9959.o(.text.AD9959_Stop_FSK_Transmission), (80 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Stop_FSK_Transmission), (8 bytes).
    Removing ad9959.o(.text.AD9959_P3_L), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P3_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_Modulation_State_Update), (588 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Modulation_State_Update), (8 bytes).
    Removing ad9959.o(.text.AD9959_P2_H), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P2_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_P3_H), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P3_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_Is_ASK_Active), (12 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Is_ASK_Active), (8 bytes).
    Removing ad9959.o(.text.AD9959_Is_FSK_Active), (12 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Is_FSK_Active), (8 bytes).
    Removing ad9959.o(.text.AD9959_proc), (192 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_proc), (8 bytes).
    Removing ad9959.o(.text.AD9959_P0_H), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P0_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_P0_L), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P0_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_P1_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P1_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_P1_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_P1_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_PDC_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_PDC_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_CS_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_CS_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_SCK_H), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SCK_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO0_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO0_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO0_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO0_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO1_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO1_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO1_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO1_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO2_H), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO2_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO2_L), (18 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO2_L), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO3_H), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO3_H), (8 bytes).
    Removing ad9959.o(.text.AD9959_SDIO3_L), (16 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_SDIO3_L), (8 bytes).
    Removing ad9959.o(.ARM.exidx.text.AD9959_Write_Byte_S), (8 bytes).
    Removing ad9959.o(.data.AD9959_Start_ASK_Transmission.ask_amplitudes), (32 bytes).
    Removing ad9959.o(.data.AD9959_Start_FSK_Transmission.fsk_frequencies), (64 bytes).
    Removing ad9959.o(.rodata.cst4), (8 bytes).
    Removing ad9959.o(.rodata.str1.1), (20 bytes).
    Removing ad9959.o(.data.pid_vin), (4 bytes).
    Removing ad9959.o(.bss..L_MergedGlobals), (32 bytes).
    Removing da_output.o(.text), (0 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_Init), (8 bytes).
    Removing da_output.o(.text.DA_SetConfig), (96 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_SetConfig), (8 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_Apply_Settings), (8 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_SetFREQ), (8 bytes).
    Removing da_output.o(.text.DA_GetFREQ), (36 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_GetFREQ), (8 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_SetAmp), (8 bytes).
    Removing da_output.o(.text.DA_GetAmp), (28 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_GetAmp), (8 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_SetPhase), (8 bytes).
    Removing da_output.o(.text.DA_GetPhase), (28 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_GetPhase), (8 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_SetWaveform), (8 bytes).
    Removing da_output.o(.text.DA_GetWaveform), (28 bytes).
    Removing da_output.o(.ARM.exidx.text.DA_GetWaveform), (8 bytes).
    Removing da_output.o(.text.wave_test), (292 bytes).
    Removing da_output.o(.ARM.exidx.text.wave_test), (8 bytes).
    Removing freq_measure.o(.text), (0 bytes).
    Removing freq_measure.o(.text.fre_measure), (204 bytes).
    Removing freq_measure.o(.ARM.exidx.text.fre_measure), (8 bytes).
    Removing freq_measure.o(.text.fre_measure_ad1), (20 bytes).
    Removing freq_measure.o(.ARM.exidx.text.fre_measure_ad1), (8 bytes).
    Removing freq_measure.o(.text.fre_measure_ad2), (26 bytes).
    Removing freq_measure.o(.ARM.exidx.text.fre_measure_ad2), (8 bytes).
    Removing freq_measure.o(.text.freq_proc), (84 bytes).
    Removing freq_measure.o(.ARM.exidx.text.freq_proc), (8 bytes).
    Removing freq_measure.o(.bss..L_MergedGlobals), (24 bytes).
    Removing key_app.o(.text), (0 bytes).
    Removing key_app.o(.ARM.exidx.text.key_proc), (8 bytes).
    Removing key_app.o(.text.set_current_ad_frequency), (14 bytes).
    Removing key_app.o(.ARM.exidx.text.set_current_ad_frequency), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.get_current_ad_frequency), (8 bytes).
    Removing key_app.o(.ARM.exidx.text.key3_action_cycle_phase), (8 bytes).
    Removing my_fft.o(.text), (0 bytes).
    Removing my_fft.o(.text.generate_hanning_window), (96 bytes).
    Removing my_fft.o(.ARM.exidx.text.generate_hanning_window), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.fft_init), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_fft_spectrum), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.output_fft_spectrum), (8 bytes).
    Removing my_fft.o(.text.get_precise_peak_frequency), (356 bytes).
    Removing my_fft.o(.ARM.exidx.text.get_precise_peak_frequency), (8 bytes).
    Removing my_fft.o(.text.round_to_nearest_k), (32 bytes).
    Removing my_fft.o(.ARM.exidx.text.round_to_nearest_k), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_thd), (8 bytes).
    Removing my_fft.o(.text.calculate_thd_n), (312 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_thd_n), (8 bytes).
    Removing my_fft.o(.ARM.exidx.text.calculate_sinad), (8 bytes).
    Removing my_filter.o(.text), (0 bytes).
    Removing my_filter.o(.text.arm_fir_f32_lp), (72 bytes).
    Removing my_filter.o(.ARM.exidx.text.arm_fir_f32_lp), (8 bytes).
    Removing my_filter.o(.rodata.BL), (4 bytes).
    Removing my_filter.o(.rodata.B), (204 bytes).
    Removing phase_measure.o(.text), (0 bytes).
    Removing phase_measure.o(.text.calculate_phase_diff), (280 bytes).
    Removing phase_measure.o(.ARM.exidx.text.calculate_phase_diff), (8 bytes).
    Removing phase_measure.o(.bss.phase_diff), (4 bytes).
    Removing kalman.o(.text), (0 bytes).
    Removing kalman.o(.text.Kalman_init), (38 bytes).
    Removing kalman.o(.ARM.exidx.text.Kalman_init), (8 bytes).
    Removing kalman.o(.text.kalman_filter), (102 bytes).
    Removing kalman.o(.ARM.exidx.text.kalman_filter), (8 bytes).
    Removing kalman.o(.text.kalman), (344 bytes).
    Removing kalman.o(.ARM.exidx.text.kalman), (8 bytes).
    Removing kalman.o(.text.kalman_thd), (172 bytes).
    Removing kalman.o(.ARM.exidx.text.kalman_thd), (8 bytes).
    Removing kalman.o(.data.last), (1 bytes).
    Removing kalman.o(.bss.current), (1 bytes).
    Removing kalman.o(.bss.state), (280 bytes).
    Removing kalman.o(.data.last1), (1 bytes).
    Removing kalman.o(.bss..L_MergedGlobals), (32 bytes).
    Removing my_hmi.o(.text), (0 bytes).
    Removing my_hmi.o(.text.HMI_Send_String), (72 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Send_String), (8 bytes).
    Removing my_hmi.o(.text.HMI_Send_Int), (60 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Send_Int), (8 bytes).
    Removing my_hmi.o(.text.HMI_Send_Float), (128 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Send_Float), (8 bytes).
    Removing my_hmi.o(.text.HMI_Wave_Clear), (72 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Wave_Clear), (8 bytes).
    Removing my_hmi.o(.text.HMI_Write_Wave_Low), (76 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Low), (8 bytes).
    Removing my_hmi.o(.text.HMI_Write_Wave_Fast), (132 bytes).
    Removing my_hmi.o(.ARM.exidx.text.HMI_Write_Wave_Fast), (8 bytes).
    Removing my_hmi.o(.rodata.str1.1), (13 bytes).
    Removing my_usart.o(.text), (0 bytes).
    Removing my_usart.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing my_usart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing my_usart.o(.data.Adjust), (1 bytes).
    Removing my_usart_pack.o(.text), (0 bytes).
    Removing my_usart_pack.o(.text.SetParseTemplate), (56 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.SetParseTemplate), (8 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.ParseFrame), (8 bytes).
    Removing my_usart_pack.o(.text.PrepareFrame), (378 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.PrepareFrame), (8 bytes).
    Removing my_usart_pack.o(.text.SendFrame), (36 bytes).
    Removing my_usart_pack.o(.ARM.exidx.text.SendFrame), (8 bytes).
    Removing cmd_to_fun.o(.text), (0 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.CTRL_INIT), (8 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_START), (8 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.DA_FPGA_STOP), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_CLR_ENABLE), (38 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_ENABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_CLR_DISABLE), (34 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_CLR_DISABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_START), (34 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_START), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_STOP), (38 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_STOP), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FREQ_SET), (26 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FREQ_SET), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_WRITE_ENABLE), (34 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_ENABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_WRITE_DISABLE), (36 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_WRITE_DISABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_READ_ENABLE), (32 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_ENABLE), (8 bytes).
    Removing cmd_to_fun.o(.text.AD_FIFO_READ_DISABLE), (36 bytes).
    Removing cmd_to_fun.o(.ARM.exidx.text.AD_FIFO_READ_DISABLE), (8 bytes).
    Removing cmd_to_fun.o(.rodata..Lswitch.table.AD_FIFO_WRITE_ENABLE), (6 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.text.uart_proc), (88 bytes).
    Removing scheduler.o(.ARM.exidx.text.uart_proc), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.bss.i), (4 bytes).
    Removing app_pid.o(.text), (0 bytes).
    Removing app_pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing app_pid.o(.text.increment_pid_ctrl), (118 bytes).
    Removing app_pid.o(.ARM.exidx.text.increment_pid_ctrl), (8 bytes).
    Removing app_pid.o(.text.Pid_Proc), (200 bytes).
    Removing app_pid.o(.ARM.exidx.text.Pid_Proc), (8 bytes).
    Removing app_pid.o(.data.output), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Init), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_DeInit), (30 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Start), (94 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop), (32 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Start_DMA), (260 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.DAC_DMAConvCpltCh1), (16 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAConvCpltCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.DAC_DMAHalfConvCpltCh1), (6 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.DAC_DMAErrorCh1), (24 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.DAC_DMAErrorCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_Stop_DMA), (80 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_IRQHandler), (104 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_DMAUnderrunCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_DMAUnderrunCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_SetValue), (44 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_SetValue), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvCpltCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_ConvHalfCpltCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConvHalfCpltCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_ErrorCallbackCh1), (2 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ErrorCallbackCh1), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetValue), (14 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetValue), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetState), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetState), (8 bytes).
    Removing stm32f4xx_hal_dac.o(.text.HAL_DAC_GetError), (4 bytes).
    Removing stm32f4xx_hal_dac.o(.ARM.exidx.text.HAL_DAC_GetError), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStart), (76 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStart), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualStop), (28 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualStop), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_TriangleWaveGenerate), (66 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_TriangleWaveGenerate), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_NoiseWaveGenerate), (66 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_NoiseWaveGenerate), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualSetValue), (28 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualSetValue), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvCpltCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ConvHalfCpltCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ConvHalfCpltCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_ErrorCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_ErrorCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DMAUnderrunCallbackCh2), (2 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DMAUnderrunCallbackCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.HAL_DACEx_DualGetValue), (12 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.HAL_DACEx_DualGetValue), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.DAC_DMAConvCpltCh2), (16 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAConvCpltCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.DAC_DMAHalfConvCpltCh2), (6 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAHalfConvCpltCh2), (8 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.text.DAC_DMAErrorCh2), (24 bytes).
    Removing stm32f4xx_hal_dac_ex.o(.ARM.exidx.text.DAC_DMAErrorCh2), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (160 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (576 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (128 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (78 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (108 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI), (108 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLSAI), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLSAI), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (342 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (210 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (242 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (254 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (310 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (392 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (98 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (214 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (370 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (54 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram), (224 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig), (22 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_GetBank2WRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_GetBank2WRP), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (260 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (112 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1526 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Init), (318 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT), (160 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (340 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (392 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (56 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (100 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rodata.cst8), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (128 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (54 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (54 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (194 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableOverDrive), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive), (118 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableOverDrive), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterUnderDriveSTOPMode), (122 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterUnderDriveSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (44 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (46 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableMemorySwappingBank), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableMemorySwappingBank), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (152 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (26 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text), (0 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Extended_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_Init), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_CommonSpace_Timing_Init), (42 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_AttributeSpace_Timing_Init), (42 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_DeInit), (72 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Enable), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Disable), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC), (100 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_GetECC), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_Init), (44 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_CommonSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_AttributeSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_IOSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_IOSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_DeInit), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init), (134 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init), (222 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand), (94 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SendCommand), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_ProgramRefreshRate), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SetAutoRefreshNumber), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_GetModeStatus), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Init), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DeInit), (30 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_DMA_XferErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_8b), (68 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_8b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_8b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_8b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_16b), (68 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_16b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_16b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_16b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_32b), (68 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_32b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_32b), (80 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_32b), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Read_DMA), (82 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_Write_DMA), (102 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Enable), (44 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_WriteOperation_Disable), (56 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_hal_sram.o(.text.HAL_SRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sram.o(.ARM.exidx.text.HAL_SRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (102 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (118 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (126 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (52 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (314 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (72 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (150 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAError), (162 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (150 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (146 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (144 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (278 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (140 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (248 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (206 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (96 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (146 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (248 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (154 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (60 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt), (124 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt), (20 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing arm_cos_f32.o(.text), (0 bytes).
    Removing arm_cos_f32.o(.ARM.exidx.text.arm_cos_f32), (8 bytes).
    Removing arm_cmplx_mag_f32.o(.text), (0 bytes).
    Removing arm_cmplx_mag_f32.o(.ARM.exidx.text.arm_cmplx_mag_f32), (8 bytes).
    Removing arm_fir_init_f32.o(.text), (0 bytes).
    Removing arm_fir_init_f32.o(.text.arm_fir_init_f32), (32 bytes).
    Removing arm_fir_init_f32.o(.ARM.exidx.text.arm_fir_init_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_cfft_radix4_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_inverse_f32), (8 bytes).
    Removing arm_cfft_radix4_f32.o(.ARM.exidx.text.arm_radix4_butterfly_f32), (8 bytes).
    Removing arm_cfft_radix4_init_f32.o(.text), (0 bytes).
    Removing arm_cfft_radix4_init_f32.o(.ARM.exidx.text.arm_cfft_radix4_init_f32), (8 bytes).
    Removing arm_bitreversal.o(.text), (0 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_f32), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q31), (160 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q31), (8 bytes).
    Removing arm_bitreversal.o(.text.arm_bitreversal_q15), (112 bytes).
    Removing arm_bitreversal.o(.ARM.exidx.text.arm_bitreversal_q15), (8 bytes).
    Removing arm_common_tables.o(.text), (0 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_16), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_32), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_64), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_128), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_256), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_512), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_1024), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_2048), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_4096), (65536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q31), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q31), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q31), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q31), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q31), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q31), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q31), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q31), (12288 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q31), (24576 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_16_q15), (48 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_32_q15), (96 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_64_q15), (192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_128_q15), (384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_256_q15), (768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_512_q15), (1536 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_1024_q15), (3072 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_2048_q15), (6144 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_4096_q15), (12288 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTableF64_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable16), (40 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable32), (96 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable128), (416 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable256), (880 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable512), (896 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable1024), (3600 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable2048), (7616 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_16), (24 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_32), (48 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_64), (112 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_128), (224 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_256), (480 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_512), (960 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_1024), (1984 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_2048), (3968 bytes).
    Removing arm_common_tables.o(.rodata.armBitRevIndexTable_fixed_4096), (8064 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_32), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_64), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_256), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_1024), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoefF64_rfft_4096), (32768 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_32), (128 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_64), (256 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_256), (1024 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_1024), (4096 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.twiddleCoef_rfft_4096), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefA), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefB), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ31), (32768 bytes).
    Removing arm_common_tables.o(.rodata.realCoefAQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.realCoefBQ15), (16384 bytes).
    Removing arm_common_tables.o(.rodata.Weights_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.Weights_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.Weights_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.Weights_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factors_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_128), (256 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_512), (1024 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_2048), (4096 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ15_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ15_8192), (16384 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_128), (1024 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_128), (512 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_512), (4096 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_512), (2048 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_2048), (16384 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_2048), (8192 bytes).
    Removing arm_common_tables.o(.rodata.WeightsQ31_8192), (65536 bytes).
    Removing arm_common_tables.o(.rodata.cos_factorsQ31_8192), (32768 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ15), (128 bytes).
    Removing arm_common_tables.o(.rodata.armRecipTableQ31), (256 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q31), (2052 bytes).
    Removing arm_common_tables.o(.rodata.sinTable_q15), (1026 bytes).
    Removing dflti.o(.text), (34 bytes).
    Removing dfixi.o(.text), (62 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dscalb.o(.text), (46 bytes).
    Removing dsqrt.o(.text), (162 bytes).

1001 unused section(s) (total 927398 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/log10f.c                      0x00000000   Number         0  log10f.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    AD9959.c                                 0x00000000   Number         0  ad9959.o ABSOLUTE
    ad_measure.c                             0x00000000   Number         0  ad_measure.o ABSOLUTE
    app_pid.c                                0x00000000   Number         0  app_pid.o ABSOLUTE
    arm_bitreversal.c                        0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    arm_cfft_radix4_f32.c                    0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    arm_cfft_radix4_init_f32.c               0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    arm_cmplx_mag_f32.c                      0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    arm_common_tables.c                      0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    arm_cos_f32.c                            0x00000000   Number         0  arm_cos_f32.o ABSOLUTE
    arm_fir_init_f32.c                       0x00000000   Number         0  arm_fir_init_f32.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    cmd_to_fun.c                             0x00000000   Number         0  cmd_to_fun.o ABSOLUTE
    da_output.c                              0x00000000   Number         0  da_output.o ABSOLUTE
    dac.c                                    0x00000000   Number         0  dac.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    fmc.c                                    0x00000000   Number         0  fmc.o ABSOLUTE
    freq_measure.c                           0x00000000   Number         0  freq_measure.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    kalman.c                                 0x00000000   Number         0  kalman.o ABSOLUTE
    key_app.c                                0x00000000   Number         0  key_app.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    my_fft.c                                 0x00000000   Number         0  my_fft.o ABSOLUTE
    my_filter.c                              0x00000000   Number         0  my_filter.o ABSOLUTE
    my_hmi.c                                 0x00000000   Number         0  my_hmi.o ABSOLUTE
    my_usart.c                               0x00000000   Number         0  my_usart.o ABSOLUTE
    my_usart_pack.c                          0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    phase_measure.c                          0x00000000   Number         0  phase_measure.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dac.c                      0x00000000   Number         0  stm32f4xx_hal_dac.o ABSOLUTE
    stm32f4xx_hal_dac_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dac_ex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_sram.c                     0x00000000   Number         0  stm32f4xx_hal_sram.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_ll_fmc.c                       0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x080001c0   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080001c0   Section       36  startup_stm32f429xx.o(.text)
    .text                                    0x080001e4   Section        0  uldiv.o(.text)
    .text                                    0x08000246   Section        0  memseta.o(.text)
    .text                                    0x0800026a   Section        0  dmul.o(.text)
    .text                                    0x0800034e   Section        0  dfltui.o(.text)
    .text                                    0x08000368   Section        0  dfixui.o(.text)
    .text                                    0x0800039a   Section        0  f2d.o(.text)
    .text                                    0x080003c0   Section        0  uidiv.o(.text)
    .text                                    0x080003ec   Section        0  llshl.o(.text)
    .text                                    0x0800040a   Section        0  llushr.o(.text)
    .text                                    0x0800042a   Section        0  iusefp.o(.text)
    .text                                    0x0800042a   Section        0  frnd.o(.text)
    .text                                    0x08000466   Section        0  depilogue.o(.text)
    .text                                    0x08000520   Section        0  dadd.o(.text)
    .text                                    0x0800066e   Section        0  ddiv.o(.text)
    .text                                    0x0800074c   Section        0  dfixul.o(.text)
    .text                                    0x0800077c   Section       48  cdrcmple.o(.text)
    .text                                    0x080007ac   Section       48  init.o(.text)
    .text                                    0x080007dc   Section        0  llsshr.o(.text)
    .text                                    0x08000800   Section        0  fepilogue.o(.text)
    [Anonymous Symbol]                       0x08000870   Section        0  ad9959.o(.text.AD9959_Init)
    [Anonymous Symbol]                       0x080009b0   Section        0  ad9959.o(.text.AD9959_Modulation_Init)
    [Anonymous Symbol]                       0x08000a80   Section        0  ad9959.o(.text.AD9959_Set_ASK)
    AD9959_Write_Byte_S                      0x08000bf9   Thumb Code   300  ad9959.o(.text.AD9959_Write_Byte_S)
    [Anonymous Symbol]                       0x08000bf8   Section        0  ad9959.o(.text.AD9959_Write_Byte_S)
    [Anonymous Symbol]                       0x08000d24   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000d28   Section        0  cmd_to_fun.o(.text.CTRL_INIT)
    [Anonymous Symbol]                       0x08000d34   Section        0  da_output.o(.text.DA_Apply_Settings)
    [Anonymous Symbol]                       0x08000e24   Section        0  cmd_to_fun.o(.text.DA_FPGA_START)
    [Anonymous Symbol]                       0x08000e34   Section        0  cmd_to_fun.o(.text.DA_FPGA_STOP)
    [Anonymous Symbol]                       0x08000e44   Section        0  da_output.o(.text.DA_Init)
    [Anonymous Symbol]                       0x08000f5c   Section        0  da_output.o(.text.DA_SetAmp)
    [Anonymous Symbol]                       0x08000f78   Section        0  da_output.o(.text.DA_SetFREQ)
    [Anonymous Symbol]                       0x08000fbc   Section        0  da_output.o(.text.DA_SetPhase)
    [Anonymous Symbol]                       0x08000fd8   Section        0  da_output.o(.text.DA_SetWaveform)
    [Anonymous Symbol]                       0x08000ff4   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000ff8   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08001004   Section        0  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init)
    [Anonymous Symbol]                       0x0800104c   Section        0  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init)
    [Anonymous Symbol]                       0x080010c4   Section        0  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init)
    [Anonymous Symbol]                       0x08001124   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel)
    [Anonymous Symbol]                       0x08001174   Section        0  stm32f4xx_hal_dac.o(.text.HAL_DAC_Init)
    [Anonymous Symbol]                       0x080011a4   Section        0  dac.o(.text.HAL_DAC_MspInit)
    [Anonymous Symbol]                       0x08001210   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x08001290   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x080012b4   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x080012dc   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x0800146c   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x08001478   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08001484   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001490   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080014ac   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x080014e4   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001534   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x0800156c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001590   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x080015e8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001608   Section        0  stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive)
    [Anonymous Symbol]                       0x08001684   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback)
    [Anonymous Symbol]                       0x08001688   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x080017e8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS)
    [Anonymous Symbol]                       0x080017f4   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x0800181c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08001844   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x080018b0   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler)
    [Anonymous Symbol]                       0x080018cc   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001c28   Section        0  stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init)
    [Anonymous Symbol]                       0x08001c80   Section        0  fmc.o(.text.HAL_SRAM_MspInit)
    [Anonymous Symbol]                       0x08001d38   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001d64   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08001d68   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08001d6c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x08002064   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x080020c4   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x080021d8   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    [Anonymous Symbol]                       0x08002230   Section        0  my_usart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x08002390   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x0800250c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08002510   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08002514   Section        0  dac.o(.text.MX_DAC_Init)
    [Anonymous Symbol]                       0x08002570   Section        0  fmc.o(.text.MX_FMC_Init)
    [Anonymous Symbol]                       0x080025e4   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08002774   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x080027b0   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x080027ec   Section        0  usart.o(.text.MX_USART3_UART_Init)
    [Anonymous Symbol]                       0x08002828   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x0800282c   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08002834   Section        0  app_pid.o(.text.PID_Init)
    [Anonymous Symbol]                       0x08002870   Section        0  my_usart_pack.o(.text.ParseFrame)
    [Anonymous Symbol]                       0x080029b8   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080029bc   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080029c0   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080029c4   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08002a8c   Section        0  system_stm32f4xx.o(.text.SystemInit)
    UART_DMAAbortOnError                     0x08002aa1   Thumb Code    12  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08002aa0   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_Receive_IT                          0x08002aad   Thumb Code   188  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08002aac   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08002b69   Thumb Code   222  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08002b68   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08002c48   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08002c54   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x08002c60   Section        0  stm32f4xx_it.o(.text.USART3_IRQHandler)
    [Anonymous Symbol]                       0x08002c6c   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08002c70   Section        0  arm_bitreversal.o(.text.arm_bitreversal_f32)
    [Anonymous Symbol]                       0x08002d2e   Section        0  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    [Anonymous Symbol]                       0x08002d70   Section        0  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    [Anonymous Symbol]                       0x08002e04   Section        0  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    [Anonymous Symbol]                       0x08002f58   Section        0  arm_cos_f32.o(.text.arm_cos_f32)
    [Anonymous Symbol]                       0x08002ff0   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    [Anonymous Symbol]                       0x0800334c   Section        0  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    [Anonymous Symbol]                       0x080036c8   Section        0  my_fft.o(.text.calculate_fft_spectrum)
    [Anonymous Symbol]                       0x08003870   Section        0  my_fft.o(.text.calculate_sinad)
    [Anonymous Symbol]                       0x080039c0   Section        0  my_fft.o(.text.calculate_thd)
    [Anonymous Symbol]                       0x08003d58   Section        0  my_fft.o(.text.fft_init)
    [Anonymous Symbol]                       0x08003dcc   Section        0  key_app.o(.text.get_current_ad_frequency)
    key3_action_cycle_phase                  0x08003ddd   Thumb Code   384  key_app.o(.text.key3_action_cycle_phase)
    [Anonymous Symbol]                       0x08003ddc   Section        0  key_app.o(.text.key3_action_cycle_phase)
    key3_action_cycle_phase.wave_types       0x08003f5c   Number         0  key_app.o(.text.key3_action_cycle_phase)
    key3_action_cycle_phase.wave_names       0x08003f60   Number         0  key_app.o(.text.key3_action_cycle_phase)
    [Anonymous Symbol]                       0x08003ff4   Section        0  key_app.o(.text.key_proc)
    [Anonymous Symbol]                       0x080040b0   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x08004184   Section        0  my_usart.o(.text.my_printf)
    [Anonymous Symbol]                       0x080041c8   Section        0  my_fft.o(.text.output_fft_spectrum)
    __arm_cp.3_14                            0x08004350   Number         4  my_fft.o(.text.output_fft_spectrum)
    [Anonymous Symbol]                       0x08004714   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x08004724   Section        0  scheduler.o(.text.scheduler_run)
    i.__0vsnprintf                           0x08004770   Section        0  printfa.o(i.__0vsnprintf)
    i.__hardfp_log10f                        0x080047a4   Section        0  log10f.o(i.__hardfp_log10f)
    i.__hardfp_roundf                        0x08004924   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sqrtf                         0x080049be   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x080049f8   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08004a0c   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08004a14   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__scatterload_copy                     0x08004a24   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08004a32   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08004a34   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08004a44   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x08004a51   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x08004a50   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x08004bd5   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x08004bd4   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x080052b1   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x080052b0   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x080052d5   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x080052d4   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x08005303   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x08005302   Section        0  printfa.o(i._snputc)
    logahi                                   0x08005318   Data          32  log10f.o(.constdata)
    .constdata                               0x08005318   Section       64  log10f.o(.constdata)
    logalo                                   0x08005338   Data          32  log10f.o(.constdata)
    key3_action_cycle_phase.amp_values       0x08005b8a   Data          10  key_app.o(.rodata.key3_action_cycle_phase.amp_values)
    [Anonymous Symbol]                       0x08005b8a   Section        0  key_app.o(.rodata.key3_action_cycle_phase.amp_values)
    .L.str.5                                 0x08006398   Data          30  key_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08006398   Section        0  key_app.o(.rodata.str1.1)
    .L.str.4                                 0x080063b6   Data          24  key_app.o(.rodata.str1.1)
    .L.str.6                                 0x080063ce   Data          25  key_app.o(.rodata.str1.1)
    .L.str.7                                 0x08006405   Data          14  my_fft.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08006405   Section        0  my_fft.o(.rodata.str1.1)
    .L.str.8                                 0x08006413   Data          16  my_fft.o(.rodata.str1.1)
    .L.str.5                                 0x08006423   Data          24  my_fft.o(.rodata.str1.1)
    .L.str.11                                0x0800643b   Data          26  my_fft.o(.rodata.str1.1)
    .L.str.6                                 0x08006455   Data          54  my_fft.o(.rodata.str1.1)
    .L.str.10                                0x0800648b   Data          22  my_fft.o(.rodata.str1.1)
    _errno                                   0x20000000   Data           4  errno.o(.data)
    .data                                    0x20000000   Section        4  errno.o(.data)
    current_ad_freq                          0x20000008   Data           4  key_app.o(.data.current_ad_freq)
    [Anonymous Symbol]                       0x20000008   Section        0  key_app.o(.data.current_ad_freq)
    key3_action_cycle_phase.current_mode     0x2000000c   Data           1  key_app.o(.data.key3_action_cycle_phase.current_mode)
    [Anonymous Symbol]                       0x2000000c   Section        0  key_app.o(.data.key3_action_cycle_phase.current_mode)
    scheduler_task                           0x20000010   Data          12  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20000010   Section        0  scheduler.o(.data.scheduler_task)
    .L_MergedGlobals                         0x20000028   Data          32  da_output.o(.bss..L_MergedGlobals)
    wave_test.current_waveform               0x20000028   Data           1  da_output.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000028   Section        0  da_output.o(.bss..L_MergedGlobals)
    wave_tick                                0x2000002c   Data           4  da_output.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000048   Data           8  key_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000048   Section        0  key_app.o(.bss..L_MergedGlobals)
    key3_action_cycle_phase.freq_step        0x2000004b   Data           1  key_app.o(.bss..L_MergedGlobals)
    key3_action_cycle_phase.amp_step         0x2000004c   Data           1  key_app.o(.bss..L_MergedGlobals)
    key3_action_cycle_phase.wave_step        0x2000004d   Data           1  key_app.o(.bss..L_MergedGlobals)
    key3_action_cycle_phase.phase_step       0x2000004e   Data           2  key_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000050   Data          12  my_usart.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000050   Section        0  my_usart.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x2000005c   Data          52  my_usart_pack.o(.bss..L_MergedGlobals)
    variableCount                            0x2000005c   Data           2  my_usart_pack.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000005c   Section        0  my_usart_pack.o(.bss..L_MergedGlobals)
    parseTemplate                            0x2000005e   Data          10  my_usart_pack.o(.bss..L_MergedGlobals)
    variableMapping                          0x20000068   Data          40  my_usart_pack.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000090   Data          40  app_pid.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000090   Section        0  app_pid.o(.bss..L_MergedGlobals)
    FMC_Initialized                          0x200000b8   Data           1  fmc.o(.bss.FMC_Initialized)
    [Anonymous Symbol]                       0x200000b8   Section        0  fmc.o(.bss.FMC_Initialized)
    STACK                                    0x20005388   Section     1024  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_stm32f429xx.o(.text)
    ADC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x080001e5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000247   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000255   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000255   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000255   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000259   Thumb Code    18  memseta.o(.text)
    __aeabi_dmul                             0x0800026b   Thumb Code   228  dmul.o(.text)
    __aeabi_ui2d                             0x0800034f   Thumb Code    26  dfltui.o(.text)
    __aeabi_d2uiz                            0x08000369   Thumb Code    50  dfixui.o(.text)
    __aeabi_f2d                              0x0800039b   Thumb Code    38  f2d.o(.text)
    __aeabi_uidiv                            0x080003c1   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080003c1   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080003ed   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080003ed   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800040b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800040b   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x0800042b   Thumb Code     0  iusefp.o(.text)
    _frnd                                    0x0800042b   Thumb Code    60  frnd.o(.text)
    _double_round                            0x08000467   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000485   Thumb Code   156  depilogue.o(.text)
    __aeabi_dadd                             0x08000521   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000663   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000669   Thumb Code     6  dadd.o(.text)
    __aeabi_ddiv                             0x0800066f   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x0800074d   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x0800077d   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x080007ad   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x080007ad   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080007dd   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080007dd   Thumb Code     0  llsshr.o(.text)
    _float_round                             0x08000801   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000813   Thumb Code    92  fepilogue.o(.text)
    AD9959_Init                              0x08000871   Thumb Code   320  ad9959.o(.text.AD9959_Init)
    AD9959_Modulation_Init                   0x080009b1   Thumb Code   204  ad9959.o(.text.AD9959_Modulation_Init)
    AD9959_Set_ASK                           0x08000a81   Thumb Code   368  ad9959.o(.text.AD9959_Set_ASK)
    BusFault_Handler                         0x08000d25   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    CTRL_INIT                                0x08000d29   Thumb Code    12  cmd_to_fun.o(.text.CTRL_INIT)
    DA_Apply_Settings                        0x08000d35   Thumb Code   224  da_output.o(.text.DA_Apply_Settings)
    DA_FPGA_START                            0x08000e25   Thumb Code    16  cmd_to_fun.o(.text.DA_FPGA_START)
    DA_FPGA_STOP                             0x08000e35   Thumb Code    16  cmd_to_fun.o(.text.DA_FPGA_STOP)
    DA_Init                                  0x08000e45   Thumb Code   264  da_output.o(.text.DA_Init)
    DA_SetAmp                                0x08000f5d   Thumb Code    26  da_output.o(.text.DA_SetAmp)
    DA_SetFREQ                               0x08000f79   Thumb Code    60  da_output.o(.text.DA_SetFREQ)
    DA_SetPhase                              0x08000fbd   Thumb Code    26  da_output.o(.text.DA_SetPhase)
    DA_SetWaveform                           0x08000fd9   Thumb Code    26  da_output.o(.text.DA_SetWaveform)
    DebugMon_Handler                         0x08000ff5   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Error_Handler                            0x08000ff9   Thumb Code    10  main.o(.text.Error_Handler)
    FMC_NORSRAM_Extended_Timing_Init         0x08001005   Thumb Code    70  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init)
    FMC_NORSRAM_Init                         0x0800104d   Thumb Code   120  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init)
    FMC_NORSRAM_Timing_Init                  0x080010c5   Thumb Code    94  stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init)
    HAL_DAC_ConfigChannel                    0x08001125   Thumb Code    80  stm32f4xx_hal_dac.o(.text.HAL_DAC_ConfigChannel)
    HAL_DAC_Init                             0x08001175   Thumb Code    46  stm32f4xx_hal_dac.o(.text.HAL_DAC_Init)
    HAL_DAC_MspInit                          0x080011a5   Thumb Code   108  dac.o(.text.HAL_DAC_MspInit)
    HAL_DMA_Abort                            0x08001211   Thumb Code   128  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08001291   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_Delay                                0x080012b5   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x080012dd   Thumb Code   400  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x0800146d   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08001479   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001485   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_IncTick                              0x08001491   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080014ad   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x080014e5   Thumb Code    80  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001535   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800156d   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001591   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080015e9   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_EnableOverDrive                0x08001609   Thumb Code   122  stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive)
    HAL_RCC_CSSCallback                      0x08001685   Thumb Code     2  stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback)
    HAL_RCC_ClockConfig                      0x08001689   Thumb Code   352  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_EnableCSS                        0x080017e9   Thumb Code    12  stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS)
    HAL_RCC_GetPCLK1Freq                     0x080017f5   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x0800181d   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001845   Thumb Code   106  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_NMI_IRQHandler                   0x080018b1   Thumb Code    28  stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler)
    HAL_RCC_OscConfig                        0x080018cd   Thumb Code   860  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SRAM_Init                            0x08001c29   Thumb Code    86  stm32f4xx_hal_sram.o(.text.HAL_SRAM_Init)
    HAL_SRAM_MspInit                         0x08001c81   Thumb Code   184  fmc.o(.text.HAL_SRAM_MspInit)
    HAL_SYSTICK_Config                       0x08001d39   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_UARTEx_RxEventCallback               0x08001d65   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001d69   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08001d6d   Thumb Code   758  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08002065   Thumb Code    94  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x080020c5   Thumb Code   274  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x080021d9   Thumb Code    86  stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08002231   Thumb Code   350  my_usart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08002391   Thumb Code   380  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x0800250d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08002511   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MX_DAC_Init                              0x08002515   Thumb Code    92  dac.o(.text.MX_DAC_Init)
    MX_FMC_Init                              0x08002571   Thumb Code   114  fmc.o(.text.MX_FMC_Init)
    MX_GPIO_Init                             0x080025e5   Thumb Code   400  gpio.o(.text.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x08002775   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080027b1   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x080027ed   Thumb Code    60  usart.o(.text.MX_USART3_UART_Init)
    MemManage_Handler                        0x08002829   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x0800282d   Thumb Code     6  stm32f4xx_it.o(.text.NMI_Handler)
    PID_Init                                 0x08002835   Thumb Code    60  app_pid.o(.text.PID_Init)
    ParseFrame                               0x08002871   Thumb Code   326  my_usart_pack.o(.text.ParseFrame)
    PendSV_Handler                           0x080029b9   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080029bd   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080029c1   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x080029c5   Thumb Code   200  main.o(.text.SystemClock_Config)
    SystemInit                               0x08002a8d   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    USART1_IRQHandler                        0x08002c49   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x08002c55   Thumb Code    12  stm32f4xx_it.o(.text.USART2_IRQHandler)
    USART3_IRQHandler                        0x08002c61   Thumb Code    12  stm32f4xx_it.o(.text.USART3_IRQHandler)
    UsageFault_Handler                       0x08002c6d   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    arm_bitreversal_f32                      0x08002c71   Thumb Code   190  arm_bitreversal.o(.text.arm_bitreversal_f32)
    arm_cfft_radix4_f32                      0x08002d2f   Thumb Code    64  arm_cfft_radix4_f32.o(.text.arm_cfft_radix4_f32)
    arm_cfft_radix4_init_f32                 0x08002d71   Thumb Code   148  arm_cfft_radix4_init_f32.o(.text.arm_cfft_radix4_init_f32)
    arm_cmplx_mag_f32                        0x08002e05   Thumb Code   340  arm_cmplx_mag_f32.o(.text.arm_cmplx_mag_f32)
    arm_cos_f32                              0x08002f59   Thumb Code   152  arm_cos_f32.o(.text.arm_cos_f32)
    arm_radix4_butterfly_f32                 0x08002ff1   Thumb Code   858  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_f32)
    arm_radix4_butterfly_inverse_f32         0x0800334d   Thumb Code   890  arm_cfft_radix4_f32.o(.text.arm_radix4_butterfly_inverse_f32)
    calculate_fft_spectrum                   0x080036c9   Thumb Code   420  my_fft.o(.text.calculate_fft_spectrum)
    calculate_sinad                          0x08003871   Thumb Code   316  my_fft.o(.text.calculate_sinad)
    calculate_thd                            0x080039c1   Thumb Code   896  my_fft.o(.text.calculate_thd)
    fft_init                                 0x08003d59   Thumb Code   108  my_fft.o(.text.fft_init)
    get_current_ad_frequency                 0x08003dcd   Thumb Code    14  key_app.o(.text.get_current_ad_frequency)
    key_proc                                 0x08003ff5   Thumb Code   164  key_app.o(.text.key_proc)
    main                                     0x080040b1   Thumb Code   192  main.o(.text.main)
    my_printf                                0x08004185   Thumb Code    62  my_usart.o(.text.my_printf)
    output_fft_spectrum                      0x080041c9   Thumb Code  1172  my_fft.o(.text.output_fft_spectrum)
    scheduler_init                           0x08004715   Thumb Code    14  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x08004725   Thumb Code    74  scheduler.o(.text.scheduler_run)
    __0vsnprintf                             0x08004771   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08004771   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08004771   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08004771   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08004771   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __hardfp_log10f                          0x080047a5   Thumb Code   332  log10f.o(i.__hardfp_log10f)
    __hardfp_roundf                          0x08004925   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sqrtf                           0x080049bf   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x080049f9   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08004a0d   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08004a15   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __scatterload_copy                       0x08004a25   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08004a33   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08004a35   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08004a45   Thumb Code     6  errno.o(i.__set_errno)
    AHBPrescTable                            0x08005358   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08005368   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    Reg_Len                                  0x08005370   Data          25  ad9959.o(.rodata.Reg_Len)
    armBitRevTable                           0x0800538a   Data        2048  arm_common_tables.o(.rodata.armBitRevTable)
    sinTable_f32                             0x08005b94   Data        2052  arm_common_tables.o(.rodata.sinTable_f32)
    twiddleCoef_4096                         0x080064a4   Data       32768  arm_common_tables.o(.rodata.twiddleCoef_4096)
    Region$$Table$$Base                      0x0800e4a4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800e4c4   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000004   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    uwTickFreq                               0x2000001c   Data           1  stm32f4xx_hal.o(.data.uwTickFreq)
    uwTickPrio                               0x20000020   Data           4  stm32f4xx_hal.o(.data.uwTickPrio)
    da_channels                              0x20000030   Data          24  da_output.o(.bss..L_MergedGlobals)
    key_map_now                              0x20000048   Data           1  key_app.o(.bss..L_MergedGlobals)
    key_map_old                              0x20000049   Data           1  key_app.o(.bss..L_MergedGlobals)
    key_map_down                             0x2000004a   Data           1  key_app.o(.bss..L_MergedGlobals)
    commandReceived1                         0x20000050   Data           1  my_usart.o(.bss..L_MergedGlobals)
    commandReceived3                         0x20000051   Data           1  my_usart.o(.bss..L_MergedGlobals)
    frameStarted                             0x20000052   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxTemp1                                  0x20000053   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxTemp3                                  0x20000054   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxTemp2                                  0x20000055   Data           1  my_usart.o(.bss..L_MergedGlobals)
    rxIndex1                                 0x20000056   Data           2  my_usart.o(.bss..L_MergedGlobals)
    rxIndex3                                 0x20000058   Data           2  my_usart.o(.bss..L_MergedGlobals)
    rxIndex2                                 0x2000005a   Data           2  my_usart.o(.bss..L_MergedGlobals)
    vin                                      0x20000090   Data           4  app_pid.o(.bss..L_MergedGlobals)
    PID                                      0x20000094   Data          36  app_pid.o(.bss..L_MergedGlobals)
    fft_input_buffer                         0x200000bc   Data        8192  my_fft.o(.bss.fft_input_buffer)
    fft_instance                             0x200020bc   Data          20  my_fft.o(.bss.fft_instance)
    fft_magnitude                            0x200020d0   Data        4096  my_fft.o(.bss.fft_magnitude)
    fifo_data1_f                             0x200030d0   Data        4096  ad_measure.o(.bss.fifo_data1_f)
    hdac                                     0x200040d0   Data          20  dac.o(.bss.hdac)
    hsram2                                   0x200040e4   Data          80  fmc.o(.bss.hsram2)
    huart1                                   0x20004134   Data          68  usart.o(.bss.huart1)
    huart2                                   0x20004178   Data          68  usart.o(.bss.huart2)
    huart3                                   0x200041bc   Data          68  usart.o(.bss.huart3)
    rxBuffer1                                0x20004200   Data         128  my_usart.o(.bss.rxBuffer1)
    rxBuffer2                                0x20004280   Data         128  my_usart.o(.bss.rxBuffer2)
    rxBuffer3                                0x20004300   Data         128  my_usart.o(.bss.rxBuffer3)
    task_num                                 0x20004380   Data           1  scheduler.o(.bss.task_num)
    uwTick                                   0x20004384   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    window_buffer                            0x20004388   Data        4096  my_fft.o(.bss.window_buffer)
    __initial_sp                             0x20005788   Data           0  startup_stm32f429xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000e4f0, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000e4c4, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         1481  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         1577    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         1580    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         1582    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         1584    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         1585    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         1587    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         1589    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         1578    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO            4    .text               startup_stm32f429xx.o
    0x080001e4   0x080001e4   0x00000062   Code   RO         1484    .text               mc_w.l(uldiv.o)
    0x08000246   0x08000246   0x00000024   Code   RO         1488    .text               mc_w.l(memseta.o)
    0x0800026a   0x0800026a   0x000000e4   Code   RO         1520    .text               mf_w.l(dmul.o)
    0x0800034e   0x0800034e   0x0000001a   Code   RO         1524    .text               mf_w.l(dfltui.o)
    0x08000368   0x08000368   0x00000032   Code   RO         1528    .text               mf_w.l(dfixui.o)
    0x0800039a   0x0800039a   0x00000026   Code   RO         1530    .text               mf_w.l(f2d.o)
    0x080003c0   0x080003c0   0x0000002c   Code   RO         1605    .text               mc_w.l(uidiv.o)
    0x080003ec   0x080003ec   0x0000001e   Code   RO         1607    .text               mc_w.l(llshl.o)
    0x0800040a   0x0800040a   0x00000020   Code   RO         1609    .text               mc_w.l(llushr.o)
    0x0800042a   0x0800042a   0x00000000   Code   RO         1618    .text               mc_w.l(iusefp.o)
    0x0800042a   0x0800042a   0x0000003c   Code   RO         1619    .text               mf_w.l(frnd.o)
    0x08000466   0x08000466   0x000000ba   Code   RO         1621    .text               mf_w.l(depilogue.o)
    0x08000520   0x08000520   0x0000014e   Code   RO         1623    .text               mf_w.l(dadd.o)
    0x0800066e   0x0800066e   0x000000de   Code   RO         1625    .text               mf_w.l(ddiv.o)
    0x0800074c   0x0800074c   0x00000030   Code   RO         1631    .text               mf_w.l(dfixul.o)
    0x0800077c   0x0800077c   0x00000030   Code   RO         1633    .text               mf_w.l(cdrcmple.o)
    0x080007ac   0x080007ac   0x00000030   Code   RO         1635    .text               mc_w.l(init.o)
    0x080007dc   0x080007dc   0x00000024   Code   RO         1638    .text               mc_w.l(llsshr.o)
    0x08000800   0x08000800   0x0000006e   Code   RO         1641    .text               mf_w.l(fepilogue.o)
    0x0800086e   0x0800086e   0x00000002   PAD
    0x08000870   0x08000870   0x00000140   Code   RO          162    .text.AD9959_Init   ad9959.o
    0x080009b0   0x080009b0   0x000000cc   Code   RO          194    .text.AD9959_Modulation_Init  ad9959.o
    0x08000a7c   0x08000a7c   0x00000004   PAD
    0x08000a80   0x08000a80   0x00000178   Code   RO          204    .text.AD9959_Set_ASK  ad9959.o
    0x08000bf8   0x08000bf8   0x0000012c   Code   RO          262    .text.AD9959_Write_Byte_S  ad9959.o
    0x08000d24   0x08000d24   0x00000002   Code   RO           94    .text.BusFault_Handler  stm32f4xx_it.o
    0x08000d26   0x08000d26   0x00000002   PAD
    0x08000d28   0x08000d28   0x0000000c   Code   RO          478    .text.CTRL_INIT     cmd_to_fun.o
    0x08000d34   0x08000d34   0x000000f0   Code   RO          283    .text.DA_Apply_Settings  da_output.o
    0x08000e24   0x08000e24   0x00000010   Code   RO          480    .text.DA_FPGA_START  cmd_to_fun.o
    0x08000e34   0x08000e34   0x00000010   Code   RO          482    .text.DA_FPGA_STOP  cmd_to_fun.o
    0x08000e44   0x08000e44   0x00000118   Code   RO          279    .text.DA_Init       da_output.o
    0x08000f5c   0x08000f5c   0x0000001a   Code   RO          289    .text.DA_SetAmp     da_output.o
    0x08000f76   0x08000f76   0x00000002   PAD
    0x08000f78   0x08000f78   0x00000044   Code   RO          285    .text.DA_SetFREQ    da_output.o
    0x08000fbc   0x08000fbc   0x0000001a   Code   RO          293    .text.DA_SetPhase   da_output.o
    0x08000fd6   0x08000fd6   0x00000002   PAD
    0x08000fd8   0x08000fd8   0x0000001a   Code   RO          297    .text.DA_SetWaveform  da_output.o
    0x08000ff2   0x08000ff2   0x00000002   PAD
    0x08000ff4   0x08000ff4   0x00000002   Code   RO          100    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08000ff6   0x08000ff6   0x00000002   PAD
    0x08000ff8   0x08000ff8   0x0000000a   Code   RO           15    .text.Error_Handler  main.o
    0x08001002   0x08001002   0x00000002   PAD
    0x08001004   0x08001004   0x00000046   Code   RO         1038    .text.FMC_NORSRAM_Extended_Timing_Init  stm32f4xx_ll_fmc.o
    0x0800104a   0x0800104a   0x00000002   PAD
    0x0800104c   0x0800104c   0x00000078   Code   RO         1032    .text.FMC_NORSRAM_Init  stm32f4xx_ll_fmc.o
    0x080010c4   0x080010c4   0x0000005e   Code   RO         1036    .text.FMC_NORSRAM_Timing_Init  stm32f4xx_ll_fmc.o
    0x08001122   0x08001122   0x00000002   PAD
    0x08001124   0x08001124   0x00000050   Code   RO          580    .text.HAL_DAC_ConfigChannel  stm32f4xx_hal_dac.o
    0x08001174   0x08001174   0x0000002e   Code   RO          544    .text.HAL_DAC_Init  stm32f4xx_hal_dac.o
    0x080011a2   0x080011a2   0x00000002   PAD
    0x080011a4   0x080011a4   0x0000006c   Code   RO           37    .text.HAL_DAC_MspInit  dac.o
    0x08001210   0x08001210   0x00000080   Code   RO          798    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x08001290   0x08001290   0x00000024   Code   RO          800    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080012b4   0x080012b4   0x00000028   Code   RO          957    .text.HAL_Delay     stm32f4xx_hal.o
    0x080012dc   0x080012dc   0x00000190   Code   RO          752    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x0800146c   0x0800146c   0x0000000a   Code   RO          756    .text.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08001476   0x08001476   0x00000002   PAD
    0x08001478   0x08001478   0x0000000a   Code   RO          758    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08001482   0x08001482   0x00000002   PAD
    0x08001484   0x08001484   0x0000000c   Code   RO          949    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08001490   0x08001490   0x0000001a   Code   RO          947    .text.HAL_IncTick   stm32f4xx_hal.o
    0x080014aa   0x080014aa   0x00000002   PAD
    0x080014ac   0x080014ac   0x00000036   Code   RO          937    .text.HAL_Init      stm32f4xx_hal.o
    0x080014e2   0x080014e2   0x00000002   PAD
    0x080014e4   0x080014e4   0x00000050   Code   RO          939    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08001534   0x08001534   0x00000038   Code   RO          119    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x0800156c   0x0800156c   0x00000022   Code   RO          895    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800158e   0x0800158e   0x00000002   PAD
    0x08001590   0x08001590   0x00000056   Code   RO          893    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080015e6   0x080015e6   0x00000002   PAD
    0x080015e8   0x080015e8   0x00000020   Code   RO          891    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001608   0x08001608   0x0000007a   Code   RO          877    .text.HAL_PWREx_EnableOverDrive  stm32f4xx_hal_pwr_ex.o
    0x08001682   0x08001682   0x00000002   PAD
    0x08001684   0x08001684   0x00000002   Code   RO          654    .text.HAL_RCC_CSSCallback  stm32f4xx_hal_rcc.o
    0x08001686   0x08001686   0x00000002   PAD
    0x08001688   0x08001688   0x00000160   Code   RO          632    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080017e8   0x080017e8   0x0000000c   Code   RO          638    .text.HAL_RCC_EnableCSS  stm32f4xx_hal_rcc.o
    0x080017f4   0x080017f4   0x00000026   Code   RO          644    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x0800181a   0x0800181a   0x00000002   PAD
    0x0800181c   0x0800181c   0x00000026   Code   RO          646    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001842   0x08001842   0x00000002   PAD
    0x08001844   0x08001844   0x0000006a   Code   RO          634    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080018ae   0x080018ae   0x00000002   PAD
    0x080018b0   0x080018b0   0x0000001c   Code   RO          652    .text.HAL_RCC_NMI_IRQHandler  stm32f4xx_hal_rcc.o
    0x080018cc   0x080018cc   0x0000035c   Code   RO          630    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001c28   0x08001c28   0x00000056   Code   RO         1094    .text.HAL_SRAM_Init  stm32f4xx_hal_sram.o
    0x08001c7e   0x08001c7e   0x00000002   PAD
    0x08001c80   0x08001c80   0x000000b8   Code   RO           52    .text.HAL_SRAM_MspInit  fmc.o
    0x08001d38   0x08001d38   0x0000002c   Code   RO          903    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001d64   0x08001d64   0x00000002   Code   RO         1222    .text.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08001d66   0x08001d66   0x00000002   PAD
    0x08001d68   0x08001d68   0x00000002   Code   RO         1220    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08001d6a   0x08001d6a   0x00000002   PAD
    0x08001d6c   0x08001d6c   0x000002f6   Code   RO         1214    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08002062   0x08002062   0x00000002   PAD
    0x08002064   0x08002064   0x0000005e   Code   RO         1138    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x080020c2   0x080020c2   0x00000002   PAD
    0x080020c4   0x080020c4   0x00000112   Code   RO           73    .text.HAL_UART_MspInit  usart.o
    0x080021d6   0x080021d6   0x00000002   PAD
    0x080021d8   0x080021d8   0x00000056   Code   RO         1160    .text.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x0800222e   0x0800222e   0x00000002   PAD
    0x08002230   0x08002230   0x0000015e   Code   RO          446    .text.HAL_UART_RxCpltCallback  my_usart.o
    0x0800238e   0x0800238e   0x00000002   PAD
    0x08002390   0x08002390   0x0000017c   Code   RO         1154    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x0800250c   0x0800250c   0x00000002   Code   RO         1224    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x0800250e   0x0800250e   0x00000002   PAD
    0x08002510   0x08002510   0x00000002   Code   RO           90    .text.HardFault_Handler  stm32f4xx_it.o
    0x08002512   0x08002512   0x00000002   PAD
    0x08002514   0x08002514   0x0000005c   Code   RO           35    .text.MX_DAC_Init   dac.o
    0x08002570   0x08002570   0x00000072   Code   RO           50    .text.MX_FMC_Init   fmc.o
    0x080025e2   0x080025e2   0x00000002   PAD
    0x080025e4   0x080025e4   0x00000190   Code   RO           26    .text.MX_GPIO_Init  gpio.o
    0x08002774   0x08002774   0x0000003c   Code   RO           67    .text.MX_USART1_UART_Init  usart.o
    0x080027b0   0x080027b0   0x0000003c   Code   RO           69    .text.MX_USART2_UART_Init  usart.o
    0x080027ec   0x080027ec   0x0000003c   Code   RO           71    .text.MX_USART3_UART_Init  usart.o
    0x08002828   0x08002828   0x00000002   Code   RO           92    .text.MemManage_Handler  stm32f4xx_it.o
    0x0800282a   0x0800282a   0x00000002   PAD
    0x0800282c   0x0800282c   0x00000006   Code   RO           88    .text.NMI_Handler   stm32f4xx_it.o
    0x08002832   0x08002832   0x00000002   PAD
    0x08002834   0x08002834   0x0000003c   Code   RO          528    .text.PID_Init      app_pid.o
    0x08002870   0x08002870   0x00000146   Code   RO          463    .text.ParseFrame    my_usart_pack.o
    0x080029b6   0x080029b6   0x00000002   PAD
    0x080029b8   0x080029b8   0x00000002   Code   RO          102    .text.PendSV_Handler  stm32f4xx_it.o
    0x080029ba   0x080029ba   0x00000002   PAD
    0x080029bc   0x080029bc   0x00000002   Code   RO           98    .text.SVC_Handler   stm32f4xx_it.o
    0x080029be   0x080029be   0x00000002   PAD
    0x080029c0   0x080029c0   0x00000004   Code   RO          104    .text.SysTick_Handler  stm32f4xx_it.o
    0x080029c4   0x080029c4   0x000000c8   Code   RO           13    .text.SystemClock_Config  main.o
    0x08002a8c   0x08002a8c   0x00000012   Code   RO         1258    .text.SystemInit    system_stm32f4xx.o
    0x08002a9e   0x08002a9e   0x00000002   PAD
    0x08002aa0   0x08002aa0   0x0000000c   Code   RO         1218    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08002aac   0x08002aac   0x000000bc   Code   RO         1216    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08002b68   0x08002b68   0x000000de   Code   RO         1142    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x08002c46   0x08002c46   0x00000002   PAD
    0x08002c48   0x08002c48   0x0000000c   Code   RO          106    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x08002c54   0x08002c54   0x0000000c   Code   RO          108    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x08002c60   0x08002c60   0x0000000c   Code   RO          110    .text.USART3_IRQHandler  stm32f4xx_it.o
    0x08002c6c   0x08002c6c   0x00000002   Code   RO           96    .text.UsageFault_Handler  stm32f4xx_it.o
    0x08002c6e   0x08002c6e   0x00000002   PAD
    0x08002c70   0x08002c70   0x000000be   Code   RO         1324    .text.arm_bitreversal_f32  arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08002d2e   0x08002d2e   0x00000040   Code   RO         1301    .text.arm_cfft_radix4_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08002d6e   0x08002d6e   0x00000002   PAD
    0x08002d70   0x08002d70   0x00000094   Code   RO         1315    .text.arm_cfft_radix4_init_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08002e04   0x08002e04   0x00000154   Code   RO         1282    .text.arm_cmplx_mag_f32  arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x08002f58   0x08002f58   0x00000098   Code   RO         1273    .text.arm_cos_f32   arm_cortexM4lf_math.lib(arm_cos_f32.o)
    0x08002ff0   0x08002ff0   0x0000035a   Code   RO         1305    .text.arm_radix4_butterfly_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x0800334a   0x0800334a   0x00000002   PAD
    0x0800334c   0x0800334c   0x0000037a   Code   RO         1303    .text.arm_radix4_butterfly_inverse_f32  arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x080036c6   0x080036c6   0x00000002   PAD
    0x080036c8   0x080036c8   0x000001a8   Code   RO          354    .text.calculate_fft_spectrum  my_fft.o
    0x08003870   0x08003870   0x00000150   Code   RO          366    .text.calculate_sinad  my_fft.o
    0x080039c0   0x080039c0   0x00000398   Code   RO          362    .text.calculate_thd  my_fft.o
    0x08003d58   0x08003d58   0x00000074   Code   RO          352    .text.fft_init      my_fft.o
    0x08003dcc   0x08003dcc   0x0000000e   Code   RO          333    .text.get_current_ad_frequency  key_app.o
    0x08003dda   0x08003dda   0x00000002   PAD
    0x08003ddc   0x08003ddc   0x00000218   Code   RO          335    .text.key3_action_cycle_phase  key_app.o
    0x08003ff4   0x08003ff4   0x000000bc   Code   RO          329    .text.key_proc      key_app.o
    0x080040b0   0x080040b0   0x000000d4   Code   RO           11    .text.main          main.o
    0x08004184   0x08004184   0x0000003e   Code   RO          444    .text.my_printf     my_usart.o
    0x080041c2   0x080041c2   0x00000006   PAD
    0x080041c8   0x080041c8   0x0000054c   Code   RO          356    .text.output_fft_spectrum  my_fft.o
    0x08004714   0x08004714   0x0000000e   Code   RO          513    .text.scheduler_init  scheduler.o
    0x08004722   0x08004722   0x00000002   PAD
    0x08004724   0x08004724   0x0000004a   Code   RO          515    .text.scheduler_run  scheduler.o
    0x0800476e   0x0800476e   0x00000002   PAD
    0x08004770   0x08004770   0x00000034   Code   RO         1498    i.__0vsnprintf      mc_w.l(printfa.o)
    0x080047a4   0x080047a4   0x00000180   Code   RO         1457    i.__hardfp_log10f   m_wm.l(log10f.o)
    0x08004924   0x08004924   0x0000009a   Code   RO         1477    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x080049be   0x080049be   0x0000003a   Code   RO         1471    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x080049f8   0x080049f8   0x00000014   Code   RO         1554    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08004a0c   0x08004a0c   0x00000006   Code   RO         1555    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08004a12   0x08004a12   0x00000002   PAD
    0x08004a14   0x08004a14   0x00000010   Code   RO         1557    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08004a24   0x08004a24   0x0000000e   Code   RO         1647    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08004a32   0x08004a32   0x00000002   Code   RO         1648    i.__scatterload_null  mc_w.l(handlers.o)
    0x08004a34   0x08004a34   0x0000000e   Code   RO         1649    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08004a42   0x08004a42   0x00000002   PAD
    0x08004a44   0x08004a44   0x0000000c   Code   RO         1613    i.__set_errno       mc_w.l(errno.o)
    0x08004a50   0x08004a50   0x00000184   Code   RO         1500    i._fp_digits        mc_w.l(printfa.o)
    0x08004bd4   0x08004bd4   0x000006dc   Code   RO         1501    i._printf_core      mc_w.l(printfa.o)
    0x080052b0   0x080052b0   0x00000024   Code   RO         1502    i._printf_post_padding  mc_w.l(printfa.o)
    0x080052d4   0x080052d4   0x0000002e   Code   RO         1503    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08005302   0x08005302   0x00000016   Code   RO         1504    i._snputc           mc_w.l(printfa.o)
    0x08005318   0x08005318   0x00000040   Data   RO         1460    .constdata          m_wm.l(log10f.o)
    0x08005358   0x08005358   0x00000010   Data   RO         1263    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08005368   0x08005368   0x00000008   Data   RO         1264    .rodata.APBPrescTable  system_stm32f4xx.o
    0x08005370   0x08005370   0x00000019   Data   RO          264    .rodata.Reg_Len     ad9959.o
    0x08005389   0x08005389   0x00000001   PAD
    0x0800538a   0x0800538a   0x00000800   Data   RO         1338    .rodata.armBitRevTable  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08005b8a   0x08005b8a   0x0000000a   Data   RO          339    .rodata.key3_action_cycle_phase.amp_values  key_app.o
    0x08005b94   0x08005b94   0x00000804   Data   RO         1450    .rodata.sinTable_f32  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08006398   0x08006398   0x0000006d   Data   RO          340    .rodata.str1.1      key_app.o
    0x08006405   0x08006405   0x0000009c   Data   RO          372    .rodata.str1.1      my_fft.o
    0x080064a1   0x080064a1   0x00000003   PAD
    0x080064a4   0x080064a4   0x00008000   Data   RO         1356    .rodata.twiddleCoef_4096  arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800e4a4   0x0800e4a4   0x00000020   Data   RO         1646    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x0800e4f0, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800e4c8, Size: 0x00005788, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800e4c8   0x00000004   Data   RW         1614    .data               mc_w.l(errno.o)
    0x20000004   0x0800e4cc   0x00000004   Data   RW         1262    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000008   0x0800e4d0   0x00000004   Data   RW          337    .data.current_ad_freq  key_app.o
    0x2000000c   0x0800e4d4   0x00000001   Data   RW          338    .data.key3_action_cycle_phase.current_mode  key_app.o
    0x2000000d   0x0800e4d5   0x00000003   PAD
    0x20000010   0x0800e4d8   0x0000000c   Data   RW          519    .data.scheduler_task  scheduler.o
    0x2000001c   0x0800e4e4   0x00000001   Data   RW          996    .data.uwTickFreq    stm32f4xx_hal.o
    0x2000001d   0x0800e4e5   0x00000003   PAD
    0x20000020   0x0800e4e8   0x00000004   Data   RW          995    .data.uwTickPrio    stm32f4xx_hal.o
    0x20000024   0x0800e4ec   0x00000004   PAD
    0x20000028        -       0x00000020   Zero   RW          303    .bss..L_MergedGlobals  da_output.o
    0x20000048        -       0x00000008   Zero   RW          341    .bss..L_MergedGlobals  key_app.o
    0x20000050        -       0x0000000c   Zero   RW          452    .bss..L_MergedGlobals  my_usart.o
    0x2000005c        -       0x00000034   Zero   RW          469    .bss..L_MergedGlobals  my_usart_pack.o
    0x20000090        -       0x00000028   Zero   RW          535    .bss..L_MergedGlobals  app_pid.o
    0x200000b8        -       0x00000001   Zero   RW           57    .bss.FMC_Initialized  fmc.o
    0x200000b9   0x0800e4ec   0x00000003   PAD
    0x200000bc        -       0x00002000   Zero   RW          370    .bss.fft_input_buffer  my_fft.o
    0x200020bc        -       0x00000014   Zero   RW          369    .bss.fft_instance   my_fft.o
    0x200020d0        -       0x00001000   Zero   RW          371    .bss.fft_magnitude  my_fft.o
    0x200030d0        -       0x00001000   Zero   RW          138    .bss.fifo_data1_f   ad_measure.o
    0x200040d0        -       0x00000014   Zero   RW           41    .bss.hdac           dac.o
    0x200040e4        -       0x00000050   Zero   RW           56    .bss.hsram2         fmc.o
    0x20004134        -       0x00000044   Zero   RW           77    .bss.huart1         usart.o
    0x20004178        -       0x00000044   Zero   RW           78    .bss.huart2         usart.o
    0x200041bc        -       0x00000044   Zero   RW           79    .bss.huart3         usart.o
    0x20004200        -       0x00000080   Zero   RW          449    .bss.rxBuffer1      my_usart.o
    0x20004280        -       0x00000080   Zero   RW          451    .bss.rxBuffer2      my_usart.o
    0x20004300        -       0x00000080   Zero   RW          450    .bss.rxBuffer3      my_usart.o
    0x20004380        -       0x00000001   Zero   RW          518    .bss.task_num       scheduler.o
    0x20004381   0x0800e4ec   0x00000003   PAD
    0x20004384        -       0x00000004   Zero   RW          997    .bss.uwTick         stm32f4xx_hal.o
    0x20004388        -       0x00001000   Zero   RW          368    .bss.window_buffer  my_fft.o
    0x20005388        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1200          8         25          0          0      32606   ad9959.o
         0          0          0          0       4096       8220   ad_measure.o
        60          0          0          0         40       1716   app_pid.o
        44          0          0          0          0       2698   cmd_to_fun.o
       666         40          0          0         32       5066   da_output.o
       200          0          0          0         20       4708   dac.o
       298          0          0          0         81       5717   fmc.o
       400          0          0          0          0       2412   gpio.o
       738        182        119          5          8       6334   key_app.o
       422         20          0          0          0       3622   main.o
      3152        244        156          0      16404      14126   my_fft.o
       412          0          0          0        396       4766   my_usart.o
       326          4          0          0         52       7058   my_usart_pack.o
        88          0          0         12          1       2026   scheduler.o
        36          8        428          0       1024        840   startup_stm32f429xx.o
       212          0          0          5          4       7712   stm32f4xx_hal.o
       196          0          0          0          0      10607   stm32f4xx_hal_cortex.o
       126          0          0          0          0       7602   stm32f4xx_hal_dac.o
       164          0          0          0          0       9765   stm32f4xx_hal_dma.o
       420          0          0          0          0       5161   stm32f4xx_hal_gpio.o
        56          0          0          0          0       1510   stm32f4xx_hal_msp.o
       122          0          0          0          0       4665   stm32f4xx_hal_pwr_ex.o
      1436          4          0          0          0       7481   stm32f4xx_hal_rcc.o
        86          0          0          0          0       8064   stm32f4xx_hal_sram.o
      1746          0          0          0          0      28013   stm32f4xx_hal_uart.o
        60          0          0          0          0       4159   stm32f4xx_it.o
       284          0          0          0          0      12288   stm32f4xx_ll_fmc.o
        18          0         24          4          0       2763   system_stm32f4xx.o
       454          0          0          0        204       7918   usart.o

    ----------------------------------------------------------------------
     13516        <USER>        <GROUP>         32      22372     219623   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        94          0          4          6         10          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       190          0          0          0          0       2756   arm_bitreversal.o
      1812          0          0          0          0       9774   arm_cfft_radix4_f32.o
       148         20          0          0          0       1573   arm_cfft_radix4_init_f32.o
       340          0          0          0          0       2270   arm_cmplx_mag_f32.o
         0          0      36868          0          0       6294   arm_common_tables.o
       152         12          0          0          0       1104   arm_cos_f32.o
        42         12          0          0          0        348   funder.o
       384         52         64          0          0        140   log10f.o
       154          0          0          0          0        140   roundf.o
        58          0          0          0          0        136   sqrtf.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2300         86          0          0          0        516   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        50          0          0          0          0         76   dfixui.o
        48          0          0          0          0         68   dfixul.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o
        60          0          0          0          0         76   frnd.o

    ----------------------------------------------------------------------
      7328        <USER>      <GROUP>          4          0      26791   Library Totals
        12          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2642         32      36868          0          0      23771   arm_cortexM4lf_math.lib
       638         64         64          0          0        764   m_wm.l
      2686        110          0          4          0       1136   mc_w.l
      1350          0          0          0          0       1120   mf_w.l

    ----------------------------------------------------------------------
      7328        <USER>      <GROUP>          4          0      26791   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     20844        716      37720         36      22372     244390   Grand Totals
     20844        716      37720         36      22372     244390   ELF Image Totals
     20844        716      37720         36          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                58564 (  57.19kB)
    Total RW  Size (RW Data + ZI Data)             22408 (  21.88kB)
    Total ROM Size (Code + RO Data + RW Data)      58600 (  57.23kB)

==============================================================================

