/*
 * Filter Coefficients (C Source) generated by the Filter Design and Analysis Tool
 * Generated by MATLAB(R) 24.2 and Signal Processing Toolbox 24.2.
 * Generated on: 26-Dec-2024 19:27:45
 */

/*
 * 离散时间 FIR 滤波器(实数)
 * ----------------
 * 滤波器结构  : 直接型 FIR
 * 滤波器长度  : 11
 * 稳定     : 是
 * 线性相位   : 是 (Type 1)
 */

/* General type conversion for MATLAB generated C-code  */
#include "tmwtypes.h"
/* 
 * Expected path to tmwtypes.h 
 * D:\Program Files\MATLAB\R2024b\extern\include\tmwtypes.h 
 */
/*
 * Warning - Filter coefficients were truncated to fit specified data type.  
 *   The resulting response may not match generated theoretical response.
 *   Use the Filter Design & Analysis Tool to design accurate
 *   single-precision filter coefficients.
 */
const int BL = 11;
const real32_T B[11] = {
    0.01459097303,   0.0306210015,  0.07259228081,   0.1244806051,   0.1664643139,
     0.1825016588,   0.1664643139,   0.1244806051,  0.07259228081,   0.0306210015,
    0.01459097303
};
