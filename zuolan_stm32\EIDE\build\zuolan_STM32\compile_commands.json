[{"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\fmc.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\fmc.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\fmc.d .\\..\\Core\\Src\\fmc.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\gpio.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\gpio.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\gpio.d .\\..\\Core\\Src\\gpio.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\main.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\main.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\main.d .\\..\\Core\\Src\\main.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\stm32f4xx_hal_msp.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\stm32f4xx_hal_msp.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\stm32f4xx_hal_msp.d .\\..\\Core\\Src\\stm32f4xx_hal_msp.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\stm32f4xx_it.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\stm32f4xx_it.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\stm32f4xx_it.d .\\..\\Core\\Src\\stm32f4xx_it.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\system_stm32f4xx.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\system_stm32f4xx.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\system_stm32f4xx.d .\\..\\Core\\Src\\system_stm32f4xx.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Core\\Src\\usart.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\usart.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Core\\Src\\usart.d .\\..\\Core\\Src\\usart.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_cortex.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_dma_ex.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_exti.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ex.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_flash_ramfunc.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_gpio.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_pwr_ex.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_rcc_ex.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_sram.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_tim_ex.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_hal_uart.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fmc.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fmc.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fmc.d .\\..\\Drivers\\STM32F4xx_HAL_Driver\\Src\\stm32f4xx_ll_fmc.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MDK-ARM\\startup_stm32f429xx.s", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armasm.exe\" --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 --cpu Cortex-M4.fp --li --pd \"__MICROLIB SETA 1\" -g -o .\\build\\zuolan_STM32\\.obj\\__\\MDK-ARM\\startup_stm32f429xx.o --depend .\\build\\zuolan_STM32\\.obj\\__\\MDK-ARM\\startup_stm32f429xx.d .\\..\\MDK-ARM\\startup_stm32f429xx.s"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_APP\\scheduler.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_APP\\scheduler.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_APP\\scheduler.d .\\..\\MY_APP\\scheduler.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Algorithms\\Src\\my_fft.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Algorithms\\Src\\my_fft.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Algorithms\\Src\\my_fft.d .\\..\\MY_Algorithms\\Src\\my_fft.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Algorithms\\Src\\my_filter.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Algorithms\\Src\\my_filter.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Algorithms\\Src\\my_filter.d .\\..\\MY_Algorithms\\Src\\my_filter.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Algorithms\\Src\\phase_measure.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Algorithms\\Src\\phase_measure.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Algorithms\\Src\\phase_measure.d .\\..\\MY_Algorithms\\Src\\phase_measure.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Communication\\Src\\my_hmi.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Communication\\Src\\my_hmi.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Communication\\Src\\my_hmi.d .\\..\\MY_Communication\\Src\\my_hmi.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Communication\\Src\\my_usart.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Communication\\Src\\my_usart.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Communication\\Src\\my_usart.d .\\..\\MY_Communication\\Src\\my_usart.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Communication\\Src\\my_usart_pack.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Communication\\Src\\my_usart_pack.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Communication\\Src\\my_usart_pack.d .\\..\\MY_Communication\\Src\\my_usart_pack.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Hardware_Drivers\\Src\\AD9959.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\AD9959.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\AD9959.d .\\..\\MY_Hardware_Drivers\\Src\\AD9959.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Hardware_Drivers\\Src\\ad_measure.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\ad_measure.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\ad_measure.d .\\..\\MY_Hardware_Drivers\\Src\\ad_measure.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Hardware_Drivers\\Src\\da_output.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\da_output.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\da_output.d .\\..\\MY_Hardware_Drivers\\Src\\da_output.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Hardware_Drivers\\Src\\freq_measure.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\freq_measure.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Hardware_Drivers\\Src\\freq_measure.d .\\..\\MY_Hardware_Drivers\\Src\\freq_measure.c"}, {"directory": "e:\\MCU\\FPGA+32 DEMO\\STM32\\EIDE", "file": "e:\\MCU\\FPGA+32 DEMO\\STM32\\MY_Utilities\\Src\\cmd_to_fun.c", "command": "\"D:\\Keil_v5\\ARM\\ARMCC\\bin\\armcc.exe\" -c --apcs=interwork -I../Core/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -I../Middlewares/ST/ARM/DSP/Inc -I../MY_Algorithms/Inc -I../MY_Communication/Inc -I../MY_Hardware_Drivers/Inc -I../MY_Utilities/Inc -I../MY_APP -I.cmsis/include -I../MDK-ARM/RTE/_zuolan_STM32 -DUSE_HAL_DRIVER -DSTM32F429xx -DARM_MATH_CM4 --cpu Cortex-M4.fp --li --c99 -D__MICROLIB -O3 --split_sections --diag_suppress=1 --diag_suppress=1295 -g -o .\\build\\zuolan_STM32\\.obj\\__\\MY_Utilities\\Src\\cmd_to_fun.o --no_depend_system_headers --depend .\\build\\zuolan_STM32\\.obj\\__\\MY_Utilities\\Src\\cmd_to_fun.d .\\..\\MY_Utilities\\Src\\cmd_to_fun.c"}]