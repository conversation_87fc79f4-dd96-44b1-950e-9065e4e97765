{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1754072280685 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Assembler Quartus Prime " "Running Quartus Prime Assembler" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1754072280688 ""} { "Info" "IQEXE_START_BANNER_TIME" "Sat Aug 02 02:18:00 2025 " "Processing started: Sat Aug 02 02:18:00 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1754072280688 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Assembler" 0 -1 1754072280688 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_asm --read_settings_files=off --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT " "Command: quartus_asm --read_settings_files=off --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Assembler" 0 -1 1754072280688 ""}
{ "Info" "IASM_ASM_GENERATING_POWER_DATA" "" "Writing out detailed assembly data for power analysis" {  } {  } 0 115031 "Writing out detailed assembly data for power analysis" 0 0 "Assembler" 0 -1 1754072281021 ""}
{ "Info" "IASM_ASM_GENERATING_PROGRAMMING_FILES" "" "Assembler is generating device programming files" {  } {  } 0 115030 "Assembler is generating device programming files" 0 0 "Assembler" 0 -1 1754072281031 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Assembler 0 s 0 s Quartus Prime " "Quartus Prime Assembler was successful. 0 errors, 0 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4682 " "Peak virtual memory: 4682 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1754072281178 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Aug 02 02:18:01 2025 " "Processing ended: Sat Aug 02 02:18:01 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1754072281178 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:01 " "Elapsed time: 00:00:01" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1754072281178 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:01 " "Total CPU time (on all processors): 00:00:01" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1754072281178 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Assembler" 0 -1 1754072281178 ""}
