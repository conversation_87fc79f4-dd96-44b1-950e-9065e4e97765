{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1754046116145 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Analysis & Synthesis Quartus Prime " "Running Quartus Prime Analysis & Synthesis" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1754046116148 ""} { "Info" "IQEXE_START_BANNER_TIME" "Fri Aug 01 19:01:56 2025 " "Processing started: Fri Aug 01 19:01:56 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1754046116148 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046116148 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_map --read_settings_files=on --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT " "Command: quartus_map --read_settings_files=on --write_settings_files=off ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046116148 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Analysis & Synthesis" 0 -1 1754046116418 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/voltage_scaler_clocked_fast.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/voltage_scaler_clocked_fast.v" { { "Info" "ISGN_ENTITY_NAME" "1 voltage_scaler_clocked_fast " "Found entity 1: voltage_scaler_clocked_fast" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121486 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121486 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/ip/tyfifo/tyfifo.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/ip/tyfifo/tyfifo.v" { { "Info" "ISGN_ENTITY_NAME" "1 TYFIFO " "Found entity 1: TYFIFO" {  } { { "../ip/TYFIFO/TYFIFO.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/TYFIFO/TYFIFO.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121488 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121488 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/ip/triangle_rom/triangle_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/ip/triangle_rom/triangle_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 triangle_rom " "Found entity 1: triangle_rom" {  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121490 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121490 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/ip/sqaure_rom/sqaure_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sqaure_rom " "Found entity 1: sqaure_rom" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121492 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121492 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/ip/sawtooth_rom/sawtooth_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sawtooth_rom " "Found entity 1: sawtooth_rom" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121494 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121494 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/ip/sin_rom/sin_rom.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/ip/sin_rom/sin_rom.v" { { "Info" "ISGN_ENTITY_NAME" "1 sin_rom " "Found entity 1: sin_rom" {  } { { "../ip/sin_rom/sin_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sin_rom/sin_rom.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121496 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121496 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/ip/mypll/mypll.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/ip/mypll/mypll.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL " "Found entity 1: MYPLL" {  } { { "../ip/MYPLL/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/MYPLL/MYPLL.v" 39 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121498 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121498 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/voltage_scaler_clocked.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/voltage_scaler_clocked.v" { { "Info" "ISGN_ENTITY_NAME" "1 VOLTAGE_SCALER_CLOCKED " "Found entity 1: VOLTAGE_SCALER_CLOCKED" {  } { { "../src/VOLTAGE_SCALER_CLOCKED.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v" 13 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121500 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121500 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/test.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/test.v" { { "Info" "ISGN_ENTITY_NAME" "1 test " "Found entity 1: test" {  } { { "../src/test.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/test.v" 12 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121502 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121502 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/master_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/master_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 MASTER_CTRL " "Found entity 1: MASTER_CTRL" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 21 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121504 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121504 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/freq_dev.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/freq_dev.v" { { "Info" "ISGN_ENTITY_NAME" "1 FREQ_DEV " "Found entity 1: FREQ_DEV" {  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FREQ_DEV.v" 13 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121506 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121506 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/fmc_control.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/fmc_control.v" { { "Info" "ISGN_ENTITY_NAME" "1 FMC_CONTROL " "Found entity 1: FMC_CONTROL" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 15 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121508 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121508 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/da_waveform.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/da_waveform.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_WAVEFORM " "Found entity 1: DA_WAVEFORM" {  } { { "../src/DA_WAVEFORM.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 15 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121510 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121510 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/da_parameter_deal.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/da_parameter_deal.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_PARAMETER_DEAL " "Found entity 1: DA_PARAMETER_DEAL" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 1 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121512 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121512 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/da_parameter_ctrl.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/da_parameter_ctrl.v" { { "Info" "ISGN_ENTITY_NAME" "1 DA_PARAMETER_CTRL " "Found entity 1: DA_PARAMETER_CTRL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 34 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121514 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121514 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/cnt32.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/cnt32.v" { { "Info" "ISGN_ENTITY_NAME" "1 CNT32 " "Found entity 1: CNT32" {  } { { "../src/CNT32.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/CNT32.v" 11 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121516 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121516 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/ad_freq_word.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/ad_freq_word.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_WORD " "Found entity 1: AD_FREQ_WORD" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 19 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121518 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121518 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/ad_freq_measure.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/ad_freq_measure.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_FREQ_MEASURE " "Found entity 1: AD_FREQ_MEASURE" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 12 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121519 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121519 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "/users/kindred.c/desktop/2025elec/zuolan_fpga/src/ad_data_deal.v 1 1 " "Found 1 design units, including 1 entities, in source file /users/kindred.c/desktop/2025elec/zuolan_fpga/src/ad_data_deal.v" { { "Info" "ISGN_ENTITY_NAME" "1 AD_DATA_DEAL " "Found entity 1: AD_DATA_DEAL" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 12 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121521 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121521 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "top.bdf 1 1 " "Found 1 design units, including 1 entities, in source file top.bdf" { { "Info" "ISGN_ENTITY_NAME" "1 TOP " "Found entity 1: TOP" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { } } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121523 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121523 ""}
{ "Info" "ISGN_START_ELABORATION_TOP" "TOP " "Elaborating entity \"TOP\" for the top level hierarchy" {  } {  } 0 12127 "Elaborating entity \"%1!s!\" for the top level hierarchy" 0 0 "Analysis & Synthesis" 0 -1 1754046121553 ""}
{ "Warning" "WGDFX_NO_SOURCE_FOR_PORT" "clk VOLTAGE_SCALER_CLOCKED inst11 " "Port \"clk\" of type VOLTAGE_SCALER_CLOCKED and instance \"inst11\" is missing source signal" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 960 4224 4464 1072 "inst11" "" } } } }  } 0 275013 "Port \"%1!s!\" of type %2!s! and instance \"%3!s!\" is missing source signal" 0 0 "Analysis & Synthesis" 0 -1 1754046121569 ""}
{ "Warning" "WGDFX_NO_SOURCE_FOR_PORT" "clk VOLTAGE_SCALER_CLOCKED inst12 " "Port \"clk\" of type VOLTAGE_SCALER_CLOCKED and instance \"inst12\" is missing source signal" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 280 4608 4848 392 "inst12" "" } } } }  } 0 275013 "Port \"%1!s!\" of type %2!s! and instance \"%3!s!\" is missing source signal" 0 0 "Analysis & Synthesis" 0 -1 1754046121569 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_PARAMETER_CTRL DA_PARAMETER_CTRL:inst7 " "Elaborating entity \"DA_PARAMETER_CTRL\" for hierarchy \"DA_PARAMETER_CTRL:inst7\"" {  } { { "TOP.bdf" "inst7" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 40 3360 3720 280 "inst7" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121577 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "16 8 DA_PARAMETER_CTRL.v(143) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(143): truncated value with size 16 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 143 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121580 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 DA_PARAMETER_CTRL.v(145) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(145): truncated value with size 32 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 145 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121580 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 DA_PARAMETER_CTRL.v(148) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(148): truncated value with size 32 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 148 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121581 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "16 8 DA_PARAMETER_CTRL.v(167) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(167): truncated value with size 16 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 167 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121581 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 DA_PARAMETER_CTRL.v(169) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(169): truncated value with size 32 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 169 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121581 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 8 DA_PARAMETER_CTRL.v(172) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(172): truncated value with size 32 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 172 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121581 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "16 8 DA_PARAMETER_CTRL.v(188) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(188): truncated value with size 16 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 188 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121581 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "16 8 DA_PARAMETER_CTRL.v(192) " "Verilog HDL assignment warning at DA_PARAMETER_CTRL.v(192): truncated value with size 16 to match size of target (8)" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 192 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046121581 "|TOP|DA_PARAMETER_CTRL:inst7"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MYPLL MYPLL:inst6 " "Elaborating entity \"MYPLL\" for hierarchy \"MYPLL:inst6\"" {  } { { "TOP.bdf" "inst6" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1248 2160 2480 1424 "inst6" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121596 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altpll MYPLL:inst6\|altpll:altpll_component " "Elaborating entity \"altpll\" for hierarchy \"MYPLL:inst6\|altpll:altpll_component\"" {  } { { "../ip/MYPLL/MYPLL.v" "altpll_component" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/MYPLL/MYPLL.v" 90 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121735 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "MYPLL:inst6\|altpll:altpll_component " "Elaborated megafunction instantiation \"MYPLL:inst6\|altpll:altpll_component\"" {  } { { "../ip/MYPLL/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/MYPLL/MYPLL.v" 90 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121743 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "MYPLL:inst6\|altpll:altpll_component " "Instantiated megafunction \"MYPLL:inst6\|altpll:altpll_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "bandwidth_type AUTO " "Parameter \"bandwidth_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_divide_by 1 " "Parameter \"clk0_divide_by\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_duty_cycle 50 " "Parameter \"clk0_duty_cycle\" = \"50\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_multiply_by 3 " "Parameter \"clk0_multiply_by\" = \"3\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clk0_phase_shift 0 " "Parameter \"clk0_phase_shift\" = \"0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "compensate_clock CLK0 " "Parameter \"compensate_clock\" = \"CLK0\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "inclk0_input_frequency 20000 " "Parameter \"inclk0_input_frequency\" = \"20000\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint CBX_MODULE_PREFIX=MYPLL " "Parameter \"lpm_hint\" = \"CBX_MODULE_PREFIX=MYPLL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altpll " "Parameter \"lpm_type\" = \"altpll\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode NORMAL " "Parameter \"operation_mode\" = \"NORMAL\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "pll_type AUTO " "Parameter \"pll_type\" = \"AUTO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_activeclock PORT_UNUSED " "Parameter \"port_activeclock\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_areset PORT_UNUSED " "Parameter \"port_areset\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad0 PORT_UNUSED " "Parameter \"port_clkbad0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkbad1 PORT_UNUSED " "Parameter \"port_clkbad1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkloss PORT_UNUSED " "Parameter \"port_clkloss\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkswitch PORT_UNUSED " "Parameter \"port_clkswitch\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_configupdate PORT_UNUSED " "Parameter \"port_configupdate\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_fbin PORT_UNUSED " "Parameter \"port_fbin\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk0 PORT_USED " "Parameter \"port_inclk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_inclk1 PORT_UNUSED " "Parameter \"port_inclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_locked PORT_UNUSED " "Parameter \"port_locked\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pfdena PORT_UNUSED " "Parameter \"port_pfdena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasecounterselect PORT_UNUSED " "Parameter \"port_phasecounterselect\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasedone PORT_UNUSED " "Parameter \"port_phasedone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phasestep PORT_UNUSED " "Parameter \"port_phasestep\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_phaseupdown PORT_UNUSED " "Parameter \"port_phaseupdown\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_pllena PORT_UNUSED " "Parameter \"port_pllena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanaclr PORT_UNUSED " "Parameter \"port_scanaclr\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclk PORT_UNUSED " "Parameter \"port_scanclk\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanclkena PORT_UNUSED " "Parameter \"port_scanclkena\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandata PORT_UNUSED " "Parameter \"port_scandata\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandataout PORT_UNUSED " "Parameter \"port_scandataout\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scandone PORT_UNUSED " "Parameter \"port_scandone\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanread PORT_UNUSED " "Parameter \"port_scanread\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_scanwrite PORT_UNUSED " "Parameter \"port_scanwrite\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk0 PORT_USED " "Parameter \"port_clk0\" = \"PORT_USED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk1 PORT_UNUSED " "Parameter \"port_clk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk2 PORT_UNUSED " "Parameter \"port_clk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk3 PORT_UNUSED " "Parameter \"port_clk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk4 PORT_UNUSED " "Parameter \"port_clk4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clk5 PORT_UNUSED " "Parameter \"port_clk5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena0 PORT_UNUSED " "Parameter \"port_clkena0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena1 PORT_UNUSED " "Parameter \"port_clkena1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena2 PORT_UNUSED " "Parameter \"port_clkena2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena3 PORT_UNUSED " "Parameter \"port_clkena3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena4 PORT_UNUSED " "Parameter \"port_clkena4\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_clkena5 PORT_UNUSED " "Parameter \"port_clkena5\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk0 PORT_UNUSED " "Parameter \"port_extclk0\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk1 PORT_UNUSED " "Parameter \"port_extclk1\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk2 PORT_UNUSED " "Parameter \"port_extclk2\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "port_extclk3 PORT_UNUSED " "Parameter \"port_extclk3\" = \"PORT_UNUSED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_clock 5 " "Parameter \"width_clock\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046121743 ""}  } { { "../ip/MYPLL/MYPLL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/MYPLL/MYPLL.v" 90 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046121743 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mypll_altpll1.v 1 1 " "Found 1 design units, including 1 entities, in source file db/mypll_altpll1.v" { { "Info" "ISGN_ENTITY_NAME" "1 MYPLL_altpll1 " "Found entity 1: MYPLL_altpll1" {  } { { "db/mypll_altpll1.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/mypll_altpll1.v" 29 -1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046121787 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121787 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MYPLL_altpll1 MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated " "Elaborating entity \"MYPLL_altpll1\" for hierarchy \"MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\"" {  } { { "altpll.tdf" "auto_generated" { Text "g:/altera/18.1/quartus/libraries/megafunctions/altpll.tdf" 897 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121788 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "MASTER_CTRL MASTER_CTRL:inst3 " "Elaborating entity \"MASTER_CTRL\" for hierarchy \"MASTER_CTRL:inst3\"" {  } { { "TOP.bdf" "inst3" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1856 1416 1656 1968 "inst3" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121801 ""}
{ "Warning" "WVRFX_VERI_INCOMPLETE_SENSITIVITY_LIST" "DATA MASTER_CTRL.v(40) " "Verilog HDL Always Construct warning at MASTER_CTRL.v(40): variable \"DATA\" is read inside the Always Construct but isn't in the Always Construct's Event Control" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 40 0 0 } }  } 0 10235 "Verilog HDL Always Construct warning at %2!s!: variable \"%1!s!\" is read inside the Always Construct but isn't in the Always Construct's Event Control" 0 0 "Analysis & Synthesis" 0 -1 1754046121801 "|TOP|MASTER_CTRL:inst3"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "CTRL_DATA MASTER_CTRL.v(36) " "Verilog HDL Always Construct warning at MASTER_CTRL.v(36): inferring latch(es) for variable \"CTRL_DATA\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[0\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[0\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[1\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[1\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[2\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[2\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[3\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[3\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[4\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[4\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[5\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[5\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[6\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[6\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[7\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[7\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[8\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[8\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[9\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[9\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[10\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[10\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[11\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[11\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[12\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[12\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[13\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[13\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[14\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[14\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "CTRL_DATA\[15\] MASTER_CTRL.v(36) " "Inferred latch for \"CTRL_DATA\[15\]\" at MASTER_CTRL.v(36)" {  } { { "../src/MASTER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/MASTER_CTRL.v" 36 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121802 "|TOP|MASTER_CTRL:inst3"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "FMC_CONTROL FMC_CONTROL:inst " "Elaborating entity \"FMC_CONTROL\" for hierarchy \"FMC_CONTROL:inst\"" {  } { { "TOP.bdf" "inst" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1464 2264 2536 1864 "inst" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121807 ""}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[0\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[0\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121813 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[1\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[1\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[2\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[2\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[3\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[3\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[4\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[4\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[5\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[5\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[6\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[6\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[7\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[7\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[8\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[8\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[9\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[9\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[10\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[10\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[11\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[11\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[12\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[12\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[13\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[13\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[14\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[14\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "addr\[15\] FMC_CONTROL.v(139) " "Inferred latch for \"addr\[15\]\" at FMC_CONTROL.v(139)" {  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 139 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121814 "|TOP|FMC_CONTROL:inst"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_FREQ_MEASURE AD_FREQ_MEASURE:u_AD_FREQ_MEASURE " "Elaborating entity \"AD_FREQ_MEASURE\" for hierarchy \"AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\"" {  } { { "TOP.bdf" "u_AD_FREQ_MEASURE" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 280 1656 2024 456 "u_AD_FREQ_MEASURE" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121826 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_FREQ_MEASURE.v(51) " "Verilog HDL Case Statement warning at AD_FREQ_MEASURE.v(51): incomplete case statement has no default case item" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 51 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1754046121826 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE1_FREQ_DATA_H AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"BASE1_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE1_FREQ_DATA_L AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"BASE1_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE2_FREQ_DATA_H AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"BASE2_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "BASE2_FREQ_DATA_L AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"BASE2_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FREQ_DATA_H AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"AD1_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FREQ_DATA_L AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"AD1_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FREQ_DATA_H AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"AD2_FREQ_DATA_H\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FREQ_DATA_L AD_FREQ_MEASURE.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_MEASURE.v(49): inferring latch(es) for variable \"AD2_FREQ_DATA_L\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121827 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121828 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD2_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121829 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"AD1_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121830 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE2_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE2_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_L\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_L\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[0\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[0\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[1\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[1\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[2\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[2\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[3\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[3\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[4\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[4\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[5\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[5\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[6\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[6\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[7\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[7\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[8\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[8\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[9\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[9\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[10\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[10\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[11\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[11\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[12\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[12\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[13\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[13\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[14\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[14\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121831 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "BASE1_FREQ_DATA_H\[15\] AD_FREQ_MEASURE.v(49) " "Inferred latch for \"BASE1_FREQ_DATA_H\[15\]\" at AD_FREQ_MEASURE.v(49)" {  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121832 "|TOP|AD_FREQ_MEASURE:u_AD_FREQ_MEASURE"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "CNT32 CNT32:u_AD1_CNT32 " "Elaborating entity \"CNT32\" for hierarchy \"CNT32:u_AD1_CNT32\"" {  } { { "TOP.bdf" "u_AD1_CNT32" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 88 1496 1720 232 "u_AD1_CNT32" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121838 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "test test:inst2 " "Elaborating entity \"test\" for hierarchy \"test:inst2\"" {  } { { "TOP.bdf" "inst2" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1360 2744 2960 1440 "inst2" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121846 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_DATA_DEAL AD_DATA_DEAL:u_AD_DATA_DEAL " "Elaborating entity \"AD_DATA_DEAL\" for hierarchy \"AD_DATA_DEAL:u_AD_DATA_DEAL\"" {  } { { "TOP.bdf" "u_AD_DATA_DEAL" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1896 3832 4184 2072 "u_AD_DATA_DEAL" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121850 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_DATA_DEAL.v(48) " "Verilog HDL Case Statement warning at AD_DATA_DEAL.v(48): incomplete case statement has no default case item" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 48 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1754046121851 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "i AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"i\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "ad1_fifo_recv AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"ad1_fifo_recv\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FIFO_DATA_OUT AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"AD1_FIFO_DATA_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_FLAG_SHOW AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"AD1_FLAG_SHOW\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "ad2_fifo_recv AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"ad2_fifo_recv\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FIFO_DATA_OUT AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"AD2_FIFO_DATA_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_FLAG_SHOW AD_DATA_DEAL.v(46) " "Verilog HDL Always Construct warning at AD_DATA_DEAL.v(46): inferring latch(es) for variable \"AD2_FLAG_SHOW\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[0\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[0\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[1\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[1\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[2\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[2\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[3\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[3\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121852 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[4\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[4\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[5\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[5\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[6\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[6\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[7\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[7\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[8\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[8\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[9\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[9\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[10\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[10\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[11\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[11\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[12\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[12\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[13\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[13\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[14\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[14\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FLAG_SHOW\[15\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FLAG_SHOW\[15\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[0\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[0\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[1\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[1\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[2\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[2\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[3\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[3\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[4\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[4\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[5\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[5\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[6\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[6\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[7\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[7\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[8\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[8\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[9\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[9\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[10\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[10\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[11\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[11\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[12\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[12\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[13\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[13\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[14\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[14\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121853 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_FIFO_DATA_OUT\[15\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD2_FIFO_DATA_OUT\[15\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[0\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[0\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[1\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[1\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[2\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[2\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[3\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[3\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[4\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[4\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[5\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[5\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[6\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[6\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[7\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[7\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[8\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[8\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[9\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[9\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[10\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[10\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[11\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[11\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[12\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[12\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[13\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[13\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[14\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[14\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FLAG_SHOW\[15\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FLAG_SHOW\[15\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[0\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[0\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[1\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[1\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[2\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[2\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[3\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[3\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[4\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[4\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[5\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[5\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[6\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[6\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[7\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[7\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[8\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[8\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121854 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[9\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[9\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[10\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[10\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[11\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[11\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[12\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[12\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[13\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[13\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[14\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[14\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_FIFO_DATA_OUT\[15\] AD_DATA_DEAL.v(46) " "Inferred latch for \"AD1_FIFO_DATA_OUT\[15\]\" at AD_DATA_DEAL.v(46)" {  } { { "../src/AD_DATA_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_DATA_DEAL.v" 46 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046121855 "|TOP|AD_DATA_DEAL:u_AD_DATA_DEAL"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "TYFIFO TYFIFO:u_AD1_FIFO " "Elaborating entity \"TYFIFO\" for hierarchy \"TYFIFO:u_AD1_FIFO\"" {  } { { "TOP.bdf" "u_AD1_FIFO" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1560 3816 3992 1752 "u_AD1_FIFO" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046121863 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dcfifo TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Elaborating entity \"dcfifo\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\"" {  } { { "../ip/TYFIFO/TYFIFO.v" "dcfifo_component" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/TYFIFO/TYFIFO.v" 75 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122152 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Elaborated megafunction instantiation \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\"" {  } { { "../ip/TYFIFO/TYFIFO.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/TYFIFO/TYFIFO.v" 75 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122157 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component " "Instantiated megafunction \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_numwords 1024 " "Parameter \"lpm_numwords\" = \"1024\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_showahead OFF " "Parameter \"lpm_showahead\" = \"OFF\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type dcfifo " "Parameter \"lpm_type\" = \"dcfifo\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_width 12 " "Parameter \"lpm_width\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_widthu 10 " "Parameter \"lpm_widthu\" = \"10\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "overflow_checking ON " "Parameter \"overflow_checking\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "rdsync_delaypipe 4 " "Parameter \"rdsync_delaypipe\" = \"4\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "underflow_checking ON " "Parameter \"underflow_checking\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "use_eab ON " "Parameter \"use_eab\" = \"ON\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "wrsync_delaypipe 4 " "Parameter \"wrsync_delaypipe\" = \"4\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122157 ""}  } { { "../ip/TYFIFO/TYFIFO.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/TYFIFO/TYFIFO.v" 75 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046122157 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dcfifo_vve1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dcfifo_vve1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dcfifo_vve1 " "Found entity 1: dcfifo_vve1" {  } { { "db/dcfifo_vve1.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 36 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122189 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122189 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dcfifo_vve1 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated " "Elaborating entity \"dcfifo_vve1\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\"" {  } { { "dcfifo.tdf" "auto_generated" { Text "g:/altera/18.1/quartus/libraries/megafunctions/dcfifo.tdf" 190 3 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122189 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/a_graycounter_4p6.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/a_graycounter_4p6.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 a_graycounter_4p6 " "Found entity 1: a_graycounter_4p6" {  } { { "db/a_graycounter_4p6.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/a_graycounter_4p6.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122223 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122223 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "a_graycounter_4p6 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_4p6:rdptr_g1p " "Elaborating entity \"a_graycounter_4p6\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_4p6:rdptr_g1p\"" {  } { { "db/dcfifo_vve1.tdf" "rdptr_g1p" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 47 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122224 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/a_graycounter_07c.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/a_graycounter_07c.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 a_graycounter_07c " "Found entity 1: a_graycounter_07c" {  } { { "db/a_graycounter_07c.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/a_graycounter_07c.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122250 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122250 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "a_graycounter_07c TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_07c:wrptr_g1p " "Elaborating entity \"a_graycounter_07c\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|a_graycounter_07c:wrptr_g1p\"" {  } { { "db/dcfifo_vve1.tdf" "wrptr_g1p" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 48 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122251 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_ce41.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_ce41.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_ce41 " "Found entity 1: altsyncram_ce41" {  } { { "db/altsyncram_ce41.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/altsyncram_ce41.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122283 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122283 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_ce41 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram " "Elaborating entity \"altsyncram_ce41\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|altsyncram_ce41:fifo_ram\"" {  } { { "db/dcfifo_vve1.tdf" "fifo_ram" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 49 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122284 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_synch_pipe_qal.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_qal.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_synch_pipe_qal " "Found entity 1: alt_synch_pipe_qal" {  } { { "db/alt_synch_pipe_qal.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122297 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122297 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "alt_synch_pipe_qal TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp " "Elaborating entity \"alt_synch_pipe_qal\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\"" {  } { { "db/dcfifo_vve1.tdf" "rs_dgwp" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 56 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122298 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dffpipe_b09.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dffpipe_b09.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dffpipe_b09 " "Found entity 1: dffpipe_b09" {  } { { "db/dffpipe_b09.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dffpipe_b09.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122307 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122307 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dffpipe_b09 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\|dffpipe_b09:dffpipe12 " "Elaborating entity \"dffpipe_b09\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_qal:rs_dgwp\|dffpipe_b09:dffpipe12\"" {  } { { "db/alt_synch_pipe_qal.tdf" "dffpipe12" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/alt_synch_pipe_qal.tdf" 33 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122308 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/alt_synch_pipe_ral.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/alt_synch_pipe_ral.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 alt_synch_pipe_ral " "Found entity 1: alt_synch_pipe_ral" {  } { { "db/alt_synch_pipe_ral.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf" 26 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122318 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122318 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "alt_synch_pipe_ral TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp " "Elaborating entity \"alt_synch_pipe_ral\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\"" {  } { { "db/dcfifo_vve1.tdf" "ws_dgrp" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 57 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122319 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/dffpipe_c09.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/dffpipe_c09.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 dffpipe_c09 " "Found entity 1: dffpipe_c09" {  } { { "db/dffpipe_c09.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dffpipe_c09.tdf" 24 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122328 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122328 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "dffpipe_c09 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\|dffpipe_c09:dffpipe15 " "Elaborating entity \"dffpipe_c09\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|alt_synch_pipe_ral:ws_dgrp\|dffpipe_c09:dffpipe15\"" {  } { { "db/alt_synch_pipe_ral.tdf" "dffpipe15" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/alt_synch_pipe_ral.tdf" 33 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122330 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/cmpr_o76.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/cmpr_o76.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 cmpr_o76 " "Found entity 1: cmpr_o76" {  } { { "db/cmpr_o76.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/cmpr_o76.tdf" 22 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122357 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122357 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "cmpr_o76 TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|cmpr_o76:rdempty_eq_comp " "Elaborating entity \"cmpr_o76\" for hierarchy \"TYFIFO:u_AD1_FIFO\|dcfifo:dcfifo_component\|dcfifo_vve1:auto_generated\|cmpr_o76:rdempty_eq_comp\"" {  } { { "db/dcfifo_vve1.tdf" "rdempty_eq_comp" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/dcfifo_vve1.tdf" 58 2 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122358 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "FREQ_DEV FREQ_DEV:u_AD1_DEV " "Elaborating entity \"FREQ_DEV\" for hierarchy \"FREQ_DEV:u_AD1_DEV\"" {  } { { "TOP.bdf" "u_AD1_DEV" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1328 4312 4544 1440 "u_AD1_DEV" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122367 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "AD_FREQ_WORD AD_FREQ_WORD:u_AD_FREQ_WORD " "Elaborating entity \"AD_FREQ_WORD\" for hierarchy \"AD_FREQ_WORD:u_AD_FREQ_WORD\"" {  } { { "TOP.bdf" "u_AD_FREQ_WORD" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1312 3696 3928 1488 "u_AD_FREQ_WORD" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122374 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "AD_FREQ_WORD.v(51) " "Verilog HDL Case Statement warning at AD_FREQ_WORD.v(51): incomplete case statement has no default case item" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 51 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1754046122374 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_OUTH AD_FREQ_WORD.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable \"AD1_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122374 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD1_OUTL AD_FREQ_WORD.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable \"AD1_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122374 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_OUTH AD_FREQ_WORD.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable \"AD2_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122374 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "AD2_OUTL AD_FREQ_WORD.v(49) " "Verilog HDL Always Construct warning at AD_FREQ_WORD.v(49): inferring latch(es) for variable \"AD2_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122374 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[0\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[0\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[1\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[1\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[2\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[2\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[3\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[3\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[4\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[4\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[5\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[5\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[6\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[6\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[7\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[7\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[8\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[8\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[9\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[9\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[10\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[10\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[11\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[11\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[12\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[12\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[13\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[13\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[14\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[14\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTL\[15\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTL\[15\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[0\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[0\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[1\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[1\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[2\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[2\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[3\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[3\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[4\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[4\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122375 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[5\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[5\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[6\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[6\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[7\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[7\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[8\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[8\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[9\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[9\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[10\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[10\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[11\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[11\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[12\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[12\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[13\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[13\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[14\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[14\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD2_OUTH\[15\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD2_OUTH\[15\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[0\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[0\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[1\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[1\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[2\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[2\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[3\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[3\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[4\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[4\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[5\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[5\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[6\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[6\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[7\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[7\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[8\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[8\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[9\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[9\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[10\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[10\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[11\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[11\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[12\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[12\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[13\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[13\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[14\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[14\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTL\[15\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTL\[15\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[0\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[0\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[1\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[1\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[2\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[2\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[3\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[3\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[4\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[4\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[5\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[5\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122376 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[6\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[6\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[7\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[7\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[8\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[8\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[9\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[9\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[10\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[10\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[11\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[11\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[12\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[12\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[13\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[13\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[14\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[14\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "AD1_OUTH\[15\] AD_FREQ_WORD.v(49) " "Inferred latch for \"AD1_OUTH\[15\]\" at AD_FREQ_WORD.v(49)" {  } { { "../src/AD_FREQ_WORD.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_WORD.v" 49 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122377 "|TOP|AD_FREQ_WORD:u_AD_FREQ_WORD"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_PARAMETER_DEAL DA_PARAMETER_DEAL:inst1 " "Elaborating entity \"DA_PARAMETER_DEAL\" for hierarchy \"DA_PARAMETER_DEAL:inst1\"" {  } { { "TOP.bdf" "inst1" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 464 3464 3768 704 "inst1" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122544 ""}
{ "Warning" "WVRFX_L2_VERI_INCOMPLETE_CASE_STATEMENT" "DA_PARAMETER_DEAL.v(43) " "Verilog HDL Case Statement warning at DA_PARAMETER_DEAL.v(43): incomplete case statement has no default case item" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 43 0 0 } }  } 0 10270 "Verilog HDL Case Statement warning at %1!s!: incomplete case statement has no default case item" 0 0 "Analysis & Synthesis" 0 -1 1754046122544 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTH DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA1_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122544 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_OUTL DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA1_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122544 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTH DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA2_OUTH\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_OUTL DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA2_OUTL\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_AMP_OUT DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA1_AMP_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_AMP_OUT DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA2_AMP_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_STEP_OUT DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA1_STEP_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_STEP_OUT DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA2_STEP_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA1_WAVE_OUT DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA1_WAVE_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Warning" "WVRFX_L2_VERI_ALWAYS_ID_HOLDS_VALUE" "DA2_WAVE_OUT DA_PARAMETER_DEAL.v(42) " "Verilog HDL Always Construct warning at DA_PARAMETER_DEAL.v(42): inferring latch(es) for variable \"DA2_WAVE_OUT\", which holds its previous value in one or more paths through the always construct" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10240 "Verilog HDL Always Construct warning at %2!s!: inferring latch(es) for variable \"%1!s!\", which holds its previous value in one or more paths through the always construct" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122545 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_WAVE_OUT\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_WAVE_OUT\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_WAVE_OUT\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_WAVE_OUT\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_STEP_OUT\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_STEP_OUT\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_STEP_OUT\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_STEP_OUT\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122546 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[8\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[8\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[9\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[9\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[10\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[10\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_AMP_OUT\[11\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_AMP_OUT\[11\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[8\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[8\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[9\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[9\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[10\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[10\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_AMP_OUT\[11\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_AMP_OUT\[11\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[8\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[8\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[9\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[9\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[10\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[10\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122547 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[11\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[11\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[12\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[12\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[13\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[13\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[14\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[14\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTL\[15\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTL\[15\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[8\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[8\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[9\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[9\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[10\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[10\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[11\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[11\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[12\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[12\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[13\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[13\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[14\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[14\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA2_OUTH\[15\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA2_OUTH\[15\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[8\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[8\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[9\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[9\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[10\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[10\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122548 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[11\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[11\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[12\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[12\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[13\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[13\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[14\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[14\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTL\[15\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTL\[15\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[0\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[0\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[1\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[1\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[2\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[2\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[3\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[3\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[4\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[4\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[5\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[5\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[6\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[6\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[7\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[7\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[8\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[8\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[9\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[9\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[10\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[10\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[11\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[11\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[12\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[12\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[13\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[13\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[14\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[14\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "IVRFX_L2_VDB_LATCH_INFERRED" "DA1_OUTH\[15\] DA_PARAMETER_DEAL.v(42) " "Inferred latch for \"DA1_OUTH\[15\]\" at DA_PARAMETER_DEAL.v(42)" {  } { { "../src/DA_PARAMETER_DEAL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_DEAL.v" 42 0 0 } }  } 0 10041 "Inferred latch for \"%1!s!\" at %2!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122549 "|TOP|DA_PARAMETER_DEAL:inst1"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "voltage_scaler_clocked_fast voltage_scaler_clocked_fast:inst10 " "Elaborating entity \"voltage_scaler_clocked_fast\" for hierarchy \"voltage_scaler_clocked_fast:inst10\"" {  } { { "TOP.bdf" "inst10" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 528 4768 5008 640 "inst10" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122556 ""}
{ "Warning" "WVRFX_L2_HDL_OBJECT_ASSIGNED_NOT_READ" "rom_data_reg2 voltage_scaler_clocked_fast.v(22) " "Verilog HDL or VHDL warning at voltage_scaler_clocked_fast.v(22): object \"rom_data_reg2\" assigned a value but never read" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 22 0 0 } }  } 0 10036 "Verilog HDL or VHDL warning at %2!s!: object \"%1!s!\" assigned a value but never read" 0 0 "Analysis & Synthesis" 0 -1 1754046122556 "|TOP|voltage_scaler_clocked_fast:inst10"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 voltage_scaler_clocked_fast.v(42) " "Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(42): truncated value with size 32 to match size of target (14)" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 42 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046122556 "|TOP|voltage_scaler_clocked_fast:inst10"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 voltage_scaler_clocked_fast.v(44) " "Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(44): truncated value with size 32 to match size of target (14)" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 44 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046122556 "|TOP|voltage_scaler_clocked_fast:inst10"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 voltage_scaler_clocked_fast.v(67) " "Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(67): truncated value with size 32 to match size of target (14)" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 67 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046122556 "|TOP|voltage_scaler_clocked_fast:inst10"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 voltage_scaler_clocked_fast.v(70) " "Verilog HDL assignment warning at voltage_scaler_clocked_fast.v(70): truncated value with size 32 to match size of target (14)" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 70 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046122556 "|TOP|voltage_scaler_clocked_fast:inst10"}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "DA_WAVEFORM DA_WAVEFORM:inst5 " "Elaborating entity \"DA_WAVEFORM\" for hierarchy \"DA_WAVEFORM:inst5\"" {  } { { "TOP.bdf" "inst5" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 520 4280 4552 632 "inst5" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122564 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sin_rom DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst " "Elaborating entity \"sin_rom\" for hierarchy \"DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\"" {  } { { "../src/DA_WAVEFORM.v" "sin_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 48 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122573 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sin_rom/sin_rom.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sin_rom/sin_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122634 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sin_rom/sin_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sin_rom/sin_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122640 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/sine_64x14.mif " "Parameter \"init_file\" = \"../script/sine_64x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 64 " "Parameter \"numwords_a\" = \"64\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 6 " "Parameter \"widthad_a\" = \"6\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122640 ""}  } { { "../ip/sin_rom/sin_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sin_rom/sin_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046122640 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_hva1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_hva1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_hva1 " "Found entity 1: altsyncram_hva1" {  } { { "db/altsyncram_hva1.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/altsyncram_hva1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122667 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122667 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_hva1 DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component\|altsyncram_hva1:auto_generated " "Elaborating entity \"altsyncram_hva1\" for hierarchy \"DA_WAVEFORM:inst5\|sin_rom:sin_rom_inst\|altsyncram:altsyncram_component\|altsyncram_hva1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122668 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sqaure_rom DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst " "Elaborating entity \"sqaure_rom\" for hierarchy \"DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\"" {  } { { "../src/DA_WAVEFORM.v" "sqaure_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 54 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122701 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122722 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122728 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/square_64x14.mif " "Parameter \"init_file\" = \"../script/square_64x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 64 " "Parameter \"numwords_a\" = \"64\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 6 " "Parameter \"widthad_a\" = \"6\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122728 ""}  } { { "../ip/sqaure_rom/sqaure_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sqaure_rom/sqaure_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046122728 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_j6b1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_j6b1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_j6b1 " "Found entity 1: altsyncram_j6b1" {  } { { "db/altsyncram_j6b1.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/altsyncram_j6b1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122765 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122765 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_j6b1 DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\|altsyncram_j6b1:auto_generated " "Elaborating entity \"altsyncram_j6b1\" for hierarchy \"DA_WAVEFORM:inst5\|sqaure_rom:sqaure_rom_inst\|altsyncram:altsyncram_component\|altsyncram_j6b1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122766 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "triangle_rom DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst " "Elaborating entity \"triangle_rom\" for hierarchy \"DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\"" {  } { { "../src/DA_WAVEFORM.v" "triangle_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 60 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122801 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/triangle_rom/triangle_rom.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122809 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122815 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/triangle_64x14.mif " "Parameter \"init_file\" = \"../script/triangle_64x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 64 " "Parameter \"numwords_a\" = \"64\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 6 " "Parameter \"widthad_a\" = \"6\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122815 ""}  } { { "../ip/triangle_rom/triangle_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/triangle_rom/triangle_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046122815 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_ocb1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_ocb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_ocb1 " "Found entity 1: altsyncram_ocb1" {  } { { "db/altsyncram_ocb1.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/altsyncram_ocb1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122841 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122841 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_ocb1 DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\|altsyncram_ocb1:auto_generated " "Elaborating entity \"altsyncram_ocb1\" for hierarchy \"DA_WAVEFORM:inst5\|triangle_rom:triangle_rom_inst\|altsyncram:altsyncram_component\|altsyncram_ocb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122842 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "sawtooth_rom DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst " "Elaborating entity \"sawtooth_rom\" for hierarchy \"DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\"" {  } { { "../src/DA_WAVEFORM.v" "sawtooth_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 66 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122876 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component " "Elaborating entity \"altsyncram\" for hierarchy \"DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "altsyncram_component" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 81 0 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122883 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component " "Elaborated megafunction instantiation \"DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\"" {  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 81 0 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122889 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component " "Instantiated megafunction \"DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "address_aclr_a NONE " "Parameter \"address_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_input_a BYPASS " "Parameter \"clock_enable_input_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "clock_enable_output_a BYPASS " "Parameter \"clock_enable_output_a\" = \"BYPASS\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "init_file ../script/sawtooth_64x14.mif " "Parameter \"init_file\" = \"../script/sawtooth_64x14.mif\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "intended_device_family Cyclone IV E " "Parameter \"intended_device_family\" = \"Cyclone IV E\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_hint ENABLE_RUNTIME_MOD=NO " "Parameter \"lpm_hint\" = \"ENABLE_RUNTIME_MOD=NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "lpm_type altsyncram " "Parameter \"lpm_type\" = \"altsyncram\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "numwords_a 64 " "Parameter \"numwords_a\" = \"64\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "operation_mode ROM " "Parameter \"operation_mode\" = \"ROM\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_aclr_a NONE " "Parameter \"outdata_aclr_a\" = \"NONE\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "outdata_reg_a UNREGISTERED " "Parameter \"outdata_reg_a\" = \"UNREGISTERED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "widthad_a 6 " "Parameter \"widthad_a\" = \"6\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_a 14 " "Parameter \"width_a\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "width_byteena_a 1 " "Parameter \"width_byteena_a\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046122889 ""}  } { { "../ip/sawtooth_rom/sawtooth_rom.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/ip/sawtooth_rom/sawtooth_rom.v" 81 0 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046122889 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/altsyncram_rdb1.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/altsyncram_rdb1.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 altsyncram_rdb1 " "Found entity 1: altsyncram_rdb1" {  } { { "db/altsyncram_rdb1.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/altsyncram_rdb1.tdf" 27 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046122915 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046122915 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "altsyncram_rdb1 DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\|altsyncram_rdb1:auto_generated " "Elaborating entity \"altsyncram_rdb1\" for hierarchy \"DA_WAVEFORM:inst5\|sawtooth_rom:sawtooth_rom_inst\|altsyncram:altsyncram_component\|altsyncram_rdb1:auto_generated\"" {  } { { "altsyncram.tdf" "auto_generated" { Text "g:/altera/18.1/quartus/libraries/megafunctions/altsyncram.tdf" 791 4 0 } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046122916 ""}
{ "Info" "ISGN_START_ELABORATION_HIERARCHY" "VOLTAGE_SCALER_CLOCKED VOLTAGE_SCALER_CLOCKED:inst11 " "Elaborating entity \"VOLTAGE_SCALER_CLOCKED\" for hierarchy \"VOLTAGE_SCALER_CLOCKED:inst11\"" {  } { { "TOP.bdf" "inst11" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 960 4224 4464 1072 "inst11" "" } } } }  } 0 12128 "Elaborating entity \"%1!s!\" for hierarchy \"%2!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046123000 ""}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 VOLTAGE_SCALER_CLOCKED.v(41) " "Verilog HDL assignment warning at VOLTAGE_SCALER_CLOCKED.v(41): truncated value with size 32 to match size of target (14)" {  } { { "../src/VOLTAGE_SCALER_CLOCKED.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v" 41 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046123000 "|TOP|VOLTAGE_SCALER_CLOCKED:inst11"}
{ "Warning" "WVRFX_L2_VERI_EXPRESSION_TRUNCATED_TO_FIT" "32 14 VOLTAGE_SCALER_CLOCKED.v(47) " "Verilog HDL assignment warning at VOLTAGE_SCALER_CLOCKED.v(47): truncated value with size 32 to match size of target (14)" {  } { { "../src/VOLTAGE_SCALER_CLOCKED.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/VOLTAGE_SCALER_CLOCKED.v" 47 0 0 } }  } 0 10230 "Verilog HDL assignment warning at %3!s!: truncated value with size %1!d! to match size of target (%2!d!)" 0 0 "Analysis & Synthesis" 0 -1 1754046123000 "|TOP|VOLTAGE_SCALER_CLOCKED:inst11"}
{ "Warning" "WSGN_WIDTH_MISMATCH_INPUT_PORT_TOO_NARROW" "address sawtooth_rom_inst 7 6 " "Port \"address\" on the entity instantiation of \"sawtooth_rom_inst\" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored." {  } { { "../src/DA_WAVEFORM.v" "sawtooth_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 66 0 0 } }  } 0 12020 "Port \"%1!s!\" on the entity instantiation of \"%2!s!\" is connected to a signal of width %3!d!. The formal width of the signal in the module is %4!d!.  The extra bits will be ignored." 0 0 "Analysis & Synthesis" 0 -1 1754046123051 "|TOP|DA_WAVEFORM:inst5|sawtooth_rom:sawtooth_rom_inst"}
{ "Warning" "WSGN_WIDTH_MISMATCH_INPUT_PORT_TOO_NARROW" "address triangle_rom_inst 7 6 " "Port \"address\" on the entity instantiation of \"triangle_rom_inst\" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored." {  } { { "../src/DA_WAVEFORM.v" "triangle_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 60 0 0 } }  } 0 12020 "Port \"%1!s!\" on the entity instantiation of \"%2!s!\" is connected to a signal of width %3!d!. The formal width of the signal in the module is %4!d!.  The extra bits will be ignored." 0 0 "Analysis & Synthesis" 0 -1 1754046123052 "|TOP|DA_WAVEFORM:inst5|triangle_rom:triangle_rom_inst"}
{ "Warning" "WSGN_WIDTH_MISMATCH_INPUT_PORT_TOO_NARROW" "address sqaure_rom_inst 7 6 " "Port \"address\" on the entity instantiation of \"sqaure_rom_inst\" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored." {  } { { "../src/DA_WAVEFORM.v" "sqaure_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 54 0 0 } }  } 0 12020 "Port \"%1!s!\" on the entity instantiation of \"%2!s!\" is connected to a signal of width %3!d!. The formal width of the signal in the module is %4!d!.  The extra bits will be ignored." 0 0 "Analysis & Synthesis" 0 -1 1754046123052 "|TOP|DA_WAVEFORM:inst5|sqaure_rom:sqaure_rom_inst"}
{ "Warning" "WSGN_WIDTH_MISMATCH_INPUT_PORT_TOO_NARROW" "address sin_rom_inst 7 6 " "Port \"address\" on the entity instantiation of \"sin_rom_inst\" is connected to a signal of width 7. The formal width of the signal in the module is 6.  The extra bits will be ignored." {  } { { "../src/DA_WAVEFORM.v" "sin_rom_inst" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_WAVEFORM.v" 48 0 0 } }  } 0 12020 "Port \"%1!s!\" on the entity instantiation of \"%2!s!\" is connected to a signal of width %3!d!. The formal width of the signal in the module is %4!d!.  The extra bits will be ignored." 0 0 "Analysis & Synthesis" 0 -1 1754046123052 "|TOP|DA_WAVEFORM:inst5|sin_rom:sin_rom_inst"}
{ "Warning" "WSGN_MISMATCH_PORT" "PHASE_ADDR\[7\] DA_WAVEFORM DA_WAVEFORM:inst8 " "Port \"PHASE_ADDR\[7\]\" does not exist in entity definition of \"DA_WAVEFORM\".  The port's range differs between the entity definition and its actual instantiation, \"DA_WAVEFORM:inst8\"." {  } { { "TOP.bdf" "inst8" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 792 4248 4520 904 "inst8" "" } } } }  } 0 12001 "Port \"%1!s!\" does not exist in entity definition of \"%2!s!\".  The port's range differs between the entity definition and its actual instantiation, \"%3!s!\"." 0 0 "Analysis & Synthesis" 0 -1 1754046123052 ""}
{ "Warning" "WSGN_MISMATCH_PORT" "PHASE_ADDR\[7\] DA_WAVEFORM DA_WAVEFORM:inst5 " "Port \"PHASE_ADDR\[7\]\" does not exist in entity definition of \"DA_WAVEFORM\".  The port's range differs between the entity definition and its actual instantiation, \"DA_WAVEFORM:inst5\"." {  } { { "TOP.bdf" "inst5" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 520 4280 4552 632 "inst5" "" } } } }  } 0 12001 "Port \"%1!s!\" does not exist in entity definition of \"%2!s!\".  The port's range differs between the entity definition and its actual instantiation, \"%3!s!\"." 0 0 "Analysis & Synthesis" 0 -1 1754046123053 ""}
{ "Warning" "WOPT_OPT_PROTECT_A_CLOCK_MUX" "" "Clock multiplexers are found and protected" { { "Warning" "WOPT_OPT_PROTECT_A_CLOCK_MUX_SUB" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL " "Found clock multiplexer DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 60 -1 0 } }  } 0 19017 "Found clock multiplexer %1!s!" 0 0 "Design Software" 0 -1 1754046123212 "|TOP|DA_PARAMETER_CTRL:inst7|FREQ_OUT_B_FINAL"}  } {  } 0 19016 "Clock multiplexers are found and protected" 0 0 "Analysis & Synthesis" 0 -1 1754046123212 ""}
{ "Info" "ILPMS_INFERENCING_SUMMARY" "4 " "Inferred 4 megafunctions from design logic" { { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked_fast:inst10\|Mult1 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked_fast:inst10\|Mult1\"" {  } { { "../src/voltage_scaler_clocked_fast.v" "Mult1" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 70 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1754046123538 ""} { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked_fast:inst14\|Mult1 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked_fast:inst14\|Mult1\"" {  } { { "../src/voltage_scaler_clocked_fast.v" "Mult1" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 70 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1754046123538 ""} { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked_fast:inst10\|Mult0 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked_fast:inst10\|Mult0\"" {  } { { "../src/voltage_scaler_clocked_fast.v" "Mult0" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 48 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1754046123538 ""} { "Info" "ILPMS_LPM_MULT_INFERRED" "voltage_scaler_clocked_fast:inst14\|Mult0 lpm_mult " "Inferred multiplier megafunction (\"lpm_mult\") from the following logic: \"voltage_scaler_clocked_fast:inst14\|Mult0\"" {  } { { "../src/voltage_scaler_clocked_fast.v" "Mult0" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 48 -1 0 } }  } 0 278003 "Inferred multiplier megafunction (\"%2!s!\") from the following logic: \"%1!s!\"" 0 0 "Design Software" 0 -1 1754046123538 ""}  } {  } 0 278001 "Inferred %1!llu! megafunctions from design logic" 0 0 "Analysis & Synthesis" 0 -1 1754046123538 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult1 " "Elaborated megafunction instantiation \"voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult1\"" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 70 -1 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046123637 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult1 " "Instantiated megafunction \"voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult1\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHA 26 " "Parameter \"LPM_WIDTHA\" = \"26\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHB 7 " "Parameter \"LPM_WIDTHB\" = \"7\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHP 33 " "Parameter \"LPM_WIDTHP\" = \"33\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHR 33 " "Parameter \"LPM_WIDTHR\" = \"33\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHS 1 " "Parameter \"LPM_WIDTHS\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_REPRESENTATION UNSIGNED " "Parameter \"LPM_REPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_A_IS_CONSTANT NO " "Parameter \"INPUT_A_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_B_IS_CONSTANT YES " "Parameter \"INPUT_B_IS_CONSTANT\" = \"YES\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "MAXIMIZE_SPEED 5 " "Parameter \"MAXIMIZE_SPEED\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123638 ""}  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 70 -1 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046123638 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mult_cft.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/mult_cft.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mult_cft " "Found entity 1: mult_cft" {  } { { "db/mult_cft.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/mult_cft.tdf" 30 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046123670 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046123670 ""}
{ "Info" "ISGN_ELABORATION_HEADER" "voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult0 " "Elaborated megafunction instantiation \"voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult0\"" {  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 48 -1 0 } }  } 0 12130 "Elaborated megafunction instantiation \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046123698 ""}
{ "Info" "ISGN_MEGAFN_PARAM_TOP" "voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult0 " "Instantiated megafunction \"voltage_scaler_clocked_fast:inst10\|lpm_mult:Mult0\" with the following parameter:" { { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHA 14 " "Parameter \"LPM_WIDTHA\" = \"14\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHB 12 " "Parameter \"LPM_WIDTHB\" = \"12\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHP 26 " "Parameter \"LPM_WIDTHP\" = \"26\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHR 26 " "Parameter \"LPM_WIDTHR\" = \"26\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_WIDTHS 1 " "Parameter \"LPM_WIDTHS\" = \"1\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "LPM_REPRESENTATION UNSIGNED " "Parameter \"LPM_REPRESENTATION\" = \"UNSIGNED\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_A_IS_CONSTANT NO " "Parameter \"INPUT_A_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "INPUT_B_IS_CONSTANT NO " "Parameter \"INPUT_B_IS_CONSTANT\" = \"NO\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""} { "Info" "ISGN_MEGAFN_PARAM_SUB" "MAXIMIZE_SPEED 5 " "Parameter \"MAXIMIZE_SPEED\" = \"5\"" {  } {  } 0 12134 "Parameter \"%1!s!\" = \"%2!s!\"" 0 0 "Design Software" 0 -1 1754046123699 ""}  } { { "../src/voltage_scaler_clocked_fast.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/voltage_scaler_clocked_fast.v" 48 -1 0 } }  } 0 12133 "Instantiated megafunction \"%1!s!\" with the following parameter:" 0 0 "Analysis & Synthesis" 0 -1 1754046123699 ""}
{ "Info" "ISGN_NUM_OF_DESIGN_UNITS_AND_ENTITIES" "db/mult_4dt.tdf 1 1 " "Found 1 design units, including 1 entities, in source file db/mult_4dt.tdf" { { "Info" "ISGN_ENTITY_NAME" "1 mult_4dt " "Found entity 1: mult_4dt" {  } { { "db/mult_4dt.tdf" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/mult_4dt.tdf" 28 1 0 } }  } 0 12023 "Found entity %1!d!: %2!s!" 0 0 "Design Software" 0 -1 1754046123724 ""}  } {  } 0 12021 "Found %2!llu! design units, including %3!llu! entities, in source file %1!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046123724 ""}
{ "Warning" "WSGN_CONNECTIVITY_WARNINGS" "4 " "4 hierarchies have connectivity warnings - see the Connectivity Checks report folder" {  } {  } 0 12241 "%1!d! hierarchies have connectivity warnings - see the Connectivity Checks report folder" 0 0 "Analysis & Synthesis" 0 -1 1754046123899 ""}
{ "Info" "IMLS_MLS_IGNORED_SUMMARY" "70 " "Ignored 70 buffer(s)" { { "Info" "IMLS_MLS_IGNORED_SOFT" "70 " "Ignored 70 SOFT buffer(s)" {  } {  } 0 13019 "Ignored %1!d! SOFT buffer(s)" 0 0 "Design Software" 0 -1 1754046123917 ""}  } {  } 0 13014 "Ignored %1!d! buffer(s)" 0 0 "Analysis & Synthesis" 0 -1 1754046123917 ""}
{ "Info" "ISUTIL_TIMING_DRIVEN_SYNTHESIS_RUNNING" "" "Timing-Driven Synthesis is running" {  } {  } 0 286030 "Timing-Driven Synthesis is running" 0 0 "Analysis & Synthesis" 0 -1 1754046124097 ""}
{ "Info" "ISCL_SCL_LOST_FANOUT_MSG_HDR" "4 " "4 registers lost all their fanouts during netlist optimizations." {  } {  } 0 17049 "%1!d! registers lost all their fanouts during netlist optimizations." 0 0 "Analysis & Synthesis" 0 -1 1754046124517 ""}
{ "Info" "IBPM_HARD_BLOCK_PARTITION_CREATED" "hard_block:auto_generated_inst " "Generating hard_block partition \"hard_block:auto_generated_inst\"" { { "Info" "IBPM_HARD_BLOCK_PARTITION_NODE" "1 0 1 0 0 " "Adding 1 node(s), including 0 DDIO, 1 PLL, 0 transceiver and 0 LCELL" {  } {  } 0 16011 "Adding %1!d! node(s), including %2!d! DDIO, %3!d! PLL, %4!d! transceiver and %5!d! LCELL" 0 0 "Design Software" 0 -1 1754046124900 ""}  } {  } 0 16010 "Generating hard_block partition \"%1!s!\"" 0 0 "Analysis & Synthesis" 0 -1 1754046124900 ""}
{ "Info" "ICUT_CUT_TM_SUMMARY" "2214 " "Implemented 2214 device resources after synthesis - the final resource count might be different" { { "Info" "ICUT_CUT_TM_IPINS" "32 " "Implemented 32 input pins" {  } {  } 0 21058 "Implemented %1!d! input pins" 0 0 "Design Software" 0 -1 1754046125120 ""} { "Info" "ICUT_CUT_TM_OPINS" "32 " "Implemented 32 output pins" {  } {  } 0 21059 "Implemented %1!d! output pins" 0 0 "Design Software" 0 -1 1754046125120 ""} { "Info" "ICUT_CUT_TM_BIDIRS" "16 " "Implemented 16 bidirectional pins" {  } {  } 0 21060 "Implemented %1!d! bidirectional pins" 0 0 "Design Software" 0 -1 1754046125120 ""} { "Info" "ICUT_CUT_TM_LCELLS" "1987 " "Implemented 1987 logic cells" {  } {  } 0 21061 "Implemented %1!d! logic cells" 0 0 "Design Software" 0 -1 1754046125120 ""} { "Info" "ICUT_CUT_TM_RAMS" "136 " "Implemented 136 RAM segments" {  } {  } 0 21064 "Implemented %1!d! RAM segments" 0 0 "Design Software" 0 -1 1754046125120 ""} { "Info" "ICUT_CUT_TM_PLLS" "1 " "Implemented 1 PLLs" {  } {  } 0 21065 "Implemented %1!d! PLLs" 0 0 "Design Software" 0 -1 1754046125120 ""} { "Info" "ICUT_CUT_TM_DSP_ELEM" "10 " "Implemented 10 DSP elements" {  } {  } 0 21062 "Implemented %1!d! DSP elements" 0 0 "Design Software" 0 -1 1754046125120 ""}  } {  } 0 21057 "Implemented %1!d! device resources after synthesis - the final resource count might be different" 0 0 "Analysis & Synthesis" 0 -1 1754046125120 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Analysis & Synthesis 0 s 61 s Quartus Prime " "Quartus Prime Analysis & Synthesis was successful. 0 errors, 61 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4885 " "Peak virtual memory: 4885 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1754046125146 ""} { "Info" "IQEXE_END_BANNER_TIME" "Fri Aug 01 19:02:05 2025 " "Processing ended: Fri Aug 01 19:02:05 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1754046125146 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:09 " "Elapsed time: 00:00:09" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1754046125146 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:15 " "Total CPU time (on all processors): 00:00:15" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1754046125146 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Analysis & Synthesis" 0 -1 1754046125146 ""}
