{ "Info" "IQEXE_SEPARATOR" "" "*******************************************************************" {  } {  } 3 0 "*******************************************************************" 0 0 "Design Software" 0 -1 1754072282043 ""}
{ "Info" "IQEXE_START_BANNER_PRODUCT" "Timing Analyzer Quartus Prime " "Running Quartus Prime Timing Analyzer" { { "Info" "IQEXE_START_BANNER_VERSION" "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition " "Version 18.1.0 Build 625 09/12/2018 SJ Lite Edition" {  } {  } 0 0 "%1!s!" 0 0 "Design Software" 0 -1 1754072282047 ""} { "Info" "IQEXE_START_BANNER_TIME" "Sat Aug 02 02:18:01 2025 " "Processing started: Sat Aug 02 02:18:01 2025" {  } {  } 0 0 "Processing started: %1!s!" 0 0 "Design Software" 0 -1 1754072282047 ""}  } {  } 4 0 "Running %2!s! %1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282047 ""}
{ "Info" "IQEXE_START_BANNER_COMMANDLINE" "quartus_sta ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT " "Command: quartus_sta ZUOLAN_FPGA_OBJECT -c ZUOLAN_FPGA_OBJECT" {  } {  } 0 0 "Command: %1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282047 ""}
{ "Info" "0" "" "qsta_default_script.tcl version: #1" {  } {  } 0 0 "qsta_default_script.tcl version: #1" 0 0 "Timing Analyzer" 0 0 1754072282087 ""}
{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Timing Analyzer" 0 -1 1754072282211 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282249 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282249 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "354 " "The Timing Analyzer is analyzing 354 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Timing Analyzer" 0 -1 1754072282352 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1754072282379 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1754072282379 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1754072282379 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Timing Analyzer" 0 -1 1754072282379 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "ZUOLAN_FPGA_OBJECT.sdc " "Synopsys Design Constraints File file not found: 'ZUOLAN_FPGA_OBJECT.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Timing Analyzer" 0 -1 1754072282382 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "generated clocks \"derive_pll_clocks -create_base_clocks\" " "No user constrained generated clocks found in the design. Calling \"derive_pll_clocks -create_base_clocks\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282382 ""}
{ "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "Deriving PLL clocks " "Deriving PLL clocks" { { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_clock -period 20.000 -waveform \{0.000 10.000\} -name CLK CLK " "create_clock -period 20.000 -waveform \{0.000 10.000\} -name CLK CLK" {  } {  } 0 332110 "%1!s!" 0 0 "Design Software" 0 -1 1754072282383 ""} { "Info" "ISTA_DERIVE_PLL_CLOCKS_INFO" "create_generated_clock -source \{inst6\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -multiply_by 3 -duty_cycle 50.00 -name \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} " "create_generated_clock -source \{inst6\|altpll_component\|auto_generated\|pll1\|inclk\[0\]\} -multiply_by 3 -duty_cycle 50.00 -name \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\} \{inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]\}" {  } {  } 0 332110 "%1!s!" 0 0 "Design Software" 0 -1 1754072282383 ""}  } {  } 0 332110 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282383 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_DERIVING" "base clocks \"derive_clocks -period 1.0\" " "No user constrained base clocks found in the design. Calling \"derive_clocks -period 1.0\"" {  } {  } 0 332142 "No user constrained %1!s! found in the design. Calling %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282383 ""}
{ "Info" "ISTA_DERIVE_CLOCKS_INFO" "Deriving Clocks " "Deriving Clocks" { { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " "create_clock -period 1.000 -name DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754072282384 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FPGA_CS_NEL FPGA_CS_NEL " "create_clock -period 1.000 -name FPGA_CS_NEL FPGA_CS_NEL" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754072282384 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name AD2_INPUT_CLK AD2_INPUT_CLK " "create_clock -period 1.000 -name AD2_INPUT_CLK AD2_INPUT_CLK" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754072282384 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name AD1_INPUT_CLK AD1_INPUT_CLK " "create_clock -period 1.000 -name AD1_INPUT_CLK AD1_INPUT_CLK" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754072282384 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FREQ_DEV:u_AD2_DEV\|FREQ_OUT FREQ_DEV:u_AD2_DEV\|FREQ_OUT " "create_clock -period 1.000 -name FREQ_DEV:u_AD2_DEV\|FREQ_OUT FREQ_DEV:u_AD2_DEV\|FREQ_OUT" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754072282384 ""} { "Info" "ISTA_DERIVE_CLOCKS_INFO" "create_clock -period 1.000 -name FREQ_DEV:u_AD1_DEV\|FREQ_OUT FREQ_DEV:u_AD1_DEV\|FREQ_OUT " "create_clock -period 1.000 -name FREQ_DEV:u_AD1_DEV\|FREQ_OUT FREQ_DEV:u_AD1_DEV\|FREQ_OUT" {  } {  } 0 332105 "%1!s!" 0 0 "Design Software" 0 -1 1754072282384 ""}  } {  } 0 332105 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282384 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282390 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282391 ""}
{ "Info" "0" "" "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" {  } {  } 0 0 "Found TIMING_ANALYZER_REPORT_SCRIPT_INCLUDE_DEFAULT_ANALYSIS = ON" 0 0 "Timing Analyzer" 0 0 1754072282392 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 85C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 85C Model" 0 0 "Timing Analyzer" 0 0 1754072282397 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1754072282453 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1754072282453 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -11.015 " "Worst-case setup slack is -11.015" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -11.015           -3870.186 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "  -11.015           -3870.186 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.902           -1188.720 FPGA_CS_NEL  " "   -7.902           -1188.720 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.257            -922.345 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -7.257            -922.345 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.679            -153.861 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -4.679            -153.861 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.449            -144.255 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -4.449            -144.255 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.219             -72.296 AD2_INPUT_CLK  " "   -3.219             -72.296 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.849             -63.036 AD1_INPUT_CLK  " "   -2.849             -63.036 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282454 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282454 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -5.025 " "Worst-case hold slack is -5.025" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -5.025            -578.308 FPGA_CS_NEL  " "   -5.025            -578.308 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.452              -9.857 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -2.452              -9.857 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.441               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.441               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.443               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.443               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.452               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.452               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.497               0.000 AD1_INPUT_CLK  " "    0.497               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.497               0.000 AD2_INPUT_CLK  " "    0.497               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282461 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282461 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -7.039 " "Worst-case recovery slack is -7.039" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282463 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282463 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.039            -436.530 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -7.039            -436.530 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282463 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.618             -71.552 AD1_INPUT_CLK  " "   -2.618             -71.552 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282463 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.905             -56.048 AD2_INPUT_CLK  " "   -1.905             -56.048 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282463 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.626             -28.836 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -1.626             -28.836 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282463 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282463 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 0.935 " "Worst-case removal slack is 0.935" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282466 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282466 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.935               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    0.935               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282466 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.886               0.000 AD2_INPUT_CLK  " "    1.886               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282466 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.118               0.000 AD1_INPUT_CLK  " "    2.118               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282466 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    5.250               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    5.250               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282466 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282466 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -4.000 " "Worst-case minimum pulse width slack is -4.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -783.691 FPGA_CS_NEL  " "   -3.201            -783.691 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.071 AD1_INPUT_CLK  " "   -3.000             -52.071 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.071 AD2_INPUT_CLK  " "   -3.000             -52.071 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.022               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.022               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.934               0.000 CLK  " "    9.934               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282468 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282468 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 96 synchronizer chains. " "Report Metastability: Found 96 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1754072282607 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282607 ""}
{ "Info" "0" "" "Analyzing Slow 1200mV 0C Model" {  } {  } 0 0 "Analyzing Slow 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1754072282612 ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Timing Analyzer" 0 -1 1754072282625 ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Timing Analyzer" 0 -1 1754072282831 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072282901 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1754072282916 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1754072282916 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -10.336 " "Worst-case setup slack is -10.336" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "  -10.336           -3598.002 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "  -10.336           -3598.002 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -7.791           -1146.429 FPGA_CS_NEL  " "   -7.791           -1146.429 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.655            -845.637 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -6.655            -845.637 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.343            -142.041 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -4.343            -142.041 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.271            -136.825 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -4.271            -136.825 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.787             -62.795 AD2_INPUT_CLK  " "   -2.787             -62.795 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.430             -53.741 AD1_INPUT_CLK  " "   -2.430             -53.741 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282919 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282919 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -4.559 " "Worst-case hold slack is -4.559" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.559            -523.221 FPGA_CS_NEL  " "   -4.559            -523.221 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.255              -9.409 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -2.255              -9.409 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.395               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.395               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.402               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.402               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.402               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.402               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.445               0.000 AD1_INPUT_CLK  " "    0.445               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.445               0.000 AD2_INPUT_CLK  " "    0.445               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282929 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282929 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -6.454 " "Worst-case recovery slack is -6.454" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -6.454            -400.082 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -6.454            -400.082 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.317             -62.576 AD1_INPUT_CLK  " "   -2.317             -62.576 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.647             -47.840 AD2_INPUT_CLK  " "   -1.647             -47.840 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282933 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.476             -25.854 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -1.476             -25.854 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282933 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282933 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 0.815 " "Worst-case removal slack is 0.815" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282937 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282937 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.815               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    0.815               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282937 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.624               0.000 AD2_INPUT_CLK  " "    1.624               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282937 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    1.843               0.000 AD1_INPUT_CLK  " "    1.843               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282937 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    4.717               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    4.717               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282937 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282937 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -4.000 " "Worst-case minimum pulse width slack is -4.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -4.000            -583.844 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -722.461 FPGA_CS_NEL  " "   -3.201            -722.461 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -3.201            -106.939 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.407 AD2_INPUT_CLK  " "   -3.000             -52.407 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -52.343 AD1_INPUT_CLK  " "   -3.000             -52.343 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.995               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    2.995               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.943               0.000 CLK  " "    9.943               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072282942 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072282942 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 96 synchronizer chains. " "Report Metastability: Found 96 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1754072283113 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072283113 ""}
{ "Info" "0" "" "Analyzing Fast 1200mV 0C Model" {  } {  } 0 0 "Analyzing Fast 1200mV 0C Model" 0 0 "Timing Analyzer" 0 0 1754072283119 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072283197 ""}
{ "Critical Warning" "WSTA_TIMING_NOT_MET" "" "Timing requirements not met" { { "Info" "ISTA_TIMING_NOT_MET_USE_ADA" "" "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." {  } {  } 0 11105 "For recommendations on closing timing, run Report Timing Closure Recommendations in the Timing Analyzer." 0 0 "Design Software" 0 -1 1754072283203 ""}  } {  } 1 332148 "Timing requirements not met" 0 0 "Timing Analyzer" 0 -1 1754072283203 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "setup -4.841 " "Worst-case setup slack is -4.841" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -4.841           -1739.421 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -4.841           -1739.421 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.631            -472.807 FPGA_CS_NEL  " "   -3.631            -472.807 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.601            -292.785 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -2.601            -292.785 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.509             -42.286 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -1.509             -42.286 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.359             -38.356 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -1.359             -38.356 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.848             -12.894 AD2_INPUT_CLK  " "   -0.848             -12.894 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.695              -8.808 AD1_INPUT_CLK  " "   -0.695              -8.808 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283207 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072283207 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "hold -2.468 " "Worst-case hold slack is -2.468" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -2.468            -287.998 FPGA_CS_NEL  " "   -2.468            -287.998 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.261              -6.837 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -1.261              -6.837 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.151               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "    0.151               0.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.170               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "    0.170               0.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.185               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    0.185               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.208               0.000 AD1_INPUT_CLK  " "    0.208               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.208               0.000 AD2_INPUT_CLK  " "    0.208               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283218 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072283218 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "recovery -3.334 " "Worst-case recovery slack is -3.334" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283224 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283224 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.334            -206.553 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "   -3.334            -206.553 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283224 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.693             -16.960 AD1_INPUT_CLK  " "   -0.693             -16.960 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283224 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.361              -9.424 AD2_INPUT_CLK  " "   -0.361              -9.424 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283224 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -0.168              -1.014 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -0.168              -1.014 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283224 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072283224 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "removal 0.396 " "Worst-case removal slack is 0.396" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283231 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283231 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.396               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "    0.396               0.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283231 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.859               0.000 AD2_INPUT_CLK  " "    0.859               0.000 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283231 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    0.976               0.000 AD1_INPUT_CLK  " "    0.976               0.000 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283231 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    2.367               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    2.367               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283231 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072283231 ""}
{ "Info" "ISTA_WORST_CASE_SLACK" "minimum pulse width -3.000 " "Worst-case minimum pulse width slack is -3.000" { { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    Slack       End Point TNS Clock  " "    Slack       End Point TNS Clock " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "========= =================== ===================== " "========= =================== =====================" {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000            -281.797 FPGA_CS_NEL  " "   -3.000            -281.797 FPGA_CS_NEL " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -42.649 AD1_INPUT_CLK  " "   -3.000             -42.649 AD1_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -3.000             -42.132 AD2_INPUT_CLK  " "   -3.000             -42.132 AD2_INPUT_CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000            -272.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "   -1.000            -272.000 DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -65.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "   -1.000             -65.000 FREQ_DEV:u_AD1_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "   -1.000             -65.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "   -1.000             -65.000 FREQ_DEV:u_AD2_DEV\|FREQ_OUT " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    3.100               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\]  " "    3.100               0.000 inst6\|altpll_component\|auto_generated\|pll1\|clk\[0\] " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""} { "Info" "ISTA_CREATE_TIMING_SUMMARY_INFO" "    9.594               0.000 CLK  " "    9.594               0.000 CLK " {  } {  } 0 332119 "%1!s!" 0 0 "Design Software" 0 -1 1754072283236 ""}  } {  } 0 332146 "Worst-case %1!s! slack is %2!s!" 0 0 "Timing Analyzer" 0 -1 1754072283236 ""}
{ "Info" "ISTA_REPORT_METASTABILITY_INFO" "Report Metastability: Found 96 synchronizer chains. " "Report Metastability: Found 96 synchronizer chains." { { "Info" "ISTA_REPORT_METASTABILITY_INFO" "Design MTBF is not calculated because the design doesn't meet its timing requirements. " "Design MTBF is not calculated because the design doesn't meet its timing requirements." {  } {  } 0 332114 "%1!s!" 0 0 "Design Software" 0 -1 1754072283436 ""}  } {  } 0 332114 "%1!s!" 0 0 "Timing Analyzer" 0 -1 1754072283436 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "setup " "Design is not fully constrained for setup requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1754072283677 ""}
{ "Info" "ISTA_UCP_NOT_CONSTRAINED" "hold " "Design is not fully constrained for hold requirements" {  } {  } 0 332102 "Design is not fully constrained for %1!s! requirements" 0 0 "Timing Analyzer" 0 -1 1754072283679 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Timing Analyzer 0 s 5 s Quartus Prime " "Quartus Prime Timing Analyzer was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "4896 " "Peak virtual memory: 4896 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1754072283758 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Aug 02 02:18:03 2025 " "Processing ended: Sat Aug 02 02:18:03 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1754072283758 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:02 " "Elapsed time: 00:00:02" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1754072283758 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:02 " "Total CPU time (on all processors): 00:00:02" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1754072283758 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Timing Analyzer" 0 -1 1754072283758 ""}
