/**
 * @file key_app.c
 * @brief ����Ӧ�ô������� (�ع���)
 * @details
 * ���ļ�ʵ���˰���ɨ�����ص�Ӧ���߼���
 * ��ʹ��һ���������������������������ÿ���������������壨GPIO��
 * �����߼����ܣ��ص����������뿪�����Ӷ�����˴���Ŀ���չ�ԺͿ�ά���ԡ�
 *
 * <AUTHOR>
 * @date 2025-07-18
 * @version 2.0 (���ع�����߿���չ��)
 */
#include "key_app.h"
#include "ad_measure.h"
#include "my_fft.h"
#include "da_output.h"
#include "AD9959.h"

// *********************************************************************************
// 1. ���Ͷ���ͳ���
// *********************************************************************************

#define NUM_KEYS 4 // ���尴��������

// ����һ������ָ�����ͣ����ڰ����Ļص�����
typedef void (*key_callback_t)(void);

// ����һ���ṹ�壬���ڱ��浥��������������Ϣ
typedef struct {
    GPIO_TypeDef* port;     // �������ӵ�GPIO�˿�
    uint16_t       pin;      // �������ӵ�GPIO����
    key_callback_t callback; // ��������ʱҪ���õĻص�����
} key_info_t;

// *********************************************************************************
// 2. ��̬�ص�������ԭ������
// *********************************************************************************
static void key1_action_toggle_ask(void);
static void key2_action_toggle_fsk(void);
static void key3_action_cycle_phase(void);
static void key4_action_perform_fft(void);

// *********************************************************************************
// 3. ������������ <--- ������Ƶĺ��ģ�
// *********************************************************************************
static const key_info_t key_config[NUM_KEYS] = {
    {KEY1_GPIO_Port, KEY1_Pin, key1_action_toggle_ask}, // ����1 -> �л�ASK
    {KEY2_GPIO_Port, KEY2_Pin, key2_action_toggle_fsk}, // ����2 -> �л�FSK
    {KEY3_GPIO_Port, KEY3_Pin, key3_action_cycle_phase},// ����3 -> ������λ
    {KEY4_GPIO_Port, KEY4_Pin, key4_action_perform_fft} // ����4 -> ִ��FFT
    // ���Ҫ���ӵ�5��������ֻ���������һ�м��ɣ�
};

// *********************************************************************************
// 4. ȫ�ּ���̬����
// *********************************************************************************
// ʹ��λ����(bitmask)�����水��״̬������Ч���ܴ�����ϼ�
uint8_t key_map_now = 0;
uint8_t key_map_old = 0;
uint8_t key_map_down = 0;
// uint8_t key_map_up = 0; // �����Ҫ�����⣬����ȡ��ע��

static float current_ad_freq = 2000000.0f;

// *********************************************************************************
// 5. ����ʵ��
// *********************************************************************************

/**
 * @brief ��ȡ�������������ĵ�ǰ״̬��������һ��λ����(bitmask)
 * @details ����`key_config`���飬ɨ��ÿ���Ѷ���İ�����������������£��͵�ƽ����
 * ���ڷ���ֵ�Ķ�Ӧλ����1�����磬����0���£����� 0b0001������1���£����� 0b0010��
 * @return uint8_t �������а���״̬��λ���롣
 */
static uint8_t key_read_map(void)
{
    uint8_t map = 0;
    for (int i = 0; i < NUM_KEYS; i++) {
        if (HAL_GPIO_ReadPin(key_config[i].port, key_config[i].pin) == GPIO_PIN_RESET) {
            map |= (1 << i);
        }
    }
    return map;
}

/**
 * @brief �������������� (�ع���)
 * @details
 * 1. ����`key_read_map()`��ȡ���а���״̬��λ���롣
 * 2. ������Щ�����ոձ����¡�
 * 3. �������а����������⵽ĳ�����������£��͵�������`key_config`������ע��Ļص�������
 */
void key_proc(void)
{
    key_map_now = key_read_map();
    key_map_down = key_map_now & (key_map_old ^ key_map_now); // �������½��ؼ��
    // key_map_up = ~key_map_now & (key_map_old ^ key_map_now);
    key_map_old = key_map_now;

    if (key_map_down == 0) {
        return; // ���û���κΰ��������£�ֱ�ӷ���
    }

    // �������п��ܵİ���
    for (int i = 0; i < NUM_KEYS; i++) {
        // ����i�������Ƿ񱻰���
        if (key_map_down & (1 << i)) {
            // ����ǣ��������й����Ļص��������������
            if (key_config[i].callback != NULL) {
                key_config[i].callback();
            }
        }
    }
}

// --- �ⲿ�ɵ��ú��� ---
void set_current_ad_frequency(float freq)
{
    current_ad_freq = freq;
}

float get_current_ad_frequency(void)
{
    return current_ad_freq;
}


// *********************************************************************************
// 6. ��̬�ص������ľ���ʵ��
// *********************************************************************************

// 全局调节模式变量
typedef enum {
    // MODE_PHASE = 0,     // 相位调节模式
    MODE_FREQUENCY = 0,     // 频率调节模式
    MODE_AMPLITUDE,     // 幅值调节模式
    //MODE_WAVEFORM,      // 波形调节模式
		MODE_DISANTI,
    MODE_COUNT          // 模式总数
} adjust_mode_t;

adjust_mode_t current_mode = MODE_FREQUENCY;

/** @brief ����1�Ĺ��ܣ�����/ֹͣASK�������� */
static void key1_action_toggle_ask(void)
{
    // 切换到下一个模式
    current_mode = (adjust_mode_t)((current_mode + 1) % MODE_COUNT);

//    // 显示当前模式
//    const char* mode_names[] = {"PHASE", "FREQUENCY", "AMPLITUDE", "WAVEFORM"};
//    my_printf(&huart3, "Mode switched to: %s\r\n", mode_names[current_mode]);
}

uint8_t freq_step = 0;
uint8_t amp_step = 0;

/** @brief ����2�Ĺ��ܣ�����/ֹͣFSK�������� */
static void key2_action_toggle_fsk(void)
{
//    if (AD9959_Is_FSK_Active()) {
//        AD9959_Stop_FSK_Transmission();
//        my_printf(&huart3, "Button2: FSK transmission stopped\r\n");
//    } else {
//        AD9959_Start_FSK_Transmission();
//        my_printf(&huart3, "Button2: FSK transmission started\r\n");
//    }
	
	
}



/** @brief ����3�Ĺ��ܣ�ѭ������DA�����������ǿ�棩 */
static void key3_action_cycle_phase(void)
{
    // 使用全局的current_mode变量
//    static uint16_t phase_step = 0;
//    static uint8_t freq_step = 0;
//    static uint8_t amp_step = 0;
//    static uint8_t wave_step = 0;

    // Ƶ�ʵ��ڲ�����MODE_FREQUENCY case�ж���

    // Ԥ����ķ���ֵ
    static const uint16_t amp_values[] = {500, 1000, 1500, 2000, 2500};
    static const uint8_t amp_count = sizeof(amp_values) / sizeof(amp_values[0]);

    // ������������
    static const Waveform_t wave_types[] = {WAVE_SINE, WAVE_SQUARE, WAVE_TRIANGLE, WAVE_SAWTOOTH};
    static const char* wave_names[] = {"SINE", "SQUARE", "TRIANGLE", "SAWTOOTH"};
    static const uint8_t wave_count = sizeof(wave_types) / sizeof(wave_types[0]);

    switch (current_mode) {
//        case MODE_PHASE:
//            // ��λ���ڣ�ÿ������30��
//            phase_step = (phase_step + 1) % 12; // 12�� = 360��/30��
//            uint16_t new_phase = phase_step * 30;
//            DA_SetPhase(1, new_phase);
//            DA_Apply_Settings();
//            my_printf(&huart3, "DA1 Phase: %d degrees\r\n", new_phase);

//            // ����ص�0�ȣ��л�����һ��ģʽ
//            if (phase_step == 0) {
//                current_mode = MODE_FREQUENCY;
//                my_printf(&huart3, "Switching to FREQUENCY mode\r\n");
//            }
//            break;

        case MODE_FREQUENCY:
            // Ƶ�ʵ��ڣ���2kHz��ʼ��ÿ������100Hz
            #define FREQ_START 0.0f    // ��ʼƵ��2kHz
            #define FREQ_STEP 100.0f      // Ƶ�ʲ���100Hz
            #define FREQ_MAX 1000000.0f     // ���Ƶ��1000kHz
            #define FREQ_STEPS ((uint32_t)((FREQ_MAX - FREQ_START) / FREQ_STEP + 1))

            freq_step = (freq_step + 1) % FREQ_STEPS;
            float current_freq = FREQ_START + freq_step * FREQ_STEP;
            DA_SetFREQ(1, current_freq);
            DA_Apply_Settings();
            my_printf(&huart3, "DA1 Frequency: %.0f Hz\r\n", current_freq);

//            // ����ص���ʼƵ�ʣ��л�����һ��ģʽ
//            if (freq_step == 0) {
//                current_mode = MODE_AMPLITUDE;
//                my_printf(&huart3, "Switching to AMPLITUDE mode\r\n");
//            }
//            break;

        case MODE_AMPLITUDE:
            // ���ȵ��ڣ�ѭ��Ԥ����ķ���ֵ
            amp_step = (amp_step + 1) % amp_count;
            DA_SetAmp(1, amp_values[amp_step]);
            DA_Apply_Settings();
            my_printf(&huart3, "DA1 Amplitude: %d\r\n", amp_values[amp_step]);

            // ����ص���һ�����ȣ��л�����һ��ģʽ
//            if (amp_step == 0) {
//                current_mode = MODE_WAVEFORM;
//                my_printf(&huart3, "Switching to WAVEFORM mode\r\n");
//            }
//            break;

//        case MODE_WAVEFORM:
//            // ���ε��ڣ�ѭ�����в�������
//            wave_step = (wave_step + 1) % wave_count;
//            DA_SetWaveform(1, wave_types[wave_step]);
//            DA_Apply_Settings();
//            my_printf(&huart3, "DA1 Waveform: %s\r\n", wave_names[wave_step]);

//            // ����ص���һ�����Σ��л�����λģʽ
//            if (wave_step == 0) {
//                current_mode = MODE_PHASE;
//                my_printf(&huart3, "Switching to PHASE mode\r\n");
//            }
//            break;

					case MODE_DISANTI:
						// 为通道1设置参数: 10kHz, 幅度1000, 相位0°, 正弦波 
						DA_SetConfig(1, 1000.0f, 404, 0, WAVE_SINE);

						// 将上述默认配置写入FPGA硬件寄存器使其生效
						DA_Apply_Settings();
						

        default:
            current_mode = MODE_FREQUENCY;
            break;
    }
}

/** @brief ����4�Ĺ��ܣ���AD������1�����ݽ���FFT����� */
static void key4_action_perform_fft(void)
{
//    my_printf(&huart3, "���ڼ���FFTƵ��...\r\n");
//    calculate_fft_spectrum(fifo_data1_f, AD_FIFO_SIZE);
//    output_fft_spectrum();
	
}
