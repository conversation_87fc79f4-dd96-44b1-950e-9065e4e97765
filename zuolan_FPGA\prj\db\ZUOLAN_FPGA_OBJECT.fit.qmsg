{ "Info" "IQCU_PARALLEL_AUTODETECT_MULTIPLE_PROCESSORS" "14 14 " "Parallel compilation is enabled and will use 14 of the 14 processors detected" {  } {  } 0 20030 "Parallel compilation is enabled and will use %1!i! of the %2!i! processors detected" 0 0 "Fitter" 0 -1 1754072266376 ""}
{ "Info" "IMPP_MPP_USER_DEVICE" "ZUOLAN_FPGA_OBJECT EP4CE10F17C8 " "Selected device EP4CE10F17C8 for design \"ZUOLAN_FPGA_OBJECT\"" {  } {  } 0 119006 "Selected device %2!s! for design \"%1!s!\"" 0 0 "Fitter" 0 -1 1754072266387 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "Low junction temperature 0 degrees C " "Low junction temperature is 0 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1754072266425 ""}
{ "Info" "ICUT_CUT_USING_OPERATING_CONDITION" "High junction temperature 85 degrees C " "High junction temperature is 85 degrees C" {  } {  } 0 21077 "%1!s! is %2!s!" 0 0 "Fitter" 0 -1 1754072266425 ""}
{ "Info" "ICUT_CUT_PLL_COMPUTATION_SUCCESS" "MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|pll1 Cyclone IV E PLL " "Implemented PLL \"MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|pll1\" as Cyclone IV E PLL type" { { "Info" "ICUT_CUT_YGR_PLL_PARAMETERS_FACTORS" "MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] 3 1 0 0 " "Implementing clock multiplication of 3, clock division of 1, and phase shift of 0 degrees (0 ps) for MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] port" {  } { { "db/mypll_altpll1.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/mypll_altpll1.v" 43 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1396 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 15099 "Implementing clock multiplication of %2!d!, clock division of %3!d!, and phase shift of %4!d! degrees (%5!d! ps) for %1!s! port" 0 0 "Design Software" 0 -1 1754072266452 ""}  } { { "db/mypll_altpll1.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/mypll_altpll1.v" 43 -1 0 } } { "" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1396 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 15535 "Implemented %3!s! \"%1!s!\" as %2!s! PLL type" 0 0 "Fitter" 0 -1 1754072266452 ""}
{ "Info" "IFITCC_FITCC_INFO_AUTO_FIT_COMPILATION_ON" "" "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" {  } {  } 0 171003 "Fitter is performing an Auto Fit compilation, which may decrease Fitter effort to reduce compilation time" 0 0 "Fitter" 0 -1 1754072266497 ""}
{ "Warning" "WCPT_FEATURE_DISABLED_POST" "LogicLock " "Feature LogicLock is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." {  } {  } 0 292013 "Feature %1!s! is only available with a valid subscription license. You can purchase a software subscription to gain full access to this feature." 0 0 "Fitter" 0 -1 1754072266500 ""}
{ "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED" "" "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" { { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE6F17C8 " "Device EP4CE6F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1754072266568 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE15F17C8 " "Device EP4CE15F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1754072266568 ""} { "Info" "IFSAC_FSAC_MIGRATION_NOT_SELECTED_SUB" "EP4CE22F17C8 " "Device EP4CE22F17C8 is compatible" {  } {  } 2 176445 "Device %1!s! is compatible" 0 0 "Design Software" 0 -1 1754072266568 ""}  } {  } 2 176444 "Device migration not selected. If you intend to use device migration later, you may need to change the pin assignments as they may be incompatible with other devices" 0 0 "Fitter" 0 -1 1754072266568 ""}
{ "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION" "5 " "Fitter converted 5 user pins into dedicated programming pins" { { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_ASDO_DATA1~ C1 " "Pin ~ALTERA_ASDO_DATA1~ is reserved at location C1" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_ASDO_DATA1~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5555 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1754072266571 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_FLASH_nCE_nCSO~ D2 " "Pin ~ALTERA_FLASH_nCE_nCSO~ is reserved at location D2" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_FLASH_nCE_nCSO~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5557 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1754072266571 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DCLK~ H1 " "Pin ~ALTERA_DCLK~ is reserved at location H1" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DCLK~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5559 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1754072266571 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_DATA0~ H2 " "Pin ~ALTERA_DATA0~ is reserved at location H2" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_DATA0~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5561 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1754072266571 ""} { "Info" "IFIOMGR_RESERVED_PIN_WITH_LOCATION_SUB" "~ALTERA_nCEO~ F16 " "Pin ~ALTERA_nCEO~ is reserved at location F16" {  } { { "g:/altera/18.1/quartus/bin64/pin_planner.ppl" "" { PinPlanner "g:/altera/18.1/quartus/bin64/pin_planner.ppl" { ~ALTERA_nCEO~ } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5563 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 169125 "Pin %1!s! is reserved at location %2!s!" 0 0 "Design Software" 0 -1 1754072266571 ""}  } {  } 0 169124 "Fitter converted %1!d! user pins into dedicated programming pins" 0 0 "Fitter" 0 -1 1754072266571 ""}
{ "Warning" "WCUT_CUT_ATOM_PINS_WITH_INCOMPLETE_IO_ASSIGNMENTS" "" "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" {  } {  } 0 15714 "Some pins have incomplete I/O assignments. Refer to the I/O Assignment Warnings report for details" 0 0 "Fitter" 0 -1 1754072266572 ""}
{ "Info" "IFSAC_FSAC_RAM_METASTABILITY_INFO" "" "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." {  } {  } 0 176045 "Design uses memory blocks. Violating setup or hold times of memory block address registers for either read or write operations could cause memory contents to be corrupted. Make sure that all memory block address registers meet the setup and hold time requirements." 0 0 "Fitter" 0 -1 1754072266595 ""}
{ "Critical Warning" "WFIOMGR_PINS_MISSING_LOCATION_INFO" "15 80 " "No exact pin location assignment(s) for 15 pins of 80 total pins. For the list of pins please refer to the I/O Assignment Warnings table in the fitter report." {  } {  } 1 169085 "No exact pin location assignment(s) for %1!d! pins of %2!d! total pins. For the list of pins please refer to the I/O Assignment Warnings table in the fitter report." 0 0 "Fitter" 0 -1 1754072266778 ""}
{ "Warning" "WTDB_ANALYZE_COMB_LATCHES" "354 " "The Timing Analyzer is analyzing 354 combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." {  } {  } 0 335093 "The Timing Analyzer is analyzing %1!d! combinational loops as latches. For more details, run the Check Timing command in the Timing Analyzer or view the \"User-Specified and Inferred Latches\" table in the Analysis & Synthesis report." 0 0 "Fitter" 0 -1 1754072266945 ""}
{ "Info" "ISTA_SDC_STATEMENT_PARENT" "" "Evaluating HDL-embedded SDC commands" { { "Info" "ISTA_SDC_STATEMENT_ENTITY" "dcfifo_vve1 " "Entity dcfifo_vve1" { { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a*  " "set_false_path -from *rdptr_g* -to *ws_dgrp\|dffpipe_c09:dffpipe15\|dffe16a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1754072266946 ""} { "Info" "ISTA_SDC_STATEMENT_EVAL" "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a*  " "set_false_path -from *delayed_wrptr_g* -to *rs_dgwp\|dffpipe_b09:dffpipe12\|dffe13a* " {  } {  } 0 332166 "%1!s!" 0 0 "Design Software" 0 -1 1754072266946 ""}  } {  } 0 332165 "Entity %1!s!" 0 0 "Design Software" 0 -1 1754072266946 ""}  } {  } 0 332164 "Evaluating HDL-embedded SDC commands" 0 0 "Fitter" 0 -1 1754072266946 ""}
{ "Critical Warning" "WSTA_SDC_NOT_FOUND" "ZUOLAN_FPGA_OBJECT.sdc " "Synopsys Design Constraints File file not found: 'ZUOLAN_FPGA_OBJECT.sdc'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." {  } {  } 1 332012 "Synopsys Design Constraints File file not found: '%1!s!'. A Synopsys Design Constraints File is required by the Timing Analyzer to get proper timing constraints. Without it, the Compiler will not properly optimize the design." 0 0 "Fitter" 0 -1 1754072266949 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "generated clocks " "No user constrained generated clocks found in the design" {  } {  } 0 332144 "No user constrained %1!s! found in the design" 0 0 "Fitter" 0 -1 1754072266950 ""}
{ "Info" "ISTA_NO_CLOCK_FOUND_NO_DERIVING_MSG" "base clocks " "No user constrained base clocks found in the design" {  } {  } 0 332144 "No user constrained %1!s! found in the design" 0 0 "Fitter" 0 -1 1754072266950 ""}
{ "Info" "ISTA_NO_CLOCK_UNCERTAINTY_FOUND_DERIVING" "\"derive_clock_uncertainty\" " "No user constrained clock uncertainty found in the design. Calling \"derive_clock_uncertainty\"" {  } {  } 0 332143 "No user constrained clock uncertainty found in the design. Calling %1!s!" 0 0 "Fitter" 0 -1 1754072266961 ""}
{ "Info" "ISTA_DERIVE_CLOCK_UNCERTAINTY_INFO" "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties. " "Deriving Clock Uncertainty. Please refer to report_sdc in the Timing Analyzer to see clock uncertainties." {  } {  } 0 332123 "%1!s!" 0 0 "Fitter" 0 -1 1754072266962 ""}
{ "Info" "ISTA_TDC_NO_DEFAULT_OPTIMIZATION_GOALS" "" "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." {  } {  } 0 332130 "Timing requirements not specified -- quality metrics such as performance may be sacrificed to reduce compilation time." 0 0 "Fitter" 0 -1 1754072266963 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_1) " "Automatically promoted node MYPLL:inst6\|altpll:altpll_component\|MYPLL_altpll1:auto_generated\|wire_pll1_clk\[0\] (placed in counter C0 of PLL_1)" { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock CLKCTRL_G3 " "Automatically promoted destinations to use location or clock signal Global Clock CLKCTRL_G3" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267028 ""}  } { { "db/mypll_altpll1.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/db/mypll_altpll1.v" 77 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1396 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267028 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A  " "Automatically promoted node DA_PARAMETER_CTRL:inst7\|FREQ_OUT_A " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL " "Destination node DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL" {  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 60 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1586 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA1_OUTCLK~output " "Destination node DA1_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 920 5160 5349 936 "DA1_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5469 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1754072267028 ""}  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 69 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1576 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267028 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL  " "Automatically promoted node DA_PARAMETER_CTRL:inst7\|FREQ_OUT_B_FINAL " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "DA2_OUTCLK~output " "Destination node DA2_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 976 5160 5349 992 "DA2_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5470 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1754072267028 ""}  } { { "../src/DA_PARAMETER_CTRL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/DA_PARAMETER_CTRL.v" 60 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1586 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267028 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FMC_CONTROL:inst\|fmc_rd_en  " "Automatically promoted node FMC_CONTROL:inst\|fmc_rd_en " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[0\]~output " "Destination node FPGA_DB\[0\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3830 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[1\]~output " "Destination node FPGA_DB\[1\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3829 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[2\]~output " "Destination node FPGA_DB\[2\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3828 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[3\]~output " "Destination node FPGA_DB\[3\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3827 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[4\]~output " "Destination node FPGA_DB\[4\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3826 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[5\]~output " "Destination node FPGA_DB\[5\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3825 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[6\]~output " "Destination node FPGA_DB\[6\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3824 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[7\]~output " "Destination node FPGA_DB\[7\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3823 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[8\]~output " "Destination node FPGA_DB\[8\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3822 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "FPGA_DB\[9\]~output " "Destination node FPGA_DB\[9\]~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1632 1584 1784 1648 "FPGA_DB\[15..0\]" "" } { 1472 2536 2612 1496 "FPGA_DB\[15..0\]" "" } { 1616 1784 1860 1640 "FPGA_DB\[15..0\]" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3821 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267028 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_LIMITED_TO_SUB" "10 " "Non-global destination nodes limited to 10 nodes" {  } {  } 0 176358 "Non-global destination nodes limited to %1!d! nodes" 0 0 "Design Software" 0 -1 1754072267028 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1754072267028 ""}  } { { "../src/FMC_CONTROL.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FMC_CONTROL.v" 69 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1347 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267028 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_AD1_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_AD1_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267029 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "AD1_OUTCLK~output " "Destination node AD1_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1512 3816 4005 1528 "AD1_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5471 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267029 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1754072267029 ""}  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FREQ_DEV.v" 20 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 677 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267029 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "FREQ_DEV:u_AD2_DEV\|FREQ_OUT  " "Automatically promoted node FREQ_DEV:u_AD2_DEV\|FREQ_OUT " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267029 ""} { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS" "" "Following destination nodes may be non-global or may not use global or regional clocks" { { "Info" "IFSAC_FSAC_GLOBAL_UNASSIGNED_FANOUTS_SUB" "AD2_OUTCLK~output " "Destination node AD2_OUTCLK~output" {  } { { "TOP.bdf" "" { Schematic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/TOP.bdf" { { 1512 4272 4456 1528 "AD2_OUTCLK" "" } } } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 5472 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176357 "Destination node %1!s!" 0 0 "Design Software" 0 -1 1754072267029 ""}  } {  } 0 176356 "Following destination nodes may be non-global or may not use global or regional clocks" 0 0 "Design Software" 0 -1 1754072267029 ""}  } { { "../src/FREQ_DEV.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/FREQ_DEV.v" 20 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 1859 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267029 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_H\[15\]~0  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_H\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267029 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3038 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267029 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_L\[15\]~4  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD1_FREQ_DATA_L\[15\]~4 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267029 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3799 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267029 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD2_FREQ_DATA_H\[15\]~2  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD2_FREQ_DATA_H\[15\]~2 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267029 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3797 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267029 ""}
{ "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL" "AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD2_FREQ_DATA_L\[15\]~0  " "Automatically promoted node AD_FREQ_MEASURE:u_AD_FREQ_MEASURE\|AD2_FREQ_DATA_L\[15\]~0 " { { "Info" "IFSAC_FSAC_ASSIGN_AUTO_GLOBAL_TO_SIGNAL_FANOUTS" "destinations Global Clock " "Automatically promoted destinations to use location or clock signal Global Clock" {  } {  } 0 176355 "Automatically promoted %1!s! to use location or clock signal %2!s!" 0 0 "Design Software" 0 -1 1754072267029 ""}  } { { "../src/AD_FREQ_MEASURE.v" "" { Text "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/src/AD_FREQ_MEASURE.v" 49 -1 0 } } { "temporary_test_loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 0 { 0 ""} 0 3039 14177 15141 0 0 "" 0 "" "" }  }  } }  } 0 176353 "Automatically promoted node %1!s! %2!s!" 0 0 "Fitter" 0 -1 1754072267029 ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_START_REGPACKING_INFO" "" "Starting register packing" {  } {  } 0 176233 "Starting register packing" 0 0 "Fitter" 0 -1 1754072267211 ""}
{ "Extra Info" "IFSAC_FSAC_START_REG_LOCATION_PROCESSING" "" "Performing register packing on registers with non-logic cell location assignments" {  } {  } 1 176273 "Performing register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1754072267212 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_REG_LOCATION_PROCESSING" "" "Completed register packing on registers with non-logic cell location assignments" {  } {  } 1 176274 "Completed register packing on registers with non-logic cell location assignments" 1 0 "Fitter" 0 -1 1754072267212 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_BEGIN_FAST_REGISTER_INFO" "" "Started Fast Input/Output/OE register processing" {  } {  } 1 176236 "Started Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1754072267214 ""}
{ "Extra Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_FAST_REGISTER_INFO" "" "Finished Fast Input/Output/OE register processing" {  } {  } 1 176237 "Finished Fast Input/Output/OE register processing" 1 0 "Fitter" 0 -1 1754072267216 ""}
{ "Extra Info" "IFSAC_FSAC_START_IO_MULT_RAM_PACKING" "" "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" {  } {  } 1 176248 "Moving registers into I/O cells, Multiplier Blocks, and RAM blocks to improve timing and density" 1 0 "Fitter" 0 -1 1754072267218 ""}
{ "Extra Info" "IFSAC_FSAC_FINISH_IO_MULT_RAM_PACKING" "" "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" {  } {  } 1 176249 "Finished moving registers into I/O cells, Multiplier Blocks, and RAM blocks" 1 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_REGISTER_PACKING_FINISH_REGPACKING_INFO" "" "Finished register packing" { { "Extra Info" "IFSAC_NUM_REGISTERS_PACKED_INTO_ATOM_TYPE" "76 Embedded multiplier block " "Packed 76 registers into blocks of type Embedded multiplier block" {  } {  } 1 176218 "Packed %1!d! registers into blocks of type %2!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176235 "Finished register packing" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_IO_BANK_PIN_GROUP_STATISTICS" "I/O pins that need to be placed that use the same VCCIO and VREF, before I/O pin placement " "Statistics of I/O pins that need to be placed that use the same VCCIO and VREF, before I/O pin placement" { { "Info" "IFSAC_FSAC_SINGLE_IOC_GROUP_STATISTICS" "15 unused 2.5V 0 15 0 " "Number of I/O pins in group: 15 (unused VREF, 2.5V VCCIO, 0 input, 15 output, 0 bidirectional)" { { "Info" "IFSAC_FSAC_IO_STDS_IN_IOC_GROUP" "2.5 V. " "I/O standards used: 2.5 V." {  } {  } 0 176212 "I/O standards used: %1!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176211 "Number of I/O pins in group: %1!d! (%2!s! VREF, %3!s! VCCIO, %4!d! input, %5!d! output, %6!d! bidirectional)" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176214 "Statistics of %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFSAC_FSAC_IO_STATS_BEFORE_AFTER_PLACEMENT" "before " "I/O bank details before I/O pin placement" { { "Info" "IFSAC_FSAC_IO_BANK_PIN_GROUP_STATISTICS" "I/O banks " "Statistics of I/O banks" { { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "1 does not use undetermined 8 9 " "I/O bank number 1 does not use VREF pins and has undetermined VCCIO pins. 8 total pin(s) used --  9 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "2 does not use 2.5V 8 11 " "I/O bank number 2 does not use VREF pins and has 2.5V VCCIO pins. 8 total pin(s) used --  11 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "3 does not use 2.5V 9 17 " "I/O bank number 3 does not use VREF pins and has 2.5V VCCIO pins. 9 total pin(s) used --  17 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "4 does not use undetermined 0 27 " "I/O bank number 4 does not use VREF pins and has undetermined VCCIO pins. 0 total pin(s) used --  27 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "5 does not use 2.5V 21 4 " "I/O bank number 5 does not use VREF pins and has 2.5V VCCIO pins. 21 total pin(s) used --  4 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "6 does not use 2.5V 9 5 " "I/O bank number 6 does not use VREF pins and has 2.5V VCCIO pins. 9 total pin(s) used --  5 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "7 does not use 2.5V 9 17 " "I/O bank number 7 does not use VREF pins and has 2.5V VCCIO pins. 9 total pin(s) used --  17 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""} { "Info" "IFSAC_FSAC_SINGLE_IO_BANK_STATISTICS" "8 does not use 2.5V 6 20 " "I/O bank number 8 does not use VREF pins and has 2.5V VCCIO pins. 6 total pin(s) used --  20 pins available" {  } {  } 0 176213 "I/O bank number %1!s! %2!s! VREF pins and has %3!s! VCCIO pins. %4!d! total pin(s) used --  %5!d! pins available" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176214 "Statistics of %1!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 176215 "I/O bank details %1!s! I/O pin placement" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITCC_FITTER_PREPARATION_END" "00:00:01 " "Fitter preparation operations ending: elapsed time is 00:00:01" {  } {  } 0 171121 "Fitter preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_FAMILY_APL_ERROR" "" "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." {  } {  } 0 14896 "Fitter has disabled Advanced Physical Optimization because it is not supported for the current family." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_START" "" "Fitter placement preparation operations beginning" {  } {  } 0 170189 "Fitter placement preparation operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_PREP_END" "00:00:00 " "Fitter placement preparation operations ending: elapsed time is 00:00:00" {  } {  } 0 170190 "Fitter placement preparation operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1754072267826 ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_START" "" "Fitter placement operations beginning" {  } {  } 0 170191 "Fitter placement operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_INFO_VPR_PLACEMENT_FINISH" "" "Fitter placement was successful" {  } {  } 0 170137 "Fitter placement was successful" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_PLACEMENT_END" "00:00:04 " "Fitter placement operations ending: elapsed time is 00:00:04" {  } {  } 0 170192 "Fitter placement operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_START" "" "Fitter routing operations beginning" {  } {  } 0 170193 "Fitter routing operations beginning" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_STATUS_DELAY_ADDED_FOR_HOLD" "2e+03 ns 7.0% " "2e+03 ns of routing delay (approximately 7.0% of available device routing delay) has been added to meet hold timing. For more information, refer to the Estimated Delay Added for Hold Timing section in the Fitter report." {  } {  } 0 170089 "%1!s! of routing delay (approximately %2!s! of available device routing delay) has been added to meet hold timing. For more information, refer to the Estimated Delay Added for Hold Timing section in the Fitter report." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_PERCENT_ROUTING_RESOURCE_USAGE" "16 " "Router estimated average interconnect usage is 16% of the available device resources" { { "Info" "IFITAPI_FITAPI_VPR_PEAK_ROUTING_REGION" "28 X0_Y0 X10_Y11 " "Router estimated peak interconnect usage is 28% of the available device resources in the region that extends from location X0_Y0 to location X10_Y11" {  } { { "loc" "" { Generic "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/" { { 1 { 0 "Router estimated peak interconnect usage is 28% of the available device resources in the region that extends from location X0_Y0 to location X10_Y11"} { { 12 { 0 ""} 0 0 11 12 }  }  }  }  } }  } 0 170196 "Router estimated peak interconnect usage is %1!d!%% of the available device resources in the region that extends from location %2!s! to location %3!s!" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170195 "Router estimated average interconnect usage is %1!d!%% of the available device resources" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_STATUS_ROUTER_HOLD_BACKOFF_ENGAGED" "" "Design requires adding a large amount of routing delay for some signals to meet hold time requirements, and there is an excessive demand for the available routing resources. The Fitter is reducing the routing delays of some signals to help the routing algorithm converge, but doing so may cause hold time failures. For more information, refer to the \"Estimated Delay Added for Hold Timing\" section in the Fitter report." {  } {  } 0 188005 "Design requires adding a large amount of routing delay for some signals to meet hold time requirements, and there is an excessive demand for the available routing resources. The Fitter is reducing the routing delays of some signals to help the routing algorithm converge, but doing so may cause hold time failures. For more information, refer to the \"Estimated Delay Added for Hold Timing\" section in the Fitter report." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED" "" "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." { { "Info" "IFITAPI_FITAPI_VPR_AUTO_FIT_ENABLED_AND_USED_FOR_ROUTABILITY" "" "Optimizations that may affect the design's routability were skipped" {  } {  } 0 170201 "Optimizations that may affect the design's routability were skipped" 0 0 "Design Software" 0 -1 ************* ""}  } {  } 0 170199 "The Fitter performed an Auto Fit compilation.  Optimizations were skipped to reduce compilation time." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IFITAPI_FITAPI_VPR_FITTER_ROUTING_END" "00:00:06 " "Fitter routing operations ending: elapsed time is 00:00:06" {  } {  } 0 170194 "Fitter routing operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "IVPR20K_VPR_TIMING_ANALYSIS_TIME" "the Fitter 1.36 " "Total time spent on timing analysis during the Fitter is 1.36 seconds." {  } {  } 0 11888 "Total time spent on timing analysis during %1!s! is %2!s! seconds." 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_STARTED" "" "Started post-fitting delay annotation" {  } {  } 0 334003 "Started post-fitting delay annotation" 0 0 "Fitter" 0 -1 ************* ""}
{ "Info" "ITAPI_TAPI_COMPLETED" "" "Delay annotation completed successfully" {  } {  } 0 334004 "Delay annotation completed successfully" 0 0 "Fitter" 0 -1 1754072278835 ""}
{ "Info" "IFITCC_FITTER_POST_OPERATION_END" "00:00:01 " "Fitter post-fit operations ending: elapsed time is 00:00:01" {  } {  } 0 11218 "Fitter post-fit operations ending: elapsed time is %1!s!" 0 0 "Fitter" 0 -1 1754072279316 ""}
{ "Info" "IRDB_WROTE_SUPPRESSED_MSGS" "C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.fit.smsg " "Generated suppressed messages file C:/Users/<USER>/Desktop/2025elec/zuolan_FPGA/prj/ZUOLAN_FPGA_OBJECT.fit.smsg" {  } {  } 0 144001 "Generated suppressed messages file %1!s!" 0 0 "Fitter" 0 -1 1754072279572 ""}
{ "Info" "IQEXE_ERROR_COUNT" "Fitter 0 s 5 s Quartus Prime " "Quartus Prime Fitter was successful. 0 errors, 5 warnings" { { "Info" "IQEXE_END_PEAK_VSIZE_MEMORY" "6351 " "Peak virtual memory: 6351 megabytes" {  } {  } 0 0 "Peak virtual memory: %1!s! megabytes" 0 0 "Design Software" 0 -1 1754072279883 ""} { "Info" "IQEXE_END_BANNER_TIME" "Sat Aug 02 02:17:59 2025 " "Processing ended: Sat Aug 02 02:17:59 2025" {  } {  } 0 0 "Processing ended: %1!s!" 0 0 "Design Software" 0 -1 1754072279883 ""} { "Info" "IQEXE_ELAPSED_TIME" "00:00:13 " "Elapsed time: 00:00:13" {  } {  } 0 0 "Elapsed time: %1!s!" 0 0 "Design Software" 0 -1 1754072279883 ""} { "Info" "IQEXE_ELAPSED_CPU_TIME" "00:00:33 " "Total CPU time (on all processors): 00:00:33" {  } {  } 0 0 "Total CPU time (on all processors): %1!s!" 0 0 "Design Software" 0 -1 1754072279883 ""}  } {  } 0 0 "%6!s! %1!s! was successful. %2!d! error%3!s!, %4!d! warning%5!s!" 0 0 "Fitter" 0 -1 1754072279883 ""}
