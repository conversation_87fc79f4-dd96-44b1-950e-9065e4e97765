/**
 * @file freq_measure.h
 * @brief FPGA频率测量驱动程序的头文件
 * @details
 * 该文件为频率测量模块（freq_measure.c）提供了公共接口。
 * 它声明了外部可以访问的全局变量（用于存储最终频率结果）和
 * 控制函数（用于启动各个通道的测量以及周期性处理）。
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
#ifndef __FREQ_MEASURE_H__
#define __FREQ_MEASURE_H__

#include "commond_init.h"
#include "cmd_to_fun.h"
#include "bsp_system.h"

// *********************************************************************************
// 1. 外部变量声明
// *********************************************************************************

/**
 * @brief 外部可访问的频率测量结果
 * @details
 * freq1: 存储通道1测量到的频率值，单位Hz。
 * freq2: 存储通道2测量到的频率值，单位Hz。
 * 这两个变量在 freq_measure.c 文件中定义和更新。
 */
extern float freq1, freq2;

// *********************************************************************************
// 2. 外部函数声明
// *********************************************************************************

/**
 * @brief 启动一次对AD1通道的频率测量。
 */
void fre_measure_ad1(void);

/**
 * @brief 启动一次对AD2通道的频率测量。
 */
void fre_measure_ad2(void);

/**
 * @brief 频率测量的周期性处理函数，会依次更新两个通道的频率值。
 */
void freq_proc(void);

#endif // __FREQ_MEASURE_H__
