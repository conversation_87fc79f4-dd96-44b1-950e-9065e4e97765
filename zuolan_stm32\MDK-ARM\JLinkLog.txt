T0818 000:004.050   SEGGER J-Link V8.16 Log File
T0818 000:004.200   DLL Compiled: Feb 26 2025 12:07:26
T0818 000:004.204   Logging started @ 2025-08-01 17:03
T0818 000:004.212   Process: G:\keil\keil arm\UV4\UV4.exe
T0818 000:004.223 - 4.215ms 
T0818 000:004.231 JLINK_SetWarnOutHandler(...)
T0818 000:004.236 - 0.006ms 
T0818 000:004.268 JLINK_OpenEx(...)
T0818 000:008.297   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0818 000:009.696   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0818 000:009.832   Decompressing FW timestamp took 87 us
T0818 000:017.377   Hardware: V9.60
T0818 000:017.394   S/N: 69655018
T0818 000:017.398   OEM: SEGGER
T0818 000:017.404   Feature(s): R<PERSON>, G<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>B<PERSON>, JFlash
T0818 000:018.675   Bootloader: (FW returned invalid version)
T0818 000:020.137   TELNET listener socket opened on port 19021
T0818 000:020.213   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T0818 000:020.349   WEBSRV Webserver running on local port 19080
T0818 000:020.418   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
T0818 000:020.505   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
T0818 000:329.456   Failed to connect to J-Link GUI Server.
T0818 000:329.484 - 325.210ms returns "O.K."
T0818 000:329.497 JLINK_GetEmuCaps()
T0818 000:329.503 - 0.004ms returns 0xB9FF7BBF
T0818 000:329.510 JLINK_TIF_GetAvailable(...)
T0818 000:329.878 - 0.370ms 
T0818 000:329.902 JLINK_SetErrorOutHandler(...)
T0818 000:329.905 - 0.003ms 
T0818 000:329.925 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025elec\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
T0818 000:340.068 - 10.145ms returns 0x00
T0818 000:342.109 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
T0818 000:343.155   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T0818 000:343.167     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T0818 000:347.450   Device "STM32F429IG" selected.
T0818 000:347.668 - 5.545ms returns 0x00
T0818 000:347.678 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T0818 000:347.691   ERROR: Unknown command
T0818 000:347.697 - 0.013ms returns 0x01
T0818 000:347.702 JLINK_GetHardwareVersion()
T0818 000:347.706 - 0.004ms returns 96000
T0818 000:347.710 JLINK_GetDLLVersion()
T0818 000:347.714 - 0.003ms returns 81600
T0818 000:347.718 JLINK_GetOEMString(...)
T0818 000:347.722 JLINK_GetFirmwareString(...)
T0818 000:347.725 - 0.003ms 
T0818 000:351.016 JLINK_GetDLLVersion()
T0818 000:351.028 - 0.012ms returns 81600
T0818 000:351.033 JLINK_GetCompileDateTime()
T0818 000:351.037 - 0.003ms 
T0818 000:352.086 JLINK_GetFirmwareString(...)
T0818 000:352.096 - 0.010ms 
T0818 000:353.215 JLINK_GetHardwareVersion()
T0818 000:353.227 - 0.011ms returns 96000
T0818 000:354.314 JLINK_GetSN()
T0818 000:354.325 - 0.011ms returns 69655018
T0818 000:355.367 JLINK_GetOEMString(...)
T0818 000:357.489 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T0818 000:358.979 - 1.491ms returns 0x00
T0818 000:358.986 JLINK_HasError()
T0818 000:358.996 JLINK_SetSpeed(5000)
T0818 000:359.311 - 0.315ms 
T0818 000:359.317 JLINK_GetId()
T0818 000:361.499   InitTarget() start
T0818 000:361.521    J-Link Script File: Executing InitTarget()
T0818 000:363.037   SWD selected. Executing JTAG -> SWD switching sequence.
T0818 000:367.040   DAP initialized successfully.
T0818 000:378.792   InitTarget() end - Took 16.1ms
T0818 000:380.914   Found SW-DP with ID 0x2BA01477
T0818 000:385.398   DPIDR: 0x2BA01477
T0818 000:386.771   CoreSight SoC-400 or earlier
T0818 000:387.862   Scanning AP map to find all available APs
T0818 000:389.792   AP[1]: Stopped AP scan as end of AP map has been reached
T0818 000:390.847   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
T0818 000:392.257   Iterating through AP map to find AHB-AP to use
T0818 000:394.665   AP[0]: Core found
T0818 000:395.791   AP[0]: AHB-AP ROM base: 0xE00FF000
T0818 000:397.493   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T0818 000:398.630   Found Cortex-M4 r0p1, Little endian.
T0818 000:399.387   -- Max. mem block: 0x00010C40
T0818 000:400.123   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:400.620   CPU_ReadMem(4 bytes @ 0x********)
T0818 000:402.264   FPUnit: 6 code (BP) slots and 2 literal slots
T0818 000:402.279   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T0818 000:402.692   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:403.181   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:403.661   CPU_WriteMem(4 bytes @ 0xE0001000)
T0818 000:404.140   CPU_ReadMem(4 bytes @ 0xE000ED88)
T0818 000:404.593   CPU_WriteMem(4 bytes @ 0xE000ED88)
T0818 000:405.092   CPU_ReadMem(4 bytes @ 0xE000ED88)
T0818 000:405.549   CPU_WriteMem(4 bytes @ 0xE000ED88)
T0818 000:407.523   CoreSight components:
T0818 000:408.758   ROMTbl[0] @ E00FF000
T0818 000:408.773   CPU_ReadMem(64 bytes @ 0xE00FF000)
T0818 000:409.473   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T0818 000:411.468   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T0818 000:411.483   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T0818 000:413.273   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T0818 000:413.288   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T0818 000:414.947   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T0818 000:414.961   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T0818 000:416.676   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T0818 000:416.691   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T0818 000:418.343   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T0818 000:418.361   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T0818 000:420.033   [0][5]: ******** CID B105900D PID 000BB925 ETM
T0818 000:420.493 - 61.175ms returns 0x2BA01477
T0818 000:420.535 JLINK_GetDLLVersion()
T0818 000:420.540 - 0.004ms returns 81600
T0818 000:420.549 JLINK_CORE_GetFound()
T0818 000:420.552 - 0.004ms returns 0xE0000FF
T0818 000:420.558 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T0818 000:420.563   Value=0xE00FF000
T0818 000:420.568 - 0.010ms returns 0
T0818 000:422.121 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T0818 000:422.132   Value=0xE00FF000
T0818 000:422.137 - 0.016ms returns 0
T0818 000:422.142 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T0818 000:422.145   Value=0x********
T0818 000:422.150 - 0.008ms returns 0
T0818 000:422.155 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T0818 000:422.177   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T0818 000:422.856   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T0818 000:422.863 - 0.708ms returns 32 (0x20)
T0818 000:422.869 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T0818 000:422.872   Value=0x00000000
T0818 000:422.877 - 0.008ms returns 0
T0818 000:422.881 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T0818 000:422.885   Value=0x********
T0818 000:422.890 - 0.008ms returns 0
T0818 000:422.894 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T0818 000:422.897   Value=0x********
T0818 000:422.902 - 0.008ms returns 0
T0818 000:422.906 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T0818 000:422.909   Value=0xE0001000
T0818 000:422.914 - 0.008ms returns 0
T0818 000:422.918 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T0818 000:422.922   Value=0x********
T0818 000:422.926 - 0.008ms returns 0
T0818 000:422.930 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T0818 000:422.934   Value=0xE000E000
T0818 000:422.938 - 0.008ms returns 0
T0818 000:422.943 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T0818 000:422.946   Value=0xE000EDF0
T0818 000:422.951 - 0.008ms returns 0
T0818 000:422.955 JLINK_GetDebugInfo(0x01 = Unknown)
T0818 000:422.958   Value=0x00000001
T0818 000:422.963 - 0.008ms returns 0
T0818 000:422.967 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T0818 000:422.973   CPU_ReadMem(4 bytes @ 0xE000ED00)
T0818 000:423.424   Data:  41 C2 0F 41
T0818 000:423.430   Debug reg: CPUID
T0818 000:423.435 - 0.467ms returns 1 (0x1)
T0818 000:423.440 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T0818 000:423.447   Value=0x00000000
T0818 000:423.452 - 0.011ms returns 0
T0818 000:423.456 JLINK_HasError()
T0818 000:423.461 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T0818 000:423.465 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T0818 000:423.469 JLINK_Reset()
T0818 000:423.475   JLINK_GetResetTypeDesc
T0818 000:423.479   - 0.003ms 
T0818 000:424.778   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T0818 000:424.801   CPU is running
T0818 000:424.808   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T0818 000:425.337   CPU is running
T0818 000:425.349   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:426.948   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T0818 000:428.856   Reset: Reset device via AIRCR.SYSRESETREQ.
T0818 000:428.870   CPU is running
T0818 000:428.877   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T0818 000:483.079   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:483.588   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:486.417   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:492.458   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:495.306   CPU_WriteMem(4 bytes @ 0x********)
T0818 000:495.828   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T0818 000:496.282   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:496.832 - 73.362ms 
T0818 000:496.876 JLINK_Halt()
T0818 000:496.882 - 0.005ms returns 0x00
T0818 000:496.888 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T0818 000:496.897   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:497.365   Data:  03 00 03 00
T0818 000:497.371   Debug reg: DHCSR
T0818 000:497.376 - 0.487ms returns 1 (0x1)
T0818 000:497.381 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T0818 000:497.384   Debug reg: DHCSR
T0818 000:497.597   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T0818 000:498.081 - 0.700ms returns 0 (0x00000000)
T0818 000:498.087 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T0818 000:498.091   Debug reg: DEMCR
T0818 000:498.098   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:498.571 - 0.484ms returns 0 (0x00000000)
T0818 000:503.821 JLINK_GetHWStatus(...)
T0818 000:504.196 - 0.375ms returns 0
T0818 000:507.317 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T0818 000:507.329 - 0.012ms returns 0x06
T0818 000:507.334 JLINK_GetNumBPUnits(Type = 0xF0)
T0818 000:507.338 - 0.004ms returns 0x2000
T0818 000:507.342 JLINK_GetNumWPUnits()
T0818 000:507.346 - 0.003ms returns 4
T0818 000:510.412 JLINK_GetSpeed()
T0818 000:510.424 - 0.011ms returns 4000
T0818 000:512.416 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T0818 000:512.433   CPU_ReadMem(4 bytes @ 0xE000E004)
T0818 000:512.994   Data:  02 00 00 00
T0818 000:513.004 - 0.587ms returns 1 (0x1)
T0818 000:513.009 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T0818 000:513.016   CPU_ReadMem(4 bytes @ 0xE000E004)
T0818 000:513.556   Data:  02 00 00 00
T0818 000:513.569 - 0.559ms returns 1 (0x1)
T0818 000:513.576 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T0818 000:513.580   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T0818 000:513.590   CPU_WriteMem(28 bytes @ 0xE0001000)
T0818 000:514.175 - 0.599ms returns 0x1C
T0818 000:514.183 JLINK_Halt()
T0818 000:514.187 - 0.003ms returns 0x00
T0818 000:514.191 JLINK_IsHalted()
T0818 000:514.195 - 0.003ms returns TRUE
T0818 000:515.811 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 000:515.819   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 000:516.027   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 000:517.865 - 2.054ms returns 0x184
T0818 000:517.899 JLINK_HasError()
T0818 000:517.905 JLINK_WriteReg(R0, 0x08000000)
T0818 000:517.910 - 0.005ms returns 0
T0818 000:517.914 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 000:517.917 - 0.003ms returns 0
T0818 000:517.921 JLINK_WriteReg(R2, 0x00000001)
T0818 000:517.925 - 0.003ms returns 0
T0818 000:517.929 JLINK_WriteReg(R3, 0x00000000)
T0818 000:517.932 - 0.003ms returns 0
T0818 000:517.936 JLINK_WriteReg(R4, 0x00000000)
T0818 000:517.940 - 0.003ms returns 0
T0818 000:517.944 JLINK_WriteReg(R5, 0x00000000)
T0818 000:517.950 - 0.006ms returns 0
T0818 000:517.955 JLINK_WriteReg(R6, 0x00000000)
T0818 000:517.959 - 0.003ms returns 0
T0818 000:517.963 JLINK_WriteReg(R7, 0x00000000)
T0818 000:517.966 - 0.003ms returns 0
T0818 000:517.980 JLINK_WriteReg(R8, 0x00000000)
T0818 000:517.983 - 0.013ms returns 0
T0818 000:517.987 JLINK_WriteReg(R9, 0x20000180)
T0818 000:517.991 - 0.003ms returns 0
T0818 000:517.995 JLINK_WriteReg(R10, 0x00000000)
T0818 000:517.998 - 0.003ms returns 0
T0818 000:518.002 JLINK_WriteReg(R11, 0x00000000)
T0818 000:518.006 - 0.003ms returns 0
T0818 000:518.010 JLINK_WriteReg(R12, 0x00000000)
T0818 000:518.013 - 0.003ms returns 0
T0818 000:518.017 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:518.021 - 0.003ms returns 0
T0818 000:518.025 JLINK_WriteReg(R14, 0x20000001)
T0818 000:518.028 - 0.003ms returns 0
T0818 000:518.036 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 000:518.039 - 0.007ms returns 0
T0818 000:518.043 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:518.047 - 0.003ms returns 0
T0818 000:518.051 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:518.054 - 0.003ms returns 0
T0818 000:518.058 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:518.061 - 0.003ms returns 0
T0818 000:518.066 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:518.069 - 0.003ms returns 0
T0818 000:518.073 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:518.079   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:518.578 - 0.505ms returns 0x00000001
T0818 000:518.587 JLINK_Go()
T0818 000:518.594   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 000:519.096   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:519.616   CPU_WriteMem(4 bytes @ 0xE0002008)
T0818 000:519.624   CPU_WriteMem(4 bytes @ 0xE000200C)
T0818 000:519.629   CPU_WriteMem(4 bytes @ 0xE0002010)
T0818 000:519.634   CPU_WriteMem(4 bytes @ 0xE0002014)
T0818 000:519.639   CPU_WriteMem(4 bytes @ 0xE0002018)
T0818 000:519.644   CPU_WriteMem(4 bytes @ 0xE000201C)
T0818 000:520.886   CPU_WriteMem(4 bytes @ 0xE0001004)
T0818 000:524.794   Memory map 'after startup completion point' is active
T0818 000:524.807 - 6.219ms 
T0818 000:524.818 JLINK_IsHalted()
T0818 000:527.083   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:527.561 - 2.743ms returns TRUE
T0818 000:527.571 JLINK_ReadReg(R15 (PC))
T0818 000:527.575 - 0.004ms returns 0x20000000
T0818 000:527.603 JLINK_ClrBPEx(BPHandle = 0x00000001)
T0818 000:527.608 - 0.005ms returns 0x00
T0818 000:527.612 JLINK_ReadReg(R0)
T0818 000:527.616 - 0.003ms returns 0x00000000
T0818 000:527.770 JLINK_HasError()
T0818 000:527.777 JLINK_WriteReg(R0, 0x08000000)
T0818 000:527.781 - 0.004ms returns 0
T0818 000:527.785 JLINK_WriteReg(R1, 0x00004000)
T0818 000:527.788 - 0.003ms returns 0
T0818 000:527.792 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:527.796 - 0.003ms returns 0
T0818 000:527.800 JLINK_WriteReg(R3, 0x00000000)
T0818 000:527.803 - 0.003ms returns 0
T0818 000:527.807 JLINK_WriteReg(R4, 0x00000000)
T0818 000:527.810 - 0.003ms returns 0
T0818 000:527.815 JLINK_WriteReg(R5, 0x00000000)
T0818 000:527.818 - 0.003ms returns 0
T0818 000:527.822 JLINK_WriteReg(R6, 0x00000000)
T0818 000:527.825 - 0.003ms returns 0
T0818 000:527.829 JLINK_WriteReg(R7, 0x00000000)
T0818 000:527.833 - 0.003ms returns 0
T0818 000:527.837 JLINK_WriteReg(R8, 0x00000000)
T0818 000:527.840 - 0.003ms returns 0
T0818 000:527.844 JLINK_WriteReg(R9, 0x20000180)
T0818 000:527.848 - 0.003ms returns 0
T0818 000:527.852 JLINK_WriteReg(R10, 0x00000000)
T0818 000:527.855 - 0.003ms returns 0
T0818 000:527.859 JLINK_WriteReg(R11, 0x00000000)
T0818 000:527.862 - 0.003ms returns 0
T0818 000:527.867 JLINK_WriteReg(R12, 0x00000000)
T0818 000:527.870 - 0.003ms returns 0
T0818 000:527.874 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:527.878 - 0.003ms returns 0
T0818 000:527.882 JLINK_WriteReg(R14, 0x20000001)
T0818 000:527.885 - 0.003ms returns 0
T0818 000:527.889 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 000:527.892 - 0.003ms returns 0
T0818 000:527.896 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:527.900 - 0.003ms returns 0
T0818 000:527.937 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:527.942 - 0.004ms returns 0
T0818 000:527.946 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:527.949 - 0.003ms returns 0
T0818 000:527.953 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:527.957 - 0.003ms returns 0
T0818 000:527.961 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:527.965 - 0.004ms returns 0x00000002
T0818 000:527.969 JLINK_Go()
T0818 000:527.977   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:530.826 - 2.857ms 
T0818 000:530.833 JLINK_IsHalted()
T0818 000:533.463   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:533.959 - 3.125ms returns TRUE
T0818 000:533.966 JLINK_ReadReg(R15 (PC))
T0818 000:533.970 - 0.004ms returns 0x20000000
T0818 000:533.974 JLINK_ClrBPEx(BPHandle = 0x00000002)
T0818 000:533.978 - 0.003ms returns 0x00
T0818 000:533.982 JLINK_ReadReg(R0)
T0818 000:533.986 - 0.003ms returns 0x00000001
T0818 000:533.990 JLINK_HasError()
T0818 000:533.995 JLINK_WriteReg(R0, 0x08000000)
T0818 000:533.998 - 0.003ms returns 0
T0818 000:534.003 JLINK_WriteReg(R1, 0x00004000)
T0818 000:534.006 - 0.003ms returns 0
T0818 000:534.010 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:534.013 - 0.003ms returns 0
T0818 000:534.017 JLINK_WriteReg(R3, 0x00000000)
T0818 000:534.021 - 0.003ms returns 0
T0818 000:534.025 JLINK_WriteReg(R4, 0x00000000)
T0818 000:534.028 - 0.003ms returns 0
T0818 000:534.032 JLINK_WriteReg(R5, 0x00000000)
T0818 000:534.036 - 0.003ms returns 0
T0818 000:534.040 JLINK_WriteReg(R6, 0x00000000)
T0818 000:534.043 - 0.003ms returns 0
T0818 000:534.047 JLINK_WriteReg(R7, 0x00000000)
T0818 000:534.050 - 0.003ms returns 0
T0818 000:534.054 JLINK_WriteReg(R8, 0x00000000)
T0818 000:534.058 - 0.003ms returns 0
T0818 000:534.062 JLINK_WriteReg(R9, 0x20000180)
T0818 000:534.065 - 0.003ms returns 0
T0818 000:534.069 JLINK_WriteReg(R10, 0x00000000)
T0818 000:534.073 - 0.003ms returns 0
T0818 000:534.077 JLINK_WriteReg(R11, 0x00000000)
T0818 000:534.080 - 0.003ms returns 0
T0818 000:534.084 JLINK_WriteReg(R12, 0x00000000)
T0818 000:534.087 - 0.003ms returns 0
T0818 000:534.091 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:534.095 - 0.003ms returns 0
T0818 000:534.099 JLINK_WriteReg(R14, 0x20000001)
T0818 000:534.102 - 0.003ms returns 0
T0818 000:534.106 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 000:534.110 - 0.003ms returns 0
T0818 000:534.114 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:534.117 - 0.003ms returns 0
T0818 000:534.121 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:534.125 - 0.003ms returns 0
T0818 000:534.129 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:534.132 - 0.003ms returns 0
T0818 000:534.136 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:534.139 - 0.003ms returns 0
T0818 000:534.144 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:534.147 - 0.003ms returns 0x00000003
T0818 000:534.151 JLINK_Go()
T0818 000:534.158   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:536.911 - 2.759ms 
T0818 000:536.921 JLINK_IsHalted()
T0818 000:537.407 - 0.485ms returns FALSE
T0818 000:537.413 JLINK_HasError()
T0818 000:545.457 JLINK_IsHalted()
T0818 000:545.896 - 0.438ms returns FALSE
T0818 000:545.903 JLINK_HasError()
T0818 000:547.454 JLINK_IsHalted()
T0818 000:547.959 - 0.505ms returns FALSE
T0818 000:547.965 JLINK_HasError()
T0818 000:549.454 JLINK_IsHalted()
T0818 000:549.999 - 0.545ms returns FALSE
T0818 000:550.005 JLINK_HasError()
T0818 000:551.460 JLINK_IsHalted()
T0818 000:552.152 - 0.691ms returns FALSE
T0818 000:552.164 JLINK_HasError()
T0818 000:553.455 JLINK_IsHalted()
T0818 000:553.953 - 0.497ms returns FALSE
T0818 000:553.960 JLINK_HasError()
T0818 000:555.455 JLINK_IsHalted()
T0818 000:555.970 - 0.515ms returns FALSE
T0818 000:555.977 JLINK_HasError()
T0818 000:557.457 JLINK_IsHalted()
T0818 000:557.969 - 0.512ms returns FALSE
T0818 000:557.975 JLINK_HasError()
T0818 000:559.457 JLINK_IsHalted()
T0818 000:559.989 - 0.532ms returns FALSE
T0818 000:559.995 JLINK_HasError()
T0818 000:561.462 JLINK_IsHalted()
T0818 000:561.867 - 0.405ms returns FALSE
T0818 000:561.883 JLINK_HasError()
T0818 000:563.455 JLINK_IsHalted()
T0818 000:563.970 - 0.514ms returns FALSE
T0818 000:563.976 JLINK_HasError()
T0818 000:566.460 JLINK_IsHalted()
T0818 000:566.895 - 0.435ms returns FALSE
T0818 000:566.901 JLINK_HasError()
T0818 000:568.454 JLINK_IsHalted()
T0818 000:568.959 - 0.504ms returns FALSE
T0818 000:568.965 JLINK_HasError()
T0818 000:570.454 JLINK_IsHalted()
T0818 000:571.002 - 0.547ms returns FALSE
T0818 000:571.008 JLINK_HasError()
T0818 000:572.455 JLINK_IsHalted()
T0818 000:572.955 - 0.500ms returns FALSE
T0818 000:572.960 JLINK_HasError()
T0818 000:574.454 JLINK_IsHalted()
T0818 000:574.996 - 0.541ms returns FALSE
T0818 000:575.002 JLINK_HasError()
T0818 000:577.455 JLINK_IsHalted()
T0818 000:577.960 - 0.504ms returns FALSE
T0818 000:577.966 JLINK_HasError()
T0818 000:579.454 JLINK_IsHalted()
T0818 000:579.969 - 0.514ms returns FALSE
T0818 000:579.975 JLINK_HasError()
T0818 000:581.457 JLINK_IsHalted()
T0818 000:581.958 - 0.501ms returns FALSE
T0818 000:581.966 JLINK_HasError()
T0818 000:583.455 JLINK_IsHalted()
T0818 000:584.000 - 0.545ms returns FALSE
T0818 000:584.006 JLINK_HasError()
T0818 000:586.456 JLINK_IsHalted()
T0818 000:586.869 - 0.413ms returns FALSE
T0818 000:586.881 JLINK_HasError()
T0818 000:588.454 JLINK_IsHalted()
T0818 000:588.997 - 0.542ms returns FALSE
T0818 000:589.003 JLINK_HasError()
T0818 000:590.454 JLINK_IsHalted()
T0818 000:590.999 - 0.545ms returns FALSE
T0818 000:591.005 JLINK_HasError()
T0818 000:592.459 JLINK_IsHalted()
T0818 000:592.968 - 0.509ms returns FALSE
T0818 000:592.975 JLINK_HasError()
T0818 000:594.454 JLINK_IsHalted()
T0818 000:594.893 - 0.439ms returns FALSE
T0818 000:594.899 JLINK_HasError()
T0818 000:596.454 JLINK_IsHalted()
T0818 000:596.998 - 0.543ms returns FALSE
T0818 000:597.004 JLINK_HasError()
T0818 000:598.456 JLINK_IsHalted()
T0818 000:599.019 - 0.563ms returns FALSE
T0818 000:599.030 JLINK_HasError()
T0818 000:601.457 JLINK_IsHalted()
T0818 000:601.913 - 0.455ms returns FALSE
T0818 000:601.919 JLINK_HasError()
T0818 000:603.456 JLINK_IsHalted()
T0818 000:603.909 - 0.452ms returns FALSE
T0818 000:603.918 JLINK_HasError()
T0818 000:605.453 JLINK_IsHalted()
T0818 000:605.998 - 0.545ms returns FALSE
T0818 000:606.004 JLINK_HasError()
T0818 000:607.452 JLINK_IsHalted()
T0818 000:607.997 - 0.544ms returns FALSE
T0818 000:608.004 JLINK_HasError()
T0818 000:609.458 JLINK_IsHalted()
T0818 000:609.995 - 0.537ms returns FALSE
T0818 000:610.001 JLINK_HasError()
T0818 000:611.455 JLINK_IsHalted()
T0818 000:611.959 - 0.503ms returns FALSE
T0818 000:611.965 JLINK_HasError()
T0818 000:613.486 JLINK_IsHalted()
T0818 000:614.137 - 0.651ms returns FALSE
T0818 000:614.150 JLINK_HasError()
T0818 000:616.455 JLINK_IsHalted()
T0818 000:616.995 - 0.539ms returns FALSE
T0818 000:617.001 JLINK_HasError()
T0818 000:618.453 JLINK_IsHalted()
T0818 000:618.922 - 0.469ms returns FALSE
T0818 000:618.928 JLINK_HasError()
T0818 000:620.452 JLINK_IsHalted()
T0818 000:621.000 - 0.547ms returns FALSE
T0818 000:621.007 JLINK_HasError()
T0818 000:623.476 JLINK_IsHalted()
T0818 000:624.040 - 0.563ms returns FALSE
T0818 000:624.048 JLINK_HasError()
T0818 000:625.475 JLINK_IsHalted()
T0818 000:625.975 - 0.499ms returns FALSE
T0818 000:625.991 JLINK_HasError()
T0818 000:627.455 JLINK_IsHalted()
T0818 000:627.959 - 0.503ms returns FALSE
T0818 000:627.965 JLINK_HasError()
T0818 000:630.454 JLINK_IsHalted()
T0818 000:630.996 - 0.541ms returns FALSE
T0818 000:631.002 JLINK_HasError()
T0818 000:632.455 JLINK_IsHalted()
T0818 000:632.953 - 0.497ms returns FALSE
T0818 000:632.959 JLINK_HasError()
T0818 000:635.454 JLINK_IsHalted()
T0818 000:635.863 - 0.409ms returns FALSE
T0818 000:635.869 JLINK_HasError()
T0818 000:637.455 JLINK_IsHalted()
T0818 000:637.860 - 0.405ms returns FALSE
T0818 000:637.867 JLINK_HasError()
T0818 000:639.452 JLINK_IsHalted()
T0818 000:639.915 - 0.462ms returns FALSE
T0818 000:639.920 JLINK_HasError()
T0818 000:641.455 JLINK_IsHalted()
T0818 000:641.906 - 0.450ms returns FALSE
T0818 000:641.912 JLINK_HasError()
T0818 000:643.461 JLINK_IsHalted()
T0818 000:643.969 - 0.507ms returns FALSE
T0818 000:643.978 JLINK_HasError()
T0818 000:645.454 JLINK_IsHalted()
T0818 000:645.959 - 0.505ms returns FALSE
T0818 000:645.966 JLINK_HasError()
T0818 000:647.453 JLINK_IsHalted()
T0818 000:647.998 - 0.545ms returns FALSE
T0818 000:648.008 JLINK_HasError()
T0818 000:649.453 JLINK_IsHalted()
T0818 000:649.892 - 0.439ms returns FALSE
T0818 000:649.898 JLINK_HasError()
T0818 000:651.455 JLINK_IsHalted()
T0818 000:651.967 - 0.512ms returns FALSE
T0818 000:651.973 JLINK_HasError()
T0818 000:653.467 JLINK_IsHalted()
T0818 000:653.967 - 0.500ms returns FALSE
T0818 000:653.976 JLINK_HasError()
T0818 000:655.477 JLINK_IsHalted()
T0818 000:655.958 - 0.480ms returns FALSE
T0818 000:655.963 JLINK_HasError()
T0818 000:657.452 JLINK_IsHalted()
T0818 000:657.995 - 0.543ms returns FALSE
T0818 000:658.001 JLINK_HasError()
T0818 000:659.455 JLINK_IsHalted()
T0818 000:659.959 - 0.503ms returns FALSE
T0818 000:659.965 JLINK_HasError()
T0818 000:661.455 JLINK_IsHalted()
T0818 000:661.958 - 0.503ms returns FALSE
T0818 000:661.964 JLINK_HasError()
T0818 000:663.452 JLINK_IsHalted()
T0818 000:663.996 - 0.543ms returns FALSE
T0818 000:664.001 JLINK_HasError()
T0818 000:665.452 JLINK_IsHalted()
T0818 000:665.996 - 0.543ms returns FALSE
T0818 000:666.002 JLINK_HasError()
T0818 000:667.452 JLINK_IsHalted()
T0818 000:667.996 - 0.543ms returns FALSE
T0818 000:668.001 JLINK_HasError()
T0818 000:669.452 JLINK_IsHalted()
T0818 000:669.899 - 0.447ms returns FALSE
T0818 000:669.913 JLINK_HasError()
T0818 000:671.455 JLINK_IsHalted()
T0818 000:671.957 - 0.502ms returns FALSE
T0818 000:671.963 JLINK_HasError()
T0818 000:673.452 JLINK_IsHalted()
T0818 000:673.996 - 0.544ms returns FALSE
T0818 000:674.002 JLINK_HasError()
T0818 000:675.455 JLINK_IsHalted()
T0818 000:675.959 - 0.503ms returns FALSE
T0818 000:675.965 JLINK_HasError()
T0818 000:677.457 JLINK_IsHalted()
T0818 000:678.002 - 0.544ms returns FALSE
T0818 000:678.010 JLINK_HasError()
T0818 000:679.459 JLINK_IsHalted()
T0818 000:679.913 - 0.453ms returns FALSE
T0818 000:679.923 JLINK_HasError()
T0818 000:681.457 JLINK_IsHalted()
T0818 000:681.953 - 0.495ms returns FALSE
T0818 000:681.959 JLINK_HasError()
T0818 000:683.453 JLINK_IsHalted()
T0818 000:683.995 - 0.542ms returns FALSE
T0818 000:684.001 JLINK_HasError()
T0818 000:685.476 JLINK_IsHalted()
T0818 000:685.972 - 0.495ms returns FALSE
T0818 000:685.985 JLINK_HasError()
T0818 000:687.457 JLINK_IsHalted()
T0818 000:687.969 - 0.512ms returns FALSE
T0818 000:687.975 JLINK_HasError()
T0818 000:689.454 JLINK_IsHalted()
T0818 000:690.001 - 0.546ms returns FALSE
T0818 000:690.007 JLINK_HasError()
T0818 000:691.459 JLINK_IsHalted()
T0818 000:691.866 - 0.406ms returns FALSE
T0818 000:691.872 JLINK_HasError()
T0818 000:693.454 JLINK_IsHalted()
T0818 000:693.998 - 0.544ms returns FALSE
T0818 000:694.004 JLINK_HasError()
T0818 000:695.454 JLINK_IsHalted()
T0818 000:695.999 - 0.544ms returns FALSE
T0818 000:696.004 JLINK_HasError()
T0818 000:697.454 JLINK_IsHalted()
T0818 000:697.893 - 0.439ms returns FALSE
T0818 000:697.899 JLINK_HasError()
T0818 000:699.454 JLINK_IsHalted()
T0818 000:699.969 - 0.515ms returns FALSE
T0818 000:699.975 JLINK_HasError()
T0818 000:701.455 JLINK_IsHalted()
T0818 000:701.968 - 0.513ms returns FALSE
T0818 000:701.974 JLINK_HasError()
T0818 000:703.454 JLINK_IsHalted()
T0818 000:703.998 - 0.543ms returns FALSE
T0818 000:704.003 JLINK_HasError()
T0818 000:705.476 JLINK_IsHalted()
T0818 000:705.970 - 0.493ms returns FALSE
T0818 000:705.975 JLINK_HasError()
T0818 000:707.464 JLINK_IsHalted()
T0818 000:707.912 - 0.448ms returns FALSE
T0818 000:707.923 JLINK_HasError()
T0818 000:710.455 JLINK_IsHalted()
T0818 000:710.971 - 0.515ms returns FALSE
T0818 000:710.977 JLINK_HasError()
T0818 000:712.455 JLINK_IsHalted()
T0818 000:712.998 - 0.543ms returns FALSE
T0818 000:713.003 JLINK_HasError()
T0818 000:714.454 JLINK_IsHalted()
T0818 000:714.954 - 0.500ms returns FALSE
T0818 000:714.960 JLINK_HasError()
T0818 000:716.454 JLINK_IsHalted()
T0818 000:716.969 - 0.515ms returns FALSE
T0818 000:716.975 JLINK_HasError()
T0818 000:718.454 JLINK_IsHalted()
T0818 000:718.972 - 0.517ms returns FALSE
T0818 000:718.978 JLINK_HasError()
T0818 000:720.454 JLINK_IsHalted()
T0818 000:720.999 - 0.544ms returns FALSE
T0818 000:721.004 JLINK_HasError()
T0818 000:722.455 JLINK_IsHalted()
T0818 000:722.969 - 0.513ms returns FALSE
T0818 000:722.976 JLINK_HasError()
T0818 000:725.455 JLINK_IsHalted()
T0818 000:725.960 - 0.504ms returns FALSE
T0818 000:726.012 JLINK_HasError()
T0818 000:727.455 JLINK_IsHalted()
T0818 000:727.961 - 0.505ms returns FALSE
T0818 000:727.966 JLINK_HasError()
T0818 000:729.453 JLINK_IsHalted()
T0818 000:729.998 - 0.544ms returns FALSE
T0818 000:730.004 JLINK_HasError()
T0818 000:731.455 JLINK_IsHalted()
T0818 000:731.953 - 0.497ms returns FALSE
T0818 000:731.959 JLINK_HasError()
T0818 000:733.452 JLINK_IsHalted()
T0818 000:733.910 - 0.457ms returns FALSE
T0818 000:733.916 JLINK_HasError()
T0818 000:735.452 JLINK_IsHalted()
T0818 000:735.995 - 0.543ms returns FALSE
T0818 000:736.001 JLINK_HasError()
T0818 000:737.452 JLINK_IsHalted()
T0818 000:737.997 - 0.544ms returns FALSE
T0818 000:738.002 JLINK_HasError()
T0818 000:739.255 JLINK_IsHalted()
T0818 000:739.723 - 0.467ms returns FALSE
T0818 000:739.733 JLINK_HasError()
T0818 000:741.761 JLINK_IsHalted()
T0818 000:743.348 - 1.586ms returns FALSE
T0818 000:743.361 JLINK_HasError()
T0818 000:744.783 JLINK_IsHalted()
T0818 000:745.298 - 0.514ms returns FALSE
T0818 000:745.304 JLINK_HasError()
T0818 000:746.757 JLINK_IsHalted()
T0818 000:747.243 - 0.486ms returns FALSE
T0818 000:747.248 JLINK_HasError()
T0818 000:748.757 JLINK_IsHalted()
T0818 000:749.238 - 0.481ms returns FALSE
T0818 000:749.245 JLINK_HasError()
T0818 000:750.756 JLINK_IsHalted()
T0818 000:751.248 - 0.492ms returns FALSE
T0818 000:751.263 JLINK_HasError()
T0818 000:752.757 JLINK_IsHalted()
T0818 000:753.330 - 0.572ms returns FALSE
T0818 000:753.337 JLINK_HasError()
T0818 000:754.758 JLINK_IsHalted()
T0818 000:755.260 - 0.501ms returns FALSE
T0818 000:755.266 JLINK_HasError()
T0818 000:756.758 JLINK_IsHalted()
T0818 000:757.243 - 0.485ms returns FALSE
T0818 000:757.249 JLINK_HasError()
T0818 000:758.756 JLINK_IsHalted()
T0818 000:759.222 - 0.465ms returns FALSE
T0818 000:759.228 JLINK_HasError()
T0818 000:760.756 JLINK_IsHalted()
T0818 000:761.250 - 0.494ms returns FALSE
T0818 000:761.260 JLINK_HasError()
T0818 000:762.759 JLINK_IsHalted()
T0818 000:763.246 - 0.486ms returns FALSE
T0818 000:763.252 JLINK_HasError()
T0818 000:764.756 JLINK_IsHalted()
T0818 000:765.201 - 0.444ms returns FALSE
T0818 000:765.207 JLINK_HasError()
T0818 000:766.755 JLINK_IsHalted()
T0818 000:767.260 - 0.504ms returns FALSE
T0818 000:767.265 JLINK_HasError()
T0818 000:768.756 JLINK_IsHalted()
T0818 000:769.236 - 0.480ms returns FALSE
T0818 000:769.242 JLINK_HasError()
T0818 000:770.759 JLINK_IsHalted()
T0818 000:771.178 - 0.418ms returns FALSE
T0818 000:771.205 JLINK_HasError()
T0818 000:772.757 JLINK_IsHalted()
T0818 000:773.209 - 0.452ms returns FALSE
T0818 000:773.216 JLINK_HasError()
T0818 000:774.787 JLINK_IsHalted()
T0818 000:775.204 - 0.417ms returns FALSE
T0818 000:775.210 JLINK_HasError()
T0818 000:776.756 JLINK_IsHalted()
T0818 000:777.260 - 0.504ms returns FALSE
T0818 000:777.266 JLINK_HasError()
T0818 000:778.757 JLINK_IsHalted()
T0818 000:779.239 - 0.481ms returns FALSE
T0818 000:779.245 JLINK_HasError()
T0818 000:780.756 JLINK_IsHalted()
T0818 000:781.248 - 0.491ms returns FALSE
T0818 000:781.257 JLINK_HasError()
T0818 000:782.756 JLINK_IsHalted()
T0818 000:783.247 - 0.491ms returns FALSE
T0818 000:783.253 JLINK_HasError()
T0818 000:784.758 JLINK_IsHalted()
T0818 000:785.249 - 0.490ms returns FALSE
T0818 000:785.255 JLINK_HasError()
T0818 000:786.758 JLINK_IsHalted()
T0818 000:787.247 - 0.489ms returns FALSE
T0818 000:787.288 JLINK_HasError()
T0818 000:788.757 JLINK_IsHalted()
T0818 000:789.235 - 0.478ms returns FALSE
T0818 000:789.241 JLINK_HasError()
T0818 000:790.760 JLINK_IsHalted()
T0818 000:791.221 - 0.461ms returns FALSE
T0818 000:791.233 JLINK_HasError()
T0818 000:792.759 JLINK_IsHalted()
T0818 000:793.211 - 0.452ms returns FALSE
T0818 000:793.217 JLINK_HasError()
T0818 000:794.758 JLINK_IsHalted()
T0818 000:795.243 - 0.485ms returns FALSE
T0818 000:795.249 JLINK_HasError()
T0818 000:796.757 JLINK_IsHalted()
T0818 000:797.240 - 0.482ms returns FALSE
T0818 000:797.245 JLINK_HasError()
T0818 000:798.758 JLINK_IsHalted()
T0818 000:799.239 - 0.481ms returns FALSE
T0818 000:799.245 JLINK_HasError()
T0818 000:800.758 JLINK_IsHalted()
T0818 000:801.424 - 0.665ms returns FALSE
T0818 000:801.429 JLINK_HasError()
T0818 000:802.759 JLINK_IsHalted()
T0818 000:803.211 - 0.452ms returns FALSE
T0818 000:803.217 JLINK_HasError()
T0818 000:804.758 JLINK_IsHalted()
T0818 000:805.238 - 0.480ms returns FALSE
T0818 000:805.244 JLINK_HasError()
T0818 000:806.758 JLINK_IsHalted()
T0818 000:807.240 - 0.481ms returns FALSE
T0818 000:807.246 JLINK_HasError()
T0818 000:808.758 JLINK_IsHalted()
T0818 000:809.240 - 0.482ms returns FALSE
T0818 000:809.246 JLINK_HasError()
T0818 000:810.758 JLINK_IsHalted()
T0818 000:811.467 - 0.709ms returns FALSE
T0818 000:811.472 JLINK_HasError()
T0818 000:812.758 JLINK_IsHalted()
T0818 000:813.203 - 0.445ms returns FALSE
T0818 000:813.209 JLINK_HasError()
T0818 000:814.758 JLINK_IsHalted()
T0818 000:815.290 - 0.532ms returns FALSE
T0818 000:815.296 JLINK_HasError()
T0818 000:816.758 JLINK_IsHalted()
T0818 000:817.229 - 0.471ms returns FALSE
T0818 000:817.235 JLINK_HasError()
T0818 000:818.760 JLINK_IsHalted()
T0818 000:819.249 - 0.489ms returns FALSE
T0818 000:819.255 JLINK_HasError()
T0818 000:821.759 JLINK_IsHalted()
T0818 000:822.241 - 0.481ms returns FALSE
T0818 000:822.247 JLINK_HasError()
T0818 000:823.758 JLINK_IsHalted()
T0818 000:824.241 - 0.483ms returns FALSE
T0818 000:824.246 JLINK_HasError()
T0818 000:825.758 JLINK_IsHalted()
T0818 000:826.338 - 0.580ms returns FALSE
T0818 000:826.344 JLINK_HasError()
T0818 000:827.758 JLINK_IsHalted()
T0818 000:828.201 - 0.443ms returns FALSE
T0818 000:828.207 JLINK_HasError()
T0818 000:829.758 JLINK_IsHalted()
T0818 000:830.202 - 0.444ms returns FALSE
T0818 000:830.208 JLINK_HasError()
T0818 000:831.758 JLINK_IsHalted()
T0818 000:832.248 - 0.490ms returns FALSE
T0818 000:832.254 JLINK_HasError()
T0818 000:833.758 JLINK_IsHalted()
T0818 000:834.240 - 0.482ms returns FALSE
T0818 000:834.248 JLINK_HasError()
T0818 000:835.758 JLINK_IsHalted()
T0818 000:836.247 - 0.488ms returns FALSE
T0818 000:836.253 JLINK_HasError()
T0818 000:837.756 JLINK_IsHalted()
T0818 000:838.236 - 0.479ms returns FALSE
T0818 000:838.242 JLINK_HasError()
T0818 000:839.756 JLINK_IsHalted()
T0818 000:840.201 - 0.445ms returns FALSE
T0818 000:840.207 JLINK_HasError()
T0818 000:841.758 JLINK_IsHalted()
T0818 000:842.239 - 0.480ms returns FALSE
T0818 000:842.245 JLINK_HasError()
T0818 000:843.758 JLINK_IsHalted()
T0818 000:844.237 - 0.479ms returns FALSE
T0818 000:844.244 JLINK_HasError()
T0818 000:845.759 JLINK_IsHalted()
T0818 000:846.238 - 0.479ms returns FALSE
T0818 000:846.244 JLINK_HasError()
T0818 000:847.756 JLINK_IsHalted()
T0818 000:848.201 - 0.444ms returns FALSE
T0818 000:848.207 JLINK_HasError()
T0818 000:849.792 JLINK_IsHalted()
T0818 000:850.300 - 0.507ms returns FALSE
T0818 000:850.308 JLINK_HasError()
T0818 000:851.758 JLINK_IsHalted()
T0818 000:852.245 - 0.486ms returns FALSE
T0818 000:852.267 JLINK_HasError()
T0818 000:853.756 JLINK_IsHalted()
T0818 000:854.239 - 0.482ms returns FALSE
T0818 000:854.244 JLINK_HasError()
T0818 000:855.756 JLINK_IsHalted()
T0818 000:856.235 - 0.479ms returns FALSE
T0818 000:856.241 JLINK_HasError()
T0818 000:857.757 JLINK_IsHalted()
T0818 000:858.237 - 0.480ms returns FALSE
T0818 000:858.246 JLINK_HasError()
T0818 000:859.756 JLINK_IsHalted()
T0818 000:860.201 - 0.444ms returns FALSE
T0818 000:860.207 JLINK_HasError()
T0818 000:861.759 JLINK_IsHalted()
T0818 000:862.247 - 0.488ms returns FALSE
T0818 000:862.253 JLINK_HasError()
T0818 000:863.756 JLINK_IsHalted()
T0818 000:864.237 - 0.480ms returns FALSE
T0818 000:864.242 JLINK_HasError()
T0818 000:865.772 JLINK_IsHalted()
T0818 000:866.367 - 0.594ms returns FALSE
T0818 000:866.377 JLINK_HasError()
T0818 000:867.756 JLINK_IsHalted()
T0818 000:868.208 - 0.451ms returns FALSE
T0818 000:868.213 JLINK_HasError()
T0818 000:869.761 JLINK_IsHalted()
T0818 000:870.262 - 0.500ms returns FALSE
T0818 000:870.267 JLINK_HasError()
T0818 000:871.758 JLINK_IsHalted()
T0818 000:872.238 - 0.480ms returns FALSE
T0818 000:872.244 JLINK_HasError()
T0818 000:873.756 JLINK_IsHalted()
T0818 000:874.237 - 0.481ms returns FALSE
T0818 000:874.243 JLINK_HasError()
T0818 000:875.755 JLINK_IsHalted()
T0818 000:876.237 - 0.481ms returns FALSE
T0818 000:876.243 JLINK_HasError()
T0818 000:877.758 JLINK_IsHalted()
T0818 000:878.207 - 0.449ms returns FALSE
T0818 000:878.213 JLINK_HasError()
T0818 000:879.778 JLINK_IsHalted()
T0818 000:880.243 - 0.465ms returns FALSE
T0818 000:880.249 JLINK_HasError()
T0818 000:881.776 JLINK_IsHalted()
T0818 000:882.265 - 0.488ms returns FALSE
T0818 000:882.274 JLINK_HasError()
T0818 000:883.756 JLINK_IsHalted()
T0818 000:884.236 - 0.480ms returns FALSE
T0818 000:884.242 JLINK_HasError()
T0818 000:885.756 JLINK_IsHalted()
T0818 000:886.236 - 0.479ms returns FALSE
T0818 000:886.241 JLINK_HasError()
T0818 000:887.758 JLINK_IsHalted()
T0818 000:888.248 - 0.489ms returns FALSE
T0818 000:888.253 JLINK_HasError()
T0818 000:889.756 JLINK_IsHalted()
T0818 000:890.158 - 0.401ms returns FALSE
T0818 000:890.164 JLINK_HasError()
T0818 000:891.758 JLINK_IsHalted()
T0818 000:892.245 - 0.487ms returns FALSE
T0818 000:892.252 JLINK_HasError()
T0818 000:893.757 JLINK_IsHalted()
T0818 000:894.239 - 0.482ms returns FALSE
T0818 000:894.245 JLINK_HasError()
T0818 000:895.756 JLINK_IsHalted()
T0818 000:896.237 - 0.480ms returns FALSE
T0818 000:896.243 JLINK_HasError()
T0818 000:897.770 JLINK_IsHalted()
T0818 000:898.453 - 0.682ms returns FALSE
T0818 000:898.466 JLINK_HasError()
T0818 000:899.756 JLINK_IsHalted()
T0818 000:900.246 - 0.489ms returns FALSE
T0818 000:900.252 JLINK_HasError()
T0818 000:901.759 JLINK_IsHalted()
T0818 000:902.216 - 0.456ms returns FALSE
T0818 000:902.222 JLINK_HasError()
T0818 000:903.756 JLINK_IsHalted()
T0818 000:904.238 - 0.482ms returns FALSE
T0818 000:904.244 JLINK_HasError()
T0818 000:905.757 JLINK_IsHalted()
T0818 000:906.238 - 0.481ms returns FALSE
T0818 000:906.244 JLINK_HasError()
T0818 000:908.757 JLINK_IsHalted()
T0818 000:909.194 - 0.436ms returns FALSE
T0818 000:909.200 JLINK_HasError()
T0818 000:911.760 JLINK_IsHalted()
T0818 000:913.227 - 1.467ms returns FALSE
T0818 000:913.236 JLINK_HasError()
T0818 000:914.761 JLINK_IsHalted()
T0818 000:915.203 - 0.441ms returns FALSE
T0818 000:915.210 JLINK_HasError()
T0818 000:916.757 JLINK_IsHalted()
T0818 000:917.227 - 0.470ms returns FALSE
T0818 000:917.233 JLINK_HasError()
T0818 000:918.756 JLINK_IsHalted()
T0818 000:919.200 - 0.443ms returns FALSE
T0818 000:919.205 JLINK_HasError()
T0818 000:920.758 JLINK_IsHalted()
T0818 000:921.294 - 0.535ms returns FALSE
T0818 000:921.305 JLINK_HasError()
T0818 000:923.758 JLINK_IsHalted()
T0818 000:924.164 - 0.406ms returns FALSE
T0818 000:924.170 JLINK_HasError()
T0818 000:925.756 JLINK_IsHalted()
T0818 000:926.214 - 0.456ms returns FALSE
T0818 000:926.227 JLINK_HasError()
T0818 000:927.756 JLINK_IsHalted()
T0818 000:928.218 - 0.461ms returns FALSE
T0818 000:928.225 JLINK_HasError()
T0818 000:929.771 JLINK_IsHalted()
T0818 000:930.266 - 0.494ms returns FALSE
T0818 000:930.276 JLINK_HasError()
T0818 000:932.759 JLINK_IsHalted()
T0818 000:933.299 - 0.539ms returns FALSE
T0818 000:933.306 JLINK_HasError()
T0818 000:934.757 JLINK_IsHalted()
T0818 000:935.249 - 0.491ms returns FALSE
T0818 000:935.279 JLINK_HasError()
T0818 000:937.758 JLINK_IsHalted()
T0818 000:938.160 - 0.401ms returns FALSE
T0818 000:938.166 JLINK_HasError()
T0818 000:939.799 JLINK_IsHalted()
T0818 000:940.291 - 0.491ms returns FALSE
T0818 000:940.297 JLINK_HasError()
T0818 000:942.759 JLINK_IsHalted()
T0818 000:943.202 - 0.442ms returns FALSE
T0818 000:943.209 JLINK_HasError()
T0818 000:944.756 JLINK_IsHalted()
T0818 000:945.239 - 0.482ms returns FALSE
T0818 000:945.246 JLINK_HasError()
T0818 000:947.763 JLINK_IsHalted()
T0818 000:948.199 - 0.435ms returns FALSE
T0818 000:948.208 JLINK_HasError()
T0818 000:949.762 JLINK_IsHalted()
T0818 000:952.091   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:952.577 - 2.815ms returns TRUE
T0818 000:952.584 JLINK_ReadReg(R15 (PC))
T0818 000:952.589 - 0.005ms returns 0x20000000
T0818 000:952.594 JLINK_ClrBPEx(BPHandle = 0x00000003)
T0818 000:952.597 - 0.003ms returns 0x00
T0818 000:952.602 JLINK_ReadReg(R0)
T0818 000:952.605 - 0.003ms returns 0x00000000
T0818 000:952.947 JLINK_HasError()
T0818 000:952.956 JLINK_WriteReg(R0, 0x08004000)
T0818 000:952.961 - 0.004ms returns 0
T0818 000:952.966 JLINK_WriteReg(R1, 0x00004000)
T0818 000:952.969 - 0.003ms returns 0
T0818 000:952.973 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:952.976 - 0.003ms returns 0
T0818 000:952.981 JLINK_WriteReg(R3, 0x00000000)
T0818 000:952.984 - 0.003ms returns 0
T0818 000:952.988 JLINK_WriteReg(R4, 0x00000000)
T0818 000:952.991 - 0.003ms returns 0
T0818 000:952.995 JLINK_WriteReg(R5, 0x00000000)
T0818 000:952.999 - 0.003ms returns 0
T0818 000:953.003 JLINK_WriteReg(R6, 0x00000000)
T0818 000:953.007 - 0.003ms returns 0
T0818 000:953.011 JLINK_WriteReg(R7, 0x00000000)
T0818 000:953.014 - 0.003ms returns 0
T0818 000:953.018 JLINK_WriteReg(R8, 0x00000000)
T0818 000:953.021 - 0.003ms returns 0
T0818 000:953.025 JLINK_WriteReg(R9, 0x20000180)
T0818 000:953.029 - 0.003ms returns 0
T0818 000:953.033 JLINK_WriteReg(R10, 0x00000000)
T0818 000:953.036 - 0.003ms returns 0
T0818 000:953.040 JLINK_WriteReg(R11, 0x00000000)
T0818 000:953.044 - 0.003ms returns 0
T0818 000:953.048 JLINK_WriteReg(R12, 0x00000000)
T0818 000:953.051 - 0.003ms returns 0
T0818 000:953.055 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:953.059 - 0.003ms returns 0
T0818 000:953.063 JLINK_WriteReg(R14, 0x20000001)
T0818 000:953.066 - 0.003ms returns 0
T0818 000:953.070 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 000:953.073 - 0.003ms returns 0
T0818 000:953.077 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:953.081 - 0.003ms returns 0
T0818 000:953.085 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:953.088 - 0.003ms returns 0
T0818 000:953.092 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:953.096 - 0.003ms returns 0
T0818 000:953.100 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:953.103 - 0.003ms returns 0
T0818 000:953.108 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:953.112 - 0.004ms returns 0x00000004
T0818 000:953.116 JLINK_Go()
T0818 000:953.125   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:955.819 - 2.703ms 
T0818 000:955.828 JLINK_IsHalted()
T0818 000:958.166   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:958.638 - 2.809ms returns TRUE
T0818 000:958.644 JLINK_ReadReg(R15 (PC))
T0818 000:958.648 - 0.004ms returns 0x20000000
T0818 000:958.652 JLINK_ClrBPEx(BPHandle = 0x00000004)
T0818 000:958.656 - 0.003ms returns 0x00
T0818 000:958.660 JLINK_ReadReg(R0)
T0818 000:958.664 - 0.003ms returns 0x00000001
T0818 000:958.668 JLINK_HasError()
T0818 000:958.673 JLINK_WriteReg(R0, 0x08004000)
T0818 000:958.676 - 0.003ms returns 0
T0818 000:958.680 JLINK_WriteReg(R1, 0x00004000)
T0818 000:958.684 - 0.003ms returns 0
T0818 000:958.688 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:958.691 - 0.003ms returns 0
T0818 000:958.696 JLINK_WriteReg(R3, 0x00000000)
T0818 000:958.699 - 0.003ms returns 0
T0818 000:958.703 JLINK_WriteReg(R4, 0x00000000)
T0818 000:958.706 - 0.003ms returns 0
T0818 000:958.710 JLINK_WriteReg(R5, 0x00000000)
T0818 000:958.714 - 0.003ms returns 0
T0818 000:958.718 JLINK_WriteReg(R6, 0x00000000)
T0818 000:958.756 - 0.033ms returns 0
T0818 000:958.761 JLINK_WriteReg(R7, 0x00000000)
T0818 000:958.765 - 0.003ms returns 0
T0818 000:958.769 JLINK_WriteReg(R8, 0x00000000)
T0818 000:958.772 - 0.003ms returns 0
T0818 000:958.777 JLINK_WriteReg(R9, 0x20000180)
T0818 000:958.780 - 0.003ms returns 0
T0818 000:958.784 JLINK_WriteReg(R10, 0x00000000)
T0818 000:958.787 - 0.003ms returns 0
T0818 000:958.791 JLINK_WriteReg(R11, 0x00000000)
T0818 000:958.795 - 0.003ms returns 0
T0818 000:958.799 JLINK_WriteReg(R12, 0x00000000)
T0818 000:958.802 - 0.003ms returns 0
T0818 000:958.806 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:958.809 - 0.003ms returns 0
T0818 000:958.813 JLINK_WriteReg(R14, 0x20000001)
T0818 000:958.817 - 0.003ms returns 0
T0818 000:958.821 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 000:958.824 - 0.003ms returns 0
T0818 000:958.828 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:958.832 - 0.003ms returns 0
T0818 000:958.836 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:958.839 - 0.003ms returns 0
T0818 000:958.843 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:958.847 - 0.003ms returns 0
T0818 000:958.851 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:958.854 - 0.003ms returns 0
T0818 000:958.858 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:958.862 - 0.003ms returns 0x00000005
T0818 000:958.866 JLINK_Go()
T0818 000:958.873   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:961.660 - 2.793ms 
T0818 000:961.674 JLINK_IsHalted()
T0818 000:964.001 - 2.326ms returns FALSE
T0818 000:964.018 JLINK_HasError()
T0818 000:965.760 JLINK_IsHalted()
T0818 000:966.203 - 0.442ms returns FALSE
T0818 000:966.209 JLINK_HasError()
T0818 000:967.756 JLINK_IsHalted()
T0818 000:968.228 - 0.471ms returns FALSE
T0818 000:968.233 JLINK_HasError()
T0818 000:969.786 JLINK_IsHalted()
T0818 000:970.342 - 0.555ms returns FALSE
T0818 000:970.349 JLINK_HasError()
T0818 000:972.758 JLINK_IsHalted()
T0818 000:973.203 - 0.444ms returns FALSE
T0818 000:973.209 JLINK_HasError()
T0818 000:974.760 JLINK_IsHalted()
T0818 000:975.393 - 0.632ms returns FALSE
T0818 000:975.403 JLINK_HasError()
T0818 000:976.757 JLINK_IsHalted()
T0818 000:977.238 - 0.480ms returns FALSE
T0818 000:977.244 JLINK_HasError()
T0818 000:979.759 JLINK_IsHalted()
T0818 000:980.196 - 0.437ms returns FALSE
T0818 000:980.203 JLINK_HasError()
T0818 000:981.760 JLINK_IsHalted()
T0818 000:982.267 - 0.506ms returns FALSE
T0818 000:982.276 JLINK_HasError()
T0818 000:984.789 JLINK_IsHalted()
T0818 000:985.285 - 0.496ms returns FALSE
T0818 000:985.293 JLINK_HasError()
T0818 000:987.758 JLINK_IsHalted()
T0818 000:988.306 - 0.547ms returns FALSE
T0818 000:988.315 JLINK_HasError()
T0818 000:990.757 JLINK_IsHalted()
T0818 000:991.266 - 0.508ms returns FALSE
T0818 000:991.275 JLINK_HasError()
T0818 000:992.757 JLINK_IsHalted()
T0818 000:993.239 - 0.482ms returns FALSE
T0818 000:993.245 JLINK_HasError()
T0818 000:994.764 JLINK_IsHalted()
T0818 001:000.296 - 5.531ms returns FALSE
T0818 001:000.310 JLINK_HasError()
T0818 001:004.762 JLINK_IsHalted()
T0818 001:005.267 - 0.504ms returns FALSE
T0818 001:005.276 JLINK_HasError()
T0818 001:007.760 JLINK_IsHalted()
T0818 001:008.197 - 0.437ms returns FALSE
T0818 001:008.203 JLINK_HasError()
T0818 001:010.761 JLINK_IsHalted()
T0818 001:011.209 - 0.447ms returns FALSE
T0818 001:011.221 JLINK_HasError()
T0818 001:012.761 JLINK_IsHalted()
T0818 001:013.252 - 0.490ms returns FALSE
T0818 001:013.258 JLINK_HasError()
T0818 001:014.758 JLINK_IsHalted()
T0818 001:015.241 - 0.482ms returns FALSE
T0818 001:015.247 JLINK_HasError()
T0818 001:016.758 JLINK_IsHalted()
T0818 001:017.241 - 0.483ms returns FALSE
T0818 001:017.247 JLINK_HasError()
T0818 001:019.760 JLINK_IsHalted()
T0818 001:020.206 - 0.445ms returns FALSE
T0818 001:020.212 JLINK_HasError()
T0818 001:021.759 JLINK_IsHalted()
T0818 001:022.241 - 0.481ms returns FALSE
T0818 001:022.246 JLINK_HasError()
T0818 001:024.759 JLINK_IsHalted()
T0818 001:025.266 - 0.506ms returns FALSE
T0818 001:025.271 JLINK_HasError()
T0818 001:026.759 JLINK_IsHalted()
T0818 001:027.241 - 0.482ms returns FALSE
T0818 001:027.247 JLINK_HasError()
T0818 001:028.759 JLINK_IsHalted()
T0818 001:029.279 - 0.520ms returns FALSE
T0818 001:029.292 JLINK_HasError()
T0818 001:030.758 JLINK_IsHalted()
T0818 001:031.243 - 0.484ms returns FALSE
T0818 001:031.249 JLINK_HasError()
T0818 001:032.758 JLINK_IsHalted()
T0818 001:033.210 - 0.451ms returns FALSE
T0818 001:033.215 JLINK_HasError()
T0818 001:034.758 JLINK_IsHalted()
T0818 001:035.245 - 0.487ms returns FALSE
T0818 001:035.251 JLINK_HasError()
T0818 001:036.757 JLINK_IsHalted()
T0818 001:037.245 - 0.487ms returns FALSE
T0818 001:037.250 JLINK_HasError()
T0818 001:038.758 JLINK_IsHalted()
T0818 001:039.229 - 0.471ms returns FALSE
T0818 001:039.235 JLINK_HasError()
T0818 001:041.760 JLINK_IsHalted()
T0818 001:042.302 - 0.542ms returns FALSE
T0818 001:042.311 JLINK_HasError()
T0818 001:044.766 JLINK_IsHalted()
T0818 001:045.274 - 0.507ms returns FALSE
T0818 001:045.287 JLINK_HasError()
T0818 001:046.761 JLINK_IsHalted()
T0818 001:047.208 - 0.447ms returns FALSE
T0818 001:047.221 JLINK_HasError()
T0818 001:048.762 JLINK_IsHalted()
T0818 001:049.221 - 0.458ms returns FALSE
T0818 001:049.231 JLINK_HasError()
T0818 001:050.761 JLINK_IsHalted()
T0818 001:051.246 - 0.485ms returns FALSE
T0818 001:051.259 JLINK_HasError()
T0818 001:052.759 JLINK_IsHalted()
T0818 001:053.204 - 0.444ms returns FALSE
T0818 001:053.210 JLINK_HasError()
T0818 001:054.760 JLINK_IsHalted()
T0818 001:055.239 - 0.478ms returns FALSE
T0818 001:055.245 JLINK_HasError()
T0818 001:056.759 JLINK_IsHalted()
T0818 001:057.211 - 0.452ms returns FALSE
T0818 001:057.216 JLINK_HasError()
T0818 001:059.766 JLINK_IsHalted()
T0818 001:060.229 - 0.463ms returns FALSE
T0818 001:060.235 JLINK_HasError()
T0818 001:061.759 JLINK_IsHalted()
T0818 001:062.252 - 0.492ms returns FALSE
T0818 001:062.258 JLINK_HasError()
T0818 001:063.760 JLINK_IsHalted()
T0818 001:064.255 - 0.495ms returns FALSE
T0818 001:064.264 JLINK_HasError()
T0818 001:066.762 JLINK_IsHalted()
T0818 001:067.250 - 0.487ms returns FALSE
T0818 001:067.256 JLINK_HasError()
T0818 001:068.762 JLINK_IsHalted()
T0818 001:069.256 - 0.494ms returns FALSE
T0818 001:069.263 JLINK_HasError()
T0818 001:070.762 JLINK_IsHalted()
T0818 001:071.275 - 0.512ms returns FALSE
T0818 001:071.281 JLINK_HasError()
T0818 001:072.761 JLINK_IsHalted()
T0818 001:073.250 - 0.489ms returns FALSE
T0818 001:073.256 JLINK_HasError()
T0818 001:074.761 JLINK_IsHalted()
T0818 001:075.249 - 0.488ms returns FALSE
T0818 001:075.255 JLINK_HasError()
T0818 001:076.759 JLINK_IsHalted()
T0818 001:077.195 - 0.436ms returns FALSE
T0818 001:077.201 JLINK_HasError()
T0818 001:079.759 JLINK_IsHalted()
T0818 001:080.250 - 0.490ms returns FALSE
T0818 001:080.256 JLINK_HasError()
T0818 001:081.759 JLINK_IsHalted()
T0818 001:082.248 - 0.489ms returns FALSE
T0818 001:082.254 JLINK_HasError()
T0818 001:083.758 JLINK_IsHalted()
T0818 001:084.195 - 0.437ms returns FALSE
T0818 001:084.201 JLINK_HasError()
T0818 001:085.757 JLINK_IsHalted()
T0818 001:086.239 - 0.481ms returns FALSE
T0818 001:086.245 JLINK_HasError()
T0818 001:087.758 JLINK_IsHalted()
T0818 001:088.202 - 0.444ms returns FALSE
T0818 001:088.207 JLINK_HasError()
T0818 001:089.760 JLINK_IsHalted()
T0818 001:090.311 - 0.550ms returns FALSE
T0818 001:090.322 JLINK_HasError()
T0818 001:092.760 JLINK_IsHalted()
T0818 001:093.208 - 0.447ms returns FALSE
T0818 001:093.214 JLINK_HasError()
T0818 001:094.758 JLINK_IsHalted()
T0818 001:095.203 - 0.445ms returns FALSE
T0818 001:095.209 JLINK_HasError()
T0818 001:096.757 JLINK_IsHalted()
T0818 001:097.245 - 0.487ms returns FALSE
T0818 001:097.256 JLINK_HasError()
T0818 001:098.758 JLINK_IsHalted()
T0818 001:099.240 - 0.482ms returns FALSE
T0818 001:099.245 JLINK_HasError()
T0818 001:100.758 JLINK_IsHalted()
T0818 001:101.242 - 0.484ms returns FALSE
T0818 001:101.247 JLINK_HasError()
T0818 001:102.758 JLINK_IsHalted()
T0818 001:103.240 - 0.481ms returns FALSE
T0818 001:103.251 JLINK_HasError()
T0818 001:104.758 JLINK_IsHalted()
T0818 001:105.204 - 0.446ms returns FALSE
T0818 001:105.210 JLINK_HasError()
T0818 001:106.762 JLINK_IsHalted()
T0818 001:107.217 - 0.455ms returns FALSE
T0818 001:107.223 JLINK_HasError()
T0818 001:108.760 JLINK_IsHalted()
T0818 001:109.264 - 0.502ms returns FALSE
T0818 001:109.278 JLINK_HasError()
T0818 001:110.758 JLINK_IsHalted()
T0818 001:111.243 - 0.484ms returns FALSE
T0818 001:111.251 JLINK_HasError()
T0818 001:112.758 JLINK_IsHalted()
T0818 001:113.239 - 0.481ms returns FALSE
T0818 001:113.245 JLINK_HasError()
T0818 001:114.758 JLINK_IsHalted()
T0818 001:115.211 - 0.452ms returns FALSE
T0818 001:115.216 JLINK_HasError()
T0818 001:116.758 JLINK_IsHalted()
T0818 001:117.244 - 0.486ms returns FALSE
T0818 001:117.250 JLINK_HasError()
T0818 001:118.758 JLINK_IsHalted()
T0818 001:119.237 - 0.479ms returns FALSE
T0818 001:119.243 JLINK_HasError()
T0818 001:120.757 JLINK_IsHalted()
T0818 001:121.242 - 0.484ms returns FALSE
T0818 001:121.249 JLINK_HasError()
T0818 001:122.762 JLINK_IsHalted()
T0818 001:123.287 - 0.524ms returns FALSE
T0818 001:123.293 JLINK_HasError()
T0818 001:124.758 JLINK_IsHalted()
T0818 001:125.204 - 0.445ms returns FALSE
T0818 001:125.209 JLINK_HasError()
T0818 001:126.758 JLINK_IsHalted()
T0818 001:127.246 - 0.488ms returns FALSE
T0818 001:127.251 JLINK_HasError()
T0818 001:128.757 JLINK_IsHalted()
T0818 001:129.238 - 0.480ms returns FALSE
T0818 001:129.248 JLINK_HasError()
T0818 001:130.758 JLINK_IsHalted()
T0818 001:131.241 - 0.483ms returns FALSE
T0818 001:131.247 JLINK_HasError()
T0818 001:132.757 JLINK_IsHalted()
T0818 001:133.202 - 0.444ms returns FALSE
T0818 001:133.208 JLINK_HasError()
T0818 001:134.763 JLINK_IsHalted()
T0818 001:135.288 - 0.525ms returns FALSE
T0818 001:135.301 JLINK_HasError()
T0818 001:136.761 JLINK_IsHalted()
T0818 001:137.205 - 0.444ms returns FALSE
T0818 001:137.211 JLINK_HasError()
T0818 001:138.758 JLINK_IsHalted()
T0818 001:139.246 - 0.488ms returns FALSE
T0818 001:139.253 JLINK_HasError()
T0818 001:141.178 JLINK_IsHalted()
T0818 001:141.643 - 0.463ms returns FALSE
T0818 001:141.656 JLINK_HasError()
T0818 001:143.386 JLINK_IsHalted()
T0818 001:143.899 - 0.513ms returns FALSE
T0818 001:143.909 JLINK_HasError()
T0818 001:145.386 JLINK_IsHalted()
T0818 001:145.856 - 0.469ms returns FALSE
T0818 001:145.861 JLINK_HasError()
T0818 001:147.387 JLINK_IsHalted()
T0818 001:147.863 - 0.476ms returns FALSE
T0818 001:147.868 JLINK_HasError()
T0818 001:149.390 JLINK_IsHalted()
T0818 001:149.870 - 0.479ms returns FALSE
T0818 001:149.876 JLINK_HasError()
T0818 001:151.140 JLINK_IsHalted()
T0818 001:151.642 - 0.502ms returns FALSE
T0818 001:151.649 JLINK_HasError()
T0818 001:153.641 JLINK_IsHalted()
T0818 001:154.105 - 0.464ms returns FALSE
T0818 001:154.113 JLINK_HasError()
T0818 001:155.645 JLINK_IsHalted()
T0818 001:156.062 - 0.415ms returns FALSE
T0818 001:156.070 JLINK_HasError()
T0818 001:157.641 JLINK_IsHalted()
T0818 001:158.101 - 0.460ms returns FALSE
T0818 001:158.107 JLINK_HasError()
T0818 001:159.641 JLINK_IsHalted()
T0818 001:160.093 - 0.451ms returns FALSE
T0818 001:160.099 JLINK_HasError()
T0818 001:161.641 JLINK_IsHalted()
T0818 001:162.125 - 0.483ms returns FALSE
T0818 001:162.131 JLINK_HasError()
T0818 001:163.640 JLINK_IsHalted()
T0818 001:164.106 - 0.465ms returns FALSE
T0818 001:164.112 JLINK_HasError()
T0818 001:165.649 JLINK_IsHalted()
T0818 001:166.096 - 0.446ms returns FALSE
T0818 001:166.102 JLINK_HasError()
T0818 001:167.641 JLINK_IsHalted()
T0818 001:168.103 - 0.461ms returns FALSE
T0818 001:168.109 JLINK_HasError()
T0818 001:169.642 JLINK_IsHalted()
T0818 001:170.142 - 0.499ms returns FALSE
T0818 001:170.165 JLINK_HasError()
T0818 001:171.642 JLINK_IsHalted()
T0818 001:172.161 - 0.518ms returns FALSE
T0818 001:172.167 JLINK_HasError()
T0818 001:173.640 JLINK_IsHalted()
T0818 001:174.106 - 0.465ms returns FALSE
T0818 001:174.111 JLINK_HasError()
T0818 001:175.640 JLINK_IsHalted()
T0818 001:176.114 - 0.473ms returns FALSE
T0818 001:176.119 JLINK_HasError()
T0818 001:177.641 JLINK_IsHalted()
T0818 001:178.110 - 0.469ms returns FALSE
T0818 001:178.115 JLINK_HasError()
T0818 001:179.641 JLINK_IsHalted()
T0818 001:180.147 - 0.506ms returns FALSE
T0818 001:180.152 JLINK_HasError()
T0818 001:181.643 JLINK_IsHalted()
T0818 001:182.161 - 0.518ms returns FALSE
T0818 001:182.168 JLINK_HasError()
T0818 001:183.641 JLINK_IsHalted()
T0818 001:184.106 - 0.465ms returns FALSE
T0818 001:184.111 JLINK_HasError()
T0818 001:185.641 JLINK_IsHalted()
T0818 001:186.188 - 0.547ms returns FALSE
T0818 001:186.200 JLINK_HasError()
T0818 001:187.646 JLINK_IsHalted()
T0818 001:188.146 - 0.500ms returns FALSE
T0818 001:188.153 JLINK_HasError()
T0818 001:189.644 JLINK_IsHalted()
T0818 001:190.209 - 0.565ms returns FALSE
T0818 001:190.216 JLINK_HasError()
T0818 001:191.642 JLINK_IsHalted()
T0818 001:192.164 - 0.522ms returns FALSE
T0818 001:192.170 JLINK_HasError()
T0818 001:193.642 JLINK_IsHalted()
T0818 001:194.113 - 0.471ms returns FALSE
T0818 001:194.119 JLINK_HasError()
T0818 001:195.641 JLINK_IsHalted()
T0818 001:196.107 - 0.466ms returns FALSE
T0818 001:196.113 JLINK_HasError()
T0818 001:197.645 JLINK_IsHalted()
T0818 001:198.144 - 0.499ms returns FALSE
T0818 001:198.150 JLINK_HasError()
T0818 001:199.640 JLINK_IsHalted()
T0818 001:200.147 - 0.507ms returns FALSE
T0818 001:200.153 JLINK_HasError()
T0818 001:201.641 JLINK_IsHalted()
T0818 001:202.168 - 0.526ms returns FALSE
T0818 001:202.176 JLINK_HasError()
T0818 001:203.641 JLINK_IsHalted()
T0818 001:204.113 - 0.471ms returns FALSE
T0818 001:204.119 JLINK_HasError()
T0818 001:205.641 JLINK_IsHalted()
T0818 001:206.106 - 0.464ms returns FALSE
T0818 001:206.111 JLINK_HasError()
T0818 001:207.642 JLINK_IsHalted()
T0818 001:208.142 - 0.499ms returns FALSE
T0818 001:208.147 JLINK_HasError()
T0818 001:209.641 JLINK_IsHalted()
T0818 001:210.148 - 0.507ms returns FALSE
T0818 001:210.153 JLINK_HasError()
T0818 001:211.642 JLINK_IsHalted()
T0818 001:212.123 - 0.481ms returns FALSE
T0818 001:212.129 JLINK_HasError()
T0818 001:213.641 JLINK_IsHalted()
T0818 001:214.112 - 0.471ms returns FALSE
T0818 001:214.118 JLINK_HasError()
T0818 001:215.640 JLINK_IsHalted()
T0818 001:216.104 - 0.463ms returns FALSE
T0818 001:216.110 JLINK_HasError()
T0818 001:217.641 JLINK_IsHalted()
T0818 001:218.167 - 0.526ms returns FALSE
T0818 001:218.176 JLINK_HasError()
T0818 001:220.643 JLINK_IsHalted()
T0818 001:221.113 - 0.470ms returns FALSE
T0818 001:221.125 JLINK_HasError()
T0818 001:222.644 JLINK_IsHalted()
T0818 001:223.117 - 0.473ms returns FALSE
T0818 001:223.123 JLINK_HasError()
T0818 001:224.641 JLINK_IsHalted()
T0818 001:225.102 - 0.460ms returns FALSE
T0818 001:225.107 JLINK_HasError()
T0818 001:226.640 JLINK_IsHalted()
T0818 001:227.159 - 0.519ms returns FALSE
T0818 001:227.165 JLINK_HasError()
T0818 001:228.640 JLINK_IsHalted()
T0818 001:229.103 - 0.462ms returns FALSE
T0818 001:229.111 JLINK_HasError()
T0818 001:230.641 JLINK_IsHalted()
T0818 001:231.121 - 0.479ms returns FALSE
T0818 001:231.137 JLINK_HasError()
T0818 001:232.642 JLINK_IsHalted()
T0818 001:233.100 - 0.458ms returns FALSE
T0818 001:233.107 JLINK_HasError()
T0818 001:234.642 JLINK_IsHalted()
T0818 001:235.094 - 0.451ms returns FALSE
T0818 001:235.100 JLINK_HasError()
T0818 001:236.641 JLINK_IsHalted()
T0818 001:237.139 - 0.498ms returns FALSE
T0818 001:237.145 JLINK_HasError()
T0818 001:238.640 JLINK_IsHalted()
T0818 001:239.138 - 0.497ms returns FALSE
T0818 001:239.144 JLINK_HasError()
T0818 001:240.649 JLINK_IsHalted()
T0818 001:241.176 - 0.526ms returns FALSE
T0818 001:241.203 JLINK_HasError()
T0818 001:242.640 JLINK_IsHalted()
T0818 001:243.103 - 0.462ms returns FALSE
T0818 001:243.108 JLINK_HasError()
T0818 001:244.639 JLINK_IsHalted()
T0818 001:245.090 - 0.451ms returns FALSE
T0818 001:245.096 JLINK_HasError()
T0818 001:246.639 JLINK_IsHalted()
T0818 001:247.091 - 0.451ms returns FALSE
T0818 001:247.099 JLINK_HasError()
T0818 001:248.639 JLINK_IsHalted()
T0818 001:249.099 - 0.460ms returns FALSE
T0818 001:249.104 JLINK_HasError()
T0818 001:250.647 JLINK_IsHalted()
T0818 001:251.127 - 0.479ms returns FALSE
T0818 001:251.145 JLINK_HasError()
T0818 001:253.642 JLINK_IsHalted()
T0818 001:254.093 - 0.450ms returns FALSE
T0818 001:254.099 JLINK_HasError()
T0818 001:255.663 JLINK_IsHalted()
T0818 001:256.103 - 0.439ms returns FALSE
T0818 001:256.109 JLINK_HasError()
T0818 001:257.647 JLINK_IsHalted()
T0818 001:258.101 - 0.453ms returns FALSE
T0818 001:258.107 JLINK_HasError()
T0818 001:259.639 JLINK_IsHalted()
T0818 001:260.099 - 0.459ms returns FALSE
T0818 001:260.105 JLINK_HasError()
T0818 001:261.645 JLINK_IsHalted()
T0818 001:262.136 - 0.490ms returns FALSE
T0818 001:262.142 JLINK_HasError()
T0818 001:263.639 JLINK_IsHalted()
T0818 001:264.137 - 0.497ms returns FALSE
T0818 001:264.142 JLINK_HasError()
T0818 001:265.657 JLINK_IsHalted()
T0818 001:266.348 - 0.690ms returns FALSE
T0818 001:266.361 JLINK_HasError()
T0818 001:267.639 JLINK_IsHalted()
T0818 001:268.113 - 0.473ms returns FALSE
T0818 001:268.118 JLINK_HasError()
T0818 001:269.639 JLINK_IsHalted()
T0818 001:270.146 - 0.507ms returns FALSE
T0818 001:270.153 JLINK_HasError()
T0818 001:271.645 JLINK_IsHalted()
T0818 001:272.076 - 0.431ms returns FALSE
T0818 001:272.086 JLINK_HasError()
T0818 001:273.639 JLINK_IsHalted()
T0818 001:274.137 - 0.498ms returns FALSE
T0818 001:274.143 JLINK_HasError()
T0818 001:275.638 JLINK_IsHalted()
T0818 001:276.099 - 0.460ms returns FALSE
T0818 001:276.104 JLINK_HasError()
T0818 001:277.639 JLINK_IsHalted()
T0818 001:278.100 - 0.461ms returns FALSE
T0818 001:278.105 JLINK_HasError()
T0818 001:279.638 JLINK_IsHalted()
T0818 001:280.101 - 0.462ms returns FALSE
T0818 001:280.107 JLINK_HasError()
T0818 001:281.653 JLINK_IsHalted()
T0818 001:282.154 - 0.500ms returns FALSE
T0818 001:282.164 JLINK_HasError()
T0818 001:283.639 JLINK_IsHalted()
T0818 001:284.157 - 0.518ms returns FALSE
T0818 001:284.163 JLINK_HasError()
T0818 001:285.661 JLINK_IsHalted()
T0818 001:286.178 - 0.517ms returns FALSE
T0818 001:286.184 JLINK_HasError()
T0818 001:287.639 JLINK_IsHalted()
T0818 001:288.108 - 0.468ms returns FALSE
T0818 001:288.113 JLINK_HasError()
T0818 001:289.639 JLINK_IsHalted()
T0818 001:290.056 - 0.417ms returns FALSE
T0818 001:290.062 JLINK_HasError()
T0818 001:291.641 JLINK_IsHalted()
T0818 001:292.123 - 0.481ms returns FALSE
T0818 001:292.129 JLINK_HasError()
T0818 001:293.641 JLINK_IsHalted()
T0818 001:294.135 - 0.494ms returns FALSE
T0818 001:294.141 JLINK_HasError()
T0818 001:295.639 JLINK_IsHalted()
T0818 001:296.098 - 0.459ms returns FALSE
T0818 001:296.104 JLINK_HasError()
T0818 001:297.653 JLINK_IsHalted()
T0818 001:298.232 - 0.578ms returns FALSE
T0818 001:298.321 JLINK_HasError()
T0818 001:299.642 JLINK_IsHalted()
T0818 001:300.104 - 0.461ms returns FALSE
T0818 001:300.114 JLINK_HasError()
T0818 001:301.649 JLINK_IsHalted()
T0818 001:302.088 - 0.438ms returns FALSE
T0818 001:302.095 JLINK_HasError()
T0818 001:303.646 JLINK_IsHalted()
T0818 001:304.097 - 0.451ms returns FALSE
T0818 001:304.105 JLINK_HasError()
T0818 001:305.646 JLINK_IsHalted()
T0818 001:306.109 - 0.462ms returns FALSE
T0818 001:306.116 JLINK_HasError()
T0818 001:307.648 JLINK_IsHalted()
T0818 001:308.160 - 0.511ms returns FALSE
T0818 001:308.165 JLINK_HasError()
T0818 001:309.665 JLINK_IsHalted()
T0818 001:310.129 - 0.464ms returns FALSE
T0818 001:310.135 JLINK_HasError()
T0818 001:311.643 JLINK_IsHalted()
T0818 001:312.143 - 0.498ms returns FALSE
T0818 001:312.153 JLINK_HasError()
T0818 001:313.653 JLINK_IsHalted()
T0818 001:314.310 - 0.656ms returns FALSE
T0818 001:314.319 JLINK_HasError()
T0818 001:315.644 JLINK_IsHalted()
T0818 001:316.059 - 0.414ms returns FALSE
T0818 001:316.067 JLINK_HasError()
T0818 001:317.645 JLINK_IsHalted()
T0818 001:318.097 - 0.451ms returns FALSE
T0818 001:318.103 JLINK_HasError()
T0818 001:319.645 JLINK_IsHalted()
T0818 001:320.137 - 0.492ms returns FALSE
T0818 001:320.146 JLINK_HasError()
T0818 001:321.645 JLINK_IsHalted()
T0818 001:322.149 - 0.495ms returns FALSE
T0818 001:322.155 JLINK_HasError()
T0818 001:323.646 JLINK_IsHalted()
T0818 001:324.196 - 0.550ms returns FALSE
T0818 001:324.202 JLINK_HasError()
T0818 001:325.645 JLINK_IsHalted()
T0818 001:326.142 - 0.497ms returns FALSE
T0818 001:326.148 JLINK_HasError()
T0818 001:327.645 JLINK_IsHalted()
T0818 001:328.185 - 0.540ms returns FALSE
T0818 001:328.191 JLINK_HasError()
T0818 001:329.645 JLINK_IsHalted()
T0818 001:330.095 - 0.450ms returns FALSE
T0818 001:330.102 JLINK_HasError()
T0818 001:331.647 JLINK_IsHalted()
T0818 001:332.108 - 0.460ms returns FALSE
T0818 001:332.117 JLINK_HasError()
T0818 001:333.644 JLINK_IsHalted()
T0818 001:334.142 - 0.497ms returns FALSE
T0818 001:334.149 JLINK_HasError()
T0818 001:335.646 JLINK_IsHalted()
T0818 001:336.115 - 0.469ms returns FALSE
T0818 001:336.121 JLINK_HasError()
T0818 001:337.644 JLINK_IsHalted()
T0818 001:338.098 - 0.454ms returns FALSE
T0818 001:338.104 JLINK_HasError()
T0818 001:339.646 JLINK_IsHalted()
T0818 001:340.095 - 0.448ms returns FALSE
T0818 001:340.101 JLINK_HasError()
T0818 001:341.645 JLINK_IsHalted()
T0818 001:342.137 - 0.492ms returns FALSE
T0818 001:342.143 JLINK_HasError()
T0818 001:343.645 JLINK_IsHalted()
T0818 001:344.140 - 0.495ms returns FALSE
T0818 001:344.147 JLINK_HasError()
T0818 001:345.645 JLINK_IsHalted()
T0818 001:346.115 - 0.469ms returns FALSE
T0818 001:346.121 JLINK_HasError()
T0818 001:347.645 JLINK_IsHalted()
T0818 001:348.141 - 0.495ms returns FALSE
T0818 001:348.148 JLINK_HasError()
T0818 001:349.644 JLINK_IsHalted()
T0818 001:351.913   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:352.372 - 2.728ms returns TRUE
T0818 001:352.380 JLINK_ReadReg(R15 (PC))
T0818 001:352.386 - 0.005ms returns 0x20000000
T0818 001:352.391 JLINK_ClrBPEx(BPHandle = 0x00000005)
T0818 001:352.395 - 0.004ms returns 0x00
T0818 001:352.400 JLINK_ReadReg(R0)
T0818 001:352.403 - 0.003ms returns 0x00000000
T0818 001:352.936 JLINK_HasError()
T0818 001:352.955 JLINK_WriteReg(R0, 0x08008000)
T0818 001:352.961 - 0.005ms returns 0
T0818 001:352.965 JLINK_WriteReg(R1, 0x00004000)
T0818 001:352.969 - 0.003ms returns 0
T0818 001:352.973 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:352.976 - 0.003ms returns 0
T0818 001:352.980 JLINK_WriteReg(R3, 0x00000000)
T0818 001:352.984 - 0.003ms returns 0
T0818 001:352.988 JLINK_WriteReg(R4, 0x00000000)
T0818 001:352.991 - 0.003ms returns 0
T0818 001:352.995 JLINK_WriteReg(R5, 0x00000000)
T0818 001:352.999 - 0.003ms returns 0
T0818 001:353.003 JLINK_WriteReg(R6, 0x00000000)
T0818 001:353.006 - 0.003ms returns 0
T0818 001:353.010 JLINK_WriteReg(R7, 0x00000000)
T0818 001:353.014 - 0.003ms returns 0
T0818 001:353.018 JLINK_WriteReg(R8, 0x00000000)
T0818 001:353.021 - 0.003ms returns 0
T0818 001:353.025 JLINK_WriteReg(R9, 0x20000180)
T0818 001:353.028 - 0.003ms returns 0
T0818 001:353.032 JLINK_WriteReg(R10, 0x00000000)
T0818 001:353.036 - 0.003ms returns 0
T0818 001:353.040 JLINK_WriteReg(R11, 0x00000000)
T0818 001:353.043 - 0.003ms returns 0
T0818 001:353.047 JLINK_WriteReg(R12, 0x00000000)
T0818 001:353.051 - 0.003ms returns 0
T0818 001:353.055 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:353.058 - 0.003ms returns 0
T0818 001:353.062 JLINK_WriteReg(R14, 0x20000001)
T0818 001:353.066 - 0.003ms returns 0
T0818 001:353.070 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 001:353.073 - 0.003ms returns 0
T0818 001:353.077 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:353.081 - 0.003ms returns 0
T0818 001:353.085 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:353.088 - 0.003ms returns 0
T0818 001:353.092 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:353.096 - 0.003ms returns 0
T0818 001:353.100 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:353.103 - 0.003ms returns 0
T0818 001:353.108 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:353.112 - 0.004ms returns 0x00000006
T0818 001:353.116 JLINK_Go()
T0818 001:353.131   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:355.961 - 2.843ms 
T0818 001:355.980 JLINK_IsHalted()
T0818 001:358.323   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:358.824 - 2.844ms returns TRUE
T0818 001:358.831 JLINK_ReadReg(R15 (PC))
T0818 001:358.836 - 0.004ms returns 0x20000000
T0818 001:358.841 JLINK_ClrBPEx(BPHandle = 0x00000006)
T0818 001:358.845 - 0.004ms returns 0x00
T0818 001:358.849 JLINK_ReadReg(R0)
T0818 001:358.853 - 0.003ms returns 0x00000001
T0818 001:358.857 JLINK_HasError()
T0818 001:358.862 JLINK_WriteReg(R0, 0x08008000)
T0818 001:358.866 - 0.004ms returns 0
T0818 001:358.870 JLINK_WriteReg(R1, 0x00004000)
T0818 001:358.873 - 0.003ms returns 0
T0818 001:358.877 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:358.881 - 0.003ms returns 0
T0818 001:358.885 JLINK_WriteReg(R3, 0x00000000)
T0818 001:358.888 - 0.003ms returns 0
T0818 001:358.892 JLINK_WriteReg(R4, 0x00000000)
T0818 001:358.896 - 0.003ms returns 0
T0818 001:358.900 JLINK_WriteReg(R5, 0x00000000)
T0818 001:358.904 - 0.003ms returns 0
T0818 001:358.908 JLINK_WriteReg(R6, 0x00000000)
T0818 001:358.911 - 0.003ms returns 0
T0818 001:358.915 JLINK_WriteReg(R7, 0x00000000)
T0818 001:358.918 - 0.003ms returns 0
T0818 001:358.923 JLINK_WriteReg(R8, 0x00000000)
T0818 001:358.926 - 0.003ms returns 0
T0818 001:358.930 JLINK_WriteReg(R9, 0x20000180)
T0818 001:358.933 - 0.003ms returns 0
T0818 001:358.937 JLINK_WriteReg(R10, 0x00000000)
T0818 001:358.941 - 0.003ms returns 0
T0818 001:358.945 JLINK_WriteReg(R11, 0x00000000)
T0818 001:358.949 - 0.003ms returns 0
T0818 001:358.953 JLINK_WriteReg(R12, 0x00000000)
T0818 001:358.956 - 0.003ms returns 0
T0818 001:358.960 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:358.964 - 0.003ms returns 0
T0818 001:358.968 JLINK_WriteReg(R14, 0x20000001)
T0818 001:358.971 - 0.003ms returns 0
T0818 001:358.975 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 001:358.979 - 0.003ms returns 0
T0818 001:358.983 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:358.986 - 0.003ms returns 0
T0818 001:358.990 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:358.994 - 0.003ms returns 0
T0818 001:358.998 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:359.001 - 0.003ms returns 0
T0818 001:359.005 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:359.008 - 0.003ms returns 0
T0818 001:359.013 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:359.017 - 0.004ms returns 0x00000007
T0818 001:359.021 JLINK_Go()
T0818 001:359.028   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:361.831 - 2.809ms 
T0818 001:361.846 JLINK_IsHalted()
T0818 001:362.291 - 0.445ms returns FALSE
T0818 001:362.299 JLINK_HasError()
T0818 001:364.651 JLINK_IsHalted()
T0818 001:365.099 - 0.447ms returns FALSE
T0818 001:365.105 JLINK_HasError()
T0818 001:366.647 JLINK_IsHalted()
T0818 001:367.097 - 0.449ms returns FALSE
T0818 001:367.105 JLINK_HasError()
T0818 001:368.645 JLINK_IsHalted()
T0818 001:369.110 - 0.464ms returns FALSE
T0818 001:369.129 JLINK_HasError()
T0818 001:370.648 JLINK_IsHalted()
T0818 001:371.120 - 0.470ms returns FALSE
T0818 001:371.134 JLINK_HasError()
T0818 001:372.645 JLINK_IsHalted()
T0818 001:373.169 - 0.523ms returns FALSE
T0818 001:373.177 JLINK_HasError()
T0818 001:374.649 JLINK_IsHalted()
T0818 001:375.098 - 0.449ms returns FALSE
T0818 001:375.107 JLINK_HasError()
T0818 001:376.647 JLINK_IsHalted()
T0818 001:377.184 - 0.537ms returns FALSE
T0818 001:377.191 JLINK_HasError()
T0818 001:378.644 JLINK_IsHalted()
T0818 001:379.096 - 0.451ms returns FALSE
T0818 001:379.104 JLINK_HasError()
T0818 001:380.647 JLINK_IsHalted()
T0818 001:381.155 - 0.507ms returns FALSE
T0818 001:381.176 JLINK_HasError()
T0818 001:382.645 JLINK_IsHalted()
T0818 001:383.117 - 0.471ms returns FALSE
T0818 001:383.125 JLINK_HasError()
T0818 001:384.648 JLINK_IsHalted()
T0818 001:385.107 - 0.459ms returns FALSE
T0818 001:385.116 JLINK_HasError()
T0818 001:386.652 JLINK_IsHalted()
T0818 001:387.118 - 0.466ms returns FALSE
T0818 001:387.125 JLINK_HasError()
T0818 001:388.648 JLINK_IsHalted()
T0818 001:389.118 - 0.469ms returns FALSE
T0818 001:389.136 JLINK_HasError()
T0818 001:390.646 JLINK_IsHalted()
T0818 001:391.150 - 0.502ms returns FALSE
T0818 001:391.163 JLINK_HasError()
T0818 001:392.644 JLINK_IsHalted()
T0818 001:393.143 - 0.497ms returns FALSE
T0818 001:393.158 JLINK_HasError()
T0818 001:394.644 JLINK_IsHalted()
T0818 001:395.095 - 0.451ms returns FALSE
T0818 001:395.103 JLINK_HasError()
T0818 001:396.649 JLINK_IsHalted()
T0818 001:397.207 - 0.557ms returns FALSE
T0818 001:397.215 JLINK_HasError()
T0818 001:398.644 JLINK_IsHalted()
T0818 001:399.102 - 0.457ms returns FALSE
T0818 001:399.113 JLINK_HasError()
T0818 001:400.641 JLINK_IsHalted()
T0818 001:401.132 - 0.490ms returns FALSE
T0818 001:401.146 JLINK_HasError()
T0818 001:402.647 JLINK_IsHalted()
T0818 001:403.097 - 0.449ms returns FALSE
T0818 001:403.107 JLINK_HasError()
T0818 001:404.645 JLINK_IsHalted()
T0818 001:405.141 - 0.496ms returns FALSE
T0818 001:405.150 JLINK_HasError()
T0818 001:406.645 JLINK_IsHalted()
T0818 001:407.141 - 0.495ms returns FALSE
T0818 001:407.147 JLINK_HasError()
T0818 001:408.674 JLINK_IsHalted()
T0818 001:409.182 - 0.507ms returns FALSE
T0818 001:409.188 JLINK_HasError()
T0818 001:410.650 JLINK_IsHalted()
T0818 001:411.103 - 0.451ms returns FALSE
T0818 001:411.119 JLINK_HasError()
T0818 001:412.648 JLINK_IsHalted()
T0818 001:413.094 - 0.446ms returns FALSE
T0818 001:413.102 JLINK_HasError()
T0818 001:414.655 JLINK_IsHalted()
T0818 001:415.142 - 0.487ms returns FALSE
T0818 001:415.153 JLINK_HasError()
T0818 001:416.646 JLINK_IsHalted()
T0818 001:417.141 - 0.494ms returns FALSE
T0818 001:417.147 JLINK_HasError()
T0818 001:418.651 JLINK_IsHalted()
T0818 001:419.096 - 0.445ms returns FALSE
T0818 001:419.103 JLINK_HasError()
T0818 001:420.648 JLINK_IsHalted()
T0818 001:421.178 - 0.529ms returns FALSE
T0818 001:421.197 JLINK_HasError()
T0818 001:422.645 JLINK_IsHalted()
T0818 001:423.141 - 0.496ms returns FALSE
T0818 001:423.150 JLINK_HasError()
T0818 001:424.651 JLINK_IsHalted()
T0818 001:425.143 - 0.491ms returns FALSE
T0818 001:425.158 JLINK_HasError()
T0818 001:426.666 JLINK_IsHalted()
T0818 001:427.147 - 0.481ms returns FALSE
T0818 001:427.157 JLINK_HasError()
T0818 001:428.649 JLINK_IsHalted()
T0818 001:429.235 - 0.585ms returns FALSE
T0818 001:429.245 JLINK_HasError()
T0818 001:430.645 JLINK_IsHalted()
T0818 001:431.154 - 0.508ms returns FALSE
T0818 001:431.170 JLINK_HasError()
T0818 001:432.647 JLINK_IsHalted()
T0818 001:433.098 - 0.451ms returns FALSE
T0818 001:433.108 JLINK_HasError()
T0818 001:434.646 JLINK_IsHalted()
T0818 001:435.150 - 0.503ms returns FALSE
T0818 001:435.158 JLINK_HasError()
T0818 001:436.648 JLINK_IsHalted()
T0818 001:437.109 - 0.460ms returns FALSE
T0818 001:437.116 JLINK_HasError()
T0818 001:438.645 JLINK_IsHalted()
T0818 001:439.114 - 0.468ms returns FALSE
T0818 001:439.120 JLINK_HasError()
T0818 001:440.644 JLINK_IsHalted()
T0818 001:441.159 - 0.514ms returns FALSE
T0818 001:441.175 JLINK_HasError()
T0818 001:442.649 JLINK_IsHalted()
T0818 001:443.092 - 0.442ms returns FALSE
T0818 001:443.099 JLINK_HasError()
T0818 001:444.654 JLINK_IsHalted()
T0818 001:445.200 - 0.545ms returns FALSE
T0818 001:445.209 JLINK_HasError()
T0818 001:446.647 JLINK_IsHalted()
T0818 001:447.106 - 0.459ms returns FALSE
T0818 001:447.114 JLINK_HasError()
T0818 001:448.645 JLINK_IsHalted()
T0818 001:449.104 - 0.458ms returns FALSE
T0818 001:449.111 JLINK_HasError()
T0818 001:450.646 JLINK_IsHalted()
T0818 001:451.170 - 0.522ms returns FALSE
T0818 001:451.196 JLINK_HasError()
T0818 001:452.644 JLINK_IsHalted()
T0818 001:453.097 - 0.452ms returns FALSE
T0818 001:453.110 JLINK_HasError()
T0818 001:454.646 JLINK_IsHalted()
T0818 001:455.111 - 0.464ms returns FALSE
T0818 001:455.120 JLINK_HasError()
T0818 001:456.646 JLINK_IsHalted()
T0818 001:457.198 - 0.552ms returns FALSE
T0818 001:457.209 JLINK_HasError()
T0818 001:458.646 JLINK_IsHalted()
T0818 001:459.060 - 0.414ms returns FALSE
T0818 001:459.068 JLINK_HasError()
T0818 001:460.644 JLINK_IsHalted()
T0818 001:461.193 - 0.548ms returns FALSE
T0818 001:461.207 JLINK_HasError()
T0818 001:462.644 JLINK_IsHalted()
T0818 001:463.142 - 0.497ms returns FALSE
T0818 001:463.150 JLINK_HasError()
T0818 001:464.650 JLINK_IsHalted()
T0818 001:465.088 - 0.438ms returns FALSE
T0818 001:465.096 JLINK_HasError()
T0818 001:466.645 JLINK_IsHalted()
T0818 001:467.140 - 0.494ms returns FALSE
T0818 001:467.146 JLINK_HasError()
T0818 001:468.647 JLINK_IsHalted()
T0818 001:469.107 - 0.460ms returns FALSE
T0818 001:469.115 JLINK_HasError()
T0818 001:470.650 JLINK_IsHalted()
T0818 001:471.156 - 0.505ms returns FALSE
T0818 001:471.166 JLINK_HasError()
T0818 001:472.645 JLINK_IsHalted()
T0818 001:473.166 - 0.520ms returns FALSE
T0818 001:473.176 JLINK_HasError()
T0818 001:475.651 JLINK_IsHalted()
T0818 001:476.150 - 0.498ms returns FALSE
T0818 001:476.156 JLINK_HasError()
T0818 001:477.647 JLINK_IsHalted()
T0818 001:478.139 - 0.491ms returns FALSE
T0818 001:478.144 JLINK_HasError()
T0818 001:479.646 JLINK_IsHalted()
T0818 001:480.140 - 0.493ms returns FALSE
T0818 001:480.147 JLINK_HasError()
T0818 001:481.652 JLINK_IsHalted()
T0818 001:482.100 - 0.448ms returns FALSE
T0818 001:482.110 JLINK_HasError()
T0818 001:483.644 JLINK_IsHalted()
T0818 001:484.116 - 0.471ms returns FALSE
T0818 001:484.126 JLINK_HasError()
T0818 001:485.649 JLINK_IsHalted()
T0818 001:486.142 - 0.491ms returns FALSE
T0818 001:486.150 JLINK_HasError()
T0818 001:487.644 JLINK_IsHalted()
T0818 001:488.151 - 0.507ms returns FALSE
T0818 001:488.161 JLINK_HasError()
T0818 001:489.693 JLINK_IsHalted()
T0818 001:490.142 - 0.448ms returns FALSE
T0818 001:490.150 JLINK_HasError()
T0818 001:491.647 JLINK_IsHalted()
T0818 001:492.141 - 0.494ms returns FALSE
T0818 001:492.150 JLINK_HasError()
T0818 001:493.647 JLINK_IsHalted()
T0818 001:494.126 - 0.479ms returns FALSE
T0818 001:494.134 JLINK_HasError()
T0818 001:495.650 JLINK_IsHalted()
T0818 001:496.153 - 0.502ms returns FALSE
T0818 001:496.160 JLINK_HasError()
T0818 001:497.647 JLINK_IsHalted()
T0818 001:498.185 - 0.538ms returns FALSE
T0818 001:498.193 JLINK_HasError()
T0818 001:499.650 JLINK_IsHalted()
T0818 001:500.103 - 0.453ms returns FALSE
T0818 001:500.111 JLINK_HasError()
T0818 001:501.643 JLINK_IsHalted()
T0818 001:502.209 - 0.565ms returns FALSE
T0818 001:502.217 JLINK_HasError()
T0818 001:505.652 JLINK_IsHalted()
T0818 001:506.142 - 0.490ms returns FALSE
T0818 001:506.149 JLINK_HasError()
T0818 001:507.645 JLINK_IsHalted()
T0818 001:508.122 - 0.477ms returns FALSE
T0818 001:508.129 JLINK_HasError()
T0818 001:509.646 JLINK_IsHalted()
T0818 001:510.109 - 0.462ms returns FALSE
T0818 001:510.115 JLINK_HasError()
T0818 001:511.647 JLINK_IsHalted()
T0818 001:512.144 - 0.497ms returns FALSE
T0818 001:512.162 JLINK_HasError()
T0818 001:513.648 JLINK_IsHalted()
T0818 001:514.109 - 0.461ms returns FALSE
T0818 001:514.118 JLINK_HasError()
T0818 001:515.647 JLINK_IsHalted()
T0818 001:516.143 - 0.496ms returns FALSE
T0818 001:516.152 JLINK_HasError()
T0818 001:517.647 JLINK_IsHalted()
T0818 001:518.200 - 0.552ms returns FALSE
T0818 001:518.212 JLINK_HasError()
T0818 001:519.642 JLINK_IsHalted()
T0818 001:520.109 - 0.466ms returns FALSE
T0818 001:520.116 JLINK_HasError()
T0818 001:521.648 JLINK_IsHalted()
T0818 001:522.108 - 0.459ms returns FALSE
T0818 001:522.114 JLINK_HasError()
T0818 001:523.645 JLINK_IsHalted()
T0818 001:524.126 - 0.480ms returns FALSE
T0818 001:524.131 JLINK_HasError()
T0818 001:525.646 JLINK_IsHalted()
T0818 001:526.096 - 0.450ms returns FALSE
T0818 001:526.102 JLINK_HasError()
T0818 001:528.648 JLINK_IsHalted()
T0818 001:529.183 - 0.534ms returns FALSE
T0818 001:529.196 JLINK_HasError()
T0818 001:530.647 JLINK_IsHalted()
T0818 001:531.115 - 0.467ms returns FALSE
T0818 001:531.135 JLINK_HasError()
T0818 001:532.646 JLINK_IsHalted()
T0818 001:533.115 - 0.469ms returns FALSE
T0818 001:533.123 JLINK_HasError()
T0818 001:534.647 JLINK_IsHalted()
T0818 001:535.126 - 0.479ms returns FALSE
T0818 001:535.138 JLINK_HasError()
T0818 001:537.648 JLINK_IsHalted()
T0818 001:538.098 - 0.450ms returns FALSE
T0818 001:538.105 JLINK_HasError()
T0818 001:539.648 JLINK_IsHalted()
T0818 001:540.098 - 0.450ms returns FALSE
T0818 001:540.105 JLINK_HasError()
T0818 001:542.646 JLINK_IsHalted()
T0818 001:543.163 - 0.516ms returns FALSE
T0818 001:543.169 JLINK_HasError()
T0818 001:544.646 JLINK_IsHalted()
T0818 001:545.097 - 0.451ms returns FALSE
T0818 001:545.104 JLINK_HasError()
T0818 001:546.647 JLINK_IsHalted()
T0818 001:547.140 - 0.493ms returns FALSE
T0818 001:547.146 JLINK_HasError()
T0818 001:548.644 JLINK_IsHalted()
T0818 001:549.117 - 0.473ms returns FALSE
T0818 001:549.123 JLINK_HasError()
T0818 001:550.646 JLINK_IsHalted()
T0818 001:551.203 - 0.556ms returns FALSE
T0818 001:551.222 JLINK_HasError()
T0818 001:553.650 JLINK_IsHalted()
T0818 001:554.142 - 0.492ms returns FALSE
T0818 001:554.158 JLINK_HasError()
T0818 001:556.648 JLINK_IsHalted()
T0818 001:557.097 - 0.448ms returns FALSE
T0818 001:557.104 JLINK_HasError()
T0818 001:558.647 JLINK_IsHalted()
T0818 001:559.098 - 0.450ms returns FALSE
T0818 001:559.104 JLINK_HasError()
T0818 001:560.644 JLINK_IsHalted()
T0818 001:561.163 - 0.518ms returns FALSE
T0818 001:561.183 JLINK_HasError()
T0818 001:562.646 JLINK_IsHalted()
T0818 001:563.163 - 0.517ms returns FALSE
T0818 001:563.173 JLINK_HasError()
T0818 001:564.652 JLINK_IsHalted()
T0818 001:565.150 - 0.497ms returns FALSE
T0818 001:565.159 JLINK_HasError()
T0818 001:566.648 JLINK_IsHalted()
T0818 001:567.108 - 0.459ms returns FALSE
T0818 001:567.117 JLINK_HasError()
T0818 001:568.646 JLINK_IsHalted()
T0818 001:569.108 - 0.460ms returns FALSE
T0818 001:569.115 JLINK_HasError()
T0818 001:571.648 JLINK_IsHalted()
T0818 001:572.118 - 0.469ms returns FALSE
T0818 001:572.124 JLINK_HasError()
T0818 001:573.645 JLINK_IsHalted()
T0818 001:574.144 - 0.498ms returns FALSE
T0818 001:574.150 JLINK_HasError()
T0818 001:575.645 JLINK_IsHalted()
T0818 001:576.140 - 0.494ms returns FALSE
T0818 001:576.146 JLINK_HasError()
T0818 001:577.645 JLINK_IsHalted()
T0818 001:578.138 - 0.492ms returns FALSE
T0818 001:578.144 JLINK_HasError()
T0818 001:579.645 JLINK_IsHalted()
T0818 001:580.119 - 0.473ms returns FALSE
T0818 001:580.142 JLINK_HasError()
T0818 001:581.646 JLINK_IsHalted()
T0818 001:582.185 - 0.538ms returns FALSE
T0818 001:582.192 JLINK_HasError()
T0818 001:584.651 JLINK_IsHalted()
T0818 001:585.191 - 0.540ms returns FALSE
T0818 001:585.198 JLINK_HasError()
T0818 001:586.645 JLINK_IsHalted()
T0818 001:587.116 - 0.470ms returns FALSE
T0818 001:587.122 JLINK_HasError()
T0818 001:588.645 JLINK_IsHalted()
T0818 001:589.059 - 0.413ms returns FALSE
T0818 001:589.066 JLINK_HasError()
T0818 001:590.646 JLINK_IsHalted()
T0818 001:591.192 - 0.545ms returns FALSE
T0818 001:591.215 JLINK_HasError()
T0818 001:592.647 JLINK_IsHalted()
T0818 001:593.107 - 0.460ms returns FALSE
T0818 001:593.117 JLINK_HasError()
T0818 001:594.648 JLINK_IsHalted()
T0818 001:595.139 - 0.490ms returns FALSE
T0818 001:595.146 JLINK_HasError()
T0818 001:596.644 JLINK_IsHalted()
T0818 001:597.237 - 0.592ms returns FALSE
T0818 001:597.263 JLINK_HasError()
T0818 001:598.651 JLINK_IsHalted()
T0818 001:599.153 - 0.502ms returns FALSE
T0818 001:599.160 JLINK_HasError()
T0818 001:600.645 JLINK_IsHalted()
T0818 001:601.215 - 0.569ms returns FALSE
T0818 001:601.234 JLINK_HasError()
T0818 001:602.642 JLINK_IsHalted()
T0818 001:603.097 - 0.454ms returns FALSE
T0818 001:603.108 JLINK_HasError()
T0818 001:604.647 JLINK_IsHalted()
T0818 001:605.198 - 0.550ms returns FALSE
T0818 001:605.205 JLINK_HasError()
T0818 001:606.642 JLINK_IsHalted()
T0818 001:607.109 - 0.466ms returns FALSE
T0818 001:607.116 JLINK_HasError()
T0818 001:608.648 JLINK_IsHalted()
T0818 001:609.124 - 0.475ms returns FALSE
T0818 001:609.133 JLINK_HasError()
T0818 001:610.645 JLINK_IsHalted()
T0818 001:611.151 - 0.505ms returns FALSE
T0818 001:611.171 JLINK_HasError()
T0818 001:612.646 JLINK_IsHalted()
T0818 001:613.122 - 0.476ms returns FALSE
T0818 001:613.135 JLINK_HasError()
T0818 001:614.647 JLINK_IsHalted()
T0818 001:615.141 - 0.494ms returns FALSE
T0818 001:615.150 JLINK_HasError()
T0818 001:616.646 JLINK_IsHalted()
T0818 001:617.060 - 0.413ms returns FALSE
T0818 001:617.066 JLINK_HasError()
T0818 001:618.645 JLINK_IsHalted()
T0818 001:619.094 - 0.449ms returns FALSE
T0818 001:619.102 JLINK_HasError()
T0818 001:620.645 JLINK_IsHalted()
T0818 001:621.174 - 0.528ms returns FALSE
T0818 001:621.203 JLINK_HasError()
T0818 001:622.645 JLINK_IsHalted()
T0818 001:623.139 - 0.493ms returns FALSE
T0818 001:623.147 JLINK_HasError()
T0818 001:624.652 JLINK_IsHalted()
T0818 001:625.142 - 0.489ms returns FALSE
T0818 001:625.152 JLINK_HasError()
T0818 001:626.646 JLINK_IsHalted()
T0818 001:627.115 - 0.469ms returns FALSE
T0818 001:627.122 JLINK_HasError()
T0818 001:628.648 JLINK_IsHalted()
T0818 001:629.259 - 0.610ms returns FALSE
T0818 001:629.267 JLINK_HasError()
T0818 001:630.648 JLINK_IsHalted()
T0818 001:631.148 - 0.499ms returns FALSE
T0818 001:631.171 JLINK_HasError()
T0818 001:632.645 JLINK_IsHalted()
T0818 001:633.143 - 0.498ms returns FALSE
T0818 001:633.152 JLINK_HasError()
T0818 001:634.649 JLINK_IsHalted()
T0818 001:635.086 - 0.437ms returns FALSE
T0818 001:635.095 JLINK_HasError()
T0818 001:636.644 JLINK_IsHalted()
T0818 001:637.117 - 0.472ms returns FALSE
T0818 001:637.124 JLINK_HasError()
T0818 001:638.646 JLINK_IsHalted()
T0818 001:639.188 - 0.542ms returns FALSE
T0818 001:639.195 JLINK_HasError()
T0818 001:640.645 JLINK_IsHalted()
T0818 001:641.101 - 0.455ms returns FALSE
T0818 001:641.119 JLINK_HasError()
T0818 001:642.647 JLINK_IsHalted()
T0818 001:643.108 - 0.460ms returns FALSE
T0818 001:643.118 JLINK_HasError()
T0818 001:644.646 JLINK_IsHalted()
T0818 001:645.061 - 0.414ms returns FALSE
T0818 001:645.069 JLINK_HasError()
T0818 001:646.653 JLINK_IsHalted()
T0818 001:647.143 - 0.489ms returns FALSE
T0818 001:647.150 JLINK_HasError()
T0818 001:648.645 JLINK_IsHalted()
T0818 001:649.138 - 0.493ms returns FALSE
T0818 001:649.147 JLINK_HasError()
T0818 001:650.647 JLINK_IsHalted()
T0818 001:651.158 - 0.510ms returns FALSE
T0818 001:651.172 JLINK_HasError()
T0818 001:652.646 JLINK_IsHalted()
T0818 001:653.124 - 0.477ms returns FALSE
T0818 001:653.133 JLINK_HasError()
T0818 001:654.646 JLINK_IsHalted()
T0818 001:655.142 - 0.496ms returns FALSE
T0818 001:655.152 JLINK_HasError()
T0818 001:656.647 JLINK_IsHalted()
T0818 001:657.094 - 0.446ms returns FALSE
T0818 001:657.107 JLINK_HasError()
T0818 001:658.643 JLINK_IsHalted()
T0818 001:659.117 - 0.474ms returns FALSE
T0818 001:659.125 JLINK_HasError()
T0818 001:660.649 JLINK_IsHalted()
T0818 001:661.109 - 0.459ms returns FALSE
T0818 001:661.126 JLINK_HasError()
T0818 001:662.645 JLINK_IsHalted()
T0818 001:663.082 - 0.436ms returns FALSE
T0818 001:663.091 JLINK_HasError()
T0818 001:664.647 JLINK_IsHalted()
T0818 001:665.119 - 0.471ms returns FALSE
T0818 001:665.128 JLINK_HasError()
T0818 001:666.643 JLINK_IsHalted()
T0818 001:667.113 - 0.470ms returns FALSE
T0818 001:667.119 JLINK_HasError()
T0818 001:668.650 JLINK_IsHalted()
T0818 001:669.118 - 0.468ms returns FALSE
T0818 001:669.125 JLINK_HasError()
T0818 001:670.645 JLINK_IsHalted()
T0818 001:671.104 - 0.458ms returns FALSE
T0818 001:671.120 JLINK_HasError()
T0818 001:672.644 JLINK_IsHalted()
T0818 001:673.141 - 0.496ms returns FALSE
T0818 001:673.150 JLINK_HasError()
T0818 001:674.646 JLINK_IsHalted()
T0818 001:675.062 - 0.416ms returns FALSE
T0818 001:675.071 JLINK_HasError()
T0818 001:676.645 JLINK_IsHalted()
T0818 001:677.186 - 0.541ms returns FALSE
T0818 001:677.193 JLINK_HasError()
T0818 001:678.647 JLINK_IsHalted()
T0818 001:679.176 - 0.529ms returns FALSE
T0818 001:679.183 JLINK_HasError()
T0818 001:680.643 JLINK_IsHalted()
T0818 001:681.148 - 0.504ms returns FALSE
T0818 001:681.164 JLINK_HasError()
T0818 001:682.647 JLINK_IsHalted()
T0818 001:683.097 - 0.450ms returns FALSE
T0818 001:683.112 JLINK_HasError()
T0818 001:684.647 JLINK_IsHalted()
T0818 001:685.107 - 0.459ms returns FALSE
T0818 001:685.116 JLINK_HasError()
T0818 001:686.648 JLINK_IsHalted()
T0818 001:687.190 - 0.542ms returns FALSE
T0818 001:687.197 JLINK_HasError()
T0818 001:688.644 JLINK_IsHalted()
T0818 001:689.125 - 0.481ms returns FALSE
T0818 001:689.131 JLINK_HasError()
T0818 001:690.673 JLINK_IsHalted()
T0818 001:691.188 - 0.514ms returns FALSE
T0818 001:691.203 JLINK_HasError()
T0818 001:692.649 JLINK_IsHalted()
T0818 001:693.096 - 0.447ms returns FALSE
T0818 001:693.104 JLINK_HasError()
T0818 001:695.650 JLINK_IsHalted()
T0818 001:696.109 - 0.459ms returns FALSE
T0818 001:696.117 JLINK_HasError()
T0818 001:697.645 JLINK_IsHalted()
T0818 001:698.138 - 0.493ms returns FALSE
T0818 001:698.145 JLINK_HasError()
T0818 001:699.646 JLINK_IsHalted()
T0818 001:700.139 - 0.492ms returns FALSE
T0818 001:700.145 JLINK_HasError()
T0818 001:701.642 JLINK_IsHalted()
T0818 001:702.139 - 0.496ms returns FALSE
T0818 001:702.145 JLINK_HasError()
T0818 001:703.644 JLINK_IsHalted()
T0818 001:704.118 - 0.472ms returns FALSE
T0818 001:704.125 JLINK_HasError()
T0818 001:705.646 JLINK_IsHalted()
T0818 001:706.139 - 0.493ms returns FALSE
T0818 001:706.145 JLINK_HasError()
T0818 001:707.641 JLINK_IsHalted()
T0818 001:708.116 - 0.475ms returns FALSE
T0818 001:708.123 JLINK_HasError()
T0818 001:709.649 JLINK_IsHalted()
T0818 001:710.108 - 0.459ms returns FALSE
T0818 001:710.114 JLINK_HasError()
T0818 001:711.646 JLINK_IsHalted()
T0818 001:712.094 - 0.448ms returns FALSE
T0818 001:712.102 JLINK_HasError()
T0818 001:713.643 JLINK_IsHalted()
T0818 001:714.107 - 0.463ms returns FALSE
T0818 001:714.116 JLINK_HasError()
T0818 001:715.643 JLINK_IsHalted()
T0818 001:716.147 - 0.504ms returns FALSE
T0818 001:716.164 JLINK_HasError()
T0818 001:717.645 JLINK_IsHalted()
T0818 001:718.141 - 0.496ms returns FALSE
T0818 001:718.152 JLINK_HasError()
T0818 001:719.648 JLINK_IsHalted()
T0818 001:720.183 - 0.534ms returns FALSE
T0818 001:720.192 JLINK_HasError()
T0818 001:721.649 JLINK_IsHalted()
T0818 001:722.142 - 0.493ms returns FALSE
T0818 001:722.150 JLINK_HasError()
T0818 001:723.648 JLINK_IsHalted()
T0818 001:724.163 - 0.514ms returns FALSE
T0818 001:724.175 JLINK_HasError()
T0818 001:726.652 JLINK_IsHalted()
T0818 001:727.121 - 0.469ms returns FALSE
T0818 001:727.129 JLINK_HasError()
T0818 001:728.643 JLINK_IsHalted()
T0818 001:729.115 - 0.472ms returns FALSE
T0818 001:729.122 JLINK_HasError()
T0818 001:730.645 JLINK_IsHalted()
T0818 001:731.125 - 0.479ms returns FALSE
T0818 001:731.142 JLINK_HasError()
T0818 001:732.651 JLINK_IsHalted()
T0818 001:733.083 - 0.431ms returns FALSE
T0818 001:733.090 JLINK_HasError()
T0818 001:734.648 JLINK_IsHalted()
T0818 001:735.185 - 0.536ms returns FALSE
T0818 001:735.202 JLINK_HasError()
T0818 001:736.648 JLINK_IsHalted()
T0818 001:737.191 - 0.543ms returns FALSE
T0818 001:737.200 JLINK_HasError()
T0818 001:738.645 JLINK_IsHalted()
T0818 001:739.105 - 0.459ms returns FALSE
T0818 001:739.112 JLINK_HasError()
T0818 001:740.376 JLINK_IsHalted()
T0818 001:740.830 - 0.453ms returns FALSE
T0818 001:740.836 JLINK_HasError()
T0818 001:742.381 JLINK_IsHalted()
T0818 001:742.829 - 0.447ms returns FALSE
T0818 001:742.835 JLINK_HasError()
T0818 001:744.377 JLINK_IsHalted()
T0818 001:744.873 - 0.495ms returns FALSE
T0818 001:744.880 JLINK_HasError()
T0818 001:746.375 JLINK_IsHalted()
T0818 001:746.912 - 0.537ms returns FALSE
T0818 001:746.918 JLINK_HasError()
T0818 001:748.377 JLINK_IsHalted()
T0818 001:748.865 - 0.488ms returns FALSE
T0818 001:748.871 JLINK_HasError()
T0818 001:750.375 JLINK_IsHalted()
T0818 001:750.865 - 0.489ms returns FALSE
T0818 001:750.870 JLINK_HasError()
T0818 001:752.379 JLINK_IsHalted()
T0818 001:752.858 - 0.478ms returns FALSE
T0818 001:752.864 JLINK_HasError()
T0818 001:754.376 JLINK_IsHalted()
T0818 001:754.865 - 0.489ms returns FALSE
T0818 001:754.872 JLINK_HasError()
T0818 001:756.377 JLINK_IsHalted()
T0818 001:756.874 - 0.496ms returns FALSE
T0818 001:756.890 JLINK_HasError()
T0818 001:758.379 JLINK_IsHalted()
T0818 001:758.902 - 0.522ms returns FALSE
T0818 001:758.909 JLINK_HasError()
T0818 001:760.375 JLINK_IsHalted()
T0818 001:761.118 - 0.742ms returns FALSE
T0818 001:761.141 JLINK_HasError()
T0818 001:762.377 JLINK_IsHalted()
T0818 001:762.839 - 0.461ms returns FALSE
T0818 001:762.849 JLINK_HasError()
T0818 001:764.375 JLINK_IsHalted()
T0818 001:764.899 - 0.524ms returns FALSE
T0818 001:764.908 JLINK_HasError()
T0818 001:766.377 JLINK_IsHalted()
T0818 001:766.902 - 0.525ms returns FALSE
T0818 001:766.909 JLINK_HasError()
T0818 001:768.374 JLINK_IsHalted()
T0818 001:768.827 - 0.452ms returns FALSE
T0818 001:768.833 JLINK_HasError()
T0818 001:770.375 JLINK_IsHalted()
T0818 001:770.865 - 0.490ms returns FALSE
T0818 001:770.871 JLINK_HasError()
T0818 001:772.376 JLINK_IsHalted()
T0818 001:772.874 - 0.497ms returns FALSE
T0818 001:772.880 JLINK_HasError()
T0818 001:774.384 JLINK_IsHalted()
T0818 001:774.911 - 0.528ms returns FALSE
T0818 001:774.918 JLINK_HasError()
T0818 001:776.377 JLINK_IsHalted()
T0818 001:778.728   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:779.223 - 2.846ms returns TRUE
T0818 001:779.232 JLINK_ReadReg(R15 (PC))
T0818 001:779.237 - 0.005ms returns 0x20000000
T0818 001:779.242 JLINK_ClrBPEx(BPHandle = 0x00000007)
T0818 001:779.246 - 0.003ms returns 0x00
T0818 001:779.251 JLINK_ReadReg(R0)
T0818 001:779.254 - 0.003ms returns 0x00000000
T0818 001:779.656 JLINK_HasError()
T0818 001:779.668 JLINK_WriteReg(R0, 0x0800C000)
T0818 001:779.673 - 0.004ms returns 0
T0818 001:779.677 JLINK_WriteReg(R1, 0x00004000)
T0818 001:779.681 - 0.003ms returns 0
T0818 001:779.685 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:779.688 - 0.003ms returns 0
T0818 001:779.692 JLINK_WriteReg(R3, 0x00000000)
T0818 001:779.696 - 0.003ms returns 0
T0818 001:779.700 JLINK_WriteReg(R4, 0x00000000)
T0818 001:779.703 - 0.003ms returns 0
T0818 001:779.708 JLINK_WriteReg(R5, 0x00000000)
T0818 001:779.711 - 0.003ms returns 0
T0818 001:779.715 JLINK_WriteReg(R6, 0x00000000)
T0818 001:779.718 - 0.003ms returns 0
T0818 001:779.722 JLINK_WriteReg(R7, 0x00000000)
T0818 001:779.726 - 0.003ms returns 0
T0818 001:779.730 JLINK_WriteReg(R8, 0x00000000)
T0818 001:779.733 - 0.003ms returns 0
T0818 001:779.737 JLINK_WriteReg(R9, 0x20000180)
T0818 001:779.741 - 0.003ms returns 0
T0818 001:779.745 JLINK_WriteReg(R10, 0x00000000)
T0818 001:779.748 - 0.003ms returns 0
T0818 001:779.752 JLINK_WriteReg(R11, 0x00000000)
T0818 001:779.755 - 0.003ms returns 0
T0818 001:779.760 JLINK_WriteReg(R12, 0x00000000)
T0818 001:779.763 - 0.003ms returns 0
T0818 001:779.767 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:779.771 - 0.004ms returns 0
T0818 001:779.775 JLINK_WriteReg(R14, 0x20000001)
T0818 001:779.779 - 0.003ms returns 0
T0818 001:779.783 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 001:779.786 - 0.003ms returns 0
T0818 001:779.790 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:779.794 - 0.003ms returns 0
T0818 001:779.798 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:779.801 - 0.003ms returns 0
T0818 001:779.806 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:779.809 - 0.003ms returns 0
T0818 001:779.814 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:779.817 - 0.003ms returns 0
T0818 001:779.821 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:779.826 - 0.004ms returns 0x00000008
T0818 001:779.830 JLINK_Go()
T0818 001:779.839   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:782.627 - 2.797ms 
T0818 001:782.644 JLINK_IsHalted()
T0818 001:784.952   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:785.380 - 2.735ms returns TRUE
T0818 001:785.424 JLINK_ReadReg(R15 (PC))
T0818 001:785.431 - 0.006ms returns 0x20000000
T0818 001:785.435 JLINK_ClrBPEx(BPHandle = 0x00000008)
T0818 001:785.439 - 0.004ms returns 0x00
T0818 001:785.444 JLINK_ReadReg(R0)
T0818 001:785.447 - 0.003ms returns 0x00000001
T0818 001:785.452 JLINK_HasError()
T0818 001:785.456 JLINK_WriteReg(R0, 0x0800C000)
T0818 001:785.460 - 0.003ms returns 0
T0818 001:785.470 JLINK_WriteReg(R1, 0x00004000)
T0818 001:785.477 - 0.007ms returns 0
T0818 001:785.482 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:785.486 - 0.003ms returns 0
T0818 001:785.491 JLINK_WriteReg(R3, 0x00000000)
T0818 001:785.494 - 0.003ms returns 0
T0818 001:785.498 JLINK_WriteReg(R4, 0x00000000)
T0818 001:785.502 - 0.003ms returns 0
T0818 001:785.506 JLINK_WriteReg(R5, 0x00000000)
T0818 001:785.509 - 0.003ms returns 0
T0818 001:785.513 JLINK_WriteReg(R6, 0x00000000)
T0818 001:785.517 - 0.003ms returns 0
T0818 001:785.521 JLINK_WriteReg(R7, 0x00000000)
T0818 001:785.524 - 0.003ms returns 0
T0818 001:785.528 JLINK_WriteReg(R8, 0x00000000)
T0818 001:785.531 - 0.003ms returns 0
T0818 001:785.535 JLINK_WriteReg(R9, 0x20000180)
T0818 001:785.539 - 0.003ms returns 0
T0818 001:785.543 JLINK_WriteReg(R10, 0x00000000)
T0818 001:785.546 - 0.003ms returns 0
T0818 001:785.550 JLINK_WriteReg(R11, 0x00000000)
T0818 001:785.554 - 0.003ms returns 0
T0818 001:785.558 JLINK_WriteReg(R12, 0x00000000)
T0818 001:785.561 - 0.003ms returns 0
T0818 001:785.565 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:785.568 - 0.003ms returns 0
T0818 001:785.573 JLINK_WriteReg(R14, 0x20000001)
T0818 001:785.576 - 0.003ms returns 0
T0818 001:785.580 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 001:785.584 - 0.003ms returns 0
T0818 001:785.588 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:785.591 - 0.003ms returns 0
T0818 001:785.595 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:785.599 - 0.003ms returns 0
T0818 001:785.603 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:785.606 - 0.003ms returns 0
T0818 001:785.610 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:785.614 - 0.003ms returns 0
T0818 001:785.618 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:785.622 - 0.004ms returns 0x00000009
T0818 001:785.626 JLINK_Go()
T0818 001:785.634   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:788.447 - 2.820ms 
T0818 001:788.461 JLINK_IsHalted()
T0818 001:788.956 - 0.494ms returns FALSE
T0818 001:788.962 JLINK_HasError()
T0818 001:790.379 JLINK_IsHalted()
T0818 001:790.870 - 0.490ms returns FALSE
T0818 001:790.878 JLINK_HasError()
T0818 001:792.376 JLINK_IsHalted()
T0818 001:792.830 - 0.454ms returns FALSE
T0818 001:792.840 JLINK_HasError()
T0818 001:794.379 JLINK_IsHalted()
T0818 001:794.868 - 0.489ms returns FALSE
T0818 001:794.877 JLINK_HasError()
T0818 001:796.374 JLINK_IsHalted()
T0818 001:796.867 - 0.493ms returns FALSE
T0818 001:796.874 JLINK_HasError()
T0818 001:798.377 JLINK_IsHalted()
T0818 001:798.824 - 0.447ms returns FALSE
T0818 001:798.830 JLINK_HasError()
T0818 001:800.374 JLINK_IsHalted()
T0818 001:800.898 - 0.524ms returns FALSE
T0818 001:800.905 JLINK_HasError()
T0818 001:802.375 JLINK_IsHalted()
T0818 001:802.824 - 0.448ms returns FALSE
T0818 001:802.831 JLINK_HasError()
T0818 001:804.376 JLINK_IsHalted()
T0818 001:804.823 - 0.447ms returns FALSE
T0818 001:804.830 JLINK_HasError()
T0818 001:807.381 JLINK_IsHalted()
T0818 001:807.899 - 0.518ms returns FALSE
T0818 001:807.906 JLINK_HasError()
T0818 001:809.374 JLINK_IsHalted()
T0818 001:809.866 - 0.491ms returns FALSE
T0818 001:809.873 JLINK_HasError()
T0818 001:811.412 JLINK_IsHalted()
T0818 001:811.826 - 0.413ms returns FALSE
T0818 001:811.839 JLINK_HasError()
T0818 001:813.379 JLINK_IsHalted()
T0818 001:813.830 - 0.450ms returns FALSE
T0818 001:813.839 JLINK_HasError()
T0818 001:815.378 JLINK_IsHalted()
T0818 001:815.872 - 0.493ms returns FALSE
T0818 001:815.879 JLINK_HasError()
T0818 001:817.385 JLINK_IsHalted()
T0818 001:817.859 - 0.474ms returns FALSE
T0818 001:817.867 JLINK_HasError()
T0818 001:819.374 JLINK_IsHalted()
T0818 001:819.878 - 0.503ms returns FALSE
T0818 001:819.892 JLINK_HasError()
T0818 001:821.379 JLINK_IsHalted()
T0818 001:821.824 - 0.445ms returns FALSE
T0818 001:821.832 JLINK_HasError()
T0818 001:823.375 JLINK_IsHalted()
T0818 001:823.867 - 0.492ms returns FALSE
T0818 001:823.875 JLINK_HasError()
T0818 001:825.379 JLINK_IsHalted()
T0818 001:825.903 - 0.524ms returns FALSE
T0818 001:825.915 JLINK_HasError()
T0818 001:827.374 JLINK_IsHalted()
T0818 001:827.865 - 0.490ms returns FALSE
T0818 001:827.871 JLINK_HasError()
T0818 001:829.482 JLINK_IsHalted()
T0818 001:830.002 - 0.519ms returns FALSE
T0818 001:830.008 JLINK_HasError()
T0818 001:831.375 JLINK_IsHalted()
T0818 001:831.865 - 0.490ms returns FALSE
T0818 001:831.871 JLINK_HasError()
T0818 001:833.373 JLINK_IsHalted()
T0818 001:833.825 - 0.451ms returns FALSE
T0818 001:833.830 JLINK_HasError()
T0818 001:835.374 JLINK_IsHalted()
T0818 001:835.865 - 0.491ms returns FALSE
T0818 001:835.871 JLINK_HasError()
T0818 001:837.373 JLINK_IsHalted()
T0818 001:837.864 - 0.491ms returns FALSE
T0818 001:837.874 JLINK_HasError()
T0818 001:839.375 JLINK_IsHalted()
T0818 001:839.851 - 0.476ms returns FALSE
T0818 001:839.858 JLINK_HasError()
T0818 001:841.372 JLINK_IsHalted()
T0818 001:841.870 - 0.498ms returns FALSE
T0818 001:841.876 JLINK_HasError()
T0818 001:843.371 JLINK_IsHalted()
T0818 001:843.820 - 0.449ms returns FALSE
T0818 001:843.826 JLINK_HasError()
T0818 001:845.372 JLINK_IsHalted()
T0818 001:845.865 - 0.492ms returns FALSE
T0818 001:845.871 JLINK_HasError()
T0818 001:847.371 JLINK_IsHalted()
T0818 001:847.863 - 0.491ms returns FALSE
T0818 001:847.868 JLINK_HasError()
T0818 001:849.372 JLINK_IsHalted()
T0818 001:849.896 - 0.524ms returns FALSE
T0818 001:849.904 JLINK_HasError()
T0818 001:851.372 JLINK_IsHalted()
T0818 001:851.871 - 0.498ms returns FALSE
T0818 001:851.877 JLINK_HasError()
T0818 001:853.372 JLINK_IsHalted()
T0818 001:853.869 - 0.497ms returns FALSE
T0818 001:853.874 JLINK_HasError()
T0818 001:855.371 JLINK_IsHalted()
T0818 001:855.858 - 0.486ms returns FALSE
T0818 001:855.863 JLINK_HasError()
T0818 001:857.371 JLINK_IsHalted()
T0818 001:857.863 - 0.491ms returns FALSE
T0818 001:857.868 JLINK_HasError()
T0818 001:859.371 JLINK_IsHalted()
T0818 001:859.864 - 0.492ms returns FALSE
T0818 001:859.870 JLINK_HasError()
T0818 001:861.372 JLINK_IsHalted()
T0818 001:861.832 - 0.460ms returns FALSE
T0818 001:861.838 JLINK_HasError()
T0818 001:863.390 JLINK_IsHalted()
T0818 001:863.855 - 0.465ms returns FALSE
T0818 001:863.861 JLINK_HasError()
T0818 001:865.371 JLINK_IsHalted()
T0818 001:865.869 - 0.497ms returns FALSE
T0818 001:865.878 JLINK_HasError()
T0818 001:867.374 JLINK_IsHalted()
T0818 001:867.823 - 0.449ms returns FALSE
T0818 001:867.829 JLINK_HasError()
T0818 001:869.373 JLINK_IsHalted()
T0818 001:869.873 - 0.499ms returns FALSE
T0818 001:869.880 JLINK_HasError()
T0818 001:871.373 JLINK_IsHalted()
T0818 001:871.827 - 0.453ms returns FALSE
T0818 001:871.832 JLINK_HasError()
T0818 001:873.371 JLINK_IsHalted()
T0818 001:873.820 - 0.448ms returns FALSE
T0818 001:873.826 JLINK_HasError()
T0818 001:875.371 JLINK_IsHalted()
T0818 001:875.864 - 0.492ms returns FALSE
T0818 001:875.869 JLINK_HasError()
T0818 001:877.371 JLINK_IsHalted()
T0818 001:877.864 - 0.492ms returns FALSE
T0818 001:877.869 JLINK_HasError()
T0818 001:879.371 JLINK_IsHalted()
T0818 001:879.863 - 0.492ms returns FALSE
T0818 001:879.869 JLINK_HasError()
T0818 001:881.398 JLINK_IsHalted()
T0818 001:881.900 - 0.501ms returns FALSE
T0818 001:881.910 JLINK_HasError()
T0818 001:883.376 JLINK_IsHalted()
T0818 001:883.896 - 0.519ms returns FALSE
T0818 001:883.904 JLINK_HasError()
T0818 001:885.374 JLINK_IsHalted()
T0818 001:885.853 - 0.478ms returns FALSE
T0818 001:885.860 JLINK_HasError()
T0818 001:887.371 JLINK_IsHalted()
T0818 001:887.871 - 0.499ms returns FALSE
T0818 001:887.876 JLINK_HasError()
T0818 001:889.372 JLINK_IsHalted()
T0818 001:889.910 - 0.537ms returns FALSE
T0818 001:889.916 JLINK_HasError()
T0818 001:891.373 JLINK_IsHalted()
T0818 001:891.851 - 0.477ms returns FALSE
T0818 001:891.856 JLINK_HasError()
T0818 001:893.371 JLINK_IsHalted()
T0818 001:893.820 - 0.448ms returns FALSE
T0818 001:893.826 JLINK_HasError()
T0818 001:895.371 JLINK_IsHalted()
T0818 001:895.864 - 0.493ms returns FALSE
T0818 001:895.870 JLINK_HasError()
T0818 001:897.372 JLINK_IsHalted()
T0818 001:897.958 - 0.585ms returns FALSE
T0818 001:897.967 JLINK_HasError()
T0818 001:899.373 JLINK_IsHalted()
T0818 001:899.851 - 0.477ms returns FALSE
T0818 001:899.857 JLINK_HasError()
T0818 001:901.376 JLINK_IsHalted()
T0818 001:901.855 - 0.479ms returns FALSE
T0818 001:901.862 JLINK_HasError()
T0818 001:903.371 JLINK_IsHalted()
T0818 001:903.821 - 0.449ms returns FALSE
T0818 001:903.826 JLINK_HasError()
T0818 001:905.372 JLINK_IsHalted()
T0818 001:905.831 - 0.459ms returns FALSE
T0818 001:905.837 JLINK_HasError()
T0818 001:907.372 JLINK_IsHalted()
T0818 001:907.870 - 0.497ms returns FALSE
T0818 001:907.875 JLINK_HasError()
T0818 001:909.371 JLINK_IsHalted()
T0818 001:909.863 - 0.491ms returns FALSE
T0818 001:909.873 JLINK_HasError()
T0818 001:911.372 JLINK_IsHalted()
T0818 001:911.822 - 0.449ms returns FALSE
T0818 001:911.828 JLINK_HasError()
T0818 001:913.372 JLINK_IsHalted()
T0818 001:913.867 - 0.494ms returns FALSE
T0818 001:913.884 JLINK_HasError()
T0818 001:916.374 JLINK_IsHalted()
T0818 001:916.866 - 0.491ms returns FALSE
T0818 001:916.872 JLINK_HasError()
T0818 001:918.372 JLINK_IsHalted()
T0818 001:918.821 - 0.449ms returns FALSE
T0818 001:918.827 JLINK_HasError()
T0818 001:920.371 JLINK_IsHalted()
T0818 001:920.820 - 0.448ms returns FALSE
T0818 001:920.825 JLINK_HasError()
T0818 001:922.373 JLINK_IsHalted()
T0818 001:922.864 - 0.491ms returns FALSE
T0818 001:922.870 JLINK_HasError()
T0818 001:924.371 JLINK_IsHalted()
T0818 001:924.863 - 0.491ms returns FALSE
T0818 001:924.868 JLINK_HasError()
T0818 001:926.371 JLINK_IsHalted()
T0818 001:926.864 - 0.493ms returns FALSE
T0818 001:926.870 JLINK_HasError()
T0818 001:928.371 JLINK_IsHalted()
T0818 001:928.833 - 0.461ms returns FALSE
T0818 001:928.839 JLINK_HasError()
T0818 001:930.375 JLINK_IsHalted()
T0818 001:930.821 - 0.446ms returns FALSE
T0818 001:930.828 JLINK_HasError()
T0818 001:932.372 JLINK_IsHalted()
T0818 001:932.863 - 0.491ms returns FALSE
T0818 001:932.869 JLINK_HasError()
T0818 001:934.372 JLINK_IsHalted()
T0818 001:934.864 - 0.492ms returns FALSE
T0818 001:934.870 JLINK_HasError()
T0818 001:936.371 JLINK_IsHalted()
T0818 001:936.834 - 0.462ms returns FALSE
T0818 001:936.839 JLINK_HasError()
T0818 001:938.371 JLINK_IsHalted()
T0818 001:938.870 - 0.498ms returns FALSE
T0818 001:938.875 JLINK_HasError()
T0818 001:940.372 JLINK_IsHalted()
T0818 001:940.864 - 0.491ms returns FALSE
T0818 001:940.870 JLINK_HasError()
T0818 001:942.372 JLINK_IsHalted()
T0818 001:942.865 - 0.493ms returns FALSE
T0818 001:942.871 JLINK_HasError()
T0818 001:944.372 JLINK_IsHalted()
T0818 001:944.862 - 0.490ms returns FALSE
T0818 001:944.868 JLINK_HasError()
T0818 001:946.375 JLINK_IsHalted()
T0818 001:946.827 - 0.452ms returns FALSE
T0818 001:946.834 JLINK_HasError()
T0818 001:948.370 JLINK_IsHalted()
T0818 001:948.818 - 0.447ms returns FALSE
T0818 001:948.824 JLINK_HasError()
T0818 001:950.370 JLINK_IsHalted()
T0818 001:950.819 - 0.448ms returns FALSE
T0818 001:950.825 JLINK_HasError()
T0818 001:952.373 JLINK_IsHalted()
T0818 001:952.830 - 0.457ms returns FALSE
T0818 001:952.840 JLINK_HasError()
T0818 001:954.370 JLINK_IsHalted()
T0818 001:954.861 - 0.490ms returns FALSE
T0818 001:954.867 JLINK_HasError()
T0818 001:956.370 JLINK_IsHalted()
T0818 001:956.820 - 0.450ms returns FALSE
T0818 001:956.825 JLINK_HasError()
T0818 001:958.370 JLINK_IsHalted()
T0818 001:958.818 - 0.448ms returns FALSE
T0818 001:958.824 JLINK_HasError()
T0818 001:960.393 JLINK_IsHalted()
T0818 001:960.849 - 0.456ms returns FALSE
T0818 001:960.855 JLINK_HasError()
T0818 001:962.377 JLINK_IsHalted()
T0818 001:962.835 - 0.457ms returns FALSE
T0818 001:962.842 JLINK_HasError()
T0818 001:965.375 JLINK_IsHalted()
T0818 001:965.827 - 0.452ms returns FALSE
T0818 001:965.835 JLINK_HasError()
T0818 001:967.370 JLINK_IsHalted()
T0818 001:967.818 - 0.448ms returns FALSE
T0818 001:967.824 JLINK_HasError()
T0818 001:969.370 JLINK_IsHalted()
T0818 001:969.818 - 0.448ms returns FALSE
T0818 001:969.824 JLINK_HasError()
T0818 001:971.373 JLINK_IsHalted()
T0818 001:971.852 - 0.478ms returns FALSE
T0818 001:971.858 JLINK_HasError()
T0818 001:973.370 JLINK_IsHalted()
T0818 001:973.870 - 0.500ms returns FALSE
T0818 001:973.876 JLINK_HasError()
T0818 001:975.395 JLINK_IsHalted()
T0818 001:975.862 - 0.466ms returns FALSE
T0818 001:975.867 JLINK_HasError()
T0818 001:977.380 JLINK_IsHalted()
T0818 001:978.827 - 1.446ms returns FALSE
T0818 001:978.838 JLINK_HasError()
T0818 001:980.370 JLINK_IsHalted()
T0818 001:980.868 - 0.497ms returns FALSE
T0818 001:980.873 JLINK_HasError()
T0818 001:982.372 JLINK_IsHalted()
T0818 001:982.827 - 0.455ms returns FALSE
T0818 001:982.834 JLINK_HasError()
T0818 001:984.370 JLINK_IsHalted()
T0818 001:984.861 - 0.491ms returns FALSE
T0818 001:984.867 JLINK_HasError()
T0818 001:986.371 JLINK_IsHalted()
T0818 001:986.861 - 0.490ms returns FALSE
T0818 001:986.867 JLINK_HasError()
T0818 001:988.370 JLINK_IsHalted()
T0818 001:988.864 - 0.494ms returns FALSE
T0818 001:988.870 JLINK_HasError()
T0818 001:990.394 JLINK_IsHalted()
T0818 001:990.906 - 0.512ms returns FALSE
T0818 001:990.912 JLINK_HasError()
T0818 001:992.388 JLINK_IsHalted()
T0818 001:992.909 - 0.521ms returns FALSE
T0818 001:992.917 JLINK_HasError()
T0818 001:994.370 JLINK_IsHalted()
T0818 001:994.819 - 0.448ms returns FALSE
T0818 001:994.825 JLINK_HasError()
T0818 001:996.373 JLINK_IsHalted()
T0818 001:996.867 - 0.494ms returns FALSE
T0818 001:996.873 JLINK_HasError()
T0818 001:998.370 JLINK_IsHalted()
T0818 001:998.870 - 0.500ms returns FALSE
T0818 001:998.876 JLINK_HasError()
T0818 002:000.370 JLINK_IsHalted()
T0818 002:000.870 - 0.499ms returns FALSE
T0818 002:000.876 JLINK_HasError()
T0818 002:002.371 JLINK_IsHalted()
T0818 002:002.863 - 0.491ms returns FALSE
T0818 002:002.873 JLINK_HasError()
T0818 002:004.370 JLINK_IsHalted()
T0818 002:004.868 - 0.498ms returns FALSE
T0818 002:004.874 JLINK_HasError()
T0818 002:006.376 JLINK_IsHalted()
T0818 002:006.864 - 0.487ms returns FALSE
T0818 002:006.870 JLINK_HasError()
T0818 002:008.372 JLINK_IsHalted()
T0818 002:008.855 - 0.483ms returns FALSE
T0818 002:008.864 JLINK_HasError()
T0818 002:012.468 JLINK_IsHalted()
T0818 002:012.990 - 0.521ms returns FALSE
T0818 002:012.997 JLINK_HasError()
T0818 002:014.372 JLINK_IsHalted()
T0818 002:014.821 - 0.449ms returns FALSE
T0818 002:014.826 JLINK_HasError()
T0818 002:016.372 JLINK_IsHalted()
T0818 002:016.820 - 0.448ms returns FALSE
T0818 002:016.825 JLINK_HasError()
T0818 002:018.372 JLINK_IsHalted()
T0818 002:018.868 - 0.495ms returns FALSE
T0818 002:018.873 JLINK_HasError()
T0818 002:020.372 JLINK_IsHalted()
T0818 002:020.859 - 0.487ms returns FALSE
T0818 002:020.867 JLINK_HasError()
T0818 002:022.372 JLINK_IsHalted()
T0818 002:022.865 - 0.492ms returns FALSE
T0818 002:022.871 JLINK_HasError()
T0818 002:024.372 JLINK_IsHalted()
T0818 002:024.864 - 0.491ms returns FALSE
T0818 002:024.871 JLINK_HasError()
T0818 002:026.372 JLINK_IsHalted()
T0818 002:026.834 - 0.462ms returns FALSE
T0818 002:026.840 JLINK_HasError()
T0818 002:028.371 JLINK_IsHalted()
T0818 002:028.863 - 0.491ms returns FALSE
T0818 002:028.869 JLINK_HasError()
T0818 002:030.371 JLINK_IsHalted()
T0818 002:030.866 - 0.494ms returns FALSE
T0818 002:030.871 JLINK_HasError()
T0818 002:032.372 JLINK_IsHalted()
T0818 002:032.833 - 0.461ms returns FALSE
T0818 002:032.838 JLINK_HasError()
T0818 002:034.371 JLINK_IsHalted()
T0818 002:034.863 - 0.491ms returns FALSE
T0818 002:034.868 JLINK_HasError()
T0818 002:036.371 JLINK_IsHalted()
T0818 002:036.820 - 0.449ms returns FALSE
T0818 002:036.826 JLINK_HasError()
T0818 002:038.375 JLINK_IsHalted()
T0818 002:038.864 - 0.488ms returns FALSE
T0818 002:038.870 JLINK_HasError()
T0818 002:040.372 JLINK_IsHalted()
T0818 002:040.963 - 0.590ms returns FALSE
T0818 002:041.158 JLINK_HasError()
T0818 002:042.373 JLINK_IsHalted()
T0818 002:042.864 - 0.491ms returns FALSE
T0818 002:042.870 JLINK_HasError()
T0818 002:044.371 JLINK_IsHalted()
T0818 002:044.821 - 0.449ms returns FALSE
T0818 002:044.831 JLINK_HasError()
T0818 002:046.371 JLINK_IsHalted()
T0818 002:046.822 - 0.450ms returns FALSE
T0818 002:046.828 JLINK_HasError()
T0818 002:048.372 JLINK_IsHalted()
T0818 002:048.864 - 0.492ms returns FALSE
T0818 002:048.869 JLINK_HasError()
T0818 002:050.372 JLINK_IsHalted()
T0818 002:050.853 - 0.480ms returns FALSE
T0818 002:050.859 JLINK_HasError()
T0818 002:052.372 JLINK_IsHalted()
T0818 002:052.870 - 0.498ms returns FALSE
T0818 002:052.876 JLINK_HasError()
T0818 002:054.372 JLINK_IsHalted()
T0818 002:054.821 - 0.449ms returns FALSE
T0818 002:054.827 JLINK_HasError()
T0818 002:056.372 JLINK_IsHalted()
T0818 002:056.831 - 0.459ms returns FALSE
T0818 002:056.842 JLINK_HasError()
T0818 002:059.373 JLINK_IsHalted()
T0818 002:059.864 - 0.491ms returns FALSE
T0818 002:059.870 JLINK_HasError()
T0818 002:061.373 JLINK_IsHalted()
T0818 002:061.822 - 0.448ms returns FALSE
T0818 002:061.828 JLINK_HasError()
T0818 002:063.371 JLINK_IsHalted()
T0818 002:063.820 - 0.448ms returns FALSE
T0818 002:063.825 JLINK_HasError()
T0818 002:065.372 JLINK_IsHalted()
T0818 002:065.864 - 0.491ms returns FALSE
T0818 002:065.879 JLINK_HasError()
T0818 002:067.370 JLINK_IsHalted()
T0818 002:067.862 - 0.491ms returns FALSE
T0818 002:067.868 JLINK_HasError()
T0818 002:069.373 JLINK_IsHalted()
T0818 002:069.872 - 0.498ms returns FALSE
T0818 002:069.878 JLINK_HasError()
T0818 002:071.372 JLINK_IsHalted()
T0818 002:071.856 - 0.483ms returns FALSE
T0818 002:071.861 JLINK_HasError()
T0818 002:073.375 JLINK_IsHalted()
T0818 002:073.821 - 0.445ms returns FALSE
T0818 002:073.828 JLINK_HasError()
T0818 002:075.370 JLINK_IsHalted()
T0818 002:075.861 - 0.490ms returns FALSE
T0818 002:075.867 JLINK_HasError()
T0818 002:077.370 JLINK_IsHalted()
T0818 002:077.864 - 0.493ms returns FALSE
T0818 002:077.870 JLINK_HasError()
T0818 002:079.372 JLINK_IsHalted()
T0818 002:079.818 - 0.446ms returns FALSE
T0818 002:079.824 JLINK_HasError()
T0818 002:081.373 JLINK_IsHalted()
T0818 002:081.820 - 0.447ms returns FALSE
T0818 002:081.826 JLINK_HasError()
T0818 002:083.370 JLINK_IsHalted()
T0818 002:083.830 - 0.460ms returns FALSE
T0818 002:083.836 JLINK_HasError()
T0818 002:085.370 JLINK_IsHalted()
T0818 002:085.861 - 0.491ms returns FALSE
T0818 002:085.866 JLINK_HasError()
T0818 002:087.370 JLINK_IsHalted()
T0818 002:087.868 - 0.498ms returns FALSE
T0818 002:087.873 JLINK_HasError()
T0818 002:089.379 JLINK_IsHalted()
T0818 002:089.871 - 0.492ms returns FALSE
T0818 002:089.877 JLINK_HasError()
T0818 002:091.372 JLINK_IsHalted()
T0818 002:091.829 - 0.457ms returns FALSE
T0818 002:091.838 JLINK_HasError()
T0818 002:093.374 JLINK_IsHalted()
T0818 002:093.822 - 0.447ms returns FALSE
T0818 002:093.831 JLINK_HasError()
T0818 002:095.400 JLINK_IsHalted()
T0818 002:096.003 - 0.601ms returns FALSE
T0818 002:096.014 JLINK_HasError()
T0818 002:097.376 JLINK_IsHalted()
T0818 002:097.872 - 0.495ms returns FALSE
T0818 002:097.877 JLINK_HasError()
T0818 002:099.373 JLINK_IsHalted()
T0818 002:099.828 - 0.454ms returns FALSE
T0818 002:099.835 JLINK_HasError()
T0818 002:101.374 JLINK_IsHalted()
T0818 002:101.871 - 0.497ms returns FALSE
T0818 002:101.877 JLINK_HasError()
T0818 002:103.372 JLINK_IsHalted()
T0818 002:103.863 - 0.491ms returns FALSE
T0818 002:103.869 JLINK_HasError()
T0818 002:105.374 JLINK_IsHalted()
T0818 002:105.821 - 0.447ms returns FALSE
T0818 002:105.827 JLINK_HasError()
T0818 002:107.371 JLINK_IsHalted()
T0818 002:107.865 - 0.493ms returns FALSE
T0818 002:107.870 JLINK_HasError()
T0818 002:109.371 JLINK_IsHalted()
T0818 002:109.821 - 0.449ms returns FALSE
T0818 002:109.826 JLINK_HasError()
T0818 002:111.379 JLINK_IsHalted()
T0818 002:111.865 - 0.486ms returns FALSE
T0818 002:111.872 JLINK_HasError()
T0818 002:113.371 JLINK_IsHalted()
T0818 002:113.864 - 0.492ms returns FALSE
T0818 002:113.869 JLINK_HasError()
T0818 002:115.371 JLINK_IsHalted()
T0818 002:115.863 - 0.491ms returns FALSE
T0818 002:115.868 JLINK_HasError()
T0818 002:117.372 JLINK_IsHalted()
T0818 002:117.864 - 0.491ms returns FALSE
T0818 002:117.869 JLINK_HasError()
T0818 002:119.371 JLINK_IsHalted()
T0818 002:119.833 - 0.461ms returns FALSE
T0818 002:119.838 JLINK_HasError()
T0818 002:121.375 JLINK_IsHalted()
T0818 002:121.910 - 0.535ms returns FALSE
T0818 002:121.916 JLINK_HasError()
T0818 002:124.373 JLINK_IsHalted()
T0818 002:124.866 - 0.492ms returns FALSE
T0818 002:124.872 JLINK_HasError()
T0818 002:126.372 JLINK_IsHalted()
T0818 002:126.833 - 0.461ms returns FALSE
T0818 002:126.838 JLINK_HasError()
T0818 002:128.371 JLINK_IsHalted()
T0818 002:128.822 - 0.450ms returns FALSE
T0818 002:128.827 JLINK_HasError()
T0818 002:130.371 JLINK_IsHalted()
T0818 002:130.863 - 0.491ms returns FALSE
T0818 002:130.868 JLINK_HasError()
T0818 002:132.375 JLINK_IsHalted()
T0818 002:132.853 - 0.478ms returns FALSE
T0818 002:132.860 JLINK_HasError()
T0818 002:134.372 JLINK_IsHalted()
T0818 002:134.870 - 0.498ms returns FALSE
T0818 002:134.876 JLINK_HasError()
T0818 002:136.374 JLINK_IsHalted()
T0818 002:136.956 - 0.582ms returns FALSE
T0818 002:136.967 JLINK_HasError()
T0818 002:138.352 JLINK_IsHalted()
T0818 002:138.866 - 0.514ms returns FALSE
T0818 002:138.872 JLINK_HasError()
T0818 002:140.387 JLINK_IsHalted()
T0818 002:140.856 - 0.468ms returns FALSE
T0818 002:140.861 JLINK_HasError()
T0818 002:141.918 JLINK_IsHalted()
T0818 002:142.411 - 0.492ms returns FALSE
T0818 002:142.417 JLINK_HasError()
T0818 002:143.457 JLINK_IsHalted()
T0818 002:143.959 - 0.502ms returns FALSE
T0818 002:143.965 JLINK_HasError()
T0818 002:145.488 JLINK_IsHalted()
T0818 002:146.004 - 0.515ms returns FALSE
T0818 002:146.017 JLINK_HasError()
T0818 002:147.522 JLINK_IsHalted()
T0818 002:148.013 - 0.491ms returns FALSE
T0818 002:148.019 JLINK_HasError()
T0818 002:150.031 JLINK_IsHalted()
T0818 002:150.513 - 0.481ms returns FALSE
T0818 002:150.518 JLINK_HasError()
T0818 002:152.031 JLINK_IsHalted()
T0818 002:152.538 - 0.506ms returns FALSE
T0818 002:152.550 JLINK_HasError()
T0818 002:154.036 JLINK_IsHalted()
T0818 002:154.540 - 0.503ms returns FALSE
T0818 002:154.546 JLINK_HasError()
T0818 002:156.031 JLINK_IsHalted()
T0818 002:156.512 - 0.480ms returns FALSE
T0818 002:156.518 JLINK_HasError()
T0818 002:158.030 JLINK_IsHalted()
T0818 002:158.514 - 0.483ms returns FALSE
T0818 002:158.519 JLINK_HasError()
T0818 002:160.030 JLINK_IsHalted()
T0818 002:160.490 - 0.459ms returns FALSE
T0818 002:160.496 JLINK_HasError()
T0818 002:162.032 JLINK_IsHalted()
T0818 002:162.491 - 0.459ms returns FALSE
T0818 002:162.496 JLINK_HasError()
T0818 002:164.035 JLINK_IsHalted()
T0818 002:164.513 - 0.478ms returns FALSE
T0818 002:164.520 JLINK_HasError()
T0818 002:166.030 JLINK_IsHalted()
T0818 002:166.499 - 0.468ms returns FALSE
T0818 002:166.505 JLINK_HasError()
T0818 002:168.032 JLINK_IsHalted()
T0818 002:168.498 - 0.465ms returns FALSE
T0818 002:168.504 JLINK_HasError()
T0818 002:170.032 JLINK_IsHalted()
T0818 002:170.519 - 0.487ms returns FALSE
T0818 002:170.525 JLINK_HasError()
T0818 002:172.031 JLINK_IsHalted()
T0818 002:172.536 - 0.505ms returns FALSE
T0818 002:172.548 JLINK_HasError()
T0818 002:174.031 JLINK_IsHalted()
T0818 002:174.516 - 0.485ms returns FALSE
T0818 002:174.522 JLINK_HasError()
T0818 002:176.031 JLINK_IsHalted()
T0818 002:176.512 - 0.480ms returns FALSE
T0818 002:176.517 JLINK_HasError()
T0818 002:178.030 JLINK_IsHalted()
T0818 002:178.514 - 0.483ms returns FALSE
T0818 002:178.520 JLINK_HasError()
T0818 002:180.030 JLINK_IsHalted()
T0818 002:180.490 - 0.459ms returns FALSE
T0818 002:180.495 JLINK_HasError()
T0818 002:182.031 JLINK_IsHalted()
T0818 002:182.536 - 0.504ms returns FALSE
T0818 002:182.541 JLINK_HasError()
T0818 002:184.032 JLINK_IsHalted()
T0818 002:184.550 - 0.517ms returns FALSE
T0818 002:184.556 JLINK_HasError()
T0818 002:186.031 JLINK_IsHalted()
T0818 002:186.511 - 0.479ms returns FALSE
T0818 002:186.517 JLINK_HasError()
T0818 002:188.028 JLINK_IsHalted()
T0818 002:188.513 - 0.484ms returns FALSE
T0818 002:188.521 JLINK_HasError()
T0818 002:190.029 JLINK_IsHalted()
T0818 002:190.487 - 0.457ms returns FALSE
T0818 002:190.493 JLINK_HasError()
T0818 002:192.033 JLINK_IsHalted()
T0818 002:192.519 - 0.486ms returns FALSE
T0818 002:192.528 JLINK_HasError()
T0818 002:194.029 JLINK_IsHalted()
T0818 002:194.492 - 0.463ms returns FALSE
T0818 002:194.498 JLINK_HasError()
T0818 002:196.029 JLINK_IsHalted()
T0818 002:196.491 - 0.462ms returns FALSE
T0818 002:196.497 JLINK_HasError()
T0818 002:198.029 JLINK_IsHalted()
T0818 002:198.493 - 0.463ms returns FALSE
T0818 002:198.498 JLINK_HasError()
T0818 002:200.048 JLINK_IsHalted()
T0818 002:200.521 - 0.472ms returns FALSE
T0818 002:200.530 JLINK_HasError()
T0818 002:202.032 JLINK_IsHalted()
T0818 002:204.353   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:204.831 - 2.799ms returns TRUE
T0818 002:204.837 JLINK_ReadReg(R15 (PC))
T0818 002:204.842 - 0.004ms returns 0x20000000
T0818 002:204.846 JLINK_ClrBPEx(BPHandle = 0x00000009)
T0818 002:204.851 - 0.004ms returns 0x00
T0818 002:204.855 JLINK_ReadReg(R0)
T0818 002:204.859 - 0.003ms returns 0x00000000
T0818 002:205.152 JLINK_HasError()
T0818 002:205.161 JLINK_WriteReg(R0, 0x00000001)
T0818 002:205.166 - 0.005ms returns 0
T0818 002:205.170 JLINK_WriteReg(R1, 0x00004000)
T0818 002:205.173 - 0.003ms returns 0
T0818 002:205.178 JLINK_WriteReg(R2, 0x000000FF)
T0818 002:205.181 - 0.003ms returns 0
T0818 002:205.185 JLINK_WriteReg(R3, 0x00000000)
T0818 002:205.188 - 0.003ms returns 0
T0818 002:205.192 JLINK_WriteReg(R4, 0x00000000)
T0818 002:205.196 - 0.003ms returns 0
T0818 002:205.200 JLINK_WriteReg(R5, 0x00000000)
T0818 002:205.204 - 0.004ms returns 0
T0818 002:205.210 JLINK_WriteReg(R6, 0x00000000)
T0818 002:205.213 - 0.003ms returns 0
T0818 002:205.217 JLINK_WriteReg(R7, 0x00000000)
T0818 002:205.221 - 0.003ms returns 0
T0818 002:205.225 JLINK_WriteReg(R8, 0x00000000)
T0818 002:205.228 - 0.003ms returns 0
T0818 002:205.232 JLINK_WriteReg(R9, 0x20000180)
T0818 002:205.236 - 0.003ms returns 0
T0818 002:205.240 JLINK_WriteReg(R10, 0x00000000)
T0818 002:205.243 - 0.003ms returns 0
T0818 002:205.247 JLINK_WriteReg(R11, 0x00000000)
T0818 002:205.251 - 0.003ms returns 0
T0818 002:205.255 JLINK_WriteReg(R12, 0x00000000)
T0818 002:205.258 - 0.003ms returns 0
T0818 002:205.263 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:205.266 - 0.004ms returns 0
T0818 002:205.270 JLINK_WriteReg(R14, 0x20000001)
T0818 002:205.274 - 0.003ms returns 0
T0818 002:205.278 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 002:205.282 - 0.003ms returns 0
T0818 002:205.286 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:205.289 - 0.003ms returns 0
T0818 002:205.293 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:205.296 - 0.003ms returns 0
T0818 002:205.300 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:205.304 - 0.003ms returns 0
T0818 002:205.308 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:205.311 - 0.003ms returns 0
T0818 002:205.316 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:205.320 - 0.004ms returns 0x0000000A
T0818 002:205.324 JLINK_Go()
T0818 002:205.332   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:208.049 - 2.724ms 
T0818 002:208.056 JLINK_IsHalted()
T0818 002:210.439   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:210.909 - 2.852ms returns TRUE
T0818 002:210.915 JLINK_ReadReg(R15 (PC))
T0818 002:210.920 - 0.004ms returns 0x20000000
T0818 002:210.924 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T0818 002:210.928 - 0.003ms returns 0x00
T0818 002:210.932 JLINK_ReadReg(R0)
T0818 002:210.936 - 0.003ms returns 0x00000000
T0818 002:265.518 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 002:265.531   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 002:265.547   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 002:267.434 - 1.916ms returns 0x184
T0818 002:267.468 JLINK_HasError()
T0818 002:267.474 JLINK_WriteReg(R0, 0x08000000)
T0818 002:267.480 - 0.006ms returns 0
T0818 002:267.484 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 002:267.488 - 0.003ms returns 0
T0818 002:267.492 JLINK_WriteReg(R2, 0x00000002)
T0818 002:267.501 - 0.008ms returns 0
T0818 002:267.505 JLINK_WriteReg(R3, 0x00000000)
T0818 002:267.508 - 0.003ms returns 0
T0818 002:267.513 JLINK_WriteReg(R4, 0x00000000)
T0818 002:267.516 - 0.003ms returns 0
T0818 002:267.520 JLINK_WriteReg(R5, 0x00000000)
T0818 002:267.523 - 0.003ms returns 0
T0818 002:267.527 JLINK_WriteReg(R6, 0x00000000)
T0818 002:267.531 - 0.003ms returns 0
T0818 002:267.535 JLINK_WriteReg(R7, 0x00000000)
T0818 002:267.538 - 0.003ms returns 0
T0818 002:267.542 JLINK_WriteReg(R8, 0x00000000)
T0818 002:267.546 - 0.003ms returns 0
T0818 002:267.550 JLINK_WriteReg(R9, 0x20000180)
T0818 002:267.554 - 0.004ms returns 0
T0818 002:267.559 JLINK_WriteReg(R10, 0x00000000)
T0818 002:267.563 - 0.003ms returns 0
T0818 002:267.567 JLINK_WriteReg(R11, 0x00000000)
T0818 002:267.570 - 0.003ms returns 0
T0818 002:267.574 JLINK_WriteReg(R12, 0x00000000)
T0818 002:267.577 - 0.003ms returns 0
T0818 002:267.581 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:267.585 - 0.004ms returns 0
T0818 002:267.589 JLINK_WriteReg(R14, 0x20000001)
T0818 002:267.593 - 0.003ms returns 0
T0818 002:267.597 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 002:267.600 - 0.003ms returns 0
T0818 002:267.604 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:267.608 - 0.003ms returns 0
T0818 002:267.612 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:267.615 - 0.003ms returns 0
T0818 002:267.619 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:267.622 - 0.003ms returns 0
T0818 002:267.627 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:267.630 - 0.003ms returns 0
T0818 002:267.635 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:267.642   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:268.142 - 0.507ms returns 0x0000000B
T0818 002:268.152 JLINK_Go()
T0818 002:268.157   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 002:268.666   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:271.586 - 3.433ms 
T0818 002:271.601 JLINK_IsHalted()
T0818 002:273.932   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:274.445 - 2.843ms returns TRUE
T0818 002:274.454 JLINK_ReadReg(R15 (PC))
T0818 002:274.459 - 0.005ms returns 0x20000000
T0818 002:274.485 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T0818 002:274.490 - 0.005ms returns 0x00
T0818 002:274.494 JLINK_ReadReg(R0)
T0818 002:274.498 - 0.003ms returns 0x00000000
T0818 002:274.677 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:274.683   Data:  88 17 00 20 C1 01 00 08 CD 28 00 08 B1 25 00 08 ...
T0818 002:274.694   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:277.273 - 2.596ms returns 0x27C
T0818 002:277.283 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:277.287   Data:  00 41 23 F0 00 45 B8 EB 02 00 A9 41 05 D2 40 46 ...
T0818 002:277.295   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:279.166 - 1.882ms returns 0x184
T0818 002:279.176 JLINK_HasError()
T0818 002:279.182 JLINK_WriteReg(R0, 0x08000000)
T0818 002:279.187 - 0.004ms returns 0
T0818 002:279.191 JLINK_WriteReg(R1, 0x00000400)
T0818 002:279.194 - 0.003ms returns 0
T0818 002:279.198 JLINK_WriteReg(R2, 0x20000184)
T0818 002:279.202 - 0.003ms returns 0
T0818 002:279.206 JLINK_WriteReg(R3, 0x00000000)
T0818 002:279.209 - 0.003ms returns 0
T0818 002:279.213 JLINK_WriteReg(R4, 0x00000000)
T0818 002:279.217 - 0.003ms returns 0
T0818 002:279.221 JLINK_WriteReg(R5, 0x00000000)
T0818 002:279.224 - 0.003ms returns 0
T0818 002:279.228 JLINK_WriteReg(R6, 0x00000000)
T0818 002:279.232 - 0.003ms returns 0
T0818 002:279.236 JLINK_WriteReg(R7, 0x00000000)
T0818 002:279.239 - 0.003ms returns 0
T0818 002:279.243 JLINK_WriteReg(R8, 0x00000000)
T0818 002:279.247 - 0.003ms returns 0
T0818 002:279.251 JLINK_WriteReg(R9, 0x20000180)
T0818 002:279.254 - 0.003ms returns 0
T0818 002:279.258 JLINK_WriteReg(R10, 0x00000000)
T0818 002:279.261 - 0.003ms returns 0
T0818 002:279.265 JLINK_WriteReg(R11, 0x00000000)
T0818 002:279.269 - 0.003ms returns 0
T0818 002:279.273 JLINK_WriteReg(R12, 0x00000000)
T0818 002:279.276 - 0.003ms returns 0
T0818 002:279.280 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:279.316 - 0.035ms returns 0
T0818 002:279.322 JLINK_WriteReg(R14, 0x20000001)
T0818 002:279.325 - 0.003ms returns 0
T0818 002:279.329 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:279.333 - 0.003ms returns 0
T0818 002:279.337 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:279.351 - 0.014ms returns 0
T0818 002:279.356 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:279.359 - 0.003ms returns 0
T0818 002:279.363 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:279.366 - 0.003ms returns 0
T0818 002:279.370 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:279.374 - 0.003ms returns 0
T0818 002:279.378 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:279.382 - 0.004ms returns 0x0000000C
T0818 002:279.386 JLINK_Go()
T0818 002:279.394   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:282.230 - 2.843ms 
T0818 002:282.242 JLINK_IsHalted()
T0818 002:282.727 - 0.484ms returns FALSE
T0818 002:282.732 JLINK_HasError()
T0818 002:286.033 JLINK_IsHalted()
T0818 002:286.470 - 0.437ms returns FALSE
T0818 002:286.476 JLINK_HasError()
T0818 002:288.031 JLINK_IsHalted()
T0818 002:290.308   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:290.821 - 2.790ms returns TRUE
T0818 002:290.827 JLINK_ReadReg(R15 (PC))
T0818 002:290.832 - 0.004ms returns 0x20000000
T0818 002:290.836 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T0818 002:290.840 - 0.003ms returns 0x00
T0818 002:290.844 JLINK_ReadReg(R0)
T0818 002:290.848 - 0.003ms returns 0x00000000
T0818 002:291.304 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:291.318   Data:  80 13 01 FB 02 44 00 FB 03 4E 84 0A 97 0A 44 EA ...
T0818 002:291.331   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:293.898 - 2.594ms returns 0x27C
T0818 002:293.910 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:293.914   Data:  49 00 08 BF 20 F0 01 00 10 BC 70 47 10 B5 41 00 ...
T0818 002:293.923   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:295.836 - 1.925ms returns 0x184
T0818 002:295.850 JLINK_HasError()
T0818 002:295.855 JLINK_WriteReg(R0, 0x08000400)
T0818 002:295.860 - 0.005ms returns 0
T0818 002:295.864 JLINK_WriteReg(R1, 0x00000400)
T0818 002:295.868 - 0.003ms returns 0
T0818 002:295.872 JLINK_WriteReg(R2, 0x20000184)
T0818 002:295.876 - 0.003ms returns 0
T0818 002:295.880 JLINK_WriteReg(R3, 0x00000000)
T0818 002:295.883 - 0.003ms returns 0
T0818 002:295.887 JLINK_WriteReg(R4, 0x00000000)
T0818 002:295.890 - 0.003ms returns 0
T0818 002:295.894 JLINK_WriteReg(R5, 0x00000000)
T0818 002:295.898 - 0.003ms returns 0
T0818 002:295.902 JLINK_WriteReg(R6, 0x00000000)
T0818 002:295.905 - 0.003ms returns 0
T0818 002:295.909 JLINK_WriteReg(R7, 0x00000000)
T0818 002:295.913 - 0.003ms returns 0
T0818 002:295.917 JLINK_WriteReg(R8, 0x00000000)
T0818 002:295.920 - 0.003ms returns 0
T0818 002:295.924 JLINK_WriteReg(R9, 0x20000180)
T0818 002:295.928 - 0.003ms returns 0
T0818 002:295.932 JLINK_WriteReg(R10, 0x00000000)
T0818 002:295.935 - 0.003ms returns 0
T0818 002:295.939 JLINK_WriteReg(R11, 0x00000000)
T0818 002:295.942 - 0.003ms returns 0
T0818 002:295.946 JLINK_WriteReg(R12, 0x00000000)
T0818 002:295.950 - 0.003ms returns 0
T0818 002:295.954 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:295.957 - 0.003ms returns 0
T0818 002:295.961 JLINK_WriteReg(R14, 0x20000001)
T0818 002:295.965 - 0.003ms returns 0
T0818 002:295.969 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:295.972 - 0.003ms returns 0
T0818 002:295.976 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:295.980 - 0.003ms returns 0
T0818 002:295.984 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:295.987 - 0.003ms returns 0
T0818 002:295.991 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:295.994 - 0.003ms returns 0
T0818 002:295.999 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:296.002 - 0.003ms returns 0
T0818 002:296.007 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:296.011 - 0.004ms returns 0x0000000D
T0818 002:296.015 JLINK_Go()
T0818 002:296.023   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:298.828 - 2.812ms 
T0818 002:298.834 JLINK_IsHalted()
T0818 002:299.322 - 0.488ms returns FALSE
T0818 002:299.365 JLINK_HasError()
T0818 002:301.076 JLINK_IsHalted()
T0818 002:301.708 - 0.630ms returns FALSE
T0818 002:301.715 JLINK_HasError()
T0818 002:303.032 JLINK_IsHalted()
T0818 002:305.353   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:305.822 - 2.790ms returns TRUE
T0818 002:305.828 JLINK_ReadReg(R15 (PC))
T0818 002:305.832 - 0.004ms returns 0x20000000
T0818 002:305.836 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T0818 002:305.840 - 0.003ms returns 0x00
T0818 002:305.844 JLINK_ReadReg(R0)
T0818 002:305.848 - 0.003ms returns 0x00000000
T0818 002:306.270 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:306.281   Data:  05 03 06 43 4C EA 01 0C 49 08 4F EA 30 00 92 18 ...
T0818 002:306.292   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:308.912 - 2.642ms returns 0x27C
T0818 002:308.923 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:308.927   Data:  00 F0 F8 F8 00 20 00 F0 F5 F8 20 46 4F F4 00 51 ...
T0818 002:308.936   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:310.832 - 1.908ms returns 0x184
T0818 002:310.841 JLINK_HasError()
T0818 002:310.846 JLINK_WriteReg(R0, 0x08000800)
T0818 002:310.851 - 0.004ms returns 0
T0818 002:310.855 JLINK_WriteReg(R1, 0x00000400)
T0818 002:310.858 - 0.003ms returns 0
T0818 002:310.863 JLINK_WriteReg(R2, 0x20000184)
T0818 002:310.866 - 0.003ms returns 0
T0818 002:310.870 JLINK_WriteReg(R3, 0x00000000)
T0818 002:310.873 - 0.003ms returns 0
T0818 002:310.877 JLINK_WriteReg(R4, 0x00000000)
T0818 002:310.881 - 0.003ms returns 0
T0818 002:310.885 JLINK_WriteReg(R5, 0x00000000)
T0818 002:310.888 - 0.003ms returns 0
T0818 002:310.892 JLINK_WriteReg(R6, 0x00000000)
T0818 002:310.895 - 0.003ms returns 0
T0818 002:310.899 JLINK_WriteReg(R7, 0x00000000)
T0818 002:310.903 - 0.003ms returns 0
T0818 002:310.907 JLINK_WriteReg(R8, 0x00000000)
T0818 002:310.910 - 0.003ms returns 0
T0818 002:310.914 JLINK_WriteReg(R9, 0x20000180)
T0818 002:310.917 - 0.003ms returns 0
T0818 002:310.921 JLINK_WriteReg(R10, 0x00000000)
T0818 002:310.925 - 0.003ms returns 0
T0818 002:310.929 JLINK_WriteReg(R11, 0x00000000)
T0818 002:310.932 - 0.003ms returns 0
T0818 002:310.936 JLINK_WriteReg(R12, 0x00000000)
T0818 002:310.940 - 0.003ms returns 0
T0818 002:310.944 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:310.948 - 0.003ms returns 0
T0818 002:310.952 JLINK_WriteReg(R14, 0x20000001)
T0818 002:310.955 - 0.003ms returns 0
T0818 002:310.959 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:311.071 - 0.111ms returns 0
T0818 002:311.081 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:311.085 - 0.004ms returns 0
T0818 002:311.089 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:311.092 - 0.003ms returns 0
T0818 002:311.096 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:311.100 - 0.003ms returns 0
T0818 002:311.104 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:311.107 - 0.003ms returns 0
T0818 002:311.112 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:311.116 - 0.004ms returns 0x0000000E
T0818 002:311.120 JLINK_Go()
T0818 002:311.128   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:313.915 - 2.794ms 
T0818 002:313.926 JLINK_IsHalted()
T0818 002:314.395 - 0.469ms returns FALSE
T0818 002:314.401 JLINK_HasError()
T0818 002:316.031 JLINK_IsHalted()
T0818 002:316.473 - 0.442ms returns FALSE
T0818 002:316.479 JLINK_HasError()
T0818 002:318.030 JLINK_IsHalted()
T0818 002:320.352   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:320.821 - 2.790ms returns TRUE
T0818 002:320.827 JLINK_ReadReg(R15 (PC))
T0818 002:320.831 - 0.004ms returns 0x20000000
T0818 002:320.836 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T0818 002:320.839 - 0.003ms returns 0x00
T0818 002:320.844 JLINK_ReadReg(R0)
T0818 002:320.847 - 0.003ms returns 0x00000000
T0818 002:321.312 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:321.323   Data:  00 EB C6 09 01 2E 88 BF 35 46 00 BF 24 FA 09 F0 ...
T0818 002:321.333   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:323.959 - 2.646ms returns 0x27C
T0818 002:323.972 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:323.976   Data:  BD EC 0A 8B 01 B0 BD E8 F0 40 00 F0 09 B8 00 BF ...
T0818 002:324.013   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:325.864 - 1.892ms returns 0x184
T0818 002:325.876 JLINK_HasError()
T0818 002:325.905 JLINK_WriteReg(R0, 0x08000C00)
T0818 002:325.912 - 0.007ms returns 0
T0818 002:325.918 JLINK_WriteReg(R1, 0x00000400)
T0818 002:325.922 - 0.004ms returns 0
T0818 002:325.927 JLINK_WriteReg(R2, 0x20000184)
T0818 002:325.931 - 0.004ms returns 0
T0818 002:325.937 JLINK_WriteReg(R3, 0x00000000)
T0818 002:325.941 - 0.004ms returns 0
T0818 002:325.946 JLINK_WriteReg(R4, 0x00000000)
T0818 002:325.950 - 0.004ms returns 0
T0818 002:325.955 JLINK_WriteReg(R5, 0x00000000)
T0818 002:325.960 - 0.005ms returns 0
T0818 002:325.964 JLINK_WriteReg(R6, 0x00000000)
T0818 002:325.968 - 0.003ms returns 0
T0818 002:325.972 JLINK_WriteReg(R7, 0x00000000)
T0818 002:325.975 - 0.003ms returns 0
T0818 002:325.979 JLINK_WriteReg(R8, 0x00000000)
T0818 002:325.983 - 0.004ms returns 0
T0818 002:325.988 JLINK_WriteReg(R9, 0x20000180)
T0818 002:325.992 - 0.003ms returns 0
T0818 002:325.996 JLINK_WriteReg(R10, 0x00000000)
T0818 002:325.999 - 0.003ms returns 0
T0818 002:326.003 JLINK_WriteReg(R11, 0x00000000)
T0818 002:326.007 - 0.003ms returns 0
T0818 002:326.011 JLINK_WriteReg(R12, 0x00000000)
T0818 002:326.014 - 0.003ms returns 0
T0818 002:326.018 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:326.022 - 0.003ms returns 0
T0818 002:326.026 JLINK_WriteReg(R14, 0x20000001)
T0818 002:326.029 - 0.003ms returns 0
T0818 002:326.033 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:326.037 - 0.003ms returns 0
T0818 002:326.041 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:326.044 - 0.003ms returns 0
T0818 002:326.048 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:326.052 - 0.003ms returns 0
T0818 002:326.056 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:326.059 - 0.003ms returns 0
T0818 002:326.063 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:326.067 - 0.003ms returns 0
T0818 002:326.071 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:326.075 - 0.004ms returns 0x0000000F
T0818 002:326.080 JLINK_Go()
T0818 002:326.087   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:328.954 - 2.873ms 
T0818 002:328.966 JLINK_IsHalted()
T0818 002:329.446 - 0.479ms returns FALSE
T0818 002:329.452 JLINK_HasError()
T0818 002:331.065 JLINK_IsHalted()
T0818 002:331.703 - 0.638ms returns FALSE
T0818 002:331.710 JLINK_HasError()
T0818 002:333.031 JLINK_IsHalted()
T0818 002:335.352   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:335.822 - 2.790ms returns TRUE
T0818 002:335.827 JLINK_ReadReg(R15 (PC))
T0818 002:335.832 - 0.004ms returns 0x20000000
T0818 002:335.836 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T0818 002:335.840 - 0.003ms returns 0x00
T0818 002:335.844 JLINK_ReadReg(R0)
T0818 002:335.847 - 0.003ms returns 0x00000000
T0818 002:336.129 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:336.136   Data:  F1 EE 10 FA 14 DB 9F ED 10 1A 9F ED 10 2A 20 EE ...
T0818 002:336.144   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:338.708 - 2.579ms returns 0x27C
T0818 002:338.714 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:338.718   Data:  03 F0 00 53 00 93 00 9B 00 92 03 68 43 F0 01 03 ...
T0818 002:338.724   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:340.550 - 1.836ms returns 0x184
T0818 002:340.556 JLINK_HasError()
T0818 002:340.561 JLINK_WriteReg(R0, 0x08001000)
T0818 002:340.565 - 0.004ms returns 0
T0818 002:340.569 JLINK_WriteReg(R1, 0x00000400)
T0818 002:340.573 - 0.003ms returns 0
T0818 002:340.577 JLINK_WriteReg(R2, 0x20000184)
T0818 002:340.580 - 0.003ms returns 0
T0818 002:340.584 JLINK_WriteReg(R3, 0x00000000)
T0818 002:340.588 - 0.003ms returns 0
T0818 002:340.592 JLINK_WriteReg(R4, 0x00000000)
T0818 002:340.595 - 0.003ms returns 0
T0818 002:340.599 JLINK_WriteReg(R5, 0x00000000)
T0818 002:340.602 - 0.003ms returns 0
T0818 002:340.606 JLINK_WriteReg(R6, 0x00000000)
T0818 002:340.610 - 0.003ms returns 0
T0818 002:340.614 JLINK_WriteReg(R7, 0x00000000)
T0818 002:340.617 - 0.003ms returns 0
T0818 002:340.645 JLINK_WriteReg(R8, 0x00000000)
T0818 002:340.650 - 0.004ms returns 0
T0818 002:340.654 JLINK_WriteReg(R9, 0x20000180)
T0818 002:340.657 - 0.003ms returns 0
T0818 002:340.661 JLINK_WriteReg(R10, 0x00000000)
T0818 002:340.664 - 0.003ms returns 0
T0818 002:340.668 JLINK_WriteReg(R11, 0x00000000)
T0818 002:340.672 - 0.003ms returns 0
T0818 002:340.676 JLINK_WriteReg(R12, 0x00000000)
T0818 002:340.679 - 0.003ms returns 0
T0818 002:340.683 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:340.687 - 0.003ms returns 0
T0818 002:340.691 JLINK_WriteReg(R14, 0x20000001)
T0818 002:340.694 - 0.003ms returns 0
T0818 002:340.699 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:340.703 - 0.003ms returns 0
T0818 002:340.707 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:340.710 - 0.003ms returns 0
T0818 002:340.714 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:340.718 - 0.003ms returns 0
T0818 002:340.722 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:340.725 - 0.003ms returns 0
T0818 002:340.729 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:340.732 - 0.003ms returns 0
T0818 002:340.737 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:340.740 - 0.003ms returns 0x00000010
T0818 002:340.748 JLINK_Go()
T0818 002:340.755   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:343.684 - 2.935ms 
T0818 002:343.697 JLINK_IsHalted()
T0818 002:344.167 - 0.470ms returns FALSE
T0818 002:344.173 JLINK_HasError()
T0818 002:346.032 JLINK_IsHalted()
T0818 002:346.512 - 0.480ms returns FALSE
T0818 002:346.518 JLINK_HasError()
T0818 002:348.031 JLINK_IsHalted()
T0818 002:350.319   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:350.820 - 2.789ms returns TRUE
T0818 002:350.826 JLINK_ReadReg(R15 (PC))
T0818 002:350.831 - 0.004ms returns 0x20000000
T0818 002:350.835 JLINK_ClrBPEx(BPHandle = 0x00000010)
T0818 002:350.839 - 0.003ms returns 0x00
T0818 002:350.844 JLINK_ReadReg(R0)
T0818 002:350.847 - 0.003ms returns 0x00000000
T0818 002:351.439 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:351.447   Data:  84 60 44 68 C5 F3 00 15 24 EA 0C 06 05 FA 08 F5 ...
T0818 002:351.457   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:354.597 - 3.158ms returns 0x27C
T0818 002:354.612 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:354.616   Data:  48 BF 03 F1 0C 02 11 70 10 BD 00 00 4E F6 0C 51 ...
T0818 002:354.624   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:356.549 - 1.937ms returns 0x184
T0818 002:356.558 JLINK_HasError()
T0818 002:356.563 JLINK_WriteReg(R0, 0x08001400)
T0818 002:356.568 - 0.005ms returns 0
T0818 002:356.572 JLINK_WriteReg(R1, 0x00000400)
T0818 002:356.576 - 0.003ms returns 0
T0818 002:356.580 JLINK_WriteReg(R2, 0x20000184)
T0818 002:356.583 - 0.003ms returns 0
T0818 002:356.587 JLINK_WriteReg(R3, 0x00000000)
T0818 002:356.590 - 0.003ms returns 0
T0818 002:356.594 JLINK_WriteReg(R4, 0x00000000)
T0818 002:356.598 - 0.003ms returns 0
T0818 002:356.602 JLINK_WriteReg(R5, 0x00000000)
T0818 002:356.605 - 0.003ms returns 0
T0818 002:356.609 JLINK_WriteReg(R6, 0x00000000)
T0818 002:356.613 - 0.003ms returns 0
T0818 002:356.617 JLINK_WriteReg(R7, 0x00000000)
T0818 002:356.620 - 0.003ms returns 0
T0818 002:356.624 JLINK_WriteReg(R8, 0x00000000)
T0818 002:356.627 - 0.003ms returns 0
T0818 002:356.631 JLINK_WriteReg(R9, 0x20000180)
T0818 002:356.635 - 0.003ms returns 0
T0818 002:356.639 JLINK_WriteReg(R10, 0x00000000)
T0818 002:356.642 - 0.003ms returns 0
T0818 002:356.646 JLINK_WriteReg(R11, 0x00000000)
T0818 002:356.649 - 0.003ms returns 0
T0818 002:356.654 JLINK_WriteReg(R12, 0x00000000)
T0818 002:356.657 - 0.003ms returns 0
T0818 002:356.661 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:356.665 - 0.003ms returns 0
T0818 002:356.669 JLINK_WriteReg(R14, 0x20000001)
T0818 002:356.672 - 0.003ms returns 0
T0818 002:356.676 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:356.680 - 0.003ms returns 0
T0818 002:356.684 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:356.687 - 0.003ms returns 0
T0818 002:356.691 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:356.694 - 0.003ms returns 0
T0818 002:356.730 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:356.734 - 0.003ms returns 0
T0818 002:356.738 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:356.741 - 0.003ms returns 0
T0818 002:356.746 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:356.749 - 0.004ms returns 0x00000011
T0818 002:356.754 JLINK_Go()
T0818 002:356.760   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:359.577 - 2.823ms 
T0818 002:359.590 JLINK_IsHalted()
T0818 002:360.058 - 0.467ms returns FALSE
T0818 002:360.065 JLINK_HasError()
T0818 002:362.033 JLINK_IsHalted()
T0818 002:362.489 - 0.455ms returns FALSE
T0818 002:362.495 JLINK_HasError()
T0818 002:365.056 JLINK_IsHalted()
T0818 002:367.384   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:367.873 - 2.816ms returns TRUE
T0818 002:367.880 JLINK_ReadReg(R15 (PC))
T0818 002:367.884 - 0.004ms returns 0x20000000
T0818 002:367.888 JLINK_ClrBPEx(BPHandle = 0x00000011)
T0818 002:367.892 - 0.003ms returns 0x00
T0818 002:367.896 JLINK_ReadReg(R0)
T0818 002:367.900 - 0.003ms returns 0x00000000
T0818 002:368.171 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:368.177   Data:  C2 F2 00 00 00 68 C2 F2 00 02 11 60 FF F7 BA FE ...
T0818 002:368.186   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:370.823 - 2.652ms returns 0x27C
T0818 002:370.830 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:370.834   Data:  82 80 70 68 E1 69 00 F4 80 02 8A 42 7B D1 21 6A ...
T0818 002:370.841   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:372.833 - 2.002ms returns 0x184
T0818 002:372.846 JLINK_HasError()
T0818 002:372.851 JLINK_WriteReg(R0, 0x08001800)
T0818 002:372.857 - 0.005ms returns 0
T0818 002:372.861 JLINK_WriteReg(R1, 0x00000400)
T0818 002:372.864 - 0.003ms returns 0
T0818 002:372.868 JLINK_WriteReg(R2, 0x20000184)
T0818 002:372.872 - 0.003ms returns 0
T0818 002:372.876 JLINK_WriteReg(R3, 0x00000000)
T0818 002:372.880 - 0.003ms returns 0
T0818 002:372.884 JLINK_WriteReg(R4, 0x00000000)
T0818 002:372.887 - 0.003ms returns 0
T0818 002:372.891 JLINK_WriteReg(R5, 0x00000000)
T0818 002:372.894 - 0.003ms returns 0
T0818 002:372.898 JLINK_WriteReg(R6, 0x00000000)
T0818 002:372.902 - 0.003ms returns 0
T0818 002:372.906 JLINK_WriteReg(R7, 0x00000000)
T0818 002:372.909 - 0.003ms returns 0
T0818 002:372.913 JLINK_WriteReg(R8, 0x00000000)
T0818 002:372.916 - 0.003ms returns 0
T0818 002:372.920 JLINK_WriteReg(R9, 0x20000180)
T0818 002:372.924 - 0.003ms returns 0
T0818 002:372.928 JLINK_WriteReg(R10, 0x00000000)
T0818 002:372.931 - 0.003ms returns 0
T0818 002:372.935 JLINK_WriteReg(R11, 0x00000000)
T0818 002:372.938 - 0.003ms returns 0
T0818 002:372.942 JLINK_WriteReg(R12, 0x00000000)
T0818 002:372.946 - 0.003ms returns 0
T0818 002:372.950 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:372.953 - 0.003ms returns 0
T0818 002:372.957 JLINK_WriteReg(R14, 0x20000001)
T0818 002:372.961 - 0.003ms returns 0
T0818 002:372.965 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:372.968 - 0.003ms returns 0
T0818 002:372.972 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:372.976 - 0.003ms returns 0
T0818 002:372.980 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:372.983 - 0.003ms returns 0
T0818 002:372.987 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:372.991 - 0.003ms returns 0
T0818 002:372.995 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:372.998 - 0.003ms returns 0
T0818 002:373.002 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:373.006 - 0.004ms returns 0x00000012
T0818 002:373.010 JLINK_Go()
T0818 002:373.018   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:375.822 - 2.811ms 
T0818 002:375.832 JLINK_IsHalted()
T0818 002:376.336 - 0.504ms returns FALSE
T0818 002:376.342 JLINK_HasError()
T0818 002:379.032 JLINK_IsHalted()
T0818 002:379.482 - 0.449ms returns FALSE
T0818 002:379.488 JLINK_HasError()
T0818 002:382.033 JLINK_IsHalted()
T0818 002:384.316   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:384.849 - 2.815ms returns TRUE
T0818 002:384.855 JLINK_ReadReg(R15 (PC))
T0818 002:384.860 - 0.004ms returns 0x20000000
T0818 002:384.864 JLINK_ClrBPEx(BPHandle = 0x00000012)
T0818 002:384.872 - 0.008ms returns 0x00
T0818 002:384.877 JLINK_ReadReg(R0)
T0818 002:384.880 - 0.003ms returns 0x00000000
T0818 002:385.247 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:385.257   Data:  05 46 00 BF 30 68 80 07 7F F5 28 AF FF F7 8A FC ...
T0818 002:385.268   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:387.896 - 2.648ms returns 0x27C
T0818 002:387.907 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:387.911   Data:  13 06 2A D5 14 F0 80 03 27 D0 90 F8 3D 20 21 2A ...
T0818 002:387.918   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:389.848 - 1.941ms returns 0x184
T0818 002:389.858 JLINK_HasError()
T0818 002:390.438 JLINK_WriteReg(R0, 0x08001C00)
T0818 002:390.447 - 0.009ms returns 0
T0818 002:390.452 JLINK_WriteReg(R1, 0x00000400)
T0818 002:390.456 - 0.003ms returns 0
T0818 002:390.460 JLINK_WriteReg(R2, 0x20000184)
T0818 002:390.463 - 0.003ms returns 0
T0818 002:390.467 JLINK_WriteReg(R3, 0x00000000)
T0818 002:390.471 - 0.004ms returns 0
T0818 002:390.475 JLINK_WriteReg(R4, 0x00000000)
T0818 002:390.479 - 0.003ms returns 0
T0818 002:390.483 JLINK_WriteReg(R5, 0x00000000)
T0818 002:390.486 - 0.003ms returns 0
T0818 002:390.490 JLINK_WriteReg(R6, 0x00000000)
T0818 002:390.494 - 0.003ms returns 0
T0818 002:390.498 JLINK_WriteReg(R7, 0x00000000)
T0818 002:390.501 - 0.003ms returns 0
T0818 002:390.505 JLINK_WriteReg(R8, 0x00000000)
T0818 002:390.508 - 0.003ms returns 0
T0818 002:390.512 JLINK_WriteReg(R9, 0x20000180)
T0818 002:390.516 - 0.003ms returns 0
T0818 002:390.520 JLINK_WriteReg(R10, 0x00000000)
T0818 002:390.523 - 0.003ms returns 0
T0818 002:390.527 JLINK_WriteReg(R11, 0x00000000)
T0818 002:390.530 - 0.003ms returns 0
T0818 002:390.534 JLINK_WriteReg(R12, 0x00000000)
T0818 002:390.538 - 0.003ms returns 0
T0818 002:390.542 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:390.546 - 0.003ms returns 0
T0818 002:390.550 JLINK_WriteReg(R14, 0x20000001)
T0818 002:390.553 - 0.003ms returns 0
T0818 002:390.557 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:390.560 - 0.003ms returns 0
T0818 002:390.564 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:390.568 - 0.003ms returns 0
T0818 002:390.572 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:390.576 - 0.003ms returns 0
T0818 002:390.580 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:390.583 - 0.003ms returns 0
T0818 002:390.587 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:390.590 - 0.003ms returns 0
T0818 002:390.595 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:390.599 - 0.004ms returns 0x00000013
T0818 002:390.603 JLINK_Go()
T0818 002:390.611   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:393.447 - 2.843ms 
T0818 002:393.458 JLINK_IsHalted()
T0818 002:393.960 - 0.502ms returns FALSE
T0818 002:393.966 JLINK_HasError()
T0818 002:396.033 JLINK_IsHalted()
T0818 002:396.535 - 0.502ms returns FALSE
T0818 002:396.541 JLINK_HasError()
T0818 002:398.031 JLINK_IsHalted()
T0818 002:400.355   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:400.822 - 2.791ms returns TRUE
T0818 002:400.828 JLINK_ReadReg(R15 (PC))
T0818 002:400.833 - 0.004ms returns 0x20000000
T0818 002:400.837 JLINK_ClrBPEx(BPHandle = 0x00000013)
T0818 002:400.841 - 0.004ms returns 0x00
T0818 002:400.846 JLINK_ReadReg(R0)
T0818 002:400.849 - 0.003ms returns 0x00000000
T0818 002:401.288 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:401.299   Data:  01 68 51 E8 05 1F 02 68 21 F0 01 01 42 E8 05 13 ...
T0818 002:401.310   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:403.920 - 2.631ms returns 0x27C
T0818 002:403.932 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:403.935   Data:  3E 00 20 28 0A D1 00 29 4F F0 01 00 18 BF 00 2A ...
T0818 002:403.944   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:405.852 - 1.920ms returns 0x184
T0818 002:405.859 JLINK_HasError()
T0818 002:405.864 JLINK_WriteReg(R0, 0x08002000)
T0818 002:405.868 - 0.004ms returns 0
T0818 002:405.873 JLINK_WriteReg(R1, 0x00000400)
T0818 002:405.876 - 0.003ms returns 0
T0818 002:405.880 JLINK_WriteReg(R2, 0x20000184)
T0818 002:405.888 - 0.008ms returns 0
T0818 002:405.892 JLINK_WriteReg(R3, 0x00000000)
T0818 002:405.896 - 0.003ms returns 0
T0818 002:405.900 JLINK_WriteReg(R4, 0x00000000)
T0818 002:405.903 - 0.003ms returns 0
T0818 002:405.907 JLINK_WriteReg(R5, 0x00000000)
T0818 002:405.911 - 0.003ms returns 0
T0818 002:405.915 JLINK_WriteReg(R6, 0x00000000)
T0818 002:405.918 - 0.003ms returns 0
T0818 002:405.922 JLINK_WriteReg(R7, 0x00000000)
T0818 002:405.926 - 0.003ms returns 0
T0818 002:405.930 JLINK_WriteReg(R8, 0x00000000)
T0818 002:405.933 - 0.003ms returns 0
T0818 002:405.937 JLINK_WriteReg(R9, 0x20000180)
T0818 002:405.941 - 0.003ms returns 0
T0818 002:405.945 JLINK_WriteReg(R10, 0x00000000)
T0818 002:405.948 - 0.003ms returns 0
T0818 002:405.952 JLINK_WriteReg(R11, 0x00000000)
T0818 002:405.955 - 0.003ms returns 0
T0818 002:405.959 JLINK_WriteReg(R12, 0x00000000)
T0818 002:405.963 - 0.003ms returns 0
T0818 002:405.967 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:405.970 - 0.003ms returns 0
T0818 002:405.974 JLINK_WriteReg(R14, 0x20000001)
T0818 002:405.978 - 0.003ms returns 0
T0818 002:405.982 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:405.985 - 0.003ms returns 0
T0818 002:405.989 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:405.992 - 0.003ms returns 0
T0818 002:405.996 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:406.000 - 0.003ms returns 0
T0818 002:406.004 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:406.007 - 0.003ms returns 0
T0818 002:406.011 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:406.015 - 0.003ms returns 0
T0818 002:406.019 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:406.023 - 0.004ms returns 0x00000014
T0818 002:406.027 JLINK_Go()
T0818 002:406.034   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:408.831 - 2.803ms 
T0818 002:408.842 JLINK_IsHalted()
T0818 002:409.320 - 0.478ms returns FALSE
T0818 002:409.326 JLINK_HasError()
T0818 002:412.032 JLINK_IsHalted()
T0818 002:412.580 - 0.548ms returns FALSE
T0818 002:412.587 JLINK_HasError()
T0818 002:415.033 JLINK_IsHalted()
T0818 002:417.354   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:417.854 - 2.821ms returns TRUE
T0818 002:417.864 JLINK_ReadReg(R15 (PC))
T0818 002:417.872 - 0.007ms returns 0x20000000
T0818 002:417.910 JLINK_ClrBPEx(BPHandle = 0x00000014)
T0818 002:417.915 - 0.005ms returns 0x00
T0818 002:417.920 JLINK_ReadReg(R0)
T0818 002:417.923 - 0.003ms returns 0x00000000
T0818 002:418.206 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:418.213   Data:  88 B2 80 28 09 D3 00 20 A0 70 60 81 40 F2 7C 20 ...
T0818 002:418.230   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:420.849 - 2.644ms returns 0x27C
T0818 002:420.856 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:420.860   Data:  0D FD 08 B0 10 BD 00 00 2D E9 F0 4F 87 B0 4F F0 ...
T0818 002:420.867   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:422.830 - 1.974ms returns 0x184
T0818 002:422.843 JLINK_HasError()
T0818 002:422.848 JLINK_WriteReg(R0, 0x08002400)
T0818 002:422.854 - 0.005ms returns 0
T0818 002:422.858 JLINK_WriteReg(R1, 0x00000400)
T0818 002:422.861 - 0.003ms returns 0
T0818 002:422.865 JLINK_WriteReg(R2, 0x20000184)
T0818 002:422.869 - 0.003ms returns 0
T0818 002:422.873 JLINK_WriteReg(R3, 0x00000000)
T0818 002:422.876 - 0.003ms returns 0
T0818 002:422.880 JLINK_WriteReg(R4, 0x00000000)
T0818 002:422.884 - 0.003ms returns 0
T0818 002:422.888 JLINK_WriteReg(R5, 0x00000000)
T0818 002:422.891 - 0.003ms returns 0
T0818 002:422.895 JLINK_WriteReg(R6, 0x00000000)
T0818 002:422.898 - 0.003ms returns 0
T0818 002:422.902 JLINK_WriteReg(R7, 0x00000000)
T0818 002:422.906 - 0.003ms returns 0
T0818 002:422.910 JLINK_WriteReg(R8, 0x00000000)
T0818 002:422.914 - 0.003ms returns 0
T0818 002:422.918 JLINK_WriteReg(R9, 0x20000180)
T0818 002:422.921 - 0.003ms returns 0
T0818 002:422.925 JLINK_WriteReg(R10, 0x00000000)
T0818 002:422.928 - 0.003ms returns 0
T0818 002:422.932 JLINK_WriteReg(R11, 0x00000000)
T0818 002:422.936 - 0.003ms returns 0
T0818 002:422.940 JLINK_WriteReg(R12, 0x00000000)
T0818 002:422.946 - 0.006ms returns 0
T0818 002:422.951 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:422.955 - 0.003ms returns 0
T0818 002:422.959 JLINK_WriteReg(R14, 0x20000001)
T0818 002:422.962 - 0.003ms returns 0
T0818 002:422.966 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:422.969 - 0.003ms returns 0
T0818 002:422.973 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:422.977 - 0.003ms returns 0
T0818 002:422.981 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:422.984 - 0.003ms returns 0
T0818 002:422.988 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:422.992 - 0.003ms returns 0
T0818 002:422.996 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:422.999 - 0.003ms returns 0
T0818 002:423.004 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:423.008 - 0.004ms returns 0x00000015
T0818 002:423.012 JLINK_Go()
T0818 002:423.019   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:426.052 - 3.040ms 
T0818 002:426.060 JLINK_IsHalted()
T0818 002:426.570 - 0.509ms returns FALSE
T0818 002:426.576 JLINK_HasError()
T0818 002:429.032 JLINK_IsHalted()
T0818 002:429.498 - 0.464ms returns FALSE
T0818 002:429.504 JLINK_HasError()
T0818 002:432.032 JLINK_IsHalted()
T0818 002:434.316   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:434.852 - 2.819ms returns TRUE
T0818 002:434.858 JLINK_ReadReg(R15 (PC))
T0818 002:434.862 - 0.004ms returns 0x20000000
T0818 002:434.867 JLINK_ClrBPEx(BPHandle = 0x00000015)
T0818 002:434.870 - 0.003ms returns 0x00
T0818 002:434.874 JLINK_ReadReg(R0)
T0818 002:434.878 - 0.003ms returns 0x00000000
T0818 002:435.153 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:435.159   Data:  02 09 A8 F5 80 60 29 46 04 94 FE F7 B7 FD 07 B0 ...
T0818 002:435.168   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:437.823 - 2.670ms returns 0x27C
T0818 002:437.829 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:437.833   Data:  03 00 CD E9 01 00 00 90 C4 F2 02 01 0A 68 42 F0 ...
T0818 002:437.840   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:439.745 - 1.915ms returns 0x184
T0818 002:439.756 JLINK_HasError()
T0818 002:439.761 JLINK_WriteReg(R0, 0x08002800)
T0818 002:439.766 - 0.005ms returns 0
T0818 002:439.770 JLINK_WriteReg(R1, 0x00000400)
T0818 002:439.774 - 0.003ms returns 0
T0818 002:439.778 JLINK_WriteReg(R2, 0x20000184)
T0818 002:439.781 - 0.003ms returns 0
T0818 002:439.785 JLINK_WriteReg(R3, 0x00000000)
T0818 002:439.788 - 0.003ms returns 0
T0818 002:439.792 JLINK_WriteReg(R4, 0x00000000)
T0818 002:439.796 - 0.003ms returns 0
T0818 002:439.800 JLINK_WriteReg(R5, 0x00000000)
T0818 002:439.804 - 0.003ms returns 0
T0818 002:439.808 JLINK_WriteReg(R6, 0x00000000)
T0818 002:439.811 - 0.003ms returns 0
T0818 002:439.818 JLINK_WriteReg(R7, 0x00000000)
T0818 002:439.821 - 0.003ms returns 0
T0818 002:439.825 JLINK_WriteReg(R8, 0x00000000)
T0818 002:439.829 - 0.003ms returns 0
T0818 002:439.833 JLINK_WriteReg(R9, 0x20000180)
T0818 002:439.836 - 0.003ms returns 0
T0818 002:439.840 JLINK_WriteReg(R10, 0x00000000)
T0818 002:439.843 - 0.003ms returns 0
T0818 002:439.848 JLINK_WriteReg(R11, 0x00000000)
T0818 002:439.851 - 0.003ms returns 0
T0818 002:439.855 JLINK_WriteReg(R12, 0x00000000)
T0818 002:439.858 - 0.003ms returns 0
T0818 002:439.862 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:439.866 - 0.003ms returns 0
T0818 002:439.870 JLINK_WriteReg(R14, 0x20000001)
T0818 002:439.873 - 0.003ms returns 0
T0818 002:439.878 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:439.881 - 0.003ms returns 0
T0818 002:439.885 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:439.888 - 0.003ms returns 0
T0818 002:439.892 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:439.896 - 0.003ms returns 0
T0818 002:439.900 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:439.903 - 0.003ms returns 0
T0818 002:439.907 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:439.910 - 0.003ms returns 0
T0818 002:439.915 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:439.919 - 0.004ms returns 0x00000016
T0818 002:439.923 JLINK_Go()
T0818 002:439.930   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:442.741 - 2.817ms 
T0818 002:442.757 JLINK_IsHalted()
T0818 002:443.240 - 0.482ms returns FALSE
T0818 002:443.249 JLINK_HasError()
T0818 002:446.032 JLINK_IsHalted()
T0818 002:446.516 - 0.483ms returns FALSE
T0818 002:446.522 JLINK_HasError()
T0818 002:448.031 JLINK_IsHalted()
T0818 002:450.307   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:450.849 - 2.818ms returns TRUE
T0818 002:450.854 JLINK_ReadReg(R15 (PC))
T0818 002:450.859 - 0.004ms returns 0x20000000
T0818 002:450.863 JLINK_ClrBPEx(BPHandle = 0x00000016)
T0818 002:450.867 - 0.003ms returns 0x00
T0818 002:450.871 JLINK_ReadReg(R0)
T0818 002:450.875 - 0.003ms returns 0x00000000
T0818 002:451.291 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:451.302   Data:  22 F8 02 1B 82 62 BF E7 B0 B5 04 46 00 68 04 F1 ...
T0818 002:451.312   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:453.914 - 2.623ms returns 0x27C
T0818 002:453.927 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:453.931   Data:  40 0A 20 EE 08 0A 80 EE 09 0A FF F7 8D FF 3A EE ...
T0818 002:453.940   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:455.846 - 1.919ms returns 0x184
T0818 002:455.858 JLINK_HasError()
T0818 002:455.863 JLINK_WriteReg(R0, 0x08002C00)
T0818 002:455.868 - 0.005ms returns 0
T0818 002:455.873 JLINK_WriteReg(R1, 0x00000400)
T0818 002:455.876 - 0.003ms returns 0
T0818 002:455.880 JLINK_WriteReg(R2, 0x20000184)
T0818 002:455.884 - 0.003ms returns 0
T0818 002:455.888 JLINK_WriteReg(R3, 0x00000000)
T0818 002:455.891 - 0.003ms returns 0
T0818 002:455.895 JLINK_WriteReg(R4, 0x00000000)
T0818 002:455.898 - 0.003ms returns 0
T0818 002:455.902 JLINK_WriteReg(R5, 0x00000000)
T0818 002:455.906 - 0.003ms returns 0
T0818 002:455.910 JLINK_WriteReg(R6, 0x00000000)
T0818 002:455.913 - 0.003ms returns 0
T0818 002:455.917 JLINK_WriteReg(R7, 0x00000000)
T0818 002:455.921 - 0.003ms returns 0
T0818 002:455.925 JLINK_WriteReg(R8, 0x00000000)
T0818 002:455.928 - 0.003ms returns 0
T0818 002:455.932 JLINK_WriteReg(R9, 0x20000180)
T0818 002:455.935 - 0.003ms returns 0
T0818 002:455.939 JLINK_WriteReg(R10, 0x00000000)
T0818 002:455.943 - 0.003ms returns 0
T0818 002:455.947 JLINK_WriteReg(R11, 0x00000000)
T0818 002:455.950 - 0.003ms returns 0
T0818 002:455.954 JLINK_WriteReg(R12, 0x00000000)
T0818 002:455.958 - 0.003ms returns 0
T0818 002:455.962 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:455.965 - 0.003ms returns 0
T0818 002:455.969 JLINK_WriteReg(R14, 0x20000001)
T0818 002:455.973 - 0.003ms returns 0
T0818 002:455.977 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:455.980 - 0.003ms returns 0
T0818 002:455.984 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:455.988 - 0.004ms returns 0
T0818 002:455.992 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:455.995 - 0.003ms returns 0
T0818 002:455.999 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:456.003 - 0.003ms returns 0
T0818 002:456.007 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:456.010 - 0.003ms returns 0
T0818 002:456.014 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:456.019 - 0.004ms returns 0x00000017
T0818 002:456.023 JLINK_Go()
T0818 002:456.030   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:458.752 - 2.729ms 
T0818 002:458.758 JLINK_IsHalted()
T0818 002:459.202 - 0.443ms returns FALSE
T0818 002:459.208 JLINK_HasError()
T0818 002:462.034 JLINK_IsHalted()
T0818 002:462.537 - 0.503ms returns FALSE
T0818 002:462.543 JLINK_HasError()
T0818 002:465.032 JLINK_IsHalted()
T0818 002:467.307   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:467.849 - 2.816ms returns TRUE
T0818 002:467.855 JLINK_ReadReg(R15 (PC))
T0818 002:467.860 - 0.004ms returns 0x20000000
T0818 002:467.864 JLINK_ClrBPEx(BPHandle = 0x00000017)
T0818 002:467.868 - 0.003ms returns 0x00
T0818 002:467.872 JLINK_ReadReg(R0)
T0818 002:467.876 - 0.004ms returns 0x00000000
T0818 002:468.158 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:468.165   Data:  34 F9 9F ED 31 0B 06 46 53 EC 10 2B 0F 46 FD F7 ...
T0818 002:468.174   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:470.757 - 2.599ms returns 0x27C
T0818 002:470.772 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:470.776   Data:  53 54 41 52 54 21 0D 0A 00 00 00 00 82 B0 70 B5 ...
T0818 002:470.785   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:472.839 - 2.066ms returns 0x184
T0818 002:472.854 JLINK_HasError()
T0818 002:472.859 JLINK_WriteReg(R0, 0x08003000)
T0818 002:472.864 - 0.005ms returns 0
T0818 002:472.868 JLINK_WriteReg(R1, 0x00000400)
T0818 002:472.872 - 0.003ms returns 0
T0818 002:472.876 JLINK_WriteReg(R2, 0x20000184)
T0818 002:472.879 - 0.003ms returns 0
T0818 002:472.883 JLINK_WriteReg(R3, 0x00000000)
T0818 002:472.887 - 0.003ms returns 0
T0818 002:472.891 JLINK_WriteReg(R4, 0x00000000)
T0818 002:472.894 - 0.003ms returns 0
T0818 002:472.898 JLINK_WriteReg(R5, 0x00000000)
T0818 002:472.902 - 0.003ms returns 0
T0818 002:472.906 JLINK_WriteReg(R6, 0x00000000)
T0818 002:472.909 - 0.003ms returns 0
T0818 002:472.914 JLINK_WriteReg(R7, 0x00000000)
T0818 002:472.917 - 0.003ms returns 0
T0818 002:472.921 JLINK_WriteReg(R8, 0x00000000)
T0818 002:472.924 - 0.003ms returns 0
T0818 002:472.928 JLINK_WriteReg(R9, 0x20000180)
T0818 002:472.932 - 0.003ms returns 0
T0818 002:472.936 JLINK_WriteReg(R10, 0x00000000)
T0818 002:472.939 - 0.003ms returns 0
T0818 002:472.943 JLINK_WriteReg(R11, 0x00000000)
T0818 002:472.946 - 0.003ms returns 0
T0818 002:472.951 JLINK_WriteReg(R12, 0x00000000)
T0818 002:472.954 - 0.003ms returns 0
T0818 002:472.958 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:472.962 - 0.003ms returns 0
T0818 002:472.966 JLINK_WriteReg(R14, 0x20000001)
T0818 002:472.969 - 0.003ms returns 0
T0818 002:472.973 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:472.977 - 0.003ms returns 0
T0818 002:472.981 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:472.984 - 0.003ms returns 0
T0818 002:472.988 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:472.991 - 0.003ms returns 0
T0818 002:472.996 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:472.999 - 0.003ms returns 0
T0818 002:473.003 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:473.006 - 0.003ms returns 0
T0818 002:473.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:473.015 - 0.004ms returns 0x00000018
T0818 002:473.019 JLINK_Go()
T0818 002:473.026   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:475.756 - 2.736ms 
T0818 002:475.767 JLINK_IsHalted()
T0818 002:476.227 - 0.458ms returns FALSE
T0818 002:476.235 JLINK_HasError()
T0818 002:479.033 JLINK_IsHalted()
T0818 002:479.572 - 0.539ms returns FALSE
T0818 002:479.579 JLINK_HasError()
T0818 002:482.035 JLINK_IsHalted()
T0818 002:484.320   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:484.859 - 2.824ms returns TRUE
T0818 002:484.873 JLINK_ReadReg(R15 (PC))
T0818 002:484.879 - 0.005ms returns 0x20000000
T0818 002:484.884 JLINK_ClrBPEx(BPHandle = 0x00000018)
T0818 002:484.888 - 0.004ms returns 0x00
T0818 002:484.893 JLINK_ReadReg(R0)
T0818 002:484.896 - 0.003ms returns 0x00000000
T0818 002:485.274 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:485.282   Data:  70 47 00 20 01 E0 01 C1 12 1F 00 2A FB D1 70 47 ...
T0818 002:485.293   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:487.899 - 2.624ms returns 0x27C
T0818 002:487.911 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:487.915   Data:  0B D0 13 DC 58 28 77 D0 09 DC 00 28 75 D0 45 28 ...
T0818 002:487.925   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:489.828 - 1.916ms returns 0x184
T0818 002:489.835 JLINK_HasError()
T0818 002:489.840 JLINK_WriteReg(R0, 0x08003400)
T0818 002:489.845 - 0.004ms returns 0
T0818 002:489.849 JLINK_WriteReg(R1, 0x00000400)
T0818 002:489.853 - 0.003ms returns 0
T0818 002:489.857 JLINK_WriteReg(R2, 0x20000184)
T0818 002:489.861 - 0.003ms returns 0
T0818 002:489.865 JLINK_WriteReg(R3, 0x00000000)
T0818 002:489.868 - 0.003ms returns 0
T0818 002:489.872 JLINK_WriteReg(R4, 0x00000000)
T0818 002:489.875 - 0.003ms returns 0
T0818 002:489.879 JLINK_WriteReg(R5, 0x00000000)
T0818 002:489.883 - 0.003ms returns 0
T0818 002:489.887 JLINK_WriteReg(R6, 0x00000000)
T0818 002:489.893 - 0.005ms returns 0
T0818 002:489.899 JLINK_WriteReg(R7, 0x00000000)
T0818 002:489.902 - 0.003ms returns 0
T0818 002:489.907 JLINK_WriteReg(R8, 0x00000000)
T0818 002:489.910 - 0.004ms returns 0
T0818 002:489.914 JLINK_WriteReg(R9, 0x20000180)
T0818 002:489.918 - 0.003ms returns 0
T0818 002:489.922 JLINK_WriteReg(R10, 0x00000000)
T0818 002:489.925 - 0.003ms returns 0
T0818 002:489.929 JLINK_WriteReg(R11, 0x00000000)
T0818 002:489.932 - 0.003ms returns 0
T0818 002:489.936 JLINK_WriteReg(R12, 0x00000000)
T0818 002:489.940 - 0.003ms returns 0
T0818 002:489.944 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:489.948 - 0.003ms returns 0
T0818 002:489.952 JLINK_WriteReg(R14, 0x20000001)
T0818 002:489.955 - 0.003ms returns 0
T0818 002:489.959 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:489.963 - 0.003ms returns 0
T0818 002:489.967 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:489.970 - 0.003ms returns 0
T0818 002:489.974 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:489.978 - 0.003ms returns 0
T0818 002:489.982 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:489.985 - 0.003ms returns 0
T0818 002:489.989 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:489.992 - 0.003ms returns 0
T0818 002:489.997 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:490.001 - 0.004ms returns 0x00000019
T0818 002:490.005 JLINK_Go()
T0818 002:490.013   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:492.855 - 2.849ms 
T0818 002:492.874 JLINK_IsHalted()
T0818 002:493.369 - 0.494ms returns FALSE
T0818 002:493.388 JLINK_HasError()
T0818 002:497.035 JLINK_IsHalted()
T0818 002:499.325   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:499.826 - 2.790ms returns TRUE
T0818 002:499.839 JLINK_ReadReg(R15 (PC))
T0818 002:499.845 - 0.005ms returns 0x20000000
T0818 002:499.849 JLINK_ClrBPEx(BPHandle = 0x00000019)
T0818 002:499.853 - 0.004ms returns 0x00
T0818 002:499.858 JLINK_ReadReg(R0)
T0818 002:499.861 - 0.003ms returns 0x00000000
T0818 002:500.194 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:500.202   Data:  01 D0 20 22 F7 E7 90 46 59 E0 0A 21 02 E0 10 22 ...
T0818 002:500.214   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:502.765 - 2.570ms returns 0x27C
T0818 002:502.782 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:502.786   Data:  DD E9 0F 02 03 92 0E 9B 11 99 00 22 DD F8 0C A0 ...
T0818 002:502.797   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:504.650 - 1.868ms returns 0x184
T0818 002:504.659 JLINK_HasError()
T0818 002:504.664 JLINK_WriteReg(R0, 0x08003800)
T0818 002:504.669 - 0.005ms returns 0
T0818 002:504.674 JLINK_WriteReg(R1, 0x00000400)
T0818 002:504.677 - 0.003ms returns 0
T0818 002:504.681 JLINK_WriteReg(R2, 0x20000184)
T0818 002:504.685 - 0.003ms returns 0
T0818 002:504.688 JLINK_WriteReg(R3, 0x00000000)
T0818 002:504.692 - 0.003ms returns 0
T0818 002:504.696 JLINK_WriteReg(R4, 0x00000000)
T0818 002:504.700 - 0.003ms returns 0
T0818 002:504.704 JLINK_WriteReg(R5, 0x00000000)
T0818 002:504.707 - 0.003ms returns 0
T0818 002:504.711 JLINK_WriteReg(R6, 0x00000000)
T0818 002:504.714 - 0.003ms returns 0
T0818 002:504.718 JLINK_WriteReg(R7, 0x00000000)
T0818 002:504.722 - 0.003ms returns 0
T0818 002:504.726 JLINK_WriteReg(R8, 0x00000000)
T0818 002:504.730 - 0.003ms returns 0
T0818 002:504.734 JLINK_WriteReg(R9, 0x20000180)
T0818 002:504.737 - 0.003ms returns 0
T0818 002:504.741 JLINK_WriteReg(R10, 0x00000000)
T0818 002:504.745 - 0.003ms returns 0
T0818 002:504.749 JLINK_WriteReg(R11, 0x00000000)
T0818 002:504.752 - 0.003ms returns 0
T0818 002:504.756 JLINK_WriteReg(R12, 0x00000000)
T0818 002:504.760 - 0.003ms returns 0
T0818 002:504.764 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:504.768 - 0.003ms returns 0
T0818 002:504.772 JLINK_WriteReg(R14, 0x20000001)
T0818 002:504.775 - 0.003ms returns 0
T0818 002:504.779 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:504.782 - 0.003ms returns 0
T0818 002:504.786 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:504.790 - 0.003ms returns 0
T0818 002:504.794 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:504.797 - 0.003ms returns 0
T0818 002:504.807 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:504.810 - 0.003ms returns 0
T0818 002:504.814 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:504.818 - 0.003ms returns 0
T0818 002:504.822 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:504.827 - 0.004ms returns 0x0000001A
T0818 002:504.831 JLINK_Go()
T0818 002:504.840   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:507.662 - 2.830ms 
T0818 002:507.675 JLINK_IsHalted()
T0818 002:508.143 - 0.468ms returns FALSE
T0818 002:508.149 JLINK_HasError()
T0818 002:511.067 JLINK_IsHalted()
T0818 002:511.662 - 0.595ms returns FALSE
T0818 002:511.673 JLINK_HasError()
T0818 002:513.031 JLINK_IsHalted()
T0818 002:515.308   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:515.821 - 2.789ms returns TRUE
T0818 002:515.827 JLINK_ReadReg(R15 (PC))
T0818 002:515.832 - 0.004ms returns 0x20000000
T0818 002:515.836 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T0818 002:515.840 - 0.003ms returns 0x00
T0818 002:515.844 JLINK_ReadReg(R0)
T0818 002:515.848 - 0.003ms returns 0x00000000
T0818 002:516.157 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:516.164   Data:  00 28 07 DB DD E9 03 01 88 42 03 DD 00 98 40 5C ...
T0818 002:516.173   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:518.725 - 2.568ms returns 0x27C
T0818 002:518.734 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:518.738   Data:  E8 02 E8 06 E8 01 E8 05 E8 03 E8 07 18 00 18 04 ...
T0818 002:518.747   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:520.594 - 1.860ms returns 0x184
T0818 002:520.605 JLINK_HasError()
T0818 002:520.636 JLINK_WriteReg(R0, 0x08003C00)
T0818 002:520.641 - 0.005ms returns 0
T0818 002:520.646 JLINK_WriteReg(R1, 0x00000400)
T0818 002:520.649 - 0.003ms returns 0
T0818 002:520.654 JLINK_WriteReg(R2, 0x20000184)
T0818 002:520.657 - 0.003ms returns 0
T0818 002:520.661 JLINK_WriteReg(R3, 0x00000000)
T0818 002:520.664 - 0.003ms returns 0
T0818 002:520.668 JLINK_WriteReg(R4, 0x00000000)
T0818 002:520.672 - 0.003ms returns 0
T0818 002:520.676 JLINK_WriteReg(R5, 0x00000000)
T0818 002:520.679 - 0.003ms returns 0
T0818 002:520.683 JLINK_WriteReg(R6, 0x00000000)
T0818 002:520.686 - 0.003ms returns 0
T0818 002:520.690 JLINK_WriteReg(R7, 0x00000000)
T0818 002:520.694 - 0.003ms returns 0
T0818 002:520.698 JLINK_WriteReg(R8, 0x00000000)
T0818 002:520.701 - 0.003ms returns 0
T0818 002:520.705 JLINK_WriteReg(R9, 0x20000180)
T0818 002:520.708 - 0.003ms returns 0
T0818 002:520.712 JLINK_WriteReg(R10, 0x00000000)
T0818 002:520.716 - 0.003ms returns 0
T0818 002:520.720 JLINK_WriteReg(R11, 0x00000000)
T0818 002:520.724 - 0.003ms returns 0
T0818 002:520.728 JLINK_WriteReg(R12, 0x00000000)
T0818 002:520.731 - 0.003ms returns 0
T0818 002:520.735 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:520.739 - 0.003ms returns 0
T0818 002:520.743 JLINK_WriteReg(R14, 0x20000001)
T0818 002:520.746 - 0.003ms returns 0
T0818 002:520.750 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:520.754 - 0.003ms returns 0
T0818 002:520.758 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:520.761 - 0.003ms returns 0
T0818 002:520.765 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:520.769 - 0.003ms returns 0
T0818 002:520.773 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:520.776 - 0.003ms returns 0
T0818 002:520.780 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:520.783 - 0.003ms returns 0
T0818 002:520.788 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:520.792 - 0.004ms returns 0x0000001B
T0818 002:520.796 JLINK_Go()
T0818 002:520.803   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:523.646 - 2.850ms 
T0818 002:523.655 JLINK_IsHalted()
T0818 002:524.140 - 0.484ms returns FALSE
T0818 002:524.145 JLINK_HasError()
T0818 002:526.033 JLINK_IsHalted()
T0818 002:526.468 - 0.435ms returns FALSE
T0818 002:526.475 JLINK_HasError()
T0818 002:528.031 JLINK_IsHalted()
T0818 002:530.367   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:530.866 - 2.835ms returns TRUE
T0818 002:530.873 JLINK_ReadReg(R15 (PC))
T0818 002:530.878 - 0.004ms returns 0x20000000
T0818 002:530.882 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T0818 002:530.890 - 0.008ms returns 0x00
T0818 002:530.895 JLINK_ReadReg(R0)
T0818 002:530.898 - 0.003ms returns 0x00000000
T0818 002:531.326 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:531.336   Data:  F4 01 F4 05 F4 03 F4 07 0C 00 0C 04 0C 02 0C 06 ...
T0818 002:531.347   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:533.930 - 2.604ms returns 0x27C
T0818 002:533.940 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:533.944   Data:  EA 02 EA 06 EA 01 EA 05 EA 03 EA 07 1A 00 1A 04 ...
T0818 002:533.952   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:535.854 - 1.913ms returns 0x184
T0818 002:535.864 JLINK_HasError()
T0818 002:535.869 JLINK_WriteReg(R0, 0x08004000)
T0818 002:535.873 - 0.004ms returns 0
T0818 002:535.878 JLINK_WriteReg(R1, 0x00000400)
T0818 002:535.881 - 0.003ms returns 0
T0818 002:535.885 JLINK_WriteReg(R2, 0x20000184)
T0818 002:535.889 - 0.003ms returns 0
T0818 002:535.893 JLINK_WriteReg(R3, 0x00000000)
T0818 002:535.896 - 0.003ms returns 0
T0818 002:535.900 JLINK_WriteReg(R4, 0x00000000)
T0818 002:535.903 - 0.003ms returns 0
T0818 002:535.907 JLINK_WriteReg(R5, 0x00000000)
T0818 002:535.911 - 0.003ms returns 0
T0818 002:535.915 JLINK_WriteReg(R6, 0x00000000)
T0818 002:535.918 - 0.003ms returns 0
T0818 002:535.922 JLINK_WriteReg(R7, 0x00000000)
T0818 002:535.925 - 0.003ms returns 0
T0818 002:535.929 JLINK_WriteReg(R8, 0x00000000)
T0818 002:535.933 - 0.003ms returns 0
T0818 002:535.937 JLINK_WriteReg(R9, 0x20000180)
T0818 002:535.940 - 0.003ms returns 0
T0818 002:535.944 JLINK_WriteReg(R10, 0x00000000)
T0818 002:535.948 - 0.003ms returns 0
T0818 002:535.952 JLINK_WriteReg(R11, 0x00000000)
T0818 002:535.955 - 0.003ms returns 0
T0818 002:535.959 JLINK_WriteReg(R12, 0x00000000)
T0818 002:535.963 - 0.003ms returns 0
T0818 002:535.967 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:535.970 - 0.003ms returns 0
T0818 002:535.974 JLINK_WriteReg(R14, 0x20000001)
T0818 002:535.978 - 0.003ms returns 0
T0818 002:535.982 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:535.985 - 0.003ms returns 0
T0818 002:535.989 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:535.993 - 0.003ms returns 0
T0818 002:535.996 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:536.000 - 0.003ms returns 0
T0818 002:536.004 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:536.007 - 0.003ms returns 0
T0818 002:536.011 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:536.015 - 0.003ms returns 0
T0818 002:536.019 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:536.023 - 0.004ms returns 0x0000001C
T0818 002:536.027 JLINK_Go()
T0818 002:536.035   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:538.848 - 2.820ms 
T0818 002:538.854 JLINK_IsHalted()
T0818 002:539.329 - 0.474ms returns FALSE
T0818 002:539.335 JLINK_HasError()
T0818 002:542.034 JLINK_IsHalted()
T0818 002:542.534 - 0.500ms returns FALSE
T0818 002:542.540 JLINK_HasError()
T0818 002:546.033 JLINK_IsHalted()
T0818 002:548.352   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:548.850 - 2.817ms returns TRUE
T0818 002:548.857 JLINK_ReadReg(R15 (PC))
T0818 002:548.861 - 0.004ms returns 0x20000000
T0818 002:548.865 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T0818 002:548.869 - 0.003ms returns 0x00
T0818 002:548.873 JLINK_ReadReg(R0)
T0818 002:548.877 - 0.003ms returns 0x00000000
T0818 002:549.154 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:549.160   Data:  F6 01 F6 05 F6 03 F6 07 0E 00 0E 04 0E 02 0E 06 ...
T0818 002:549.170   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:551.829 - 2.675ms returns 0x27C
T0818 002:551.842 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:551.846   Data:  D8 6B 67 3F 3C BF 68 3F A7 09 6A 3F 0C 4B 6B 3F ...
T0818 002:551.854   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:553.850 - 2.008ms returns 0x184
T0818 002:553.861 JLINK_HasError()
T0818 002:553.866 JLINK_WriteReg(R0, 0x08004400)
T0818 002:553.871 - 0.005ms returns 0
T0818 002:553.875 JLINK_WriteReg(R1, 0x00000400)
T0818 002:553.879 - 0.003ms returns 0
T0818 002:553.883 JLINK_WriteReg(R2, 0x20000184)
T0818 002:553.891 - 0.007ms returns 0
T0818 002:553.895 JLINK_WriteReg(R3, 0x00000000)
T0818 002:553.898 - 0.003ms returns 0
T0818 002:553.902 JLINK_WriteReg(R4, 0x00000000)
T0818 002:553.906 - 0.003ms returns 0
T0818 002:553.910 JLINK_WriteReg(R5, 0x00000000)
T0818 002:553.913 - 0.003ms returns 0
T0818 002:553.917 JLINK_WriteReg(R6, 0x00000000)
T0818 002:553.921 - 0.003ms returns 0
T0818 002:553.925 JLINK_WriteReg(R7, 0x00000000)
T0818 002:553.928 - 0.003ms returns 0
T0818 002:553.932 JLINK_WriteReg(R8, 0x00000000)
T0818 002:553.935 - 0.003ms returns 0
T0818 002:553.939 JLINK_WriteReg(R9, 0x20000180)
T0818 002:553.943 - 0.003ms returns 0
T0818 002:553.947 JLINK_WriteReg(R10, 0x00000000)
T0818 002:553.950 - 0.003ms returns 0
T0818 002:553.954 JLINK_WriteReg(R11, 0x00000000)
T0818 002:553.957 - 0.003ms returns 0
T0818 002:553.961 JLINK_WriteReg(R12, 0x00000000)
T0818 002:553.965 - 0.003ms returns 0
T0818 002:553.969 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:553.972 - 0.003ms returns 0
T0818 002:553.977 JLINK_WriteReg(R14, 0x20000001)
T0818 002:553.980 - 0.003ms returns 0
T0818 002:553.984 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:553.988 - 0.003ms returns 0
T0818 002:553.992 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:553.995 - 0.003ms returns 0
T0818 002:553.999 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:554.003 - 0.003ms returns 0
T0818 002:554.007 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:554.010 - 0.003ms returns 0
T0818 002:554.014 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:554.017 - 0.003ms returns 0
T0818 002:554.022 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:554.026 - 0.004ms returns 0x0000001D
T0818 002:554.030 JLINK_Go()
T0818 002:554.037   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:556.820 - 2.790ms 
T0818 002:556.826 JLINK_IsHalted()
T0818 002:557.266 - 0.440ms returns FALSE
T0818 002:557.272 JLINK_HasError()
T0818 002:561.049 JLINK_IsHalted()
T0818 002:563.532   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:564.000 - 2.950ms returns TRUE
T0818 002:564.006 JLINK_ReadReg(R15 (PC))
T0818 002:564.011 - 0.004ms returns 0x20000000
T0818 002:564.015 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T0818 002:564.019 - 0.003ms returns 0x00
T0818 002:564.023 JLINK_ReadReg(R0)
T0818 002:564.027 - 0.003ms returns 0x00000000
T0818 002:564.325 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:564.331   Data:  3B 8F 3B 3F 42 68 39 3F 23 3A 37 3F F3 04 35 3F ...
T0818 002:564.340   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:566.961 - 2.636ms returns 0x27C
T0818 002:566.974 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:566.978   Data:  D8 6B 67 BF 3C BF 68 BF A7 09 6A BF 0C 4B 6B BF ...
T0818 002:566.988   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:568.913 - 1.938ms returns 0x184
T0818 002:568.926 JLINK_HasError()
T0818 002:568.931 JLINK_WriteReg(R0, 0x08004800)
T0818 002:568.936 - 0.005ms returns 0
T0818 002:568.940 JLINK_WriteReg(R1, 0x00000400)
T0818 002:568.944 - 0.003ms returns 0
T0818 002:568.948 JLINK_WriteReg(R2, 0x20000184)
T0818 002:568.951 - 0.003ms returns 0
T0818 002:568.955 JLINK_WriteReg(R3, 0x00000000)
T0818 002:568.959 - 0.003ms returns 0
T0818 002:568.963 JLINK_WriteReg(R4, 0x00000000)
T0818 002:568.966 - 0.003ms returns 0
T0818 002:568.970 JLINK_WriteReg(R5, 0x00000000)
T0818 002:568.974 - 0.003ms returns 0
T0818 002:568.978 JLINK_WriteReg(R6, 0x00000000)
T0818 002:568.981 - 0.003ms returns 0
T0818 002:568.985 JLINK_WriteReg(R7, 0x00000000)
T0818 002:568.988 - 0.003ms returns 0
T0818 002:568.993 JLINK_WriteReg(R8, 0x00000000)
T0818 002:568.996 - 0.003ms returns 0
T0818 002:569.000 JLINK_WriteReg(R9, 0x20000180)
T0818 002:569.003 - 0.003ms returns 0
T0818 002:569.007 JLINK_WriteReg(R10, 0x00000000)
T0818 002:569.011 - 0.003ms returns 0
T0818 002:569.015 JLINK_WriteReg(R11, 0x00000000)
T0818 002:569.018 - 0.003ms returns 0
T0818 002:569.022 JLINK_WriteReg(R12, 0x00000000)
T0818 002:569.028 - 0.006ms returns 0
T0818 002:569.033 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:569.039 - 0.006ms returns 0
T0818 002:569.044 JLINK_WriteReg(R14, 0x20000001)
T0818 002:569.048 - 0.003ms returns 0
T0818 002:569.052 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:569.055 - 0.003ms returns 0
T0818 002:569.059 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:569.063 - 0.003ms returns 0
T0818 002:569.067 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:569.070 - 0.003ms returns 0
T0818 002:569.074 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:569.078 - 0.003ms returns 0
T0818 002:569.082 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:569.085 - 0.003ms returns 0
T0818 002:569.090 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:569.094 - 0.004ms returns 0x0000001E
T0818 002:569.098 JLINK_Go()
T0818 002:569.106   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:571.871 - 2.772ms 
T0818 002:571.890 JLINK_IsHalted()
T0818 002:572.390 - 0.500ms returns FALSE
T0818 002:572.405 JLINK_HasError()
T0818 002:575.036 JLINK_IsHalted()
T0818 002:575.582 - 0.545ms returns FALSE
T0818 002:575.595 JLINK_HasError()
T0818 002:578.033 JLINK_IsHalted()
T0818 002:580.364   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:580.857 - 2.823ms returns TRUE
T0818 002:580.864 JLINK_ReadReg(R15 (PC))
T0818 002:580.868 - 0.004ms returns 0x20000000
T0818 002:580.873 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T0818 002:580.877 - 0.004ms returns 0x00
T0818 002:580.881 JLINK_ReadReg(R0)
T0818 002:580.885 - 0.003ms returns 0x00000000
T0818 002:581.371 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:581.382   Data:  3B 8F 3B BF 42 68 39 BF 23 3A 37 BF F3 04 35 BF ...
T0818 002:581.394   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:583.900 - 2.529ms returns 0x27C
T0818 002:583.911 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:583.915   Data:  0E 88 55 3D 91 A1 7F 3F 4C CE 5B 3D 18 9C 7F 3F ...
T0818 002:583.924   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:585.849 - 1.938ms returns 0x184
T0818 002:585.856 JLINK_HasError()
T0818 002:585.861 JLINK_WriteReg(R0, 0x08004C00)
T0818 002:585.866 - 0.004ms returns 0
T0818 002:585.871 JLINK_WriteReg(R1, 0x00000400)
T0818 002:585.875 - 0.003ms returns 0
T0818 002:585.879 JLINK_WriteReg(R2, 0x20000184)
T0818 002:585.882 - 0.003ms returns 0
T0818 002:585.886 JLINK_WriteReg(R3, 0x00000000)
T0818 002:585.890 - 0.003ms returns 0
T0818 002:585.894 JLINK_WriteReg(R4, 0x00000000)
T0818 002:585.898 - 0.004ms returns 0
T0818 002:585.902 JLINK_WriteReg(R5, 0x00000000)
T0818 002:585.905 - 0.003ms returns 0
T0818 002:585.909 JLINK_WriteReg(R6, 0x00000000)
T0818 002:585.912 - 0.003ms returns 0
T0818 002:585.916 JLINK_WriteReg(R7, 0x00000000)
T0818 002:585.920 - 0.003ms returns 0
T0818 002:585.924 JLINK_WriteReg(R8, 0x00000000)
T0818 002:585.927 - 0.003ms returns 0
T0818 002:585.931 JLINK_WriteReg(R9, 0x20000180)
T0818 002:585.935 - 0.003ms returns 0
T0818 002:585.939 JLINK_WriteReg(R10, 0x00000000)
T0818 002:585.942 - 0.003ms returns 0
T0818 002:585.946 JLINK_WriteReg(R11, 0x00000000)
T0818 002:585.949 - 0.003ms returns 0
T0818 002:585.953 JLINK_WriteReg(R12, 0x00000000)
T0818 002:585.957 - 0.003ms returns 0
T0818 002:585.968 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:585.972 - 0.011ms returns 0
T0818 002:585.976 JLINK_WriteReg(R14, 0x20000001)
T0818 002:585.979 - 0.003ms returns 0
T0818 002:585.984 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:585.987 - 0.003ms returns 0
T0818 002:585.991 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:585.994 - 0.003ms returns 0
T0818 002:585.999 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:586.002 - 0.003ms returns 0
T0818 002:586.006 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:586.009 - 0.003ms returns 0
T0818 002:586.013 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:586.017 - 0.003ms returns 0
T0818 002:586.021 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:586.025 - 0.004ms returns 0x0000001F
T0818 002:586.030 JLINK_Go()
T0818 002:586.038   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:588.848 - 2.818ms 
T0818 002:588.854 JLINK_IsHalted()
T0818 002:589.328 - 0.474ms returns FALSE
T0818 002:589.336 JLINK_HasError()
T0818 002:592.037 JLINK_IsHalted()
T0818 002:592.491 - 0.453ms returns FALSE
T0818 002:592.497 JLINK_HasError()
T0818 002:596.032 JLINK_IsHalted()
T0818 002:598.793   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:599.285 - 3.253ms returns TRUE
T0818 002:599.296 JLINK_ReadReg(R15 (PC))
T0818 002:599.301 - 0.005ms returns 0x20000000
T0818 002:599.330 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T0818 002:599.334 - 0.004ms returns 0x00
T0818 002:599.339 JLINK_ReadReg(R0)
T0818 002:599.343 - 0.003ms returns 0x00000000
T0818 002:599.621 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:599.627   Data:  88 ED 7D 3F 2E 06 02 3E B1 E0 7D 3F 02 95 03 3E ...
T0818 002:599.636   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:602.235 - 2.614ms returns 0x27C
T0818 002:602.246 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:602.250   Data:  A4 DB 7B 3E 04 0A 78 3F 56 61 7D 3E 10 F1 77 3F ...
T0818 002:602.265   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:604.197 - 1.950ms returns 0x184
T0818 002:604.203 JLINK_HasError()
T0818 002:604.208 JLINK_WriteReg(R0, 0x08005000)
T0818 002:604.213 - 0.004ms returns 0
T0818 002:604.217 JLINK_WriteReg(R1, 0x00000400)
T0818 002:604.220 - 0.003ms returns 0
T0818 002:604.224 JLINK_WriteReg(R2, 0x20000184)
T0818 002:604.228 - 0.003ms returns 0
T0818 002:604.232 JLINK_WriteReg(R3, 0x00000000)
T0818 002:604.235 - 0.003ms returns 0
T0818 002:604.239 JLINK_WriteReg(R4, 0x00000000)
T0818 002:604.243 - 0.003ms returns 0
T0818 002:604.247 JLINK_WriteReg(R5, 0x00000000)
T0818 002:604.250 - 0.003ms returns 0
T0818 002:604.254 JLINK_WriteReg(R6, 0x00000000)
T0818 002:604.258 - 0.003ms returns 0
T0818 002:604.262 JLINK_WriteReg(R7, 0x00000000)
T0818 002:604.265 - 0.003ms returns 0
T0818 002:604.269 JLINK_WriteReg(R8, 0x00000000)
T0818 002:604.272 - 0.003ms returns 0
T0818 002:604.276 JLINK_WriteReg(R9, 0x20000180)
T0818 002:604.280 - 0.003ms returns 0
T0818 002:604.284 JLINK_WriteReg(R10, 0x00000000)
T0818 002:604.287 - 0.003ms returns 0
T0818 002:604.291 JLINK_WriteReg(R11, 0x00000000)
T0818 002:604.295 - 0.003ms returns 0
T0818 002:604.298 JLINK_WriteReg(R12, 0x00000000)
T0818 002:604.302 - 0.003ms returns 0
T0818 002:604.306 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:604.310 - 0.003ms returns 0
T0818 002:604.314 JLINK_WriteReg(R14, 0x20000001)
T0818 002:604.317 - 0.003ms returns 0
T0818 002:604.321 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:604.325 - 0.003ms returns 0
T0818 002:604.329 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:604.332 - 0.003ms returns 0
T0818 002:604.336 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:604.340 - 0.003ms returns 0
T0818 002:604.344 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:604.347 - 0.003ms returns 0
T0818 002:604.351 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:604.354 - 0.003ms returns 0
T0818 002:604.359 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:604.363 - 0.004ms returns 0x00000020
T0818 002:604.367 JLINK_Go()
T0818 002:604.374   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:607.086 - 2.718ms 
T0818 002:607.099 JLINK_IsHalted()
T0818 002:607.580 - 0.480ms returns FALSE
T0818 002:607.589 JLINK_HasError()
T0818 002:610.033 JLINK_IsHalted()
T0818 002:610.515 - 0.481ms returns FALSE
T0818 002:610.521 JLINK_HasError()
T0818 002:613.034 JLINK_IsHalted()
T0818 002:615.357   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:615.850 - 2.815ms returns TRUE
T0818 002:615.856 JLINK_ReadReg(R15 (PC))
T0818 002:615.861 - 0.004ms returns 0x20000000
T0818 002:615.866 JLINK_ClrBPEx(BPHandle = 0x00000020)
T0818 002:615.869 - 0.003ms returns 0x00
T0818 002:615.874 JLINK_ReadReg(R0)
T0818 002:615.877 - 0.003ms returns 0x00000000
T0818 002:616.156 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:616.163   Data:  04 B5 72 3F 33 D7 A2 3E F8 94 72 3F C5 95 A3 3E ...
T0818 002:616.172   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:618.822 - 2.666ms returns 0x27C
T0818 002:618.829 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:618.832   Data:  C1 53 DC 3E 45 EA 66 3F 2E 09 DD 3E CC BE 66 3F ...
T0818 002:618.844   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:620.730 - 1.901ms returns 0x184
T0818 002:620.736 JLINK_HasError()
T0818 002:620.741 JLINK_WriteReg(R0, 0x08005400)
T0818 002:620.745 - 0.004ms returns 0
T0818 002:620.760 JLINK_WriteReg(R1, 0x00000400)
T0818 002:620.763 - 0.003ms returns 0
T0818 002:620.767 JLINK_WriteReg(R2, 0x20000184)
T0818 002:620.770 - 0.003ms returns 0
T0818 002:620.775 JLINK_WriteReg(R3, 0x00000000)
T0818 002:620.778 - 0.003ms returns 0
T0818 002:620.782 JLINK_WriteReg(R4, 0x00000000)
T0818 002:620.785 - 0.003ms returns 0
T0818 002:620.790 JLINK_WriteReg(R5, 0x00000000)
T0818 002:620.793 - 0.003ms returns 0
T0818 002:620.797 JLINK_WriteReg(R6, 0x00000000)
T0818 002:620.800 - 0.003ms returns 0
T0818 002:620.804 JLINK_WriteReg(R7, 0x00000000)
T0818 002:620.808 - 0.003ms returns 0
T0818 002:620.814 JLINK_WriteReg(R8, 0x00000000)
T0818 002:620.818 - 0.003ms returns 0
T0818 002:620.822 JLINK_WriteReg(R9, 0x20000180)
T0818 002:620.825 - 0.003ms returns 0
T0818 002:620.829 JLINK_WriteReg(R10, 0x00000000)
T0818 002:620.833 - 0.003ms returns 0
T0818 002:620.837 JLINK_WriteReg(R11, 0x00000000)
T0818 002:620.840 - 0.003ms returns 0
T0818 002:620.844 JLINK_WriteReg(R12, 0x00000000)
T0818 002:620.848 - 0.003ms returns 0
T0818 002:620.852 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:620.855 - 0.003ms returns 0
T0818 002:620.859 JLINK_WriteReg(R14, 0x20000001)
T0818 002:620.863 - 0.003ms returns 0
T0818 002:620.867 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:620.870 - 0.003ms returns 0
T0818 002:620.874 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:620.877 - 0.003ms returns 0
T0818 002:620.881 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:620.885 - 0.003ms returns 0
T0818 002:620.889 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:620.892 - 0.003ms returns 0
T0818 002:620.896 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:620.900 - 0.003ms returns 0
T0818 002:620.904 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:620.908 - 0.004ms returns 0x00000021
T0818 002:620.912 JLINK_Go()
T0818 002:620.919   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:623.740 - 2.827ms 
T0818 002:623.751 JLINK_IsHalted()
T0818 002:624.196 - 0.445ms returns FALSE
T0818 002:624.202 JLINK_HasError()
T0818 002:627.032 JLINK_IsHalted()
T0818 002:627.522 - 0.490ms returns FALSE
T0818 002:627.528 JLINK_HasError()
T0818 002:630.032 JLINK_IsHalted()
T0818 002:632.365   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:632.822 - 2.790ms returns TRUE
T0818 002:632.829 JLINK_ReadReg(R15 (PC))
T0818 002:632.834 - 0.004ms returns 0x20000000
T0818 002:632.838 JLINK_ClrBPEx(BPHandle = 0x00000021)
T0818 002:632.842 - 0.003ms returns 0x00
T0818 002:632.846 JLINK_ReadReg(R0)
T0818 002:632.850 - 0.003ms returns 0x00000000
T0818 002:633.125 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:633.132   Data:  C3 28 5E 3F 4A 69 FE 3E BE F6 5D 3F B2 17 FF 3E ...
T0818 002:633.141   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:635.851 - 2.725ms returns 0x27C
T0818 002:635.860 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:635.864   Data:  0F 21 19 3F CB EA 4C 3F 94 71 19 3F 79 AE 4C 3F ...
T0818 002:635.872   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:637.743 - 1.882ms returns 0x184
T0818 002:637.749 JLINK_HasError()
T0818 002:637.754 JLINK_WriteReg(R0, 0x08005800)
T0818 002:637.758 - 0.004ms returns 0
T0818 002:637.762 JLINK_WriteReg(R1, 0x00000400)
T0818 002:637.765 - 0.003ms returns 0
T0818 002:637.769 JLINK_WriteReg(R2, 0x20000184)
T0818 002:637.773 - 0.003ms returns 0
T0818 002:637.777 JLINK_WriteReg(R3, 0x00000000)
T0818 002:637.780 - 0.003ms returns 0
T0818 002:637.784 JLINK_WriteReg(R4, 0x00000000)
T0818 002:637.787 - 0.003ms returns 0
T0818 002:637.791 JLINK_WriteReg(R5, 0x00000000)
T0818 002:637.795 - 0.003ms returns 0
T0818 002:637.799 JLINK_WriteReg(R6, 0x00000000)
T0818 002:637.802 - 0.003ms returns 0
T0818 002:637.806 JLINK_WriteReg(R7, 0x00000000)
T0818 002:637.810 - 0.003ms returns 0
T0818 002:637.817 JLINK_WriteReg(R8, 0x00000000)
T0818 002:637.821 - 0.004ms returns 0
T0818 002:637.825 JLINK_WriteReg(R9, 0x20000180)
T0818 002:637.829 - 0.003ms returns 0
T0818 002:637.833 JLINK_WriteReg(R10, 0x00000000)
T0818 002:637.836 - 0.003ms returns 0
T0818 002:637.840 JLINK_WriteReg(R11, 0x00000000)
T0818 002:637.844 - 0.003ms returns 0
T0818 002:637.848 JLINK_WriteReg(R12, 0x00000000)
T0818 002:637.857 - 0.009ms returns 0
T0818 002:637.862 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:637.866 - 0.003ms returns 0
T0818 002:637.870 JLINK_WriteReg(R14, 0x20000001)
T0818 002:637.873 - 0.003ms returns 0
T0818 002:637.877 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:637.880 - 0.003ms returns 0
T0818 002:637.884 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:637.888 - 0.003ms returns 0
T0818 002:637.892 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:637.895 - 0.003ms returns 0
T0818 002:637.899 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:637.902 - 0.003ms returns 0
T0818 002:637.907 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:637.910 - 0.003ms returns 0
T0818 002:637.915 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:637.918 - 0.004ms returns 0x00000022
T0818 002:637.922 JLINK_Go()
T0818 002:637.929   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:640.703 - 2.780ms 
T0818 002:640.709 JLINK_IsHalted()
T0818 002:641.417 - 0.707ms returns FALSE
T0818 002:641.424 JLINK_HasError()
T0818 002:645.033 JLINK_IsHalted()
T0818 002:647.403   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:647.913 - 2.879ms returns TRUE
T0818 002:647.921 JLINK_ReadReg(R15 (PC))
T0818 002:647.926 - 0.004ms returns 0x20000000
T0818 002:647.930 JLINK_ClrBPEx(BPHandle = 0x00000022)
T0818 002:647.934 - 0.003ms returns 0x00
T0818 002:647.938 JLINK_ReadReg(R0)
T0818 002:647.942 - 0.003ms returns 0x00000000
T0818 002:648.209 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:648.216   Data:  EC 12 41 3F 40 1A 28 3F DA D0 40 3F 05 66 28 3F ...
T0818 002:648.225   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:650.910 - 2.701ms returns 0x27C
T0818 002:650.917 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:650.921   Data:  C5 35 3E 3F 5B 0B 2B 3F FF 78 3E 3F 82 C0 2A 3F ...
T0818 002:650.928   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:652.859 - 1.941ms returns 0x184
T0818 002:652.876 JLINK_HasError()
T0818 002:652.907 JLINK_WriteReg(R0, 0x08005C00)
T0818 002:652.913 - 0.006ms returns 0
T0818 002:652.918 JLINK_WriteReg(R1, 0x00000400)
T0818 002:652.921 - 0.003ms returns 0
T0818 002:652.925 JLINK_WriteReg(R2, 0x20000184)
T0818 002:652.929 - 0.003ms returns 0
T0818 002:652.933 JLINK_WriteReg(R3, 0x00000000)
T0818 002:652.936 - 0.003ms returns 0
T0818 002:652.940 JLINK_WriteReg(R4, 0x00000000)
T0818 002:652.943 - 0.003ms returns 0
T0818 002:652.947 JLINK_WriteReg(R5, 0x00000000)
T0818 002:652.951 - 0.003ms returns 0
T0818 002:652.955 JLINK_WriteReg(R6, 0x00000000)
T0818 002:652.958 - 0.003ms returns 0
T0818 002:652.962 JLINK_WriteReg(R7, 0x00000000)
T0818 002:652.966 - 0.003ms returns 0
T0818 002:652.970 JLINK_WriteReg(R8, 0x00000000)
T0818 002:652.984 - 0.014ms returns 0
T0818 002:652.988 JLINK_WriteReg(R9, 0x20000180)
T0818 002:652.992 - 0.003ms returns 0
T0818 002:652.996 JLINK_WriteReg(R10, 0x00000000)
T0818 002:652.999 - 0.003ms returns 0
T0818 002:653.003 JLINK_WriteReg(R11, 0x00000000)
T0818 002:653.007 - 0.003ms returns 0
T0818 002:653.010 JLINK_WriteReg(R12, 0x00000000)
T0818 002:653.014 - 0.003ms returns 0
T0818 002:653.018 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:653.022 - 0.003ms returns 0
T0818 002:653.026 JLINK_WriteReg(R14, 0x20000001)
T0818 002:653.029 - 0.003ms returns 0
T0818 002:653.033 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:653.037 - 0.003ms returns 0
T0818 002:653.041 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:653.044 - 0.003ms returns 0
T0818 002:653.048 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:653.052 - 0.003ms returns 0
T0818 002:653.056 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:653.059 - 0.003ms returns 0
T0818 002:653.066 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:653.071 - 0.004ms returns 0
T0818 002:653.076 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:653.080 - 0.004ms returns 0x00000023
T0818 002:653.084 JLINK_Go()
T0818 002:653.092   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:655.848 - 2.764ms 
T0818 002:655.857 JLINK_IsHalted()
T0818 002:656.343 - 0.486ms returns FALSE
T0818 002:656.349 JLINK_HasError()
T0818 002:659.032 JLINK_IsHalted()
T0818 002:659.551 - 0.518ms returns FALSE
T0818 002:659.556 JLINK_HasError()
T0818 002:661.192 JLINK_IsHalted()
T0818 002:663.488   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:663.993 - 2.800ms returns TRUE
T0818 002:664.000 JLINK_ReadReg(R15 (PC))
T0818 002:664.004 - 0.004ms returns 0x20000000
T0818 002:664.008 JLINK_ClrBPEx(BPHandle = 0x00000023)
T0818 002:664.012 - 0.003ms returns 0x00
T0818 002:664.016 JLINK_ReadReg(R0)
T0818 002:664.020 - 0.003ms returns 0x00000000
T0818 002:664.294 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:664.301   Data:  A2 91 1C 3F 13 8A 4A 3F 0C 42 1C 3F 7F C7 4A 3F ...
T0818 002:664.310   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:666.911 - 2.617ms returns 0x27C
T0818 002:666.918 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:666.921   Data:  34 FB 5B 3F 32 99 02 3F 8E 2E 5C 3F B1 42 02 3F ...
T0818 002:666.928   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:668.848 - 1.930ms returns 0x184
T0818 002:668.855 JLINK_HasError()
T0818 002:668.859 JLINK_WriteReg(R0, 0x08006000)
T0818 002:668.864 - 0.004ms returns 0
T0818 002:668.868 JLINK_WriteReg(R1, 0x00000400)
T0818 002:668.871 - 0.003ms returns 0
T0818 002:668.875 JLINK_WriteReg(R2, 0x20000184)
T0818 002:668.879 - 0.003ms returns 0
T0818 002:668.883 JLINK_WriteReg(R3, 0x00000000)
T0818 002:668.886 - 0.003ms returns 0
T0818 002:668.890 JLINK_WriteReg(R4, 0x00000000)
T0818 002:668.894 - 0.003ms returns 0
T0818 002:668.898 JLINK_WriteReg(R5, 0x00000000)
T0818 002:668.901 - 0.003ms returns 0
T0818 002:668.905 JLINK_WriteReg(R6, 0x00000000)
T0818 002:668.908 - 0.003ms returns 0
T0818 002:668.912 JLINK_WriteReg(R7, 0x00000000)
T0818 002:668.916 - 0.003ms returns 0
T0818 002:668.920 JLINK_WriteReg(R8, 0x00000000)
T0818 002:668.923 - 0.003ms returns 0
T0818 002:668.927 JLINK_WriteReg(R9, 0x20000180)
T0818 002:668.931 - 0.003ms returns 0
T0818 002:668.935 JLINK_WriteReg(R10, 0x00000000)
T0818 002:668.938 - 0.003ms returns 0
T0818 002:668.942 JLINK_WriteReg(R11, 0x00000000)
T0818 002:668.945 - 0.003ms returns 0
T0818 002:668.949 JLINK_WriteReg(R12, 0x00000000)
T0818 002:668.953 - 0.003ms returns 0
T0818 002:668.957 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:668.960 - 0.003ms returns 0
T0818 002:668.964 JLINK_WriteReg(R14, 0x20000001)
T0818 002:668.968 - 0.003ms returns 0
T0818 002:668.972 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:668.975 - 0.003ms returns 0
T0818 002:668.979 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:668.983 - 0.003ms returns 0
T0818 002:668.987 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:668.990 - 0.003ms returns 0
T0818 002:668.994 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:668.998 - 0.004ms returns 0
T0818 002:669.004 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:669.007 - 0.003ms returns 0
T0818 002:669.011 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:669.015 - 0.004ms returns 0x00000024
T0818 002:669.019 JLINK_Go()
T0818 002:669.034   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:671.829 - 2.809ms 
T0818 002:671.842 JLINK_IsHalted()
T0818 002:672.455 - 0.613ms returns FALSE
T0818 002:672.467 JLINK_HasError()
T0818 002:675.033 JLINK_IsHalted()
T0818 002:675.514 - 0.481ms returns FALSE
T0818 002:675.521 JLINK_HasError()
T0818 002:678.056 JLINK_IsHalted()
T0818 002:680.353   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:680.851 - 2.794ms returns TRUE
T0818 002:680.857 JLINK_ReadReg(R15 (PC))
T0818 002:680.862 - 0.004ms returns 0x20000000
T0818 002:680.866 JLINK_ClrBPEx(BPHandle = 0x00000024)
T0818 002:680.870 - 0.003ms returns 0x00
T0818 002:680.874 JLINK_ReadReg(R0)
T0818 002:680.881 - 0.007ms returns 0x00000000
T0818 002:681.296 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:681.307   Data:  0E 18 E4 3E 54 31 65 3F FA 63 E3 3E 0B 5E 65 3F ...
T0818 002:681.317   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:683.898 - 2.602ms returns 0x27C
T0818 002:683.910 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:683.914   Data:  7A 4C 71 3F 6F 44 AA 3E FB 6D 71 3F C4 86 A9 3E ...
T0818 002:683.923   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:685.830 - 1.920ms returns 0x184
T0818 002:685.836 JLINK_HasError()
T0818 002:685.841 JLINK_WriteReg(R0, 0x08006400)
T0818 002:685.846 - 0.004ms returns 0
T0818 002:685.850 JLINK_WriteReg(R1, 0x00000400)
T0818 002:685.853 - 0.003ms returns 0
T0818 002:685.857 JLINK_WriteReg(R2, 0x20000184)
T0818 002:685.861 - 0.003ms returns 0
T0818 002:685.865 JLINK_WriteReg(R3, 0x00000000)
T0818 002:685.868 - 0.003ms returns 0
T0818 002:685.872 JLINK_WriteReg(R4, 0x00000000)
T0818 002:685.876 - 0.003ms returns 0
T0818 002:685.880 JLINK_WriteReg(R5, 0x00000000)
T0818 002:685.883 - 0.003ms returns 0
T0818 002:685.887 JLINK_WriteReg(R6, 0x00000000)
T0818 002:685.890 - 0.003ms returns 0
T0818 002:685.894 JLINK_WriteReg(R7, 0x00000000)
T0818 002:685.898 - 0.003ms returns 0
T0818 002:685.902 JLINK_WriteReg(R8, 0x00000000)
T0818 002:685.905 - 0.003ms returns 0
T0818 002:685.909 JLINK_WriteReg(R9, 0x20000180)
T0818 002:685.912 - 0.003ms returns 0
T0818 002:685.917 JLINK_WriteReg(R10, 0x00000000)
T0818 002:685.920 - 0.003ms returns 0
T0818 002:685.924 JLINK_WriteReg(R11, 0x00000000)
T0818 002:685.928 - 0.003ms returns 0
T0818 002:685.932 JLINK_WriteReg(R12, 0x00000000)
T0818 002:685.935 - 0.003ms returns 0
T0818 002:685.939 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:685.943 - 0.003ms returns 0
T0818 002:685.947 JLINK_WriteReg(R14, 0x20000001)
T0818 002:685.950 - 0.003ms returns 0
T0818 002:685.954 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:685.958 - 0.003ms returns 0
T0818 002:685.962 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:685.965 - 0.003ms returns 0
T0818 002:685.969 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:685.978 - 0.009ms returns 0
T0818 002:685.983 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:685.986 - 0.003ms returns 0
T0818 002:685.990 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:685.994 - 0.003ms returns 0
T0818 002:685.998 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:686.002 - 0.004ms returns 0x00000025
T0818 002:686.006 JLINK_Go()
T0818 002:686.013   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:688.852 - 2.846ms 
T0818 002:688.858 JLINK_IsHalted()
T0818 002:689.312 - 0.453ms returns FALSE
T0818 002:689.318 JLINK_HasError()
T0818 002:692.035 JLINK_IsHalted()
T0818 002:692.513 - 0.477ms returns FALSE
T0818 002:692.524 JLINK_HasError()
T0818 002:694.032 JLINK_IsHalted()
T0818 002:696.316   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:696.835 - 2.803ms returns TRUE
T0818 002:696.841 JLINK_ReadReg(R15 (PC))
T0818 002:696.846 - 0.004ms returns 0x20000000
T0818 002:696.850 JLINK_ClrBPEx(BPHandle = 0x00000025)
T0818 002:696.854 - 0.003ms returns 0x00
T0818 002:696.858 JLINK_ReadReg(R0)
T0818 002:696.862 - 0.003ms returns 0x00000000
T0818 002:697.155 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:697.163   Data:  DF 48 86 3E CC 09 77 3F CE 86 85 3E 17 24 77 3F ...
T0818 002:697.172   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:699.729 - 2.574ms returns 0x27C
T0818 002:699.736 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:699.741   Data:  DE 57 7D 3F CC 96 11 3E 3D 66 7D 3F B7 08 10 3E ...
T0818 002:699.748   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:701.607 - 1.870ms returns 0x184
T0818 002:701.621 JLINK_HasError()
T0818 002:701.626 JLINK_WriteReg(R0, 0x08006800)
T0818 002:701.631 - 0.005ms returns 0
T0818 002:701.635 JLINK_WriteReg(R1, 0x00000400)
T0818 002:701.639 - 0.003ms returns 0
T0818 002:701.643 JLINK_WriteReg(R2, 0x20000184)
T0818 002:701.646 - 0.003ms returns 0
T0818 002:701.650 JLINK_WriteReg(R3, 0x00000000)
T0818 002:701.657 - 0.006ms returns 0
T0818 002:701.662 JLINK_WriteReg(R4, 0x00000000)
T0818 002:701.666 - 0.003ms returns 0
T0818 002:701.670 JLINK_WriteReg(R5, 0x00000000)
T0818 002:701.673 - 0.003ms returns 0
T0818 002:701.677 JLINK_WriteReg(R6, 0x00000000)
T0818 002:701.680 - 0.003ms returns 0
T0818 002:701.684 JLINK_WriteReg(R7, 0x00000000)
T0818 002:701.688 - 0.003ms returns 0
T0818 002:701.692 JLINK_WriteReg(R8, 0x00000000)
T0818 002:701.696 - 0.003ms returns 0
T0818 002:701.700 JLINK_WriteReg(R9, 0x20000180)
T0818 002:701.703 - 0.003ms returns 0
T0818 002:701.707 JLINK_WriteReg(R10, 0x00000000)
T0818 002:701.710 - 0.003ms returns 0
T0818 002:701.714 JLINK_WriteReg(R11, 0x00000000)
T0818 002:701.718 - 0.003ms returns 0
T0818 002:701.722 JLINK_WriteReg(R12, 0x00000000)
T0818 002:701.725 - 0.003ms returns 0
T0818 002:701.729 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:701.733 - 0.003ms returns 0
T0818 002:701.737 JLINK_WriteReg(R14, 0x20000001)
T0818 002:701.740 - 0.003ms returns 0
T0818 002:701.744 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:701.748 - 0.003ms returns 0
T0818 002:701.752 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:701.756 - 0.003ms returns 0
T0818 002:701.760 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:701.763 - 0.003ms returns 0
T0818 002:701.767 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:701.771 - 0.003ms returns 0
T0818 002:701.775 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:701.778 - 0.003ms returns 0
T0818 002:701.783 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:701.787 - 0.004ms returns 0x00000026
T0818 002:701.791 JLINK_Go()
T0818 002:701.798   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:704.535 - 2.743ms 
T0818 002:704.556 JLINK_IsHalted()
T0818 002:705.059 - 0.502ms returns FALSE
T0818 002:705.067 JLINK_HasError()
T0818 002:707.070 JLINK_IsHalted()
T0818 002:707.542 - 0.471ms returns FALSE
T0818 002:707.553 JLINK_HasError()
T0818 002:709.036 JLINK_IsHalted()
T0818 002:711.312   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:711.824 - 2.787ms returns TRUE
T0818 002:711.831 JLINK_ReadReg(R15 (PC))
T0818 002:711.836 - 0.005ms returns 0x20000000
T0818 002:711.841 JLINK_ClrBPEx(BPHandle = 0x00000026)
T0818 002:711.845 - 0.004ms returns 0x00
T0818 002:711.849 JLINK_ReadReg(R0)
T0818 002:711.853 - 0.003ms returns 0x00000000
T0818 002:712.201 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:712.209   Data:  6A 42 8D 3D EC 63 7F 3F 0A 20 8A 3D C7 6A 7F 3F ...
T0818 002:712.219   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:714.833 - 2.632ms returns 0x27C
T0818 002:714.841 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:714.844   Data:  E3 A6 7F 3F 4C CE 5B BD 91 A1 7F 3F 68 14 62 BD ...
T0818 002:714.851   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:716.737 - 1.895ms returns 0x184
T0818 002:716.748 JLINK_HasError()
T0818 002:716.753 JLINK_WriteReg(R0, 0x08006C00)
T0818 002:716.758 - 0.005ms returns 0
T0818 002:716.762 JLINK_WriteReg(R1, 0x00000400)
T0818 002:716.766 - 0.003ms returns 0
T0818 002:716.770 JLINK_WriteReg(R2, 0x20000184)
T0818 002:716.773 - 0.003ms returns 0
T0818 002:716.777 JLINK_WriteReg(R3, 0x00000000)
T0818 002:716.781 - 0.003ms returns 0
T0818 002:716.785 JLINK_WriteReg(R4, 0x00000000)
T0818 002:716.788 - 0.003ms returns 0
T0818 002:716.792 JLINK_WriteReg(R5, 0x00000000)
T0818 002:716.796 - 0.003ms returns 0
T0818 002:716.800 JLINK_WriteReg(R6, 0x00000000)
T0818 002:716.803 - 0.003ms returns 0
T0818 002:716.807 JLINK_WriteReg(R7, 0x00000000)
T0818 002:716.810 - 0.003ms returns 0
T0818 002:716.815 JLINK_WriteReg(R8, 0x00000000)
T0818 002:716.818 - 0.003ms returns 0
T0818 002:716.822 JLINK_WriteReg(R9, 0x20000180)
T0818 002:716.825 - 0.003ms returns 0
T0818 002:716.829 JLINK_WriteReg(R10, 0x00000000)
T0818 002:716.833 - 0.003ms returns 0
T0818 002:716.837 JLINK_WriteReg(R11, 0x00000000)
T0818 002:716.840 - 0.003ms returns 0
T0818 002:716.844 JLINK_WriteReg(R12, 0x00000000)
T0818 002:716.848 - 0.003ms returns 0
T0818 002:716.852 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:716.858 - 0.006ms returns 0
T0818 002:716.864 JLINK_WriteReg(R14, 0x20000001)
T0818 002:716.867 - 0.003ms returns 0
T0818 002:716.872 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:716.875 - 0.004ms returns 0
T0818 002:716.879 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:716.883 - 0.003ms returns 0
T0818 002:716.887 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:716.890 - 0.003ms returns 0
T0818 002:716.894 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:716.898 - 0.003ms returns 0
T0818 002:716.902 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:716.905 - 0.003ms returns 0
T0818 002:716.910 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:716.914 - 0.004ms returns 0x00000027
T0818 002:716.918 JLINK_Go()
T0818 002:716.934   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:719.685 - 2.766ms 
T0818 002:719.691 JLINK_IsHalted()
T0818 002:720.194 - 0.503ms returns FALSE
T0818 002:720.200 JLINK_HasError()
T0818 002:722.049 JLINK_IsHalted()
T0818 002:722.736 - 0.685ms returns FALSE
T0818 002:722.749 JLINK_HasError()
T0818 002:725.033 JLINK_IsHalted()
T0818 002:727.466   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:728.004 - 2.970ms returns TRUE
T0818 002:728.016 JLINK_ReadReg(R15 (PC))
T0818 002:728.021 - 0.005ms returns 0x20000000
T0818 002:728.029 JLINK_ClrBPEx(BPHandle = 0x00000027)
T0818 002:728.033 - 0.004ms returns 0x00
T0818 002:728.037 JLINK_ReadReg(R0)
T0818 002:728.041 - 0.003ms returns 0x00000000
T0818 002:728.379 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:728.386   Data:  2E 06 02 BE 88 ED 7D 3F 02 95 03 BE B1 E0 7D 3F ...
T0818 002:728.396   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:730.928 - 2.549ms returns 0x27C
T0818 002:730.941 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:730.945   Data:  D1 22 78 3F 56 61 7D BE 04 0A 78 3F E1 E6 7E BE ...
T0818 002:731.067   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:733.119 - 2.177ms returns 0x184
T0818 002:733.133 JLINK_HasError()
T0818 002:733.166 JLINK_WriteReg(R0, 0x08007000)
T0818 002:733.172 - 0.006ms returns 0
T0818 002:733.177 JLINK_WriteReg(R1, 0x00000400)
T0818 002:733.180 - 0.003ms returns 0
T0818 002:733.184 JLINK_WriteReg(R2, 0x20000184)
T0818 002:733.188 - 0.003ms returns 0
T0818 002:733.192 JLINK_WriteReg(R3, 0x00000000)
T0818 002:733.195 - 0.003ms returns 0
T0818 002:733.199 JLINK_WriteReg(R4, 0x00000000)
T0818 002:733.202 - 0.003ms returns 0
T0818 002:733.207 JLINK_WriteReg(R5, 0x00000000)
T0818 002:733.210 - 0.003ms returns 0
T0818 002:733.214 JLINK_WriteReg(R6, 0x00000000)
T0818 002:733.218 - 0.003ms returns 0
T0818 002:733.222 JLINK_WriteReg(R7, 0x00000000)
T0818 002:733.225 - 0.003ms returns 0
T0818 002:733.229 JLINK_WriteReg(R8, 0x00000000)
T0818 002:733.232 - 0.003ms returns 0
T0818 002:733.237 JLINK_WriteReg(R9, 0x20000180)
T0818 002:733.240 - 0.003ms returns 0
T0818 002:733.244 JLINK_WriteReg(R10, 0x00000000)
T0818 002:733.248 - 0.003ms returns 0
T0818 002:733.252 JLINK_WriteReg(R11, 0x00000000)
T0818 002:733.255 - 0.003ms returns 0
T0818 002:733.259 JLINK_WriteReg(R12, 0x00000000)
T0818 002:733.263 - 0.003ms returns 0
T0818 002:733.267 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:733.270 - 0.003ms returns 0
T0818 002:733.274 JLINK_WriteReg(R14, 0x20000001)
T0818 002:733.278 - 0.003ms returns 0
T0818 002:733.282 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:733.285 - 0.003ms returns 0
T0818 002:733.289 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:733.292 - 0.003ms returns 0
T0818 002:733.305 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:733.308 - 0.003ms returns 0
T0818 002:733.312 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:733.316 - 0.003ms returns 0
T0818 002:733.320 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:733.323 - 0.003ms returns 0
T0818 002:733.328 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:733.332 - 0.004ms returns 0x00000028
T0818 002:733.336 JLINK_Go()
T0818 002:733.345   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:736.086 - 2.750ms 
T0818 002:736.093 JLINK_IsHalted()
T0818 002:736.571 - 0.478ms returns FALSE
T0818 002:736.579 JLINK_HasError()
T0818 002:738.031 JLINK_IsHalted()
T0818 002:738.575 - 0.543ms returns FALSE
T0818 002:738.587 JLINK_HasError()
T0818 002:740.199 JLINK_IsHalted()
T0818 002:742.486   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:742.972 - 2.772ms returns TRUE
T0818 002:742.978 JLINK_ReadReg(R15 (PC))
T0818 002:742.982 - 0.004ms returns 0x20000000
T0818 002:742.986 JLINK_ClrBPEx(BPHandle = 0x00000028)
T0818 002:742.990 - 0.003ms returns 0x00
T0818 002:742.994 JLINK_ReadReg(R0)
T0818 002:742.998 - 0.003ms returns 0x00000000
T0818 002:743.300 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:743.307   Data:  33 D7 A2 BE 04 B5 72 3F C5 95 A3 BE F8 94 72 3F ...
T0818 002:743.316   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:745.895 - 2.595ms returns 0x27C
T0818 002:745.901 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:745.905   Data:  99 15 67 3F 2E 09 DD BE 45 EA 66 3F 79 BE DD BE ...
T0818 002:745.912   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:747.829 - 1.927ms returns 0x184
T0818 002:747.835 JLINK_HasError()
T0818 002:747.839 JLINK_WriteReg(R0, 0x08007400)
T0818 002:747.844 - 0.004ms returns 0
T0818 002:747.848 JLINK_WriteReg(R1, 0x00000400)
T0818 002:747.851 - 0.003ms returns 0
T0818 002:747.855 JLINK_WriteReg(R2, 0x20000184)
T0818 002:747.859 - 0.003ms returns 0
T0818 002:747.863 JLINK_WriteReg(R3, 0x00000000)
T0818 002:747.866 - 0.003ms returns 0
T0818 002:747.870 JLINK_WriteReg(R4, 0x00000000)
T0818 002:747.874 - 0.003ms returns 0
T0818 002:747.878 JLINK_WriteReg(R5, 0x00000000)
T0818 002:747.881 - 0.003ms returns 0
T0818 002:747.885 JLINK_WriteReg(R6, 0x00000000)
T0818 002:747.888 - 0.003ms returns 0
T0818 002:747.892 JLINK_WriteReg(R7, 0x00000000)
T0818 002:747.896 - 0.003ms returns 0
T0818 002:747.900 JLINK_WriteReg(R8, 0x00000000)
T0818 002:747.903 - 0.003ms returns 0
T0818 002:747.907 JLINK_WriteReg(R9, 0x20000180)
T0818 002:747.910 - 0.003ms returns 0
T0818 002:747.914 JLINK_WriteReg(R10, 0x00000000)
T0818 002:747.918 - 0.003ms returns 0
T0818 002:747.922 JLINK_WriteReg(R11, 0x00000000)
T0818 002:747.925 - 0.003ms returns 0
T0818 002:747.929 JLINK_WriteReg(R12, 0x00000000)
T0818 002:747.933 - 0.003ms returns 0
T0818 002:747.937 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:747.940 - 0.003ms returns 0
T0818 002:747.944 JLINK_WriteReg(R14, 0x20000001)
T0818 002:747.948 - 0.003ms returns 0
T0818 002:747.952 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:747.955 - 0.003ms returns 0
T0818 002:747.959 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:747.963 - 0.003ms returns 0
T0818 002:747.967 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:747.970 - 0.003ms returns 0
T0818 002:747.974 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:747.977 - 0.003ms returns 0
T0818 002:747.981 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:747.985 - 0.004ms returns 0
T0818 002:747.991 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:747.995 - 0.004ms returns 0x00000029
T0818 002:747.999 JLINK_Go()
T0818 002:748.006   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:750.756 - 2.756ms 
T0818 002:750.762 JLINK_IsHalted()
T0818 002:751.428 - 0.665ms returns FALSE
T0818 002:751.479 JLINK_HasError()
T0818 002:753.205 JLINK_IsHalted()
T0818 002:754.065 - 0.860ms returns FALSE
T0818 002:754.079 JLINK_HasError()
T0818 002:755.217 JLINK_IsHalted()
T0818 002:757.498   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:758.016 - 2.798ms returns TRUE
T0818 002:758.022 JLINK_ReadReg(R15 (PC))
T0818 002:758.027 - 0.005ms returns 0x20000000
T0818 002:758.032 JLINK_ClrBPEx(BPHandle = 0x00000029)
T0818 002:758.036 - 0.004ms returns 0x00
T0818 002:758.040 JLINK_ReadReg(R0)
T0818 002:758.044 - 0.003ms returns 0x00000000
T0818 002:758.368 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:758.375   Data:  4A 69 FE BE C3 28 5E 3F B2 17 FF BE BE F6 5D 3F ...
T0818 002:758.386   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:760.893 - 2.524ms returns 0x27C
T0818 002:760.899 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:760.903   Data:  FD 26 4D 3F 94 71 19 BF CB EA 4C 3F 00 C2 19 BF ...
T0818 002:760.914   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:762.838 - 1.938ms returns 0x184
T0818 002:762.848 JLINK_HasError()
T0818 002:762.854 JLINK_WriteReg(R0, 0x08007800)
T0818 002:762.859 - 0.004ms returns 0
T0818 002:762.863 JLINK_WriteReg(R1, 0x00000400)
T0818 002:762.866 - 0.003ms returns 0
T0818 002:762.870 JLINK_WriteReg(R2, 0x20000184)
T0818 002:762.874 - 0.003ms returns 0
T0818 002:762.878 JLINK_WriteReg(R3, 0x00000000)
T0818 002:762.881 - 0.003ms returns 0
T0818 002:762.885 JLINK_WriteReg(R4, 0x00000000)
T0818 002:762.888 - 0.003ms returns 0
T0818 002:762.892 JLINK_WriteReg(R5, 0x00000000)
T0818 002:762.896 - 0.003ms returns 0
T0818 002:762.900 JLINK_WriteReg(R6, 0x00000000)
T0818 002:762.903 - 0.003ms returns 0
T0818 002:762.907 JLINK_WriteReg(R7, 0x00000000)
T0818 002:762.910 - 0.003ms returns 0
T0818 002:762.914 JLINK_WriteReg(R8, 0x00000000)
T0818 002:762.918 - 0.003ms returns 0
T0818 002:762.922 JLINK_WriteReg(R9, 0x20000180)
T0818 002:762.926 - 0.003ms returns 0
T0818 002:762.930 JLINK_WriteReg(R10, 0x00000000)
T0818 002:762.933 - 0.003ms returns 0
T0818 002:762.937 JLINK_WriteReg(R11, 0x00000000)
T0818 002:762.940 - 0.003ms returns 0
T0818 002:762.944 JLINK_WriteReg(R12, 0x00000000)
T0818 002:762.948 - 0.003ms returns 0
T0818 002:762.952 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:762.956 - 0.003ms returns 0
T0818 002:762.960 JLINK_WriteReg(R14, 0x20000001)
T0818 002:762.963 - 0.003ms returns 0
T0818 002:762.967 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:762.970 - 0.003ms returns 0
T0818 002:762.974 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:762.978 - 0.003ms returns 0
T0818 002:762.982 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:762.985 - 0.003ms returns 0
T0818 002:762.989 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:762.993 - 0.003ms returns 0
T0818 002:762.997 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:763.000 - 0.003ms returns 0
T0818 002:763.004 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:763.008 - 0.004ms returns 0x0000002A
T0818 002:763.012 JLINK_Go()
T0818 002:763.020   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:765.706 - 2.693ms 
T0818 002:765.712 JLINK_IsHalted()
T0818 002:766.194 - 0.482ms returns FALSE
T0818 002:766.200 JLINK_HasError()
T0818 002:768.197 JLINK_IsHalted()
T0818 002:768.687 - 0.488ms returns FALSE
T0818 002:768.695 JLINK_HasError()
T0818 002:770.198 JLINK_IsHalted()
T0818 002:772.485   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:772.974 - 2.773ms returns TRUE
T0818 002:772.980 JLINK_ReadReg(R15 (PC))
T0818 002:772.984 - 0.004ms returns 0x20000000
T0818 002:772.989 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T0818 002:772.993 - 0.003ms returns 0x00
T0818 002:772.997 JLINK_ReadReg(R0)
T0818 002:773.000 - 0.003ms returns 0x00000000
T0818 002:773.310 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:773.317   Data:  40 1A 28 BF EC 12 41 3F 05 66 28 BF DA D0 40 3F ...
T0818 002:773.326   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:775.911 - 2.601ms returns 0x27C
T0818 002:775.918 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:775.922   Data:  1B 56 2B 3F FF 78 3E BF 5B 0B 2B 3F 1B BC 3E BF ...
T0818 002:775.929   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:777.821 - 1.902ms returns 0x184
T0818 002:777.826 JLINK_HasError()
T0818 002:777.831 JLINK_WriteReg(R0, 0x08007C00)
T0818 002:777.836 - 0.005ms returns 0
T0818 002:777.840 JLINK_WriteReg(R1, 0x00000400)
T0818 002:777.844 - 0.004ms returns 0
T0818 002:777.848 JLINK_WriteReg(R2, 0x20000184)
T0818 002:777.852 - 0.003ms returns 0
T0818 002:777.856 JLINK_WriteReg(R3, 0x00000000)
T0818 002:777.859 - 0.003ms returns 0
T0818 002:777.863 JLINK_WriteReg(R4, 0x00000000)
T0818 002:777.866 - 0.003ms returns 0
T0818 002:777.870 JLINK_WriteReg(R5, 0x00000000)
T0818 002:777.874 - 0.003ms returns 0
T0818 002:777.878 JLINK_WriteReg(R6, 0x00000000)
T0818 002:777.881 - 0.003ms returns 0
T0818 002:777.885 JLINK_WriteReg(R7, 0x00000000)
T0818 002:777.888 - 0.003ms returns 0
T0818 002:777.896 JLINK_WriteReg(R8, 0x00000000)
T0818 002:777.900 - 0.003ms returns 0
T0818 002:777.904 JLINK_WriteReg(R9, 0x20000180)
T0818 002:777.907 - 0.003ms returns 0
T0818 002:777.911 JLINK_WriteReg(R10, 0x00000000)
T0818 002:777.915 - 0.003ms returns 0
T0818 002:777.919 JLINK_WriteReg(R11, 0x00000000)
T0818 002:777.932 - 0.013ms returns 0
T0818 002:777.936 JLINK_WriteReg(R12, 0x00000000)
T0818 002:777.939 - 0.003ms returns 0
T0818 002:777.944 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:777.947 - 0.003ms returns 0
T0818 002:777.951 JLINK_WriteReg(R14, 0x20000001)
T0818 002:777.955 - 0.003ms returns 0
T0818 002:777.959 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:777.962 - 0.003ms returns 0
T0818 002:777.966 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:777.970 - 0.003ms returns 0
T0818 002:777.974 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:777.977 - 0.003ms returns 0
T0818 002:777.981 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:777.984 - 0.003ms returns 0
T0818 002:777.988 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:777.992 - 0.003ms returns 0
T0818 002:777.996 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:778.000 - 0.004ms returns 0x0000002B
T0818 002:778.004 JLINK_Go()
T0818 002:778.011   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:780.706 - 2.702ms 
T0818 002:780.712 JLINK_IsHalted()
T0818 002:781.195 - 0.483ms returns FALSE
T0818 002:781.201 JLINK_HasError()
T0818 002:783.199 JLINK_IsHalted()
T0818 002:783.644 - 0.445ms returns FALSE
T0818 002:783.652 JLINK_HasError()
T0818 002:786.200 JLINK_IsHalted()
T0818 002:788.488   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:788.971 - 2.771ms returns TRUE
T0818 002:788.977 JLINK_ReadReg(R15 (PC))
T0818 002:788.982 - 0.004ms returns 0x20000000
T0818 002:788.986 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T0818 002:788.990 - 0.003ms returns 0x00
T0818 002:788.994 JLINK_ReadReg(R0)
T0818 002:788.998 - 0.003ms returns 0x00000000
T0818 002:789.283 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:789.290   Data:  13 8A 4A BF A2 91 1C 3F 7F C7 4A BF 0C 42 1C 3F ...
T0818 002:789.298   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:791.917 - 2.633ms returns 0x27C
T0818 002:791.927 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:791.931   Data:  9F EF 02 3F 8E 2E 5C BF 32 99 02 3F C7 61 5C BF ...
T0818 002:791.939   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:793.821 - 1.894ms returns 0x184
T0818 002:793.827 JLINK_HasError()
T0818 002:793.832 JLINK_WriteReg(R0, 0x08008000)
T0818 002:793.836 - 0.004ms returns 0
T0818 002:793.840 JLINK_WriteReg(R1, 0x00000400)
T0818 002:793.843 - 0.003ms returns 0
T0818 002:793.848 JLINK_WriteReg(R2, 0x20000184)
T0818 002:793.851 - 0.003ms returns 0
T0818 002:793.855 JLINK_WriteReg(R3, 0x00000000)
T0818 002:793.858 - 0.003ms returns 0
T0818 002:793.862 JLINK_WriteReg(R4, 0x00000000)
T0818 002:793.866 - 0.003ms returns 0
T0818 002:793.870 JLINK_WriteReg(R5, 0x00000000)
T0818 002:793.873 - 0.003ms returns 0
T0818 002:793.877 JLINK_WriteReg(R6, 0x00000000)
T0818 002:793.881 - 0.003ms returns 0
T0818 002:793.884 JLINK_WriteReg(R7, 0x00000000)
T0818 002:793.888 - 0.003ms returns 0
T0818 002:793.892 JLINK_WriteReg(R8, 0x00000000)
T0818 002:793.895 - 0.003ms returns 0
T0818 002:793.899 JLINK_WriteReg(R9, 0x20000180)
T0818 002:793.903 - 0.003ms returns 0
T0818 002:793.907 JLINK_WriteReg(R10, 0x00000000)
T0818 002:793.910 - 0.003ms returns 0
T0818 002:793.914 JLINK_WriteReg(R11, 0x00000000)
T0818 002:793.918 - 0.003ms returns 0
T0818 002:793.922 JLINK_WriteReg(R12, 0x00000000)
T0818 002:793.925 - 0.003ms returns 0
T0818 002:793.929 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:793.933 - 0.003ms returns 0
T0818 002:793.937 JLINK_WriteReg(R14, 0x20000001)
T0818 002:793.940 - 0.003ms returns 0
T0818 002:793.944 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:793.948 - 0.003ms returns 0
T0818 002:793.952 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:793.955 - 0.003ms returns 0
T0818 002:793.959 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:793.962 - 0.003ms returns 0
T0818 002:793.970 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:793.973 - 0.003ms returns 0
T0818 002:793.978 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:793.981 - 0.003ms returns 0
T0818 002:793.985 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:793.989 - 0.004ms returns 0x0000002C
T0818 002:793.993 JLINK_Go()
T0818 002:794.000   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:796.729 - 2.735ms 
T0818 002:796.734 JLINK_IsHalted()
T0818 002:797.198 - 0.463ms returns FALSE
T0818 002:797.203 JLINK_HasError()
T0818 002:799.199 JLINK_IsHalted()
T0818 002:799.639 - 0.440ms returns FALSE
T0818 002:799.648 JLINK_HasError()
T0818 002:801.206 JLINK_IsHalted()
T0818 002:803.527   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:804.014 - 2.808ms returns TRUE
T0818 002:804.021 JLINK_ReadReg(R15 (PC))
T0818 002:804.026 - 0.004ms returns 0x20000000
T0818 002:804.030 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T0818 002:804.034 - 0.003ms returns 0x00
T0818 002:804.038 JLINK_ReadReg(R0)
T0818 002:804.042 - 0.003ms returns 0x00000000
T0818 002:804.316 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:804.323   Data:  54 31 65 BF 0E 18 E4 3E 0B 5E 65 BF FA 63 E3 3E ...
T0818 002:804.332   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:806.908 - 2.592ms returns 0x27C
T0818 002:806.915 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:806.918   Data:  01 02 AB 3E FB 6D 71 BF 6F 44 AA 3E 57 8F 71 BF ...
T0818 002:806.925   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:808.819 - 1.904ms returns 0x184
T0818 002:808.825 JLINK_HasError()
T0818 002:808.830 JLINK_WriteReg(R0, 0x08008400)
T0818 002:808.834 - 0.004ms returns 0
T0818 002:808.838 JLINK_WriteReg(R1, 0x00000400)
T0818 002:808.841 - 0.003ms returns 0
T0818 002:808.846 JLINK_WriteReg(R2, 0x20000184)
T0818 002:808.849 - 0.003ms returns 0
T0818 002:808.853 JLINK_WriteReg(R3, 0x00000000)
T0818 002:808.856 - 0.003ms returns 0
T0818 002:808.861 JLINK_WriteReg(R4, 0x00000000)
T0818 002:808.864 - 0.003ms returns 0
T0818 002:808.868 JLINK_WriteReg(R5, 0x00000000)
T0818 002:808.871 - 0.003ms returns 0
T0818 002:808.875 JLINK_WriteReg(R6, 0x00000000)
T0818 002:808.879 - 0.003ms returns 0
T0818 002:808.883 JLINK_WriteReg(R7, 0x00000000)
T0818 002:808.886 - 0.003ms returns 0
T0818 002:808.890 JLINK_WriteReg(R8, 0x00000000)
T0818 002:808.894 - 0.003ms returns 0
T0818 002:808.898 JLINK_WriteReg(R9, 0x20000180)
T0818 002:808.901 - 0.003ms returns 0
T0818 002:808.905 JLINK_WriteReg(R10, 0x00000000)
T0818 002:808.908 - 0.003ms returns 0
T0818 002:808.912 JLINK_WriteReg(R11, 0x00000000)
T0818 002:808.916 - 0.003ms returns 0
T0818 002:808.920 JLINK_WriteReg(R12, 0x00000000)
T0818 002:808.923 - 0.003ms returns 0
T0818 002:808.927 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:808.931 - 0.003ms returns 0
T0818 002:808.935 JLINK_WriteReg(R14, 0x20000001)
T0818 002:808.938 - 0.003ms returns 0
T0818 002:808.942 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:808.946 - 0.003ms returns 0
T0818 002:808.950 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:808.953 - 0.003ms returns 0
T0818 002:808.957 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:808.960 - 0.003ms returns 0
T0818 002:808.964 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:808.968 - 0.003ms returns 0
T0818 002:808.972 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:808.975 - 0.003ms returns 0
T0818 002:808.980 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:808.983 - 0.004ms returns 0x0000002D
T0818 002:808.987 JLINK_Go()
T0818 002:808.994   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:811.835 - 2.846ms 
T0818 002:811.851 JLINK_IsHalted()
T0818 002:812.410 - 0.558ms returns FALSE
T0818 002:812.423 JLINK_HasError()
T0818 002:814.213 JLINK_IsHalted()
T0818 002:814.707 - 0.493ms returns FALSE
T0818 002:814.717 JLINK_HasError()
T0818 002:816.196 JLINK_IsHalted()
T0818 002:818.454   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:819.000 - 2.802ms returns TRUE
T0818 002:819.005 JLINK_ReadReg(R15 (PC))
T0818 002:819.010 - 0.004ms returns 0x20000000
T0818 002:819.016 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T0818 002:819.021 - 0.004ms returns 0x00
T0818 002:819.026 JLINK_ReadReg(R0)
T0818 002:819.029 - 0.003ms returns 0x00000000
T0818 002:819.342 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:819.350   Data:  CC 09 77 BF DF 48 86 3E 17 24 77 BF CE 86 85 3E ...
T0818 002:819.359   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:821.925 - 2.582ms returns 0x27C
T0818 002:821.938 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:821.942   Data:  CA 24 13 3E 3D 66 7D BF CC 96 11 3E 74 74 7D BF ...
T0818 002:821.951   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:823.983 - 2.044ms returns 0x184
T0818 002:823.993 JLINK_HasError()
T0818 002:823.998 JLINK_WriteReg(R0, 0x08008800)
T0818 002:824.002 - 0.004ms returns 0
T0818 002:824.007 JLINK_WriteReg(R1, 0x00000400)
T0818 002:824.010 - 0.003ms returns 0
T0818 002:824.014 JLINK_WriteReg(R2, 0x20000184)
T0818 002:824.018 - 0.003ms returns 0
T0818 002:824.022 JLINK_WriteReg(R3, 0x00000000)
T0818 002:824.025 - 0.003ms returns 0
T0818 002:824.029 JLINK_WriteReg(R4, 0x00000000)
T0818 002:824.033 - 0.003ms returns 0
T0818 002:824.037 JLINK_WriteReg(R5, 0x00000000)
T0818 002:824.040 - 0.003ms returns 0
T0818 002:824.044 JLINK_WriteReg(R6, 0x00000000)
T0818 002:824.048 - 0.003ms returns 0
T0818 002:824.052 JLINK_WriteReg(R7, 0x00000000)
T0818 002:824.055 - 0.003ms returns 0
T0818 002:824.059 JLINK_WriteReg(R8, 0x00000000)
T0818 002:824.063 - 0.003ms returns 0
T0818 002:824.067 JLINK_WriteReg(R9, 0x20000180)
T0818 002:824.070 - 0.003ms returns 0
T0818 002:824.074 JLINK_WriteReg(R10, 0x00000000)
T0818 002:824.077 - 0.003ms returns 0
T0818 002:824.081 JLINK_WriteReg(R11, 0x00000000)
T0818 002:824.085 - 0.003ms returns 0
T0818 002:824.089 JLINK_WriteReg(R12, 0x00000000)
T0818 002:824.092 - 0.003ms returns 0
T0818 002:824.096 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:824.100 - 0.003ms returns 0
T0818 002:824.104 JLINK_WriteReg(R14, 0x20000001)
T0818 002:824.107 - 0.003ms returns 0
T0818 002:824.111 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:824.114 - 0.003ms returns 0
T0818 002:824.118 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:824.122 - 0.003ms returns 0
T0818 002:824.126 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:824.129 - 0.003ms returns 0
T0818 002:824.133 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:824.137 - 0.003ms returns 0
T0818 002:824.141 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:824.144 - 0.003ms returns 0
T0818 002:824.149 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:824.152 - 0.004ms returns 0x0000002E
T0818 002:824.157 JLINK_Go()
T0818 002:824.164   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:826.863 - 2.705ms 
T0818 002:826.870 JLINK_IsHalted()
T0818 002:827.341 - 0.471ms returns FALSE
T0818 002:827.347 JLINK_HasError()
T0818 002:829.196 JLINK_IsHalted()
T0818 002:829.659 - 0.463ms returns FALSE
T0818 002:829.665 JLINK_HasError()
T0818 002:831.201 JLINK_IsHalted()
T0818 002:833.498   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:833.960 - 2.758ms returns TRUE
T0818 002:833.966 JLINK_ReadReg(R15 (PC))
T0818 002:833.970 - 0.004ms returns 0x20000000
T0818 002:833.974 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T0818 002:833.978 - 0.003ms returns 0x00
T0818 002:833.983 JLINK_ReadReg(R0)
T0818 002:833.986 - 0.003ms returns 0x00000000
T0818 002:834.256 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:834.263   Data:  EC 63 7F BF 6A 42 8D 3D C7 6A 7F BF 0A 20 8A 3D ...
T0818 002:834.272   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:836.822 - 2.566ms returns 0x27C
T0818 002:836.828 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:836.832   Data:  0E 88 55 BD 91 A1 7F BF 4C CE 5B BD 18 9C 7F BF ...
T0818 002:836.839   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:838.695 - 1.866ms returns 0x184
T0818 002:838.703 JLINK_HasError()
T0818 002:838.708 JLINK_WriteReg(R0, 0x08008C00)
T0818 002:838.712 - 0.004ms returns 0
T0818 002:838.716 JLINK_WriteReg(R1, 0x00000400)
T0818 002:838.720 - 0.003ms returns 0
T0818 002:838.724 JLINK_WriteReg(R2, 0x20000184)
T0818 002:838.730 - 0.006ms returns 0
T0818 002:838.735 JLINK_WriteReg(R3, 0x00000000)
T0818 002:838.738 - 0.003ms returns 0
T0818 002:838.742 JLINK_WriteReg(R4, 0x00000000)
T0818 002:838.745 - 0.003ms returns 0
T0818 002:838.749 JLINK_WriteReg(R5, 0x00000000)
T0818 002:838.753 - 0.003ms returns 0
T0818 002:838.757 JLINK_WriteReg(R6, 0x00000000)
T0818 002:838.761 - 0.003ms returns 0
T0818 002:838.765 JLINK_WriteReg(R7, 0x00000000)
T0818 002:838.768 - 0.003ms returns 0
T0818 002:838.772 JLINK_WriteReg(R8, 0x00000000)
T0818 002:838.775 - 0.003ms returns 0
T0818 002:838.779 JLINK_WriteReg(R9, 0x20000180)
T0818 002:838.783 - 0.003ms returns 0
T0818 002:838.787 JLINK_WriteReg(R10, 0x00000000)
T0818 002:838.790 - 0.003ms returns 0
T0818 002:838.794 JLINK_WriteReg(R11, 0x00000000)
T0818 002:838.797 - 0.003ms returns 0
T0818 002:838.801 JLINK_WriteReg(R12, 0x00000000)
T0818 002:838.805 - 0.003ms returns 0
T0818 002:838.809 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:838.812 - 0.003ms returns 0
T0818 002:838.816 JLINK_WriteReg(R14, 0x20000001)
T0818 002:838.820 - 0.003ms returns 0
T0818 002:838.824 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:838.827 - 0.003ms returns 0
T0818 002:838.831 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:838.835 - 0.003ms returns 0
T0818 002:838.839 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:838.842 - 0.003ms returns 0
T0818 002:838.846 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:838.850 - 0.003ms returns 0
T0818 002:838.854 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:838.857 - 0.003ms returns 0
T0818 002:838.862 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:838.865 - 0.004ms returns 0x0000002F
T0818 002:838.870 JLINK_Go()
T0818 002:838.876   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:841.757 - 2.887ms 
T0818 002:841.771 JLINK_IsHalted()
T0818 002:842.371 - 0.599ms returns FALSE
T0818 002:842.384 JLINK_HasError()
T0818 002:844.198 JLINK_IsHalted()
T0818 002:844.642 - 0.444ms returns FALSE
T0818 002:844.649 JLINK_HasError()
T0818 002:846.212 JLINK_IsHalted()
T0818 002:848.712   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:849.214 - 3.001ms returns TRUE
T0818 002:849.228 JLINK_ReadReg(R15 (PC))
T0818 002:849.235 - 0.006ms returns 0x20000000
T0818 002:849.240 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T0818 002:849.243 - 0.004ms returns 0x00
T0818 002:849.248 JLINK_ReadReg(R0)
T0818 002:849.252 - 0.004ms returns 0x00000000
T0818 002:849.654 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:849.663   Data:  88 ED 7D BF 2E 06 02 BE B1 E0 7D BF 02 95 03 BE ...
T0818 002:849.674   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:852.290 - 2.635ms returns 0x27C
T0818 002:852.321 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:852.325   Data:  A4 DB 7B BE 04 0A 78 BF 56 61 7D BE 10 F1 77 BF ...
T0818 002:852.339   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:854.190 - 1.869ms returns 0x184
T0818 002:854.202 JLINK_HasError()
T0818 002:854.208 JLINK_WriteReg(R0, 0x08009000)
T0818 002:854.214 - 0.005ms returns 0
T0818 002:854.218 JLINK_WriteReg(R1, 0x00000400)
T0818 002:854.221 - 0.003ms returns 0
T0818 002:854.225 JLINK_WriteReg(R2, 0x20000184)
T0818 002:854.229 - 0.003ms returns 0
T0818 002:854.233 JLINK_WriteReg(R3, 0x00000000)
T0818 002:854.236 - 0.003ms returns 0
T0818 002:854.240 JLINK_WriteReg(R4, 0x00000000)
T0818 002:854.244 - 0.003ms returns 0
T0818 002:854.248 JLINK_WriteReg(R5, 0x00000000)
T0818 002:854.252 - 0.003ms returns 0
T0818 002:854.256 JLINK_WriteReg(R6, 0x00000000)
T0818 002:854.259 - 0.003ms returns 0
T0818 002:854.264 JLINK_WriteReg(R7, 0x00000000)
T0818 002:854.268 - 0.003ms returns 0
T0818 002:854.272 JLINK_WriteReg(R8, 0x00000000)
T0818 002:854.276 - 0.003ms returns 0
T0818 002:854.280 JLINK_WriteReg(R9, 0x20000180)
T0818 002:854.283 - 0.003ms returns 0
T0818 002:854.287 JLINK_WriteReg(R10, 0x00000000)
T0818 002:854.290 - 0.003ms returns 0
T0818 002:854.294 JLINK_WriteReg(R11, 0x00000000)
T0818 002:854.298 - 0.003ms returns 0
T0818 002:854.302 JLINK_WriteReg(R12, 0x00000000)
T0818 002:854.312 - 0.010ms returns 0
T0818 002:854.316 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:854.321 - 0.004ms returns 0
T0818 002:854.325 JLINK_WriteReg(R14, 0x20000001)
T0818 002:854.328 - 0.003ms returns 0
T0818 002:854.332 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:854.336 - 0.003ms returns 0
T0818 002:854.340 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:854.344 - 0.003ms returns 0
T0818 002:854.348 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:854.351 - 0.003ms returns 0
T0818 002:854.355 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:854.358 - 0.003ms returns 0
T0818 002:854.362 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:854.366 - 0.003ms returns 0
T0818 002:854.371 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:854.377 - 0.006ms returns 0x00000030
T0818 002:854.381 JLINK_Go()
T0818 002:854.390   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:857.116 - 2.734ms 
T0818 002:857.165 JLINK_IsHalted()
T0818 002:857.673 - 0.507ms returns FALSE
T0818 002:857.684 JLINK_HasError()
T0818 002:859.201 JLINK_IsHalted()
T0818 002:859.639 - 0.438ms returns FALSE
T0818 002:859.647 JLINK_HasError()
T0818 002:861.198 JLINK_IsHalted()
T0818 002:863.504   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:864.011 - 2.813ms returns TRUE
T0818 002:864.019 JLINK_ReadReg(R15 (PC))
T0818 002:864.025 - 0.005ms returns 0x20000000
T0818 002:864.030 JLINK_ClrBPEx(BPHandle = 0x00000030)
T0818 002:864.033 - 0.003ms returns 0x00
T0818 002:864.038 JLINK_ReadReg(R0)
T0818 002:864.042 - 0.003ms returns 0x00000000
T0818 002:864.441 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:864.450   Data:  04 B5 72 BF 33 D7 A2 BE F8 94 72 BF C5 95 A3 BE ...
T0818 002:864.462   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:867.028 - 2.586ms returns 0x27C
T0818 002:867.043 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:867.047   Data:  C1 53 DC BE 45 EA 66 BF 2E 09 DD BE CC BE 66 BF ...
T0818 002:867.055   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:868.945 - 1.902ms returns 0x184
T0818 002:868.963 JLINK_HasError()
T0818 002:869.006 JLINK_WriteReg(R0, 0x08009400)
T0818 002:869.012 - 0.005ms returns 0
T0818 002:869.016 JLINK_WriteReg(R1, 0x00000400)
T0818 002:869.020 - 0.003ms returns 0
T0818 002:869.024 JLINK_WriteReg(R2, 0x20000184)
T0818 002:869.028 - 0.003ms returns 0
T0818 002:869.032 JLINK_WriteReg(R3, 0x00000000)
T0818 002:869.036 - 0.003ms returns 0
T0818 002:869.040 JLINK_WriteReg(R4, 0x00000000)
T0818 002:869.044 - 0.003ms returns 0
T0818 002:869.048 JLINK_WriteReg(R5, 0x00000000)
T0818 002:869.051 - 0.003ms returns 0
T0818 002:869.056 JLINK_WriteReg(R6, 0x00000000)
T0818 002:869.059 - 0.003ms returns 0
T0818 002:869.064 JLINK_WriteReg(R7, 0x00000000)
T0818 002:869.067 - 0.003ms returns 0
T0818 002:869.071 JLINK_WriteReg(R8, 0x00000000)
T0818 002:869.075 - 0.003ms returns 0
T0818 002:869.079 JLINK_WriteReg(R9, 0x20000180)
T0818 002:869.082 - 0.003ms returns 0
T0818 002:869.086 JLINK_WriteReg(R10, 0x00000000)
T0818 002:869.090 - 0.003ms returns 0
T0818 002:869.094 JLINK_WriteReg(R11, 0x00000000)
T0818 002:869.098 - 0.004ms returns 0
T0818 002:869.103 JLINK_WriteReg(R12, 0x00000000)
T0818 002:869.107 - 0.003ms returns 0
T0818 002:869.111 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:869.115 - 0.004ms returns 0
T0818 002:869.119 JLINK_WriteReg(R14, 0x20000001)
T0818 002:869.123 - 0.003ms returns 0
T0818 002:869.127 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:869.131 - 0.003ms returns 0
T0818 002:869.135 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:869.138 - 0.003ms returns 0
T0818 002:869.142 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:869.146 - 0.003ms returns 0
T0818 002:869.150 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:869.153 - 0.003ms returns 0
T0818 002:869.157 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:869.160 - 0.003ms returns 0
T0818 002:869.165 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:869.170 - 0.005ms returns 0x00000031
T0818 002:869.174 JLINK_Go()
T0818 002:869.183   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:871.877 - 2.701ms 
T0818 002:871.889 JLINK_IsHalted()
T0818 002:872.401 - 0.512ms returns FALSE
T0818 002:872.409 JLINK_HasError()
T0818 002:874.203 JLINK_IsHalted()
T0818 002:874.683 - 0.480ms returns FALSE
T0818 002:874.689 JLINK_HasError()
T0818 002:876.203 JLINK_IsHalted()
T0818 002:878.543   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:879.018 - 2.815ms returns TRUE
T0818 002:879.025 JLINK_ReadReg(R15 (PC))
T0818 002:879.030 - 0.005ms returns 0x20000000
T0818 002:879.035 JLINK_ClrBPEx(BPHandle = 0x00000031)
T0818 002:879.039 - 0.003ms returns 0x00
T0818 002:879.043 JLINK_ReadReg(R0)
T0818 002:879.047 - 0.003ms returns 0x00000000
T0818 002:879.447 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:879.457   Data:  C3 28 5E BF 4A 69 FE BE BE F6 5D BF B2 17 FF BE ...
T0818 002:879.470   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:882.120 - 2.673ms returns 0x27C
T0818 002:882.142 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:882.146   Data:  0F 21 19 BF CB EA 4C BF 94 71 19 BF 79 AE 4C BF ...
T0818 002:882.158   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:884.036 - 1.893ms returns 0x184
T0818 002:884.052 JLINK_HasError()
T0818 002:884.058 JLINK_WriteReg(R0, 0x08009800)
T0818 002:884.063 - 0.005ms returns 0
T0818 002:884.067 JLINK_WriteReg(R1, 0x00000400)
T0818 002:884.071 - 0.003ms returns 0
T0818 002:884.075 JLINK_WriteReg(R2, 0x20000184)
T0818 002:884.078 - 0.003ms returns 0
T0818 002:884.083 JLINK_WriteReg(R3, 0x00000000)
T0818 002:884.086 - 0.003ms returns 0
T0818 002:884.090 JLINK_WriteReg(R4, 0x00000000)
T0818 002:884.093 - 0.003ms returns 0
T0818 002:884.097 JLINK_WriteReg(R5, 0x00000000)
T0818 002:884.101 - 0.003ms returns 0
T0818 002:884.105 JLINK_WriteReg(R6, 0x00000000)
T0818 002:884.108 - 0.003ms returns 0
T0818 002:884.112 JLINK_WriteReg(R7, 0x00000000)
T0818 002:884.115 - 0.003ms returns 0
T0818 002:884.120 JLINK_WriteReg(R8, 0x00000000)
T0818 002:884.123 - 0.003ms returns 0
T0818 002:884.128 JLINK_WriteReg(R9, 0x20000180)
T0818 002:884.131 - 0.003ms returns 0
T0818 002:884.135 JLINK_WriteReg(R10, 0x00000000)
T0818 002:884.139 - 0.003ms returns 0
T0818 002:884.143 JLINK_WriteReg(R11, 0x00000000)
T0818 002:884.146 - 0.003ms returns 0
T0818 002:884.150 JLINK_WriteReg(R12, 0x00000000)
T0818 002:884.154 - 0.003ms returns 0
T0818 002:884.158 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:884.162 - 0.004ms returns 0
T0818 002:884.166 JLINK_WriteReg(R14, 0x20000001)
T0818 002:884.169 - 0.003ms returns 0
T0818 002:884.173 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:884.177 - 0.004ms returns 0
T0818 002:884.181 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:884.185 - 0.003ms returns 0
T0818 002:884.189 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:884.192 - 0.003ms returns 0
T0818 002:884.196 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:884.200 - 0.003ms returns 0
T0818 002:884.204 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:884.207 - 0.003ms returns 0
T0818 002:884.212 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:884.217 - 0.004ms returns 0x00000032
T0818 002:884.221 JLINK_Go()
T0818 002:884.229   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:887.045 - 2.823ms 
T0818 002:887.059 JLINK_IsHalted()
T0818 002:887.521 - 0.461ms returns FALSE
T0818 002:887.528 JLINK_HasError()
T0818 002:889.200 JLINK_IsHalted()
T0818 002:889.677 - 0.477ms returns FALSE
T0818 002:889.685 JLINK_HasError()
T0818 002:891.204 JLINK_IsHalted()
T0818 002:893.615   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:894.058 - 2.853ms returns TRUE
T0818 002:894.073 JLINK_ReadReg(R15 (PC))
T0818 002:894.078 - 0.005ms returns 0x20000000
T0818 002:894.082 JLINK_ClrBPEx(BPHandle = 0x00000032)
T0818 002:894.086 - 0.003ms returns 0x00
T0818 002:894.091 JLINK_ReadReg(R0)
T0818 002:894.094 - 0.003ms returns 0x00000000
T0818 002:894.659 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:894.673   Data:  EC 12 41 BF 40 1A 28 BF DA D0 40 BF 05 66 28 BF ...
T0818 002:894.687   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:897.317 - 2.657ms returns 0x27C
T0818 002:897.331 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:897.335   Data:  C5 35 3E BF 5B 0B 2B BF FF 78 3E BF 82 C0 2A BF ...
T0818 002:897.343   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:899.192 - 1.861ms returns 0x184
T0818 002:899.204 JLINK_HasError()
T0818 002:899.210 JLINK_WriteReg(R0, 0x08009C00)
T0818 002:899.214 - 0.005ms returns 0
T0818 002:899.218 JLINK_WriteReg(R1, 0x00000400)
T0818 002:899.222 - 0.003ms returns 0
T0818 002:899.226 JLINK_WriteReg(R2, 0x20000184)
T0818 002:899.229 - 0.003ms returns 0
T0818 002:899.233 JLINK_WriteReg(R3, 0x00000000)
T0818 002:899.237 - 0.003ms returns 0
T0818 002:899.241 JLINK_WriteReg(R4, 0x00000000)
T0818 002:899.244 - 0.003ms returns 0
T0818 002:899.248 JLINK_WriteReg(R5, 0x00000000)
T0818 002:899.252 - 0.003ms returns 0
T0818 002:899.256 JLINK_WriteReg(R6, 0x00000000)
T0818 002:899.259 - 0.003ms returns 0
T0818 002:899.263 JLINK_WriteReg(R7, 0x00000000)
T0818 002:899.267 - 0.003ms returns 0
T0818 002:899.271 JLINK_WriteReg(R8, 0x00000000)
T0818 002:899.275 - 0.004ms returns 0
T0818 002:899.279 JLINK_WriteReg(R9, 0x20000180)
T0818 002:899.282 - 0.003ms returns 0
T0818 002:899.286 JLINK_WriteReg(R10, 0x00000000)
T0818 002:899.290 - 0.003ms returns 0
T0818 002:899.294 JLINK_WriteReg(R11, 0x00000000)
T0818 002:899.298 - 0.003ms returns 0
T0818 002:899.302 JLINK_WriteReg(R12, 0x00000000)
T0818 002:899.305 - 0.003ms returns 0
T0818 002:899.309 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:899.314 - 0.004ms returns 0
T0818 002:899.318 JLINK_WriteReg(R14, 0x20000001)
T0818 002:899.321 - 0.003ms returns 0
T0818 002:899.325 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:899.329 - 0.003ms returns 0
T0818 002:899.333 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:899.336 - 0.003ms returns 0
T0818 002:899.340 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:899.344 - 0.003ms returns 0
T0818 002:899.348 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:899.351 - 0.003ms returns 0
T0818 002:899.355 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:899.358 - 0.003ms returns 0
T0818 002:899.363 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:899.368 - 0.005ms returns 0x00000033
T0818 002:899.372 JLINK_Go()
T0818 002:899.380   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:902.143 - 2.770ms 
T0818 002:902.153 JLINK_IsHalted()
T0818 002:902.627 - 0.473ms returns FALSE
T0818 002:902.634 JLINK_HasError()
T0818 002:905.239 JLINK_IsHalted()
T0818 002:905.724 - 0.484ms returns FALSE
T0818 002:905.739 JLINK_HasError()
T0818 002:907.208 JLINK_IsHalted()
T0818 002:909.455   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:909.979 - 2.771ms returns TRUE
T0818 002:909.989 JLINK_ReadReg(R15 (PC))
T0818 002:909.994 - 0.005ms returns 0x20000000
T0818 002:909.998 JLINK_ClrBPEx(BPHandle = 0x00000033)
T0818 002:910.002 - 0.003ms returns 0x00
T0818 002:910.006 JLINK_ReadReg(R0)
T0818 002:910.010 - 0.003ms returns 0x00000000
T0818 002:910.446 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:910.459   Data:  A2 91 1C BF 13 8A 4A BF 0C 42 1C BF 7F C7 4A BF ...
T0818 002:910.471   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:913.118 - 2.671ms returns 0x27C
T0818 002:913.140 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:913.144   Data:  34 FB 5B BF 32 99 02 BF 8E 2E 5C BF B1 42 02 BF ...
T0818 002:913.156   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:915.080 - 1.940ms returns 0x184
T0818 002:915.088 JLINK_HasError()
T0818 002:915.093 JLINK_WriteReg(R0, 0x0800A000)
T0818 002:915.098 - 0.005ms returns 0
T0818 002:915.102 JLINK_WriteReg(R1, 0x00000400)
T0818 002:915.106 - 0.003ms returns 0
T0818 002:915.110 JLINK_WriteReg(R2, 0x20000184)
T0818 002:915.113 - 0.003ms returns 0
T0818 002:915.117 JLINK_WriteReg(R3, 0x00000000)
T0818 002:915.121 - 0.003ms returns 0
T0818 002:915.125 JLINK_WriteReg(R4, 0x00000000)
T0818 002:915.128 - 0.003ms returns 0
T0818 002:915.132 JLINK_WriteReg(R5, 0x00000000)
T0818 002:915.136 - 0.003ms returns 0
T0818 002:915.140 JLINK_WriteReg(R6, 0x00000000)
T0818 002:915.149 - 0.009ms returns 0
T0818 002:915.156 JLINK_WriteReg(R7, 0x00000000)
T0818 002:915.159 - 0.003ms returns 0
T0818 002:915.163 JLINK_WriteReg(R8, 0x00000000)
T0818 002:915.167 - 0.003ms returns 0
T0818 002:915.171 JLINK_WriteReg(R9, 0x20000180)
T0818 002:915.174 - 0.003ms returns 0
T0818 002:915.178 JLINK_WriteReg(R10, 0x00000000)
T0818 002:915.182 - 0.003ms returns 0
T0818 002:915.186 JLINK_WriteReg(R11, 0x00000000)
T0818 002:915.189 - 0.003ms returns 0
T0818 002:915.193 JLINK_WriteReg(R12, 0x00000000)
T0818 002:915.197 - 0.003ms returns 0
T0818 002:915.201 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:915.205 - 0.003ms returns 0
T0818 002:915.209 JLINK_WriteReg(R14, 0x20000001)
T0818 002:915.212 - 0.003ms returns 0
T0818 002:915.217 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:915.220 - 0.003ms returns 0
T0818 002:915.224 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:915.228 - 0.003ms returns 0
T0818 002:915.232 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:915.235 - 0.003ms returns 0
T0818 002:915.239 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:915.243 - 0.003ms returns 0
T0818 002:915.247 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:915.250 - 0.003ms returns 0
T0818 002:915.255 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:915.259 - 0.004ms returns 0x00000034
T0818 002:915.263 JLINK_Go()
T0818 002:915.282   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:918.053 - 2.789ms 
T0818 002:918.061 JLINK_IsHalted()
T0818 002:918.574 - 0.512ms returns FALSE
T0818 002:918.580 JLINK_HasError()
T0818 002:920.203 JLINK_IsHalted()
T0818 002:920.729 - 0.526ms returns FALSE
T0818 002:920.741 JLINK_HasError()
T0818 002:922.203 JLINK_IsHalted()
T0818 002:924.473   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:924.975 - 2.771ms returns TRUE
T0818 002:924.983 JLINK_ReadReg(R15 (PC))
T0818 002:924.988 - 0.005ms returns 0x20000000
T0818 002:924.992 JLINK_ClrBPEx(BPHandle = 0x00000034)
T0818 002:924.996 - 0.003ms returns 0x00
T0818 002:925.000 JLINK_ReadReg(R0)
T0818 002:925.004 - 0.003ms returns 0x00000000
T0818 002:925.402 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:925.411   Data:  0E 18 E4 BE 54 31 65 BF FA 63 E3 BE 0B 5E 65 BF ...
T0818 002:925.422   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:928.106 - 2.703ms returns 0x27C
T0818 002:928.126 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:928.130   Data:  7A 4C 71 BF 6F 44 AA BE FB 6D 71 BF C4 86 A9 BE ...
T0818 002:928.142   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:930.005 - 1.878ms returns 0x184
T0818 002:930.022 JLINK_HasError()
T0818 002:930.028 JLINK_WriteReg(R0, 0x0800A400)
T0818 002:930.033 - 0.005ms returns 0
T0818 002:930.037 JLINK_WriteReg(R1, 0x00000400)
T0818 002:930.041 - 0.003ms returns 0
T0818 002:930.045 JLINK_WriteReg(R2, 0x20000184)
T0818 002:930.048 - 0.003ms returns 0
T0818 002:930.052 JLINK_WriteReg(R3, 0x00000000)
T0818 002:930.097 - 0.044ms returns 0
T0818 002:930.102 JLINK_WriteReg(R4, 0x00000000)
T0818 002:930.105 - 0.003ms returns 0
T0818 002:930.110 JLINK_WriteReg(R5, 0x00000000)
T0818 002:930.113 - 0.003ms returns 0
T0818 002:930.117 JLINK_WriteReg(R6, 0x00000000)
T0818 002:930.120 - 0.003ms returns 0
T0818 002:930.124 JLINK_WriteReg(R7, 0x00000000)
T0818 002:930.128 - 0.003ms returns 0
T0818 002:930.132 JLINK_WriteReg(R8, 0x00000000)
T0818 002:930.135 - 0.003ms returns 0
T0818 002:930.139 JLINK_WriteReg(R9, 0x20000180)
T0818 002:930.143 - 0.003ms returns 0
T0818 002:930.147 JLINK_WriteReg(R10, 0x00000000)
T0818 002:930.150 - 0.003ms returns 0
T0818 002:930.154 JLINK_WriteReg(R11, 0x00000000)
T0818 002:930.157 - 0.003ms returns 0
T0818 002:930.162 JLINK_WriteReg(R12, 0x00000000)
T0818 002:930.165 - 0.003ms returns 0
T0818 002:930.169 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:930.173 - 0.004ms returns 0
T0818 002:930.177 JLINK_WriteReg(R14, 0x20000001)
T0818 002:930.180 - 0.003ms returns 0
T0818 002:930.185 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:930.189 - 0.003ms returns 0
T0818 002:930.196 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:930.268 - 0.071ms returns 0
T0818 002:930.272 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:930.276 - 0.003ms returns 0
T0818 002:930.280 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:930.283 - 0.003ms returns 0
T0818 002:930.287 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:930.291 - 0.003ms returns 0
T0818 002:930.295 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:930.310 - 0.015ms returns 0x00000035
T0818 002:930.315 JLINK_Go()
T0818 002:930.323   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:933.111 - 2.795ms 
T0818 002:933.133 JLINK_IsHalted()
T0818 002:933.608 - 0.474ms returns FALSE
T0818 002:933.615 JLINK_HasError()
T0818 002:935.203 JLINK_IsHalted()
T0818 002:935.639 - 0.435ms returns FALSE
T0818 002:935.646 JLINK_HasError()
T0818 002:937.203 JLINK_IsHalted()
T0818 002:939.474   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:939.975 - 2.772ms returns TRUE
T0818 002:939.983 JLINK_ReadReg(R15 (PC))
T0818 002:939.988 - 0.005ms returns 0x20000000
T0818 002:939.992 JLINK_ClrBPEx(BPHandle = 0x00000035)
T0818 002:939.996 - 0.003ms returns 0x00
T0818 002:940.000 JLINK_ReadReg(R0)
T0818 002:940.004 - 0.003ms returns 0x00000000
T0818 002:940.403 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:940.412   Data:  DF 48 86 BE CC 09 77 BF CE 86 85 BE 17 24 77 BF ...
T0818 002:940.424   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:943.091 - 2.687ms returns 0x27C
T0818 002:943.108 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:943.113   Data:  DE 57 7D BF CC 96 11 BE 3D 66 7D BF B7 08 10 BE ...
T0818 002:943.124   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:945.036 - 1.927ms returns 0x184
T0818 002:945.046 JLINK_HasError()
T0818 002:945.052 JLINK_WriteReg(R0, 0x0800A800)
T0818 002:945.057 - 0.004ms returns 0
T0818 002:945.061 JLINK_WriteReg(R1, 0x00000400)
T0818 002:945.065 - 0.004ms returns 0
T0818 002:945.069 JLINK_WriteReg(R2, 0x20000184)
T0818 002:945.073 - 0.003ms returns 0
T0818 002:945.077 JLINK_WriteReg(R3, 0x00000000)
T0818 002:945.081 - 0.004ms returns 0
T0818 002:945.085 JLINK_WriteReg(R4, 0x00000000)
T0818 002:945.088 - 0.003ms returns 0
T0818 002:945.092 JLINK_WriteReg(R5, 0x00000000)
T0818 002:945.096 - 0.003ms returns 0
T0818 002:945.100 JLINK_WriteReg(R6, 0x00000000)
T0818 002:945.103 - 0.003ms returns 0
T0818 002:945.107 JLINK_WriteReg(R7, 0x00000000)
T0818 002:945.111 - 0.003ms returns 0
T0818 002:945.115 JLINK_WriteReg(R8, 0x00000000)
T0818 002:945.118 - 0.003ms returns 0
T0818 002:945.122 JLINK_WriteReg(R9, 0x20000180)
T0818 002:945.126 - 0.003ms returns 0
T0818 002:945.130 JLINK_WriteReg(R10, 0x00000000)
T0818 002:945.133 - 0.003ms returns 0
T0818 002:945.137 JLINK_WriteReg(R11, 0x00000000)
T0818 002:945.140 - 0.003ms returns 0
T0818 002:945.144 JLINK_WriteReg(R12, 0x00000000)
T0818 002:945.148 - 0.003ms returns 0
T0818 002:945.153 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:945.157 - 0.004ms returns 0
T0818 002:945.161 JLINK_WriteReg(R14, 0x20000001)
T0818 002:945.164 - 0.003ms returns 0
T0818 002:945.169 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:945.173 - 0.004ms returns 0
T0818 002:945.177 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:945.180 - 0.003ms returns 0
T0818 002:945.184 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:945.188 - 0.003ms returns 0
T0818 002:945.192 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:945.195 - 0.003ms returns 0
T0818 002:945.200 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:945.203 - 0.003ms returns 0
T0818 002:945.208 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:945.213 - 0.004ms returns 0x00000036
T0818 002:945.217 JLINK_Go()
T0818 002:945.226   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:947.875 - 2.657ms 
T0818 002:947.892 JLINK_IsHalted()
T0818 002:948.310 - 0.418ms returns FALSE
T0818 002:948.319 JLINK_HasError()
T0818 002:950.204 JLINK_IsHalted()
T0818 002:950.668 - 0.464ms returns FALSE
T0818 002:950.675 JLINK_HasError()
T0818 002:952.202 JLINK_IsHalted()
T0818 002:954.523   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:954.976 - 2.774ms returns TRUE
T0818 002:955.031 JLINK_ReadReg(R15 (PC))
T0818 002:955.036 - 0.005ms returns 0x20000000
T0818 002:955.040 JLINK_ClrBPEx(BPHandle = 0x00000036)
T0818 002:955.044 - 0.004ms returns 0x00
T0818 002:955.049 JLINK_ReadReg(R0)
T0818 002:955.053 - 0.003ms returns 0x00000000
T0818 002:955.426 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:955.436   Data:  6A 42 8D BD EC 63 7F BF 0A 20 8A BD C7 6A 7F BF ...
T0818 002:955.447   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:958.108 - 2.682ms returns 0x27C
T0818 002:958.120 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:958.124   Data:  E3 A6 7F BF 4C CE 5B 3D 91 A1 7F BF 68 14 62 3D ...
T0818 002:958.131   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:959.964 - 1.843ms returns 0x184
T0818 002:959.978 JLINK_HasError()
T0818 002:959.984 JLINK_WriteReg(R0, 0x0800AC00)
T0818 002:959.990 - 0.006ms returns 0
T0818 002:959.994 JLINK_WriteReg(R1, 0x00000400)
T0818 002:959.998 - 0.003ms returns 0
T0818 002:960.002 JLINK_WriteReg(R2, 0x20000184)
T0818 002:960.005 - 0.003ms returns 0
T0818 002:960.009 JLINK_WriteReg(R3, 0x00000000)
T0818 002:960.012 - 0.003ms returns 0
T0818 002:960.017 JLINK_WriteReg(R4, 0x00000000)
T0818 002:960.020 - 0.003ms returns 0
T0818 002:960.024 JLINK_WriteReg(R5, 0x00000000)
T0818 002:960.028 - 0.003ms returns 0
T0818 002:960.032 JLINK_WriteReg(R6, 0x00000000)
T0818 002:960.035 - 0.003ms returns 0
T0818 002:960.039 JLINK_WriteReg(R7, 0x00000000)
T0818 002:960.042 - 0.003ms returns 0
T0818 002:960.047 JLINK_WriteReg(R8, 0x00000000)
T0818 002:960.050 - 0.003ms returns 0
T0818 002:960.054 JLINK_WriteReg(R9, 0x20000180)
T0818 002:960.058 - 0.003ms returns 0
T0818 002:960.062 JLINK_WriteReg(R10, 0x00000000)
T0818 002:960.065 - 0.003ms returns 0
T0818 002:960.069 JLINK_WriteReg(R11, 0x00000000)
T0818 002:960.073 - 0.003ms returns 0
T0818 002:960.077 JLINK_WriteReg(R12, 0x00000000)
T0818 002:960.080 - 0.003ms returns 0
T0818 002:960.084 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:960.088 - 0.004ms returns 0
T0818 002:960.092 JLINK_WriteReg(R14, 0x20000001)
T0818 002:960.096 - 0.003ms returns 0
T0818 002:960.100 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:960.106 - 0.006ms returns 0
T0818 002:960.111 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:960.114 - 0.003ms returns 0
T0818 002:960.119 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:960.122 - 0.003ms returns 0
T0818 002:960.126 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:960.130 - 0.003ms returns 0
T0818 002:960.134 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:960.137 - 0.003ms returns 0
T0818 002:960.142 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:960.146 - 0.004ms returns 0x00000037
T0818 002:960.151 JLINK_Go()
T0818 002:960.162   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:962.831 - 2.680ms 
T0818 002:962.849 JLINK_IsHalted()
T0818 002:963.335 - 0.486ms returns FALSE
T0818 002:963.342 JLINK_HasError()
T0818 002:965.204 JLINK_IsHalted()
T0818 002:965.735 - 0.529ms returns FALSE
T0818 002:965.741 JLINK_HasError()
T0818 002:967.219 JLINK_IsHalted()
T0818 002:969.594   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:970.086 - 2.866ms returns TRUE
T0818 002:970.093 JLINK_ReadReg(R15 (PC))
T0818 002:970.098 - 0.005ms returns 0x20000000
T0818 002:970.103 JLINK_ClrBPEx(BPHandle = 0x00000037)
T0818 002:970.106 - 0.003ms returns 0x00
T0818 002:970.111 JLINK_ReadReg(R0)
T0818 002:970.115 - 0.003ms returns 0x00000000
T0818 002:970.556 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:970.566   Data:  2E 06 02 3E 88 ED 7D BF 02 95 03 3E B1 E0 7D BF ...
T0818 002:970.577   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:973.152 - 2.595ms returns 0x27C
T0818 002:973.165 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:973.168   Data:  D1 22 78 BF 56 61 7D 3E 04 0A 78 BF E1 E6 7E 3E ...
T0818 002:973.178   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:975.084 - 1.919ms returns 0x184
T0818 002:975.102 JLINK_HasError()
T0818 002:975.133 JLINK_WriteReg(R0, 0x0800B000)
T0818 002:975.140 - 0.007ms returns 0
T0818 002:975.191 JLINK_WriteReg(R1, 0x00000400)
T0818 002:975.194 - 0.003ms returns 0
T0818 002:975.198 JLINK_WriteReg(R2, 0x20000184)
T0818 002:975.202 - 0.003ms returns 0
T0818 002:975.206 JLINK_WriteReg(R3, 0x00000000)
T0818 002:975.209 - 0.003ms returns 0
T0818 002:975.213 JLINK_WriteReg(R4, 0x00000000)
T0818 002:975.217 - 0.003ms returns 0
T0818 002:975.221 JLINK_WriteReg(R5, 0x00000000)
T0818 002:975.224 - 0.003ms returns 0
T0818 002:975.229 JLINK_WriteReg(R6, 0x00000000)
T0818 002:975.232 - 0.003ms returns 0
T0818 002:975.236 JLINK_WriteReg(R7, 0x00000000)
T0818 002:975.240 - 0.003ms returns 0
T0818 002:975.244 JLINK_WriteReg(R8, 0x00000000)
T0818 002:975.247 - 0.003ms returns 0
T0818 002:975.251 JLINK_WriteReg(R9, 0x20000180)
T0818 002:975.255 - 0.003ms returns 0
T0818 002:975.259 JLINK_WriteReg(R10, 0x00000000)
T0818 002:975.262 - 0.003ms returns 0
T0818 002:975.267 JLINK_WriteReg(R11, 0x00000000)
T0818 002:975.270 - 0.003ms returns 0
T0818 002:975.274 JLINK_WriteReg(R12, 0x00000000)
T0818 002:975.278 - 0.003ms returns 0
T0818 002:975.282 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:975.286 - 0.004ms returns 0
T0818 002:975.290 JLINK_WriteReg(R14, 0x20000001)
T0818 002:975.293 - 0.003ms returns 0
T0818 002:975.297 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:975.301 - 0.003ms returns 0
T0818 002:975.305 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:975.308 - 0.003ms returns 0
T0818 002:975.312 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:975.316 - 0.003ms returns 0
T0818 002:975.320 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:975.324 - 0.003ms returns 0
T0818 002:975.328 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:975.331 - 0.003ms returns 0
T0818 002:975.336 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:975.341 - 0.005ms returns 0x00000038
T0818 002:975.345 JLINK_Go()
T0818 002:975.354   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:978.191 - 2.846ms 
T0818 002:978.205 JLINK_IsHalted()
T0818 002:978.675 - 0.470ms returns FALSE
T0818 002:978.682 JLINK_HasError()
T0818 002:980.205 JLINK_IsHalted()
T0818 002:980.720 - 0.515ms returns FALSE
T0818 002:980.727 JLINK_HasError()
T0818 002:982.200 JLINK_IsHalted()
T0818 002:984.454   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:984.977 - 2.777ms returns TRUE
T0818 002:984.984 JLINK_ReadReg(R15 (PC))
T0818 002:984.989 - 0.004ms returns 0x20000000
T0818 002:984.994 JLINK_ClrBPEx(BPHandle = 0x00000038)
T0818 002:984.997 - 0.003ms returns 0x00
T0818 002:985.001 JLINK_ReadReg(R0)
T0818 002:985.005 - 0.003ms returns 0x00000000
T0818 002:985.446 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:985.458   Data:  33 D7 A2 3E 04 B5 72 BF C5 95 A3 3E F8 94 72 BF ...
T0818 002:985.469   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:988.105 - 2.658ms returns 0x27C
T0818 002:988.114 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:988.118   Data:  99 15 67 BF 2E 09 DD 3E 45 EA 66 BF 79 BE DD 3E ...
T0818 002:988.126   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:989.987 - 1.872ms returns 0x184
T0818 002:989.994 JLINK_HasError()
T0818 002:989.999 JLINK_WriteReg(R0, 0x0800B400)
T0818 002:990.236 - 0.236ms returns 0
T0818 002:990.240 JLINK_WriteReg(R1, 0x00000400)
T0818 002:990.244 - 0.003ms returns 0
T0818 002:990.248 JLINK_WriteReg(R2, 0x20000184)
T0818 002:990.252 - 0.003ms returns 0
T0818 002:990.256 JLINK_WriteReg(R3, 0x00000000)
T0818 002:990.259 - 0.003ms returns 0
T0818 002:990.263 JLINK_WriteReg(R4, 0x00000000)
T0818 002:990.266 - 0.003ms returns 0
T0818 002:990.271 JLINK_WriteReg(R5, 0x00000000)
T0818 002:990.274 - 0.003ms returns 0
T0818 002:990.278 JLINK_WriteReg(R6, 0x00000000)
T0818 002:990.282 - 0.003ms returns 0
T0818 002:990.286 JLINK_WriteReg(R7, 0x00000000)
T0818 002:990.289 - 0.003ms returns 0
T0818 002:990.294 JLINK_WriteReg(R8, 0x00000000)
T0818 002:990.297 - 0.003ms returns 0
T0818 002:990.301 JLINK_WriteReg(R9, 0x20000180)
T0818 002:990.305 - 0.003ms returns 0
T0818 002:990.309 JLINK_WriteReg(R10, 0x00000000)
T0818 002:990.312 - 0.003ms returns 0
T0818 002:990.320 JLINK_WriteReg(R11, 0x00000000)
T0818 002:990.324 - 0.003ms returns 0
T0818 002:990.328 JLINK_WriteReg(R12, 0x00000000)
T0818 002:990.331 - 0.003ms returns 0
T0818 002:990.336 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:990.340 - 0.004ms returns 0
T0818 002:990.344 JLINK_WriteReg(R14, 0x20000001)
T0818 002:990.348 - 0.003ms returns 0
T0818 002:990.352 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:990.355 - 0.003ms returns 0
T0818 002:990.360 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:990.363 - 0.003ms returns 0
T0818 002:990.367 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:990.370 - 0.003ms returns 0
T0818 002:990.374 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:990.378 - 0.003ms returns 0
T0818 002:990.382 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:990.385 - 0.003ms returns 0
T0818 002:990.390 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:990.394 - 0.004ms returns 0x00000039
T0818 002:990.398 JLINK_Go()
T0818 002:990.407   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:993.245 - 2.846ms 
T0818 002:993.264 JLINK_IsHalted()
T0818 002:993.752 - 0.488ms returns FALSE
T0818 002:993.759 JLINK_HasError()
T0818 002:995.208 JLINK_IsHalted()
T0818 002:995.750 - 0.541ms returns FALSE
T0818 002:995.759 JLINK_HasError()
T0818 002:997.201 JLINK_IsHalted()
T0818 002:999.523   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:999.994 - 2.793ms returns TRUE
T0818 003:000.008 JLINK_ReadReg(R15 (PC))
T0818 003:000.013 - 0.005ms returns 0x20000000
T0818 003:000.053 JLINK_ClrBPEx(BPHandle = 0x00000039)
T0818 003:000.058 - 0.005ms returns 0x00
T0818 003:000.063 JLINK_ReadReg(R0)
T0818 003:000.067 - 0.003ms returns 0x00000000
T0818 003:000.504 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:000.513   Data:  4A 69 FE 3E C3 28 5E BF B2 17 FF 3E BE F6 5D BF ...
T0818 003:000.526   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:003.157 - 2.652ms returns 0x27C
T0818 003:003.183 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:003.187   Data:  FD 26 4D BF 94 71 19 3F CB EA 4C BF 00 C2 19 3F ...
T0818 003:003.202   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:005.099 - 1.916ms returns 0x184
T0818 003:005.109 JLINK_HasError()
T0818 003:005.115 JLINK_WriteReg(R0, 0x0800B800)
T0818 003:005.120 - 0.005ms returns 0
T0818 003:005.124 JLINK_WriteReg(R1, 0x00000400)
T0818 003:005.128 - 0.003ms returns 0
T0818 003:005.132 JLINK_WriteReg(R2, 0x20000184)
T0818 003:005.135 - 0.003ms returns 0
T0818 003:005.139 JLINK_WriteReg(R3, 0x00000000)
T0818 003:005.143 - 0.003ms returns 0
T0818 003:005.147 JLINK_WriteReg(R4, 0x00000000)
T0818 003:005.150 - 0.003ms returns 0
T0818 003:005.154 JLINK_WriteReg(R5, 0x00000000)
T0818 003:005.158 - 0.003ms returns 0
T0818 003:005.162 JLINK_WriteReg(R6, 0x00000000)
T0818 003:005.400 - 0.237ms returns 0
T0818 003:005.404 JLINK_WriteReg(R7, 0x00000000)
T0818 003:005.407 - 0.003ms returns 0
T0818 003:005.412 JLINK_WriteReg(R8, 0x00000000)
T0818 003:005.415 - 0.003ms returns 0
T0818 003:005.419 JLINK_WriteReg(R9, 0x20000180)
T0818 003:005.423 - 0.003ms returns 0
T0818 003:005.427 JLINK_WriteReg(R10, 0x00000000)
T0818 003:005.430 - 0.003ms returns 0
T0818 003:005.434 JLINK_WriteReg(R11, 0x00000000)
T0818 003:005.438 - 0.003ms returns 0
T0818 003:005.442 JLINK_WriteReg(R12, 0x00000000)
T0818 003:005.445 - 0.003ms returns 0
T0818 003:005.449 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:005.453 - 0.004ms returns 0
T0818 003:005.457 JLINK_WriteReg(R14, 0x20000001)
T0818 003:005.461 - 0.003ms returns 0
T0818 003:005.466 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:005.469 - 0.004ms returns 0
T0818 003:005.473 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:005.476 - 0.003ms returns 0
T0818 003:005.480 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:005.484 - 0.003ms returns 0
T0818 003:005.488 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:005.491 - 0.003ms returns 0
T0818 003:005.495 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:005.498 - 0.003ms returns 0
T0818 003:005.503 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:005.513 - 0.010ms returns 0x0000003A
T0818 003:005.517 JLINK_Go()
T0818 003:005.525   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:008.202 - 2.684ms 
T0818 003:008.213 JLINK_IsHalted()
T0818 003:008.697 - 0.483ms returns FALSE
T0818 003:008.704 JLINK_HasError()
T0818 003:013.206 JLINK_IsHalted()
T0818 003:015.521   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:015.994 - 2.788ms returns TRUE
T0818 003:016.002 JLINK_ReadReg(R15 (PC))
T0818 003:016.007 - 0.005ms returns 0x20000000
T0818 003:016.012 JLINK_ClrBPEx(BPHandle = 0x0000003A)
T0818 003:016.016 - 0.003ms returns 0x00
T0818 003:016.021 JLINK_ReadReg(R0)
T0818 003:016.024 - 0.003ms returns 0x00000000
T0818 003:016.435 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:016.445   Data:  40 1A 28 3F EC 12 41 BF 05 66 28 3F DA D0 40 BF ...
T0818 003:016.456   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:019.024 - 2.589ms returns 0x27C
T0818 003:019.033 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:019.037   Data:  1B 56 2B BF FF 78 3E 3F 5B 0B 2B BF 1B BC 3E 3F ...
T0818 003:019.045   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:020.912 - 1.879ms returns 0x184
T0818 003:020.919 JLINK_HasError()
T0818 003:020.925 JLINK_WriteReg(R0, 0x0800BC00)
T0818 003:020.930 - 0.005ms returns 0
T0818 003:020.934 JLINK_WriteReg(R1, 0x00000400)
T0818 003:021.090 - 0.155ms returns 0
T0818 003:021.105 JLINK_WriteReg(R2, 0x20000184)
T0818 003:021.109 - 0.004ms returns 0
T0818 003:021.113 JLINK_WriteReg(R3, 0x00000000)
T0818 003:021.117 - 0.003ms returns 0
T0818 003:021.121 JLINK_WriteReg(R4, 0x00000000)
T0818 003:021.124 - 0.003ms returns 0
T0818 003:021.128 JLINK_WriteReg(R5, 0x00000000)
T0818 003:021.132 - 0.003ms returns 0
T0818 003:021.136 JLINK_WriteReg(R6, 0x00000000)
T0818 003:021.139 - 0.003ms returns 0
T0818 003:021.143 JLINK_WriteReg(R7, 0x00000000)
T0818 003:021.147 - 0.003ms returns 0
T0818 003:021.151 JLINK_WriteReg(R8, 0x00000000)
T0818 003:021.154 - 0.003ms returns 0
T0818 003:021.158 JLINK_WriteReg(R9, 0x20000180)
T0818 003:021.162 - 0.003ms returns 0
T0818 003:021.166 JLINK_WriteReg(R10, 0x00000000)
T0818 003:021.169 - 0.003ms returns 0
T0818 003:021.173 JLINK_WriteReg(R11, 0x00000000)
T0818 003:021.177 - 0.003ms returns 0
T0818 003:021.181 JLINK_WriteReg(R12, 0x00000000)
T0818 003:021.184 - 0.003ms returns 0
T0818 003:021.188 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:021.192 - 0.004ms returns 0
T0818 003:021.196 JLINK_WriteReg(R14, 0x20000001)
T0818 003:021.200 - 0.003ms returns 0
T0818 003:021.204 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:021.207 - 0.003ms returns 0
T0818 003:021.211 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:021.215 - 0.003ms returns 0
T0818 003:021.219 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:021.222 - 0.003ms returns 0
T0818 003:021.226 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:021.230 - 0.003ms returns 0
T0818 003:021.234 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:021.237 - 0.003ms returns 0
T0818 003:021.242 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:021.247 - 0.005ms returns 0x0000003B
T0818 003:021.252 JLINK_Go()
T0818 003:021.262   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:024.077 - 2.824ms 
T0818 003:024.095 JLINK_IsHalted()
T0818 003:024.575 - 0.479ms returns FALSE
T0818 003:024.584 JLINK_HasError()
T0818 003:026.201 JLINK_IsHalted()
T0818 003:026.616 - 0.415ms returns FALSE
T0818 003:026.625 JLINK_HasError()
T0818 003:028.205 JLINK_IsHalted()
T0818 003:030.550   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:031.059 - 2.852ms returns TRUE
T0818 003:031.074 JLINK_ReadReg(R15 (PC))
T0818 003:031.080 - 0.006ms returns 0x20000000
T0818 003:031.085 JLINK_ClrBPEx(BPHandle = 0x0000003B)
T0818 003:031.089 - 0.003ms returns 0x00
T0818 003:031.094 JLINK_ReadReg(R0)
T0818 003:031.097 - 0.003ms returns 0x00000000
T0818 003:031.501 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:031.511   Data:  13 8A 4A 3F A2 91 1C BF 7F C7 4A 3F 0C 42 1C BF ...
T0818 003:031.522   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:034.162 - 2.661ms returns 0x27C
T0818 003:034.177 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:034.181   Data:  9F EF 02 BF 8E 2E 5C 3F 32 99 02 BF C7 61 5C 3F ...
T0818 003:034.192   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:036.069 - 1.892ms returns 0x184
T0818 003:036.079 JLINK_HasError()
T0818 003:036.085 JLINK_WriteReg(R0, 0x0800C000)
T0818 003:036.091 - 0.006ms returns 0
T0818 003:036.095 JLINK_WriteReg(R1, 0x00000400)
T0818 003:036.099 - 0.003ms returns 0
T0818 003:036.103 JLINK_WriteReg(R2, 0x20000184)
T0818 003:036.106 - 0.003ms returns 0
T0818 003:036.111 JLINK_WriteReg(R3, 0x00000000)
T0818 003:036.115 - 0.003ms returns 0
T0818 003:036.119 JLINK_WriteReg(R4, 0x00000000)
T0818 003:036.122 - 0.003ms returns 0
T0818 003:036.126 JLINK_WriteReg(R5, 0x00000000)
T0818 003:036.130 - 0.003ms returns 0
T0818 003:036.134 JLINK_WriteReg(R6, 0x00000000)
T0818 003:036.137 - 0.003ms returns 0
T0818 003:036.141 JLINK_WriteReg(R7, 0x00000000)
T0818 003:036.145 - 0.003ms returns 0
T0818 003:036.149 JLINK_WriteReg(R8, 0x00000000)
T0818 003:036.152 - 0.003ms returns 0
T0818 003:036.156 JLINK_WriteReg(R9, 0x20000180)
T0818 003:036.159 - 0.003ms returns 0
T0818 003:036.163 JLINK_WriteReg(R10, 0x00000000)
T0818 003:036.167 - 0.003ms returns 0
T0818 003:036.171 JLINK_WriteReg(R11, 0x00000000)
T0818 003:036.174 - 0.003ms returns 0
T0818 003:036.178 JLINK_WriteReg(R12, 0x00000000)
T0818 003:036.182 - 0.003ms returns 0
T0818 003:036.186 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:036.190 - 0.004ms returns 0
T0818 003:036.195 JLINK_WriteReg(R14, 0x20000001)
T0818 003:036.198 - 0.003ms returns 0
T0818 003:036.202 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:036.206 - 0.003ms returns 0
T0818 003:036.210 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:036.214 - 0.003ms returns 0
T0818 003:036.218 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:036.221 - 0.003ms returns 0
T0818 003:036.225 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:036.228 - 0.003ms returns 0
T0818 003:036.232 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:036.236 - 0.003ms returns 0
T0818 003:036.240 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:036.245 - 0.005ms returns 0x0000003C
T0818 003:036.249 JLINK_Go()
T0818 003:036.257   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:038.959 - 2.709ms 
T0818 003:038.975 JLINK_IsHalted()
T0818 003:039.500 - 0.524ms returns FALSE
T0818 003:039.506 JLINK_HasError()
T0818 003:041.202 JLINK_IsHalted()
T0818 003:041.737 - 0.535ms returns FALSE
T0818 003:041.744 JLINK_HasError()
T0818 003:043.205 JLINK_IsHalted()
T0818 003:045.495   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:045.996 - 2.790ms returns TRUE
T0818 003:046.002 JLINK_ReadReg(R15 (PC))
T0818 003:046.008 - 0.006ms returns 0x20000000
T0818 003:046.013 JLINK_ClrBPEx(BPHandle = 0x0000003C)
T0818 003:046.016 - 0.003ms returns 0x00
T0818 003:046.021 JLINK_ReadReg(R0)
T0818 003:046.024 - 0.003ms returns 0x00000000
T0818 003:046.394 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:046.403   Data:  54 31 65 3F 0E 18 E4 BE 0B 5E 65 3F FA 63 E3 BE ...
T0818 003:046.415   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:048.923 - 2.529ms returns 0x27C
T0818 003:048.933 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:048.937   Data:  01 02 AB BE FB 6D 71 3F 6F 44 AA BE 57 8F 71 3F ...
T0818 003:048.947   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:050.835 - 1.902ms returns 0x184
T0818 003:050.849 JLINK_HasError()
T0818 003:050.888 JLINK_WriteReg(R0, 0x0800C400)
T0818 003:050.894 - 0.006ms returns 0
T0818 003:050.898 JLINK_WriteReg(R1, 0x00000400)
T0818 003:050.901 - 0.003ms returns 0
T0818 003:050.906 JLINK_WriteReg(R2, 0x20000184)
T0818 003:050.909 - 0.003ms returns 0
T0818 003:050.914 JLINK_WriteReg(R3, 0x00000000)
T0818 003:050.917 - 0.003ms returns 0
T0818 003:050.921 JLINK_WriteReg(R4, 0x00000000)
T0818 003:050.925 - 0.004ms returns 0
T0818 003:050.929 JLINK_WriteReg(R5, 0x00000000)
T0818 003:050.932 - 0.003ms returns 0
T0818 003:051.057 JLINK_WriteReg(R6, 0x00000000)
T0818 003:051.080 - 0.023ms returns 0
T0818 003:051.085 JLINK_WriteReg(R7, 0x00000000)
T0818 003:051.089 - 0.003ms returns 0
T0818 003:051.093 JLINK_WriteReg(R8, 0x00000000)
T0818 003:051.096 - 0.003ms returns 0
T0818 003:051.100 JLINK_WriteReg(R9, 0x20000180)
T0818 003:051.104 - 0.003ms returns 0
T0818 003:051.108 JLINK_WriteReg(R10, 0x00000000)
T0818 003:051.111 - 0.003ms returns 0
T0818 003:051.115 JLINK_WriteReg(R11, 0x00000000)
T0818 003:051.119 - 0.003ms returns 0
T0818 003:051.123 JLINK_WriteReg(R12, 0x00000000)
T0818 003:051.126 - 0.003ms returns 0
T0818 003:051.130 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:051.134 - 0.004ms returns 0
T0818 003:051.138 JLINK_WriteReg(R14, 0x20000001)
T0818 003:051.142 - 0.003ms returns 0
T0818 003:051.146 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:051.150 - 0.003ms returns 0
T0818 003:051.154 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:051.157 - 0.003ms returns 0
T0818 003:051.161 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:051.165 - 0.003ms returns 0
T0818 003:051.169 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:051.172 - 0.003ms returns 0
T0818 003:051.176 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:051.180 - 0.003ms returns 0
T0818 003:051.185 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:051.189 - 0.004ms returns 0x0000003D
T0818 003:051.196 JLINK_Go()
T0818 003:051.207   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:053.913 - 2.716ms 
T0818 003:053.932 JLINK_IsHalted()
T0818 003:054.487 - 0.554ms returns FALSE
T0818 003:054.498 JLINK_HasError()
T0818 003:056.200 JLINK_IsHalted()
T0818 003:056.641 - 0.441ms returns FALSE
T0818 003:056.649 JLINK_HasError()
T0818 003:058.202 JLINK_IsHalted()
T0818 003:060.519   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:061.082 - 2.879ms returns TRUE
T0818 003:061.102 JLINK_ReadReg(R15 (PC))
T0818 003:061.108 - 0.006ms returns 0x20000000
T0818 003:061.113 JLINK_ClrBPEx(BPHandle = 0x0000003D)
T0818 003:061.117 - 0.004ms returns 0x00
T0818 003:061.121 JLINK_ReadReg(R0)
T0818 003:061.125 - 0.004ms returns 0x00000000
T0818 003:061.516 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:061.525   Data:  CC 09 77 3F DF 48 86 BE 17 24 77 3F CE 86 85 BE ...
T0818 003:061.536   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:064.112 - 2.594ms returns 0x27C
T0818 003:064.129 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:064.132   Data:  CA 24 13 BE 3D 66 7D 3F CC 96 11 BE 74 74 7D 3F ...
T0818 003:064.144   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:066.035 - 1.906ms returns 0x184
T0818 003:066.045 JLINK_HasError()
T0818 003:066.051 JLINK_WriteReg(R0, 0x0800C800)
T0818 003:066.056 - 0.005ms returns 0
T0818 003:066.060 JLINK_WriteReg(R1, 0x00000400)
T0818 003:066.064 - 0.003ms returns 0
T0818 003:066.068 JLINK_WriteReg(R2, 0x20000184)
T0818 003:066.071 - 0.003ms returns 0
T0818 003:066.075 JLINK_WriteReg(R3, 0x00000000)
T0818 003:066.078 - 0.003ms returns 0
T0818 003:066.083 JLINK_WriteReg(R4, 0x00000000)
T0818 003:066.087 - 0.004ms returns 0
T0818 003:066.091 JLINK_WriteReg(R5, 0x00000000)
T0818 003:066.094 - 0.003ms returns 0
T0818 003:066.098 JLINK_WriteReg(R6, 0x00000000)
T0818 003:066.103 - 0.004ms returns 0
T0818 003:066.107 JLINK_WriteReg(R7, 0x00000000)
T0818 003:066.110 - 0.003ms returns 0
T0818 003:066.115 JLINK_WriteReg(R8, 0x00000000)
T0818 003:066.118 - 0.003ms returns 0
T0818 003:066.122 JLINK_WriteReg(R9, 0x20000180)
T0818 003:066.126 - 0.003ms returns 0
T0818 003:066.130 JLINK_WriteReg(R10, 0x00000000)
T0818 003:066.133 - 0.003ms returns 0
T0818 003:066.137 JLINK_WriteReg(R11, 0x00000000)
T0818 003:066.141 - 0.003ms returns 0
T0818 003:066.144 JLINK_WriteReg(R12, 0x00000000)
T0818 003:066.148 - 0.003ms returns 0
T0818 003:066.152 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:066.156 - 0.004ms returns 0
T0818 003:066.160 JLINK_WriteReg(R14, 0x20000001)
T0818 003:066.164 - 0.003ms returns 0
T0818 003:066.168 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:066.183 - 0.015ms returns 0
T0818 003:066.188 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:066.195 - 0.007ms returns 0
T0818 003:066.200 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:066.203 - 0.003ms returns 0
T0818 003:066.208 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:066.211 - 0.003ms returns 0
T0818 003:066.215 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:066.219 - 0.003ms returns 0
T0818 003:066.223 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:066.228 - 0.005ms returns 0x0000003E
T0818 003:066.232 JLINK_Go()
T0818 003:066.242   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:068.929 - 2.696ms 
T0818 003:068.937 JLINK_IsHalted()
T0818 003:069.447 - 0.510ms returns FALSE
T0818 003:069.454 JLINK_HasError()
T0818 003:071.206 JLINK_IsHalted()
T0818 003:071.723 - 0.517ms returns FALSE
T0818 003:071.730 JLINK_HasError()
T0818 003:073.202 JLINK_IsHalted()
T0818 003:075.480   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:075.997 - 2.794ms returns TRUE
T0818 003:076.010 JLINK_ReadReg(R15 (PC))
T0818 003:076.016 - 0.006ms returns 0x20000000
T0818 003:076.061 JLINK_ClrBPEx(BPHandle = 0x0000003E)
T0818 003:076.067 - 0.006ms returns 0x00
T0818 003:076.073 JLINK_ReadReg(R0)
T0818 003:076.078 - 0.004ms returns 0x00000000
T0818 003:076.575 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:076.585   Data:  EC 63 7F 3F 6A 42 8D BD C7 6A 7F 3F 0A 20 8A BD ...
T0818 003:076.597   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:079.220 - 2.645ms returns 0x27C
T0818 003:079.230 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:079.234   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T0818 003:079.241   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:081.107 - 1.876ms returns 0x184
T0818 003:081.128 JLINK_HasError()
T0818 003:081.135 JLINK_WriteReg(R0, 0x0800CC00)
T0818 003:081.142 - 0.007ms returns 0
T0818 003:081.146 JLINK_WriteReg(R1, 0x000001B0)
T0818 003:081.149 - 0.003ms returns 0
T0818 003:081.153 JLINK_WriteReg(R2, 0x20000184)
T0818 003:081.157 - 0.003ms returns 0
T0818 003:081.161 JLINK_WriteReg(R3, 0x00000000)
T0818 003:081.165 - 0.003ms returns 0
T0818 003:081.170 JLINK_WriteReg(R4, 0x00000000)
T0818 003:081.173 - 0.003ms returns 0
T0818 003:081.177 JLINK_WriteReg(R5, 0x00000000)
T0818 003:081.181 - 0.003ms returns 0
T0818 003:081.185 JLINK_WriteReg(R6, 0x00000000)
T0818 003:081.188 - 0.003ms returns 0
T0818 003:081.193 JLINK_WriteReg(R7, 0x00000000)
T0818 003:081.196 - 0.003ms returns 0
T0818 003:081.200 JLINK_WriteReg(R8, 0x00000000)
T0818 003:081.204 - 0.003ms returns 0
T0818 003:081.208 JLINK_WriteReg(R9, 0x20000180)
T0818 003:081.211 - 0.003ms returns 0
T0818 003:081.216 JLINK_WriteReg(R10, 0x00000000)
T0818 003:081.219 - 0.003ms returns 0
T0818 003:081.223 JLINK_WriteReg(R11, 0x00000000)
T0818 003:081.226 - 0.003ms returns 0
T0818 003:081.230 JLINK_WriteReg(R12, 0x00000000)
T0818 003:081.234 - 0.003ms returns 0
T0818 003:081.238 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:081.242 - 0.004ms returns 0
T0818 003:081.246 JLINK_WriteReg(R14, 0x20000001)
T0818 003:081.250 - 0.003ms returns 0
T0818 003:081.254 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:081.257 - 0.003ms returns 0
T0818 003:081.261 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:081.265 - 0.003ms returns 0
T0818 003:081.269 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:081.272 - 0.003ms returns 0
T0818 003:081.276 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:081.280 - 0.003ms returns 0
T0818 003:081.284 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:081.288 - 0.003ms returns 0
T0818 003:081.292 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:081.297 - 0.004ms returns 0x0000003F
T0818 003:081.302 JLINK_Go()
T0818 003:081.311   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:084.059 - 2.755ms 
T0818 003:084.079 JLINK_IsHalted()
T0818 003:084.565 - 0.486ms returns FALSE
T0818 003:084.573 JLINK_HasError()
T0818 003:086.204 JLINK_IsHalted()
T0818 003:088.527   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:089.011 - 2.807ms returns TRUE
T0818 003:089.022 JLINK_ReadReg(R15 (PC))
T0818 003:089.028 - 0.006ms returns 0x20000000
T0818 003:089.037 JLINK_ClrBPEx(BPHandle = 0x0000003F)
T0818 003:089.042 - 0.004ms returns 0x00
T0818 003:089.046 JLINK_ReadReg(R0)
T0818 003:089.050 - 0.003ms returns 0x00000000
T0818 003:089.055 JLINK_HasError()
T0818 003:089.059 JLINK_WriteReg(R0, 0x00000002)
T0818 003:089.063 - 0.003ms returns 0
T0818 003:089.068 JLINK_WriteReg(R1, 0x000001B0)
T0818 003:089.072 - 0.004ms returns 0
T0818 003:089.087 JLINK_WriteReg(R2, 0x20000184)
T0818 003:089.091 - 0.004ms returns 0
T0818 003:089.095 JLINK_WriteReg(R3, 0x00000000)
T0818 003:089.099 - 0.003ms returns 0
T0818 003:089.103 JLINK_WriteReg(R4, 0x00000000)
T0818 003:089.106 - 0.003ms returns 0
T0818 003:089.110 JLINK_WriteReg(R5, 0x00000000)
T0818 003:089.114 - 0.003ms returns 0
T0818 003:089.118 JLINK_WriteReg(R6, 0x00000000)
T0818 003:089.121 - 0.003ms returns 0
T0818 003:089.125 JLINK_WriteReg(R7, 0x00000000)
T0818 003:089.129 - 0.003ms returns 0
T0818 003:089.133 JLINK_WriteReg(R8, 0x00000000)
T0818 003:089.136 - 0.003ms returns 0
T0818 003:089.140 JLINK_WriteReg(R9, 0x20000180)
T0818 003:089.143 - 0.003ms returns 0
T0818 003:089.147 JLINK_WriteReg(R10, 0x00000000)
T0818 003:089.151 - 0.003ms returns 0
T0818 003:089.155 JLINK_WriteReg(R11, 0x00000000)
T0818 003:089.158 - 0.003ms returns 0
T0818 003:089.162 JLINK_WriteReg(R12, 0x00000000)
T0818 003:089.166 - 0.003ms returns 0
T0818 003:089.170 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:089.173 - 0.003ms returns 0
T0818 003:089.178 JLINK_WriteReg(R14, 0x20000001)
T0818 003:089.182 - 0.003ms returns 0
T0818 003:089.186 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 003:089.189 - 0.003ms returns 0
T0818 003:089.193 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:089.197 - 0.003ms returns 0
T0818 003:089.201 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:089.204 - 0.003ms returns 0
T0818 003:089.208 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:089.212 - 0.003ms returns 0
T0818 003:089.216 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:089.219 - 0.003ms returns 0
T0818 003:089.223 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:089.228 - 0.004ms returns 0x00000040
T0818 003:089.232 JLINK_Go()
T0818 003:089.239   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:091.912 - 2.680ms 
T0818 003:091.924 JLINK_IsHalted()
T0818 003:094.267   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:094.743 - 2.819ms returns TRUE
T0818 003:094.749 JLINK_ReadReg(R15 (PC))
T0818 003:094.753 - 0.004ms returns 0x20000000
T0818 003:094.758 JLINK_ClrBPEx(BPHandle = 0x00000040)
T0818 003:094.761 - 0.003ms returns 0x00
T0818 003:094.766 JLINK_ReadReg(R0)
T0818 003:094.770 - 0.003ms returns 0x00000000
T0818 003:148.991 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 003:149.006   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 003:149.025   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 003:150.924 - 1.932ms returns 0x184
T0818 003:151.125 JLINK_HasError()
T0818 003:151.134 JLINK_WriteReg(R0, 0x08000000)
T0818 003:151.141 - 0.007ms returns 0
T0818 003:151.146 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 003:151.149 - 0.003ms returns 0
T0818 003:151.153 JLINK_WriteReg(R2, 0x00000003)
T0818 003:151.158 - 0.004ms returns 0
T0818 003:151.162 JLINK_WriteReg(R3, 0x00000000)
T0818 003:151.165 - 0.003ms returns 0
T0818 003:151.169 JLINK_WriteReg(R4, 0x00000000)
T0818 003:151.173 - 0.003ms returns 0
T0818 003:151.177 JLINK_WriteReg(R5, 0x00000000)
T0818 003:151.181 - 0.003ms returns 0
T0818 003:151.185 JLINK_WriteReg(R6, 0x00000000)
T0818 003:151.189 - 0.003ms returns 0
T0818 003:151.193 JLINK_WriteReg(R7, 0x00000000)
T0818 003:151.197 - 0.004ms returns 0
T0818 003:151.201 JLINK_WriteReg(R8, 0x00000000)
T0818 003:151.205 - 0.003ms returns 0
T0818 003:151.209 JLINK_WriteReg(R9, 0x20000180)
T0818 003:151.212 - 0.003ms returns 0
T0818 003:151.216 JLINK_WriteReg(R10, 0x00000000)
T0818 003:151.220 - 0.003ms returns 0
T0818 003:151.224 JLINK_WriteReg(R11, 0x00000000)
T0818 003:151.227 - 0.003ms returns 0
T0818 003:151.231 JLINK_WriteReg(R12, 0x00000000)
T0818 003:151.235 - 0.003ms returns 0
T0818 003:151.243 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:151.247 - 0.004ms returns 0
T0818 003:151.251 JLINK_WriteReg(R14, 0x20000001)
T0818 003:151.255 - 0.003ms returns 0
T0818 003:151.259 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 003:151.262 - 0.003ms returns 0
T0818 003:151.267 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:151.270 - 0.003ms returns 0
T0818 003:151.274 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:151.278 - 0.003ms returns 0
T0818 003:151.282 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:151.285 - 0.003ms returns 0
T0818 003:151.289 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:151.293 - 0.003ms returns 0
T0818 003:151.298 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:151.308   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:151.845 - 0.547ms returns 0x00000041
T0818 003:151.856 JLINK_Go()
T0818 003:151.863   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 003:152.372   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:154.959 - 3.102ms 
T0818 003:154.982 JLINK_IsHalted()
T0818 003:157.245   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:157.744 - 2.762ms returns TRUE
T0818 003:157.755 JLINK_ReadReg(R15 (PC))
T0818 003:157.760 - 0.005ms returns 0x20000000
T0818 003:157.801 JLINK_ClrBPEx(BPHandle = 0x00000041)
T0818 003:157.807 - 0.006ms returns 0x00
T0818 003:157.812 JLINK_ReadReg(R0)
T0818 003:157.815 - 0.003ms returns 0x00000000
T0818 003:157.820 JLINK_HasError()
T0818 003:157.824 JLINK_WriteReg(R0, 0xFFFFFFFF)
T0818 003:157.829 - 0.004ms returns 0
T0818 003:157.833 JLINK_WriteReg(R1, 0x08000000)
T0818 003:157.836 - 0.003ms returns 0
T0818 003:157.840 JLINK_WriteReg(R2, 0x0000CDB0)
T0818 003:157.844 - 0.003ms returns 0
T0818 003:157.848 JLINK_WriteReg(R3, 0x04C11DB7)
T0818 003:157.851 - 0.003ms returns 0
T0818 003:157.855 JLINK_WriteReg(R4, 0x00000000)
T0818 003:157.859 - 0.003ms returns 0
T0818 003:157.863 JLINK_WriteReg(R5, 0x00000000)
T0818 003:157.866 - 0.003ms returns 0
T0818 003:157.870 JLINK_WriteReg(R6, 0x00000000)
T0818 003:157.874 - 0.003ms returns 0
T0818 003:157.878 JLINK_WriteReg(R7, 0x00000000)
T0818 003:157.881 - 0.003ms returns 0
T0818 003:157.885 JLINK_WriteReg(R8, 0x00000000)
T0818 003:157.888 - 0.003ms returns 0
T0818 003:157.892 JLINK_WriteReg(R9, 0x20000180)
T0818 003:157.896 - 0.003ms returns 0
T0818 003:157.900 JLINK_WriteReg(R10, 0x00000000)
T0818 003:157.903 - 0.003ms returns 0
T0818 003:157.907 JLINK_WriteReg(R11, 0x00000000)
T0818 003:157.910 - 0.003ms returns 0
T0818 003:157.914 JLINK_WriteReg(R12, 0x00000000)
T0818 003:157.918 - 0.003ms returns 0
T0818 003:157.922 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:157.925 - 0.003ms returns 0
T0818 003:157.930 JLINK_WriteReg(R14, 0x20000001)
T0818 003:157.933 - 0.003ms returns 0
T0818 003:157.937 JLINK_WriteReg(R15 (PC), 0x20000002)
T0818 003:157.940 - 0.003ms returns 0
T0818 003:157.945 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:157.948 - 0.003ms returns 0
T0818 003:157.952 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:157.955 - 0.003ms returns 0
T0818 003:157.959 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:157.963 - 0.003ms returns 0
T0818 003:157.967 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:157.970 - 0.003ms returns 0
T0818 003:157.975 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:157.979 - 0.004ms returns 0x00000042
T0818 003:157.983 JLINK_Go()
T0818 003:157.989   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:160.743 - 2.759ms 
T0818 003:160.752 JLINK_IsHalted()
T0818 003:161.292 - 0.539ms returns FALSE
T0818 003:161.298 JLINK_HasError()
T0818 003:164.206 JLINK_IsHalted()
T0818 003:164.671 - 0.465ms returns FALSE
T0818 003:164.678 JLINK_HasError()
T0818 003:166.204 JLINK_IsHalted()
T0818 003:166.716 - 0.510ms returns FALSE
T0818 003:166.727 JLINK_HasError()
T0818 003:168.200 JLINK_IsHalted()
T0818 003:168.647 - 0.446ms returns FALSE
T0818 003:168.656 JLINK_HasError()
T0818 003:170.207 JLINK_IsHalted()
T0818 003:170.680 - 0.473ms returns FALSE
T0818 003:170.688 JLINK_HasError()
T0818 003:172.201 JLINK_IsHalted()
T0818 003:172.686 - 0.484ms returns FALSE
T0818 003:172.692 JLINK_HasError()
T0818 003:174.200 JLINK_IsHalted()
T0818 003:174.616 - 0.415ms returns FALSE
T0818 003:174.622 JLINK_HasError()
T0818 003:176.202 JLINK_IsHalted()
T0818 003:176.697 - 0.495ms returns FALSE
T0818 003:176.703 JLINK_HasError()
T0818 003:178.201 JLINK_IsHalted()
T0818 003:178.688 - 0.486ms returns FALSE
T0818 003:178.695 JLINK_HasError()
T0818 003:180.200 JLINK_IsHalted()
T0818 003:180.652 - 0.451ms returns FALSE
T0818 003:180.658 JLINK_HasError()
T0818 003:182.202 JLINK_IsHalted()
T0818 003:182.619 - 0.416ms returns FALSE
T0818 003:182.631 JLINK_HasError()
T0818 003:184.200 JLINK_IsHalted()
T0818 003:184.651 - 0.450ms returns FALSE
T0818 003:184.656 JLINK_HasError()
T0818 003:186.203 JLINK_IsHalted()
T0818 003:186.748 - 0.545ms returns FALSE
T0818 003:186.755 JLINK_HasError()
T0818 003:188.203 JLINK_IsHalted()
T0818 003:188.687 - 0.483ms returns FALSE
T0818 003:188.693 JLINK_HasError()
T0818 003:190.200 JLINK_IsHalted()
T0818 003:190.710 - 0.509ms returns FALSE
T0818 003:190.716 JLINK_HasError()
T0818 003:192.203 JLINK_IsHalted()
T0818 003:192.664 - 0.461ms returns FALSE
T0818 003:192.670 JLINK_HasError()
T0818 003:194.209 JLINK_IsHalted()
T0818 003:194.721 - 0.512ms returns FALSE
T0818 003:194.728 JLINK_HasError()
T0818 003:196.204 JLINK_IsHalted()
T0818 003:196.644 - 0.439ms returns FALSE
T0818 003:196.654 JLINK_HasError()
T0818 003:198.205 JLINK_IsHalted()
T0818 003:198.652 - 0.446ms returns FALSE
T0818 003:198.658 JLINK_HasError()
T0818 003:200.202 JLINK_IsHalted()
T0818 003:200.721 - 0.519ms returns FALSE
T0818 003:200.728 JLINK_HasError()
T0818 003:202.200 JLINK_IsHalted()
T0818 003:202.720 - 0.519ms returns FALSE
T0818 003:202.726 JLINK_HasError()
T0818 003:204.202 JLINK_IsHalted()
T0818 003:204.685 - 0.482ms returns FALSE
T0818 003:204.691 JLINK_HasError()
T0818 003:206.201 JLINK_IsHalted()
T0818 003:206.696 - 0.495ms returns FALSE
T0818 003:206.703 JLINK_HasError()
T0818 003:208.201 JLINK_IsHalted()
T0818 003:208.625 - 0.424ms returns FALSE
T0818 003:208.637 JLINK_HasError()
T0818 003:210.203 JLINK_IsHalted()
T0818 003:210.730 - 0.526ms returns FALSE
T0818 003:210.736 JLINK_HasError()
T0818 003:212.203 JLINK_IsHalted()
T0818 003:212.650 - 0.446ms returns FALSE
T0818 003:212.656 JLINK_HasError()
T0818 003:214.207 JLINK_IsHalted()
T0818 003:214.679 - 0.471ms returns FALSE
T0818 003:214.686 JLINK_HasError()
T0818 003:217.206 JLINK_IsHalted()
T0818 003:217.725 - 0.518ms returns FALSE
T0818 003:217.732 JLINK_HasError()
T0818 003:219.204 JLINK_IsHalted()
T0818 003:219.677 - 0.473ms returns FALSE
T0818 003:219.683 JLINK_HasError()
T0818 003:221.205 JLINK_IsHalted()
T0818 003:221.722 - 0.517ms returns FALSE
T0818 003:221.730 JLINK_HasError()
T0818 003:223.204 JLINK_IsHalted()
T0818 003:223.686 - 0.481ms returns FALSE
T0818 003:223.697 JLINK_HasError()
T0818 003:225.210 JLINK_IsHalted()
T0818 003:225.708 - 0.499ms returns FALSE
T0818 003:225.715 JLINK_HasError()
T0818 003:227.205 JLINK_IsHalted()
T0818 003:227.654 - 0.449ms returns FALSE
T0818 003:227.665 JLINK_HasError()
T0818 003:229.203 JLINK_IsHalted()
T0818 003:229.722 - 0.519ms returns FALSE
T0818 003:229.732 JLINK_HasError()
T0818 003:231.202 JLINK_IsHalted()
T0818 003:231.663 - 0.460ms returns FALSE
T0818 003:231.670 JLINK_HasError()
T0818 003:233.203 JLINK_IsHalted()
T0818 003:233.698 - 0.495ms returns FALSE
T0818 003:233.704 JLINK_HasError()
T0818 003:235.202 JLINK_IsHalted()
T0818 003:235.734 - 0.532ms returns FALSE
T0818 003:235.741 JLINK_HasError()
T0818 003:239.205 JLINK_IsHalted()
T0818 003:239.702 - 0.496ms returns FALSE
T0818 003:239.709 JLINK_HasError()
T0818 003:241.208 JLINK_IsHalted()
T0818 003:241.690 - 0.481ms returns FALSE
T0818 003:241.704 JLINK_HasError()
T0818 003:243.202 JLINK_IsHalted()
T0818 003:243.744 - 0.541ms returns FALSE
T0818 003:243.751 JLINK_HasError()
T0818 003:245.238 JLINK_IsHalted()
T0818 003:245.746 - 0.507ms returns FALSE
T0818 003:245.759 JLINK_HasError()
T0818 003:247.207 JLINK_IsHalted()
T0818 003:247.730 - 0.522ms returns FALSE
T0818 003:247.740 JLINK_HasError()
T0818 003:249.201 JLINK_IsHalted()
T0818 003:249.655 - 0.454ms returns FALSE
T0818 003:249.664 JLINK_HasError()
T0818 003:251.203 JLINK_IsHalted()
T0818 003:251.710 - 0.507ms returns FALSE
T0818 003:251.717 JLINK_HasError()
T0818 003:253.200 JLINK_IsHalted()
T0818 003:253.700 - 0.499ms returns FALSE
T0818 003:253.707 JLINK_HasError()
T0818 003:256.210 JLINK_IsHalted()
T0818 003:256.702 - 0.492ms returns FALSE
T0818 003:256.711 JLINK_HasError()
T0818 003:258.203 JLINK_IsHalted()
T0818 003:258.644 - 0.440ms returns FALSE
T0818 003:258.654 JLINK_HasError()
T0818 003:260.205 JLINK_IsHalted()
T0818 003:260.660 - 0.455ms returns FALSE
T0818 003:260.672 JLINK_HasError()
T0818 003:262.202 JLINK_IsHalted()
T0818 003:262.753 - 0.551ms returns FALSE
T0818 003:262.760 JLINK_HasError()
T0818 003:265.204 JLINK_IsHalted()
T0818 003:265.720 - 0.516ms returns FALSE
T0818 003:265.728 JLINK_HasError()
T0818 003:267.207 JLINK_IsHalted()
T0818 003:267.745 - 0.537ms returns FALSE
T0818 003:267.751 JLINK_HasError()
T0818 003:269.207 JLINK_IsHalted()
T0818 003:269.750 - 0.542ms returns FALSE
T0818 003:269.763 JLINK_HasError()
T0818 003:272.204 JLINK_IsHalted()
T0818 003:272.688 - 0.483ms returns FALSE
T0818 003:272.697 JLINK_HasError()
T0818 003:274.204 JLINK_IsHalted()
T0818 003:274.683 - 0.479ms returns FALSE
T0818 003:274.691 JLINK_HasError()
T0818 003:276.203 JLINK_IsHalted()
T0818 003:276.747 - 0.544ms returns FALSE
T0818 003:276.754 JLINK_HasError()
T0818 003:279.207 JLINK_IsHalted()
T0818 003:279.689 - 0.481ms returns FALSE
T0818 003:279.697 JLINK_HasError()
T0818 003:281.202 JLINK_IsHalted()
T0818 003:281.747 - 0.545ms returns FALSE
T0818 003:281.757 JLINK_HasError()
T0818 003:283.204 JLINK_IsHalted()
T0818 003:283.685 - 0.480ms returns FALSE
T0818 003:283.694 JLINK_HasError()
T0818 003:285.206 JLINK_IsHalted()
T0818 003:285.829 - 0.623ms returns FALSE
T0818 003:285.850 JLINK_HasError()
T0818 003:287.203 JLINK_IsHalted()
T0818 003:287.680 - 0.476ms returns FALSE
T0818 003:287.691 JLINK_HasError()
T0818 003:289.209 JLINK_IsHalted()
T0818 003:289.715 - 0.505ms returns FALSE
T0818 003:289.724 JLINK_HasError()
T0818 003:291.205 JLINK_IsHalted()
T0818 003:291.682 - 0.476ms returns FALSE
T0818 003:291.692 JLINK_HasError()
T0818 003:293.202 JLINK_IsHalted()
T0818 003:293.831 - 0.627ms returns FALSE
T0818 003:293.846 JLINK_HasError()
T0818 003:295.202 JLINK_IsHalted()
T0818 003:295.649 - 0.447ms returns FALSE
T0818 003:295.658 JLINK_HasError()
T0818 003:297.204 JLINK_IsHalted()
T0818 003:297.683 - 0.478ms returns FALSE
T0818 003:297.689 JLINK_HasError()
T0818 003:300.203 JLINK_IsHalted()
T0818 003:300.651 - 0.447ms returns FALSE
T0818 003:300.657 JLINK_HasError()
T0818 003:302.205 JLINK_IsHalted()
T0818 003:302.729 - 0.524ms returns FALSE
T0818 003:302.735 JLINK_HasError()
T0818 003:304.202 JLINK_IsHalted()
T0818 003:304.652 - 0.449ms returns FALSE
T0818 003:304.658 JLINK_HasError()
T0818 003:306.201 JLINK_IsHalted()
T0818 003:306.734 - 0.532ms returns FALSE
T0818 003:306.740 JLINK_HasError()
T0818 003:309.206 JLINK_IsHalted()
T0818 003:309.736 - 0.529ms returns FALSE
T0818 003:309.754 JLINK_HasError()
T0818 003:311.205 JLINK_IsHalted()
T0818 003:311.665 - 0.459ms returns FALSE
T0818 003:311.679 JLINK_HasError()
T0818 003:313.203 JLINK_IsHalted()
T0818 003:313.686 - 0.483ms returns FALSE
T0818 003:313.695 JLINK_HasError()
T0818 003:315.203 JLINK_IsHalted()
T0818 003:315.641 - 0.438ms returns FALSE
T0818 003:315.648 JLINK_HasError()
T0818 003:317.202 JLINK_IsHalted()
T0818 003:317.745 - 0.543ms returns FALSE
T0818 003:317.751 JLINK_HasError()
T0818 003:319.202 JLINK_IsHalted()
T0818 003:319.696 - 0.494ms returns FALSE
T0818 003:319.702 JLINK_HasError()
T0818 003:321.200 JLINK_IsHalted()
T0818 003:321.668 - 0.467ms returns FALSE
T0818 003:321.675 JLINK_HasError()
T0818 003:323.204 JLINK_IsHalted()
T0818 003:323.696 - 0.492ms returns FALSE
T0818 003:323.707 JLINK_HasError()
T0818 003:325.201 JLINK_IsHalted()
T0818 003:325.662 - 0.460ms returns FALSE
T0818 003:325.669 JLINK_HasError()
T0818 003:328.206 JLINK_IsHalted()
T0818 003:328.720 - 0.513ms returns FALSE
T0818 003:328.727 JLINK_HasError()
T0818 003:330.208 JLINK_IsHalted()
T0818 003:330.698 - 0.489ms returns FALSE
T0818 003:330.705 JLINK_HasError()
T0818 003:332.204 JLINK_IsHalted()
T0818 003:332.667 - 0.462ms returns FALSE
T0818 003:332.676 JLINK_HasError()
T0818 003:334.204 JLINK_IsHalted()
T0818 003:334.697 - 0.492ms returns FALSE
T0818 003:334.705 JLINK_HasError()
T0818 003:336.202 JLINK_IsHalted()
T0818 003:336.731 - 0.528ms returns FALSE
T0818 003:336.737 JLINK_HasError()
T0818 003:338.199 JLINK_IsHalted()
T0818 003:338.734 - 0.534ms returns FALSE
T0818 003:338.740 JLINK_HasError()
T0818 003:340.204 JLINK_IsHalted()
T0818 003:340.758 - 0.553ms returns FALSE
T0818 003:340.770 JLINK_HasError()
T0818 003:343.205 JLINK_IsHalted()
T0818 003:343.701 - 0.495ms returns FALSE
T0818 003:343.708 JLINK_HasError()
T0818 003:345.201 JLINK_IsHalted()
T0818 003:345.736 - 0.534ms returns FALSE
T0818 003:345.743 JLINK_HasError()
T0818 003:347.201 JLINK_IsHalted()
T0818 003:347.678 - 0.477ms returns FALSE
T0818 003:347.684 JLINK_HasError()
T0818 003:349.201 JLINK_IsHalted()
T0818 003:349.667 - 0.466ms returns FALSE
T0818 003:349.673 JLINK_HasError()
T0818 003:351.210 JLINK_IsHalted()
T0818 003:351.707 - 0.497ms returns FALSE
T0818 003:351.715 JLINK_HasError()
T0818 003:353.201 JLINK_IsHalted()
T0818 003:353.733 - 0.531ms returns FALSE
T0818 003:353.745 JLINK_HasError()
T0818 003:355.204 JLINK_IsHalted()
T0818 003:355.855 - 0.650ms returns FALSE
T0818 003:355.868 JLINK_HasError()
T0818 003:357.201 JLINK_IsHalted()
T0818 003:357.644 - 0.441ms returns FALSE
T0818 003:357.654 JLINK_HasError()
T0818 003:360.205 JLINK_IsHalted()
T0818 003:360.652 - 0.447ms returns FALSE
T0818 003:360.660 JLINK_HasError()
T0818 003:362.214 JLINK_IsHalted()
T0818 003:362.700 - 0.486ms returns FALSE
T0818 003:362.714 JLINK_HasError()
T0818 003:364.202 JLINK_IsHalted()
T0818 003:364.680 - 0.476ms returns FALSE
T0818 003:364.689 JLINK_HasError()
T0818 003:366.205 JLINK_IsHalted()
T0818 003:366.730 - 0.525ms returns FALSE
T0818 003:366.739 JLINK_HasError()
T0818 003:368.202 JLINK_IsHalted()
T0818 003:368.686 - 0.483ms returns FALSE
T0818 003:368.691 JLINK_HasError()
T0818 003:370.200 JLINK_IsHalted()
T0818 003:370.751 - 0.551ms returns FALSE
T0818 003:370.759 JLINK_HasError()
T0818 003:372.204 JLINK_IsHalted()
T0818 003:372.642 - 0.438ms returns FALSE
T0818 003:372.649 JLINK_HasError()
T0818 003:374.202 JLINK_IsHalted()
T0818 003:374.700 - 0.498ms returns FALSE
T0818 003:374.707 JLINK_HasError()
T0818 003:376.207 JLINK_IsHalted()
T0818 003:376.730 - 0.523ms returns FALSE
T0818 003:376.736 JLINK_HasError()
T0818 003:378.200 JLINK_IsHalted()
T0818 003:378.688 - 0.487ms returns FALSE
T0818 003:378.694 JLINK_HasError()
T0818 003:380.206 JLINK_IsHalted()
T0818 003:380.729 - 0.523ms returns FALSE
T0818 003:380.736 JLINK_HasError()
T0818 003:382.200 JLINK_IsHalted()
T0818 003:382.652 - 0.451ms returns FALSE
T0818 003:382.658 JLINK_HasError()
T0818 003:384.202 JLINK_IsHalted()
T0818 003:384.645 - 0.443ms returns FALSE
T0818 003:384.651 JLINK_HasError()
T0818 003:386.200 JLINK_IsHalted()
T0818 003:386.668 - 0.467ms returns FALSE
T0818 003:386.674 JLINK_HasError()
T0818 003:389.205 JLINK_IsHalted()
T0818 003:389.702 - 0.496ms returns FALSE
T0818 003:389.709 JLINK_HasError()
T0818 003:391.200 JLINK_IsHalted()
T0818 003:391.697 - 0.497ms returns FALSE
T0818 003:391.705 JLINK_HasError()
T0818 003:393.197 JLINK_IsHalted()
T0818 003:393.683 - 0.485ms returns FALSE
T0818 003:393.688 JLINK_HasError()
T0818 003:395.197 JLINK_IsHalted()
T0818 003:395.660 - 0.462ms returns FALSE
T0818 003:395.666 JLINK_HasError()
T0818 003:397.197 JLINK_IsHalted()
T0818 003:397.702 - 0.504ms returns FALSE
T0818 003:397.707 JLINK_HasError()
T0818 003:399.197 JLINK_IsHalted()
T0818 003:399.682 - 0.484ms returns FALSE
T0818 003:399.687 JLINK_HasError()
T0818 003:401.200 JLINK_IsHalted()
T0818 003:401.686 - 0.485ms returns FALSE
T0818 003:401.694 JLINK_HasError()
T0818 003:403.198 JLINK_IsHalted()
T0818 003:403.680 - 0.482ms returns FALSE
T0818 003:403.686 JLINK_HasError()
T0818 003:405.198 JLINK_IsHalted()
T0818 003:405.659 - 0.461ms returns FALSE
T0818 003:405.664 JLINK_HasError()
T0818 003:407.197 JLINK_IsHalted()
T0818 003:407.702 - 0.505ms returns FALSE
T0818 003:407.708 JLINK_HasError()
T0818 003:409.234 JLINK_IsHalted()
T0818 003:409.728 - 0.495ms returns FALSE
T0818 003:409.735 JLINK_HasError()
T0818 003:411.199 JLINK_IsHalted()
T0818 003:411.681 - 0.482ms returns FALSE
T0818 003:411.686 JLINK_HasError()
T0818 003:413.198 JLINK_IsHalted()
T0818 003:413.645 - 0.446ms returns FALSE
T0818 003:413.654 JLINK_HasError()
T0818 003:415.198 JLINK_IsHalted()
T0818 003:415.662 - 0.464ms returns FALSE
T0818 003:415.668 JLINK_HasError()
T0818 003:417.197 JLINK_IsHalted()
T0818 003:417.705 - 0.507ms returns FALSE
T0818 003:417.713 JLINK_HasError()
T0818 003:419.198 JLINK_IsHalted()
T0818 003:419.696 - 0.497ms returns FALSE
T0818 003:419.702 JLINK_HasError()
T0818 003:421.197 JLINK_IsHalted()
T0818 003:421.680 - 0.483ms returns FALSE
T0818 003:421.686 JLINK_HasError()
T0818 003:423.196 JLINK_IsHalted()
T0818 003:423.694 - 0.497ms returns FALSE
T0818 003:423.700 JLINK_HasError()
T0818 003:425.207 JLINK_IsHalted()
T0818 003:425.656 - 0.449ms returns FALSE
T0818 003:425.668 JLINK_HasError()
T0818 003:427.200 JLINK_IsHalted()
T0818 003:427.668 - 0.467ms returns FALSE
T0818 003:427.674 JLINK_HasError()
T0818 003:429.197 JLINK_IsHalted()
T0818 003:429.674 - 0.475ms returns FALSE
T0818 003:429.679 JLINK_HasError()
T0818 003:431.201 JLINK_IsHalted()
T0818 003:431.623 - 0.421ms returns FALSE
T0818 003:431.635 JLINK_HasError()
T0818 003:433.198 JLINK_IsHalted()
T0818 003:433.710 - 0.512ms returns FALSE
T0818 003:433.718 JLINK_HasError()
T0818 003:436.198 JLINK_IsHalted()
T0818 003:436.677 - 0.478ms returns FALSE
T0818 003:436.683 JLINK_HasError()
T0818 003:438.197 JLINK_IsHalted()
T0818 003:438.675 - 0.477ms returns FALSE
T0818 003:438.680 JLINK_HasError()
T0818 003:440.198 JLINK_IsHalted()
T0818 003:440.660 - 0.461ms returns FALSE
T0818 003:440.668 JLINK_HasError()
T0818 003:442.198 JLINK_IsHalted()
T0818 003:442.682 - 0.484ms returns FALSE
T0818 003:442.688 JLINK_HasError()
T0818 003:444.197 JLINK_IsHalted()
T0818 003:444.697 - 0.499ms returns FALSE
T0818 003:444.702 JLINK_HasError()
T0818 003:446.198 JLINK_IsHalted()
T0818 003:446.717 - 0.518ms returns FALSE
T0818 003:446.723 JLINK_HasError()
T0818 003:448.196 JLINK_IsHalted()
T0818 003:448.658 - 0.462ms returns FALSE
T0818 003:448.664 JLINK_HasError()
T0818 003:450.199 JLINK_IsHalted()
T0818 003:452.537   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:453.001 - 2.801ms returns TRUE
T0818 003:453.008 JLINK_ReadReg(R15 (PC))
T0818 003:453.014 - 0.005ms returns 0x20000000
T0818 003:453.023 JLINK_ClrBPEx(BPHandle = 0x00000042)
T0818 003:453.035 - 0.011ms returns 0x00
T0818 003:453.049 JLINK_ReadReg(R0)
T0818 003:453.063 - 0.013ms returns 0x0AE5EAE6
T0818 003:454.661 JLINK_HasError()
T0818 003:454.672 JLINK_WriteReg(R0, 0x00000003)
T0818 003:454.677 - 0.004ms returns 0
T0818 003:454.681 JLINK_WriteReg(R1, 0x08000000)
T0818 003:454.684 - 0.003ms returns 0
T0818 003:454.688 JLINK_WriteReg(R2, 0x0000CDB0)
T0818 003:454.692 - 0.003ms returns 0
T0818 003:454.696 JLINK_WriteReg(R3, 0x04C11DB7)
T0818 003:454.700 - 0.003ms returns 0
T0818 003:454.704 JLINK_WriteReg(R4, 0x00000000)
T0818 003:454.708 - 0.003ms returns 0
T0818 003:454.712 JLINK_WriteReg(R5, 0x00000000)
T0818 003:454.715 - 0.003ms returns 0
T0818 003:454.719 JLINK_WriteReg(R6, 0x00000000)
T0818 003:454.722 - 0.003ms returns 0
T0818 003:454.726 JLINK_WriteReg(R7, 0x00000000)
T0818 003:454.730 - 0.003ms returns 0
T0818 003:454.734 JLINK_WriteReg(R8, 0x00000000)
T0818 003:454.737 - 0.003ms returns 0
T0818 003:454.744 JLINK_WriteReg(R9, 0x20000180)
T0818 003:454.747 - 0.003ms returns 0
T0818 003:454.752 JLINK_WriteReg(R10, 0x00000000)
T0818 003:454.755 - 0.003ms returns 0
T0818 003:454.759 JLINK_WriteReg(R11, 0x00000000)
T0818 003:454.762 - 0.003ms returns 0
T0818 003:454.766 JLINK_WriteReg(R12, 0x00000000)
T0818 003:454.770 - 0.003ms returns 0
T0818 003:454.774 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:454.777 - 0.003ms returns 0
T0818 003:454.781 JLINK_WriteReg(R14, 0x20000001)
T0818 003:454.785 - 0.003ms returns 0
T0818 003:454.789 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 003:454.792 - 0.003ms returns 0
T0818 003:454.796 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:454.800 - 0.003ms returns 0
T0818 003:454.804 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:454.808 - 0.003ms returns 0
T0818 003:454.812 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:454.815 - 0.003ms returns 0
T0818 003:454.819 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:454.822 - 0.003ms returns 0
T0818 003:454.827 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:454.831 - 0.004ms returns 0x00000043
T0818 003:454.836 JLINK_Go()
T0818 003:454.844   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:457.614 - 2.777ms 
T0818 003:457.623 JLINK_IsHalted()
T0818 003:459.937   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:460.473 - 2.849ms returns TRUE
T0818 003:460.482 JLINK_ReadReg(R15 (PC))
T0818 003:460.486 - 0.005ms returns 0x20000000
T0818 003:460.491 JLINK_ClrBPEx(BPHandle = 0x00000043)
T0818 003:460.495 - 0.003ms returns 0x00
T0818 003:460.499 JLINK_ReadReg(R0)
T0818 003:460.503 - 0.003ms returns 0x00000000
T0818 003:512.887 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T0818 003:512.902   Data:  FE E7
T0818 003:512.916   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 003:513.912 - 1.024ms returns 0x2
T0818 003:513.925 JLINK_HasError()
T0818 003:517.376 JLINK_Close()
T0818 003:518.968   OnDisconnectTarget() start
T0818 003:518.982    J-Link Script File: Executing OnDisconnectTarget()
T0818 003:518.991   CPU_WriteMem(4 bytes @ 0xE0042004)
T0818 003:519.488   CPU_WriteMem(4 bytes @ 0xE0042008)
T0818 003:521.216   OnDisconnectTarget() end - Took 991us
T0818 003:521.233   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:540.776 - 23.399ms
T0818 003:540.790   
T0818 003:540.793   Closed
