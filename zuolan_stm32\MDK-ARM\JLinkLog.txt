T0818 000:003.843   SEGGER J-Link V8.16 Log File
T0818 000:003.978   DLL Compiled: Feb 26 2025 12:07:26
T0818 000:003.982   Logging started @ 2025-08-01 16:23
T0818 000:003.986   Process: G:\keil\keil arm\UV4\UV4.exe
T0818 000:003.996 - 3.989ms 
T0818 000:004.003 JLINK_SetWarnOutHandler(...)
T0818 000:004.007 - 0.004ms 
T0818 000:004.014 JLINK_OpenEx(...)
T0818 000:007.844   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0818 000:009.355   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0818 000:009.464   Decompressing FW timestamp took 83 us
T0818 000:016.787   Hardware: V9.60
T0818 000:016.797   S/N: 69655018
T0818 000:016.802   OEM: SEGGER
T0818 000:016.808   Feature(s): RDI, GDB, <PERSON><PERSON><PERSON>, <PERSON>B<PERSON>, JFlash
T0818 000:018.002   Bootloader: (FW returned invalid version)
T0818 000:019.468   TELNET listener socket opened on port 19021
T0818 000:019.534   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T0818 000:019.650   WEBSRV Webserver running on local port 19080
T0818 000:019.712   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
T0818 000:019.786   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
T0818 000:318.940   Failed to connect to J-Link GUI Server.
T0818 000:318.964 - 314.944ms returns "O.K."
T0818 000:318.977 JLINK_GetEmuCaps()
T0818 000:318.983 - 0.004ms returns 0xB9FF7BBF
T0818 000:318.988 JLINK_TIF_GetAvailable(...)
T0818 000:319.384 - 0.396ms 
T0818 000:319.400 JLINK_SetErrorOutHandler(...)
T0818 000:319.404 - 0.003ms 
T0818 000:319.424 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025elec\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
T0818 000:329.847 - 10.424ms returns 0x00
T0818 000:331.532 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
T0818 000:332.665   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T0818 000:332.677     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T0818 000:337.655   Device "STM32F429IG" selected.
T0818 000:337.867 - 6.321ms returns 0x00
T0818 000:337.877 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T0818 000:337.890   ERROR: Unknown command
T0818 000:337.896 - 0.013ms returns 0x01
T0818 000:337.901 JLINK_GetHardwareVersion()
T0818 000:337.905 - 0.004ms returns 96000
T0818 000:337.909 JLINK_GetDLLVersion()
T0818 000:337.912 - 0.003ms returns 81600
T0818 000:337.916 JLINK_GetOEMString(...)
T0818 000:337.921 JLINK_GetFirmwareString(...)
T0818 000:337.925 - 0.003ms 
T0818 000:344.337 JLINK_GetDLLVersion()
T0818 000:344.352 - 0.015ms returns 81600
T0818 000:344.357 JLINK_GetCompileDateTime()
T0818 000:344.360 - 0.003ms 
T0818 000:348.372 JLINK_GetFirmwareString(...)
T0818 000:348.387 - 0.015ms 
T0818 000:358.148 JLINK_GetHardwareVersion()
T0818 000:358.162 - 0.014ms returns 96000
T0818 000:360.915 JLINK_GetSN()
T0818 000:360.946 - 0.030ms returns 69655018
T0818 000:363.306 JLINK_GetOEMString(...)
T0818 000:366.882 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T0818 000:368.738 - 1.858ms returns 0x00
T0818 000:368.753 JLINK_HasError()
T0818 000:368.768 JLINK_SetSpeed(5000)
T0818 000:369.069 - 0.302ms 
T0818 000:369.082 JLINK_GetId()
T0818 000:372.131   InitTarget() start
T0818 000:372.154    J-Link Script File: Executing InitTarget()
T0818 000:374.182   SWD selected. Executing JTAG -> SWD switching sequence.
T0818 000:378.794   DAP initialized successfully.
T0818 000:391.220   InitTarget() end - Took 17.1ms
T0818 000:393.841   Found SW-DP with ID 0x2BA01477
T0818 000:398.970   DPIDR: 0x2BA01477
T0818 000:400.638   CoreSight SoC-400 or earlier
T0818 000:402.526   Scanning AP map to find all available APs
T0818 000:405.246   AP[1]: Stopped AP scan as end of AP map has been reached
T0818 000:406.874   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
T0818 000:408.636   Iterating through AP map to find AHB-AP to use
T0818 000:411.117   AP[0]: Core found
T0818 000:412.178   AP[0]: AHB-AP ROM base: 0xE00FF000
T0818 000:413.870   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T0818 000:414.953   Found Cortex-M4 r0p1, Little endian.
T0818 000:415.782   -- Max. mem block: 0x00010C40
T0818 000:416.518   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:417.140   CPU_ReadMem(4 bytes @ 0x********)
T0818 000:418.733   FPUnit: 6 code (BP) slots and 2 literal slots
T0818 000:418.747   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T0818 000:419.216   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:419.692   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:420.173   CPU_WriteMem(4 bytes @ 0xE0001000)
T0818 000:420.695   CPU_ReadMem(4 bytes @ 0xE000ED88)
T0818 000:421.185   CPU_WriteMem(4 bytes @ 0xE000ED88)
T0818 000:421.678   CPU_ReadMem(4 bytes @ 0xE000ED88)
T0818 000:422.149   CPU_WriteMem(4 bytes @ 0xE000ED88)
T0818 000:423.702   CoreSight components:
T0818 000:424.745   ROMTbl[0] @ E00FF000
T0818 000:424.759   CPU_ReadMem(64 bytes @ 0xE00FF000)
T0818 000:425.507   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T0818 000:427.369   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T0818 000:427.384   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T0818 000:429.100   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T0818 000:429.115   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T0818 000:430.765   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T0818 000:430.778   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T0818 000:432.469   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T0818 000:432.483   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T0818 000:434.133   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T0818 000:434.147   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T0818 000:435.760   [0][5]: ******** CID B105900D PID 000BB925 ETM
T0818 000:436.195 - 67.113ms returns 0x2BA01477
T0818 000:436.237 JLINK_GetDLLVersion()
T0818 000:436.248 - 0.010ms returns 81600
T0818 000:436.260 JLINK_CORE_GetFound()
T0818 000:436.263 - 0.003ms returns 0xE0000FF
T0818 000:436.268 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T0818 000:436.274   Value=0xE00FF000
T0818 000:436.279 - 0.011ms returns 0
T0818 000:437.418 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T0818 000:437.428   Value=0xE00FF000
T0818 000:437.434 - 0.015ms returns 0
T0818 000:437.438 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T0818 000:437.442   Value=0x********
T0818 000:437.447 - 0.008ms returns 0
T0818 000:437.452 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T0818 000:437.473   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T0818 000:438.025   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T0818 000:438.036 - 0.584ms returns 32 (0x20)
T0818 000:438.042 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T0818 000:438.046   Value=0x00000000
T0818 000:438.051 - 0.008ms returns 0
T0818 000:438.055 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T0818 000:438.059   Value=0x********
T0818 000:438.063 - 0.008ms returns 0
T0818 000:438.068 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T0818 000:438.071   Value=0x********
T0818 000:438.076 - 0.008ms returns 0
T0818 000:438.080 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T0818 000:438.083   Value=0xE0001000
T0818 000:438.088 - 0.008ms returns 0
T0818 000:438.092 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T0818 000:438.096   Value=0x********
T0818 000:438.100 - 0.008ms returns 0
T0818 000:438.104 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T0818 000:438.108   Value=0xE000E000
T0818 000:438.113 - 0.008ms returns 0
T0818 000:438.117 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T0818 000:438.120   Value=0xE000EDF0
T0818 000:438.125 - 0.008ms returns 0
T0818 000:438.129 JLINK_GetDebugInfo(0x01 = Unknown)
T0818 000:438.132   Value=0x00000001
T0818 000:438.137 - 0.008ms returns 0
T0818 000:438.141 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T0818 000:438.148   CPU_ReadMem(4 bytes @ 0xE000ED00)
T0818 000:438.617   Data:  41 C2 0F 41
T0818 000:438.623   Debug reg: CPUID
T0818 000:438.628 - 0.486ms returns 1 (0x1)
T0818 000:438.633 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T0818 000:438.640   Value=0x00000000
T0818 000:438.645 - 0.012ms returns 0
T0818 000:438.650 JLINK_HasError()
T0818 000:438.654 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T0818 000:438.658 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T0818 000:438.662 JLINK_Reset()
T0818 000:438.668   JLINK_GetResetTypeDesc
T0818 000:438.671   - 0.003ms 
T0818 000:439.775   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T0818 000:439.795   CPU is running
T0818 000:439.802   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T0818 000:440.340   CPU is running
T0818 000:440.347   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:441.878   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T0818 000:443.768   Reset: Reset device via AIRCR.SYSRESETREQ.
T0818 000:443.782   CPU is running
T0818 000:443.788   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T0818 000:498.196   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:498.659   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:501.461   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:508.058   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:510.863   CPU_WriteMem(4 bytes @ 0x********)
T0818 000:511.384   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T0818 000:511.876   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:512.382 - 73.719ms 
T0818 000:512.424 JLINK_Halt()
T0818 000:512.430 - 0.006ms returns 0x00
T0818 000:512.436 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T0818 000:512.445   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:512.937   Data:  03 00 03 00
T0818 000:512.943   Debug reg: DHCSR
T0818 000:512.948 - 0.511ms returns 1 (0x1)
T0818 000:512.953 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T0818 000:512.956   Debug reg: DHCSR
T0818 000:513.217   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T0818 000:513.696 - 0.743ms returns 0 (0x00000000)
T0818 000:513.702 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T0818 000:513.706   Debug reg: DEMCR
T0818 000:513.714   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:514.216 - 0.513ms returns 0 (0x00000000)
T0818 000:518.879 JLINK_GetHWStatus(...)
T0818 000:519.267 - 0.387ms returns 0
T0818 000:522.462 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T0818 000:522.474 - 0.011ms returns 0x06
T0818 000:522.479 JLINK_GetNumBPUnits(Type = 0xF0)
T0818 000:522.483 - 0.003ms returns 0x2000
T0818 000:522.487 JLINK_GetNumWPUnits()
T0818 000:522.490 - 0.003ms returns 4
T0818 000:525.406 JLINK_GetSpeed()
T0818 000:525.417 - 0.010ms returns 4000
T0818 000:528.163 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T0818 000:528.181   CPU_ReadMem(4 bytes @ 0xE000E004)
T0818 000:528.752   Data:  02 00 00 00
T0818 000:528.761 - 0.598ms returns 1 (0x1)
T0818 000:528.767 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T0818 000:528.773   CPU_ReadMem(4 bytes @ 0xE000E004)
T0818 000:529.253   Data:  02 00 00 00
T0818 000:529.259 - 0.491ms returns 1 (0x1)
T0818 000:529.264 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T0818 000:529.268   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T0818 000:529.276   CPU_WriteMem(28 bytes @ 0xE0001000)
T0818 000:529.857 - 0.593ms returns 0x1C
T0818 000:529.865 JLINK_Halt()
T0818 000:529.869 - 0.003ms returns 0x00
T0818 000:529.873 JLINK_IsHalted()
T0818 000:529.877 - 0.003ms returns TRUE
T0818 000:531.500 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 000:531.508   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 000:531.712   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 000:533.573 - 2.072ms returns 0x184
T0818 000:533.605 JLINK_HasError()
T0818 000:533.611 JLINK_WriteReg(R0, 0x08000000)
T0818 000:533.616 - 0.005ms returns 0
T0818 000:533.621 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 000:533.624 - 0.003ms returns 0
T0818 000:533.628 JLINK_WriteReg(R2, 0x00000001)
T0818 000:533.632 - 0.003ms returns 0
T0818 000:533.636 JLINK_WriteReg(R3, 0x00000000)
T0818 000:533.639 - 0.003ms returns 0
T0818 000:533.643 JLINK_WriteReg(R4, 0x00000000)
T0818 000:533.647 - 0.003ms returns 0
T0818 000:533.651 JLINK_WriteReg(R5, 0x00000000)
T0818 000:533.657 - 0.006ms returns 0
T0818 000:533.664 JLINK_WriteReg(R6, 0x00000000)
T0818 000:533.667 - 0.003ms returns 0
T0818 000:533.671 JLINK_WriteReg(R7, 0x00000000)
T0818 000:533.674 - 0.003ms returns 0
T0818 000:533.687 JLINK_WriteReg(R8, 0x00000000)
T0818 000:533.691 - 0.012ms returns 0
T0818 000:533.695 JLINK_WriteReg(R9, 0x20000180)
T0818 000:533.698 - 0.003ms returns 0
T0818 000:533.702 JLINK_WriteReg(R10, 0x00000000)
T0818 000:533.706 - 0.003ms returns 0
T0818 000:533.710 JLINK_WriteReg(R11, 0x00000000)
T0818 000:533.713 - 0.003ms returns 0
T0818 000:533.717 JLINK_WriteReg(R12, 0x00000000)
T0818 000:533.720 - 0.003ms returns 0
T0818 000:533.725 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:533.728 - 0.003ms returns 0
T0818 000:533.732 JLINK_WriteReg(R14, 0x20000001)
T0818 000:533.736 - 0.003ms returns 0
T0818 000:533.743 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 000:533.747 - 0.007ms returns 0
T0818 000:533.751 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:533.754 - 0.003ms returns 0
T0818 000:533.758 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:533.762 - 0.003ms returns 0
T0818 000:533.766 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:533.769 - 0.003ms returns 0
T0818 000:533.773 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:533.777 - 0.003ms returns 0
T0818 000:533.781 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:533.787   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:534.340 - 0.558ms returns 0x00000001
T0818 000:534.345 JLINK_Go()
T0818 000:534.350   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 000:534.841   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:535.338   CPU_WriteMem(4 bytes @ 0xE0002008)
T0818 000:535.344   CPU_WriteMem(4 bytes @ 0xE000200C)
T0818 000:535.349   CPU_WriteMem(4 bytes @ 0xE0002010)
T0818 000:535.354   CPU_WriteMem(4 bytes @ 0xE0002014)
T0818 000:535.359   CPU_WriteMem(4 bytes @ 0xE0002018)
T0818 000:535.363   CPU_WriteMem(4 bytes @ 0xE000201C)
T0818 000:536.630   CPU_WriteMem(4 bytes @ 0xE0001004)
T0818 000:540.468   Memory map 'after startup completion point' is active
T0818 000:540.481 - 6.135ms 
T0818 000:540.491 JLINK_IsHalted()
T0818 000:542.818   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:543.341 - 2.849ms returns TRUE
T0818 000:543.349 JLINK_ReadReg(R15 (PC))
T0818 000:543.354 - 0.004ms returns 0x20000000
T0818 000:543.380 JLINK_ClrBPEx(BPHandle = 0x00000001)
T0818 000:543.385 - 0.005ms returns 0x00
T0818 000:543.389 JLINK_ReadReg(R0)
T0818 000:543.393 - 0.003ms returns 0x00000000
T0818 000:543.538 JLINK_HasError()
T0818 000:543.545 JLINK_WriteReg(R0, 0x08000000)
T0818 000:543.549 - 0.004ms returns 0
T0818 000:543.553 JLINK_WriteReg(R1, 0x00004000)
T0818 000:543.557 - 0.003ms returns 0
T0818 000:543.561 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:543.564 - 0.003ms returns 0
T0818 000:543.568 JLINK_WriteReg(R3, 0x00000000)
T0818 000:543.571 - 0.003ms returns 0
T0818 000:543.575 JLINK_WriteReg(R4, 0x00000000)
T0818 000:543.579 - 0.003ms returns 0
T0818 000:543.583 JLINK_WriteReg(R5, 0x00000000)
T0818 000:543.586 - 0.003ms returns 0
T0818 000:543.590 JLINK_WriteReg(R6, 0x00000000)
T0818 000:543.593 - 0.003ms returns 0
T0818 000:543.597 JLINK_WriteReg(R7, 0x00000000)
T0818 000:543.601 - 0.003ms returns 0
T0818 000:543.605 JLINK_WriteReg(R8, 0x00000000)
T0818 000:543.608 - 0.003ms returns 0
T0818 000:543.613 JLINK_WriteReg(R9, 0x20000180)
T0818 000:543.616 - 0.003ms returns 0
T0818 000:543.620 JLINK_WriteReg(R10, 0x00000000)
T0818 000:543.623 - 0.003ms returns 0
T0818 000:543.627 JLINK_WriteReg(R11, 0x00000000)
T0818 000:543.631 - 0.003ms returns 0
T0818 000:543.635 JLINK_WriteReg(R12, 0x00000000)
T0818 000:543.638 - 0.003ms returns 0
T0818 000:543.642 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:543.646 - 0.003ms returns 0
T0818 000:543.650 JLINK_WriteReg(R14, 0x20000001)
T0818 000:543.653 - 0.003ms returns 0
T0818 000:543.657 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 000:543.661 - 0.003ms returns 0
T0818 000:543.665 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:543.668 - 0.003ms returns 0
T0818 000:543.703 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:543.708 - 0.004ms returns 0
T0818 000:543.712 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:543.715 - 0.003ms returns 0
T0818 000:543.719 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:543.723 - 0.003ms returns 0
T0818 000:543.727 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:543.731 - 0.004ms returns 0x00000002
T0818 000:543.735 JLINK_Go()
T0818 000:543.743   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:546.571 - 2.835ms 
T0818 000:546.580 JLINK_IsHalted()
T0818 000:548.952   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:549.419 - 2.839ms returns TRUE
T0818 000:549.425 JLINK_ReadReg(R15 (PC))
T0818 000:549.430 - 0.004ms returns 0x20000000
T0818 000:549.434 JLINK_ClrBPEx(BPHandle = 0x00000002)
T0818 000:549.437 - 0.003ms returns 0x00
T0818 000:549.442 JLINK_ReadReg(R0)
T0818 000:549.445 - 0.003ms returns 0x00000001
T0818 000:549.449 JLINK_HasError()
T0818 000:549.454 JLINK_WriteReg(R0, 0x08000000)
T0818 000:549.458 - 0.003ms returns 0
T0818 000:549.462 JLINK_WriteReg(R1, 0x00004000)
T0818 000:549.515 - 0.052ms returns 0
T0818 000:549.521 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:549.524 - 0.003ms returns 0
T0818 000:549.529 JLINK_WriteReg(R3, 0x00000000)
T0818 000:549.532 - 0.003ms returns 0
T0818 000:549.536 JLINK_WriteReg(R4, 0x00000000)
T0818 000:549.539 - 0.003ms returns 0
T0818 000:549.543 JLINK_WriteReg(R5, 0x00000000)
T0818 000:549.547 - 0.003ms returns 0
T0818 000:549.551 JLINK_WriteReg(R6, 0x00000000)
T0818 000:549.554 - 0.003ms returns 0
T0818 000:549.558 JLINK_WriteReg(R7, 0x00000000)
T0818 000:549.562 - 0.003ms returns 0
T0818 000:549.567 JLINK_WriteReg(R8, 0x00000000)
T0818 000:549.571 - 0.003ms returns 0
T0818 000:549.575 JLINK_WriteReg(R9, 0x20000180)
T0818 000:549.578 - 0.003ms returns 0
T0818 000:549.582 JLINK_WriteReg(R10, 0x00000000)
T0818 000:549.585 - 0.003ms returns 0
T0818 000:549.589 JLINK_WriteReg(R11, 0x00000000)
T0818 000:549.593 - 0.003ms returns 0
T0818 000:549.597 JLINK_WriteReg(R12, 0x00000000)
T0818 000:549.600 - 0.003ms returns 0
T0818 000:549.604 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:549.608 - 0.003ms returns 0
T0818 000:549.612 JLINK_WriteReg(R14, 0x20000001)
T0818 000:549.615 - 0.003ms returns 0
T0818 000:549.620 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 000:549.623 - 0.003ms returns 0
T0818 000:549.627 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:549.630 - 0.003ms returns 0
T0818 000:549.634 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:549.638 - 0.003ms returns 0
T0818 000:549.642 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:549.646 - 0.003ms returns 0
T0818 000:549.650 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:549.653 - 0.003ms returns 0
T0818 000:549.657 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:549.662 - 0.004ms returns 0x00000003
T0818 000:549.666 JLINK_Go()
T0818 000:549.678   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:552.384 - 2.717ms 
T0818 000:552.396 JLINK_IsHalted()
T0818 000:552.863 - 0.467ms returns FALSE
T0818 000:552.869 JLINK_HasError()
T0818 000:561.561 JLINK_IsHalted()
T0818 000:562.046 - 0.485ms returns FALSE
T0818 000:562.052 JLINK_HasError()
T0818 000:563.559 JLINK_IsHalted()
T0818 000:564.046 - 0.487ms returns FALSE
T0818 000:564.052 JLINK_HasError()
T0818 000:565.559 JLINK_IsHalted()
T0818 000:566.044 - 0.485ms returns FALSE
T0818 000:566.050 JLINK_HasError()
T0818 000:567.562 JLINK_IsHalted()
T0818 000:568.083 - 0.520ms returns FALSE
T0818 000:568.092 JLINK_HasError()
T0818 000:569.561 JLINK_IsHalted()
T0818 000:570.048 - 0.487ms returns FALSE
T0818 000:570.056 JLINK_HasError()
T0818 000:571.558 JLINK_IsHalted()
T0818 000:572.045 - 0.486ms returns FALSE
T0818 000:572.050 JLINK_HasError()
T0818 000:573.558 JLINK_IsHalted()
T0818 000:574.045 - 0.486ms returns FALSE
T0818 000:574.050 JLINK_HasError()
T0818 000:575.559 JLINK_IsHalted()
T0818 000:576.044 - 0.484ms returns FALSE
T0818 000:576.050 JLINK_HasError()
T0818 000:577.561 JLINK_IsHalted()
T0818 000:578.013 - 0.451ms returns FALSE
T0818 000:578.051 JLINK_HasError()
T0818 000:579.561 JLINK_IsHalted()
T0818 000:580.023 - 0.462ms returns FALSE
T0818 000:580.028 JLINK_HasError()
T0818 000:581.561 JLINK_IsHalted()
T0818 000:582.082 - 0.521ms returns FALSE
T0818 000:582.088 JLINK_HasError()
T0818 000:583.562 JLINK_IsHalted()
T0818 000:584.046 - 0.483ms returns FALSE
T0818 000:584.052 JLINK_HasError()
T0818 000:585.560 JLINK_IsHalted()
T0818 000:586.045 - 0.484ms returns FALSE
T0818 000:586.051 JLINK_HasError()
T0818 000:587.563 JLINK_IsHalted()
T0818 000:588.007 - 0.444ms returns FALSE
T0818 000:588.013 JLINK_HasError()
T0818 000:589.562 JLINK_IsHalted()
T0818 000:590.068 - 0.506ms returns FALSE
T0818 000:590.074 JLINK_HasError()
T0818 000:592.064 JLINK_IsHalted()
T0818 000:592.544 - 0.479ms returns FALSE
T0818 000:592.549 JLINK_HasError()
T0818 000:594.064 JLINK_IsHalted()
T0818 000:594.526 - 0.461ms returns FALSE
T0818 000:594.532 JLINK_HasError()
T0818 000:596.064 JLINK_IsHalted()
T0818 000:596.618 - 0.553ms returns FALSE
T0818 000:596.625 JLINK_HasError()
T0818 000:598.065 JLINK_IsHalted()
T0818 000:598.626 - 0.560ms returns FALSE
T0818 000:598.635 JLINK_HasError()
T0818 000:600.064 JLINK_IsHalted()
T0818 000:600.523 - 0.458ms returns FALSE
T0818 000:600.528 JLINK_HasError()
T0818 000:602.064 JLINK_IsHalted()
T0818 000:602.523 - 0.458ms returns FALSE
T0818 000:602.528 JLINK_HasError()
T0818 000:604.067 JLINK_IsHalted()
T0818 000:604.562 - 0.494ms returns FALSE
T0818 000:604.567 JLINK_HasError()
T0818 000:606.068 JLINK_IsHalted()
T0818 000:606.615 - 0.547ms returns FALSE
T0818 000:606.623 JLINK_HasError()
T0818 000:608.064 JLINK_IsHalted()
T0818 000:608.547 - 0.482ms returns FALSE
T0818 000:608.552 JLINK_HasError()
T0818 000:610.216 JLINK_IsHalted()
T0818 000:611.735 - 1.519ms returns FALSE
T0818 000:611.741 JLINK_HasError()
T0818 000:613.064 JLINK_IsHalted()
T0818 000:613.650 - 0.585ms returns FALSE
T0818 000:613.660 JLINK_HasError()
T0818 000:615.067 JLINK_IsHalted()
T0818 000:615.550 - 0.483ms returns FALSE
T0818 000:615.556 JLINK_HasError()
T0818 000:617.065 JLINK_IsHalted()
T0818 000:617.544 - 0.479ms returns FALSE
T0818 000:617.551 JLINK_HasError()
T0818 000:619.065 JLINK_IsHalted()
T0818 000:619.547 - 0.482ms returns FALSE
T0818 000:619.555 JLINK_HasError()
T0818 000:621.064 JLINK_IsHalted()
T0818 000:621.546 - 0.481ms returns FALSE
T0818 000:621.552 JLINK_HasError()
T0818 000:623.081 JLINK_IsHalted()
T0818 000:623.650 - 0.569ms returns FALSE
T0818 000:623.658 JLINK_HasError()
T0818 000:625.064 JLINK_IsHalted()
T0818 000:625.615 - 0.551ms returns FALSE
T0818 000:625.623 JLINK_HasError()
T0818 000:627.072 JLINK_IsHalted()
T0818 000:627.547 - 0.475ms returns FALSE
T0818 000:627.557 JLINK_HasError()
T0818 000:629.069 JLINK_IsHalted()
T0818 000:629.573 - 0.504ms returns FALSE
T0818 000:629.582 JLINK_HasError()
T0818 000:632.069 JLINK_IsHalted()
T0818 000:632.572 - 0.503ms returns FALSE
T0818 000:632.584 JLINK_HasError()
T0818 000:634.064 JLINK_IsHalted()
T0818 000:634.544 - 0.479ms returns FALSE
T0818 000:634.549 JLINK_HasError()
T0818 000:636.069 JLINK_IsHalted()
T0818 000:636.562 - 0.493ms returns FALSE
T0818 000:636.568 JLINK_HasError()
T0818 000:638.064 JLINK_IsHalted()
T0818 000:638.523 - 0.458ms returns FALSE
T0818 000:638.528 JLINK_HasError()
T0818 000:640.065 JLINK_IsHalted()
T0818 000:640.525 - 0.460ms returns FALSE
T0818 000:640.532 JLINK_HasError()
T0818 000:642.069 JLINK_IsHalted()
T0818 000:642.480 - 0.411ms returns FALSE
T0818 000:642.486 JLINK_HasError()
T0818 000:644.068 JLINK_IsHalted()
T0818 000:644.545 - 0.477ms returns FALSE
T0818 000:644.556 JLINK_HasError()
T0818 000:647.080 JLINK_IsHalted()
T0818 000:647.528 - 0.448ms returns FALSE
T0818 000:647.541 JLINK_HasError()
T0818 000:649.065 JLINK_IsHalted()
T0818 000:649.562 - 0.496ms returns FALSE
T0818 000:649.568 JLINK_HasError()
T0818 000:651.065 JLINK_IsHalted()
T0818 000:651.523 - 0.458ms returns FALSE
T0818 000:651.528 JLINK_HasError()
T0818 000:653.064 JLINK_IsHalted()
T0818 000:653.591 - 0.526ms returns FALSE
T0818 000:653.596 JLINK_HasError()
T0818 000:655.064 JLINK_IsHalted()
T0818 000:655.498 - 0.434ms returns FALSE
T0818 000:655.503 JLINK_HasError()
T0818 000:657.068 JLINK_IsHalted()
T0818 000:657.562 - 0.493ms returns FALSE
T0818 000:657.569 JLINK_HasError()
T0818 000:659.065 JLINK_IsHalted()
T0818 000:659.528 - 0.462ms returns FALSE
T0818 000:659.541 JLINK_HasError()
T0818 000:661.065 JLINK_IsHalted()
T0818 000:661.544 - 0.478ms returns FALSE
T0818 000:661.551 JLINK_HasError()
T0818 000:663.064 JLINK_IsHalted()
T0818 000:663.542 - 0.478ms returns FALSE
T0818 000:663.548 JLINK_HasError()
T0818 000:665.064 JLINK_IsHalted()
T0818 000:665.521 - 0.456ms returns FALSE
T0818 000:665.526 JLINK_HasError()
T0818 000:667.064 JLINK_IsHalted()
T0818 000:667.565 - 0.500ms returns FALSE
T0818 000:667.570 JLINK_HasError()
T0818 000:669.074 JLINK_IsHalted()
T0818 000:669.523 - 0.449ms returns FALSE
T0818 000:669.531 JLINK_HasError()
T0818 000:671.065 JLINK_IsHalted()
T0818 000:671.551 - 0.486ms returns FALSE
T0818 000:671.557 JLINK_HasError()
T0818 000:673.064 JLINK_IsHalted()
T0818 000:673.542 - 0.477ms returns FALSE
T0818 000:673.548 JLINK_HasError()
T0818 000:675.070 JLINK_IsHalted()
T0818 000:675.589 - 0.518ms returns FALSE
T0818 000:675.595 JLINK_HasError()
T0818 000:677.065 JLINK_IsHalted()
T0818 000:677.552 - 0.486ms returns FALSE
T0818 000:677.557 JLINK_HasError()
T0818 000:679.065 JLINK_IsHalted()
T0818 000:679.544 - 0.479ms returns FALSE
T0818 000:679.550 JLINK_HasError()
T0818 000:681.064 JLINK_IsHalted()
T0818 000:681.544 - 0.479ms returns FALSE
T0818 000:681.549 JLINK_HasError()
T0818 000:683.064 JLINK_IsHalted()
T0818 000:683.535 - 0.470ms returns FALSE
T0818 000:683.540 JLINK_HasError()
T0818 000:685.064 JLINK_IsHalted()
T0818 000:685.521 - 0.457ms returns FALSE
T0818 000:685.526 JLINK_HasError()
T0818 000:687.069 JLINK_IsHalted()
T0818 000:689.042 - 1.972ms returns FALSE
T0818 000:689.054 JLINK_HasError()
T0818 000:691.065 JLINK_IsHalted()
T0818 000:691.708 - 0.643ms returns FALSE
T0818 000:691.718 JLINK_HasError()
T0818 000:694.068 JLINK_IsHalted()
T0818 000:694.529 - 0.460ms returns FALSE
T0818 000:694.535 JLINK_HasError()
T0818 000:696.064 JLINK_IsHalted()
T0818 000:696.687 - 0.622ms returns FALSE
T0818 000:696.694 JLINK_HasError()
T0818 000:698.065 JLINK_IsHalted()
T0818 000:698.561 - 0.495ms returns FALSE
T0818 000:698.567 JLINK_HasError()
T0818 000:700.067 JLINK_IsHalted()
T0818 000:700.548 - 0.481ms returns FALSE
T0818 000:700.555 JLINK_HasError()
T0818 000:702.065 JLINK_IsHalted()
T0818 000:702.562 - 0.496ms returns FALSE
T0818 000:702.567 JLINK_HasError()
T0818 000:704.064 JLINK_IsHalted()
T0818 000:704.524 - 0.459ms returns FALSE
T0818 000:704.530 JLINK_HasError()
T0818 000:706.064 JLINK_IsHalted()
T0818 000:706.543 - 0.478ms returns FALSE
T0818 000:706.549 JLINK_HasError()
T0818 000:708.065 JLINK_IsHalted()
T0818 000:708.588 - 0.523ms returns FALSE
T0818 000:708.594 JLINK_HasError()
T0818 000:710.064 JLINK_IsHalted()
T0818 000:710.524 - 0.459ms returns FALSE
T0818 000:710.529 JLINK_HasError()
T0818 000:712.064 JLINK_IsHalted()
T0818 000:712.544 - 0.480ms returns FALSE
T0818 000:712.550 JLINK_HasError()
T0818 000:714.064 JLINK_IsHalted()
T0818 000:714.501 - 0.436ms returns FALSE
T0818 000:714.506 JLINK_HasError()
T0818 000:716.064 JLINK_IsHalted()
T0818 000:716.546 - 0.481ms returns FALSE
T0818 000:716.553 JLINK_HasError()
T0818 000:718.066 JLINK_IsHalted()
T0818 000:718.544 - 0.477ms returns FALSE
T0818 000:718.549 JLINK_HasError()
T0818 000:720.064 JLINK_IsHalted()
T0818 000:720.567 - 0.503ms returns FALSE
T0818 000:720.573 JLINK_HasError()
T0818 000:722.064 JLINK_IsHalted()
T0818 000:722.530 - 0.465ms returns FALSE
T0818 000:722.535 JLINK_HasError()
T0818 000:724.065 JLINK_IsHalted()
T0818 000:724.511 - 0.446ms returns FALSE
T0818 000:724.518 JLINK_HasError()
T0818 000:726.064 JLINK_IsHalted()
T0818 000:726.524 - 0.459ms returns FALSE
T0818 000:726.530 JLINK_HasError()
T0818 000:728.064 JLINK_IsHalted()
T0818 000:728.546 - 0.481ms returns FALSE
T0818 000:728.551 JLINK_HasError()
T0818 000:730.064 JLINK_IsHalted()
T0818 000:730.545 - 0.481ms returns FALSE
T0818 000:730.551 JLINK_HasError()
T0818 000:732.064 JLINK_IsHalted()
T0818 000:732.544 - 0.479ms returns FALSE
T0818 000:732.549 JLINK_HasError()
T0818 000:734.064 JLINK_IsHalted()
T0818 000:734.501 - 0.436ms returns FALSE
T0818 000:734.506 JLINK_HasError()
T0818 000:736.064 JLINK_IsHalted()
T0818 000:736.542 - 0.478ms returns FALSE
T0818 000:736.548 JLINK_HasError()
T0818 000:738.064 JLINK_IsHalted()
T0818 000:738.568 - 0.503ms returns FALSE
T0818 000:738.573 JLINK_HasError()
T0818 000:740.065 JLINK_IsHalted()
T0818 000:740.545 - 0.480ms returns FALSE
T0818 000:740.551 JLINK_HasError()
T0818 000:742.068 JLINK_IsHalted()
T0818 000:742.510 - 0.441ms returns FALSE
T0818 000:742.516 JLINK_HasError()
T0818 000:744.064 JLINK_IsHalted()
T0818 000:744.524 - 0.459ms returns FALSE
T0818 000:744.529 JLINK_HasError()
T0818 000:746.064 JLINK_IsHalted()
T0818 000:746.642 - 0.577ms returns FALSE
T0818 000:746.651 JLINK_HasError()
T0818 000:748.064 JLINK_IsHalted()
T0818 000:748.666 - 0.601ms returns FALSE
T0818 000:748.671 JLINK_HasError()
T0818 000:750.064 JLINK_IsHalted()
T0818 000:750.524 - 0.459ms returns FALSE
T0818 000:750.529 JLINK_HasError()
T0818 000:752.064 JLINK_IsHalted()
T0818 000:752.549 - 0.484ms returns FALSE
T0818 000:752.554 JLINK_HasError()
T0818 000:754.064 JLINK_IsHalted()
T0818 000:754.501 - 0.436ms returns FALSE
T0818 000:754.506 JLINK_HasError()
T0818 000:756.066 JLINK_IsHalted()
T0818 000:756.605 - 0.538ms returns FALSE
T0818 000:756.613 JLINK_HasError()
T0818 000:759.067 JLINK_IsHalted()
T0818 000:759.594 - 0.526ms returns FALSE
T0818 000:759.600 JLINK_HasError()
T0818 000:761.065 JLINK_IsHalted()
T0818 000:761.562 - 0.497ms returns FALSE
T0818 000:761.567 JLINK_HasError()
T0818 000:763.064 JLINK_IsHalted()
T0818 000:763.543 - 0.478ms returns FALSE
T0818 000:763.549 JLINK_HasError()
T0818 000:765.064 JLINK_IsHalted()
T0818 000:765.568 - 0.504ms returns FALSE
T0818 000:765.574 JLINK_HasError()
T0818 000:767.065 JLINK_IsHalted()
T0818 000:767.525 - 0.459ms returns FALSE
T0818 000:767.530 JLINK_HasError()
T0818 000:769.185 JLINK_IsHalted()
T0818 000:769.649 - 0.463ms returns FALSE
T0818 000:769.655 JLINK_HasError()
T0818 000:771.064 JLINK_IsHalted()
T0818 000:771.593 - 0.528ms returns FALSE
T0818 000:771.603 JLINK_HasError()
T0818 000:773.065 JLINK_IsHalted()
T0818 000:773.567 - 0.502ms returns FALSE
T0818 000:773.573 JLINK_HasError()
T0818 000:775.064 JLINK_IsHalted()
T0818 000:775.547 - 0.482ms returns FALSE
T0818 000:775.552 JLINK_HasError()
T0818 000:777.788 JLINK_IsHalted()
T0818 000:778.337 - 0.549ms returns FALSE
T0818 000:778.343 JLINK_HasError()
T0818 000:780.064 JLINK_IsHalted()
T0818 000:780.523 - 0.458ms returns FALSE
T0818 000:780.528 JLINK_HasError()
T0818 000:782.064 JLINK_IsHalted()
T0818 000:782.537 - 0.473ms returns FALSE
T0818 000:782.543 JLINK_HasError()
T0818 000:784.066 JLINK_IsHalted()
T0818 000:784.552 - 0.485ms returns FALSE
T0818 000:784.557 JLINK_HasError()
T0818 000:786.065 JLINK_IsHalted()
T0818 000:786.923 - 0.858ms returns FALSE
T0818 000:786.933 JLINK_HasError()
T0818 000:790.068 JLINK_IsHalted()
T0818 000:790.570 - 0.502ms returns FALSE
T0818 000:790.576 JLINK_HasError()
T0818 000:792.064 JLINK_IsHalted()
T0818 000:792.546 - 0.481ms returns FALSE
T0818 000:792.552 JLINK_HasError()
T0818 000:794.064 JLINK_IsHalted()
T0818 000:794.561 - 0.496ms returns FALSE
T0818 000:794.567 JLINK_HasError()
T0818 000:796.064 JLINK_IsHalted()
T0818 000:796.671 - 0.606ms returns FALSE
T0818 000:796.679 JLINK_HasError()
T0818 000:798.066 JLINK_IsHalted()
T0818 000:798.527 - 0.460ms returns FALSE
T0818 000:798.533 JLINK_HasError()
T0818 000:800.064 JLINK_IsHalted()
T0818 000:800.524 - 0.459ms returns FALSE
T0818 000:800.529 JLINK_HasError()
T0818 000:802.067 JLINK_IsHalted()
T0818 000:802.570 - 0.502ms returns FALSE
T0818 000:802.580 JLINK_HasError()
T0818 000:804.065 JLINK_IsHalted()
T0818 000:804.568 - 0.503ms returns FALSE
T0818 000:804.574 JLINK_HasError()
T0818 000:806.064 JLINK_IsHalted()
T0818 000:806.614 - 0.549ms returns FALSE
T0818 000:806.621 JLINK_HasError()
T0818 000:808.064 JLINK_IsHalted()
T0818 000:808.546 - 0.481ms returns FALSE
T0818 000:808.552 JLINK_HasError()
T0818 000:810.064 JLINK_IsHalted()
T0818 000:810.544 - 0.479ms returns FALSE
T0818 000:810.549 JLINK_HasError()
T0818 000:812.082 JLINK_IsHalted()
T0818 000:813.032 - 0.950ms returns FALSE
T0818 000:813.040 JLINK_HasError()
T0818 000:815.064 JLINK_IsHalted()
T0818 000:815.524 - 0.459ms returns FALSE
T0818 000:815.529 JLINK_HasError()
T0818 000:817.066 JLINK_IsHalted()
T0818 000:817.562 - 0.495ms returns FALSE
T0818 000:817.567 JLINK_HasError()
T0818 000:819.064 JLINK_IsHalted()
T0818 000:819.524 - 0.459ms returns FALSE
T0818 000:819.530 JLINK_HasError()
T0818 000:821.064 JLINK_IsHalted()
T0818 000:821.569 - 0.504ms returns FALSE
T0818 000:821.575 JLINK_HasError()
T0818 000:823.064 JLINK_IsHalted()
T0818 000:823.547 - 0.482ms returns FALSE
T0818 000:823.552 JLINK_HasError()
T0818 000:825.066 JLINK_IsHalted()
T0818 000:825.526 - 0.459ms returns FALSE
T0818 000:825.531 JLINK_HasError()
T0818 000:827.065 JLINK_IsHalted()
T0818 000:827.523 - 0.458ms returns FALSE
T0818 000:827.528 JLINK_HasError()
T0818 000:829.108 JLINK_IsHalted()
T0818 000:829.686 - 0.578ms returns FALSE
T0818 000:829.692 JLINK_HasError()
T0818 000:831.064 JLINK_IsHalted()
T0818 000:831.566 - 0.502ms returns FALSE
T0818 000:831.572 JLINK_HasError()
T0818 000:833.065 JLINK_IsHalted()
T0818 000:833.562 - 0.496ms returns FALSE
T0818 000:833.568 JLINK_HasError()
T0818 000:835.065 JLINK_IsHalted()
T0818 000:835.592 - 0.527ms returns FALSE
T0818 000:835.599 JLINK_HasError()
T0818 000:837.065 JLINK_IsHalted()
T0818 000:837.546 - 0.481ms returns FALSE
T0818 000:837.552 JLINK_HasError()
T0818 000:839.064 JLINK_IsHalted()
T0818 000:839.502 - 0.437ms returns FALSE
T0818 000:839.508 JLINK_HasError()
T0818 000:841.064 JLINK_IsHalted()
T0818 000:841.567 - 0.502ms returns FALSE
T0818 000:841.573 JLINK_HasError()
T0818 000:843.064 JLINK_IsHalted()
T0818 000:843.546 - 0.481ms returns FALSE
T0818 000:843.552 JLINK_HasError()
T0818 000:845.065 JLINK_IsHalted()
T0818 000:845.562 - 0.497ms returns FALSE
T0818 000:845.568 JLINK_HasError()
T0818 000:847.065 JLINK_IsHalted()
T0818 000:847.659 - 0.593ms returns FALSE
T0818 000:847.670 JLINK_HasError()
T0818 000:849.098 JLINK_IsHalted()
T0818 000:849.606 - 0.507ms returns FALSE
T0818 000:849.612 JLINK_HasError()
T0818 000:851.064 JLINK_IsHalted()
T0818 000:851.605 - 0.540ms returns FALSE
T0818 000:851.615 JLINK_HasError()
T0818 000:853.066 JLINK_IsHalted()
T0818 000:853.572 - 0.505ms returns FALSE
T0818 000:853.578 JLINK_HasError()
T0818 000:855.065 JLINK_IsHalted()
T0818 000:855.510 - 0.445ms returns FALSE
T0818 000:855.516 JLINK_HasError()
T0818 000:857.065 JLINK_IsHalted()
T0818 000:857.527 - 0.462ms returns FALSE
T0818 000:857.535 JLINK_HasError()
T0818 000:860.066 JLINK_IsHalted()
T0818 000:860.563 - 0.497ms returns FALSE
T0818 000:860.569 JLINK_HasError()
T0818 000:863.065 JLINK_IsHalted()
T0818 000:863.562 - 0.496ms returns FALSE
T0818 000:863.570 JLINK_HasError()
T0818 000:866.071 JLINK_IsHalted()
T0818 000:866.907 - 0.836ms returns FALSE
T0818 000:866.917 JLINK_HasError()
T0818 000:868.068 JLINK_IsHalted()
T0818 000:868.584 - 0.515ms returns FALSE
T0818 000:868.592 JLINK_HasError()
T0818 000:870.067 JLINK_IsHalted()
T0818 000:870.553 - 0.486ms returns FALSE
T0818 000:870.561 JLINK_HasError()
T0818 000:872.068 JLINK_IsHalted()
T0818 000:872.663 - 0.594ms returns FALSE
T0818 000:872.676 JLINK_HasError()
T0818 000:875.068 JLINK_IsHalted()
T0818 000:875.570 - 0.501ms returns FALSE
T0818 000:875.581 JLINK_HasError()
T0818 000:877.077 JLINK_IsHalted()
T0818 000:877.638 - 0.560ms returns FALSE
T0818 000:877.648 JLINK_HasError()
T0818 000:879.067 JLINK_IsHalted()
T0818 000:879.577 - 0.509ms returns FALSE
T0818 000:879.593 JLINK_HasError()
T0818 000:881.073 JLINK_IsHalted()
T0818 000:881.594 - 0.521ms returns FALSE
T0818 000:881.603 JLINK_HasError()
T0818 000:883.070 JLINK_IsHalted()
T0818 000:883.532 - 0.461ms returns FALSE
T0818 000:883.543 JLINK_HasError()
T0818 000:885.073 JLINK_IsHalted()
T0818 000:885.594 - 0.520ms returns FALSE
T0818 000:885.602 JLINK_HasError()
T0818 000:887.079 JLINK_IsHalted()
T0818 000:887.664 - 0.584ms returns FALSE
T0818 000:887.672 JLINK_HasError()
T0818 000:889.071 JLINK_IsHalted()
T0818 000:889.575 - 0.503ms returns FALSE
T0818 000:889.585 JLINK_HasError()
T0818 000:891.072 JLINK_IsHalted()
T0818 000:891.541 - 0.468ms returns FALSE
T0818 000:891.548 JLINK_HasError()
T0818 000:893.071 JLINK_IsHalted()
T0818 000:893.485 - 0.415ms returns FALSE
T0818 000:893.492 JLINK_HasError()
T0818 000:895.070 JLINK_IsHalted()
T0818 000:895.576 - 0.505ms returns FALSE
T0818 000:895.594 JLINK_HasError()
T0818 000:897.069 JLINK_IsHalted()
T0818 000:897.574 - 0.504ms returns FALSE
T0818 000:897.582 JLINK_HasError()
T0818 000:899.079 JLINK_IsHalted()
T0818 000:899.552 - 0.473ms returns FALSE
T0818 000:899.562 JLINK_HasError()
T0818 000:901.070 JLINK_IsHalted()
T0818 000:901.572 - 0.501ms returns FALSE
T0818 000:901.580 JLINK_HasError()
T0818 000:904.069 JLINK_IsHalted()
T0818 000:904.596 - 0.527ms returns FALSE
T0818 000:904.604 JLINK_HasError()
T0818 000:906.070 JLINK_IsHalted()
T0818 000:906.609 - 0.538ms returns FALSE
T0818 000:906.617 JLINK_HasError()
T0818 000:908.069 JLINK_IsHalted()
T0818 000:908.527 - 0.458ms returns FALSE
T0818 000:908.536 JLINK_HasError()
T0818 000:910.069 JLINK_IsHalted()
T0818 000:910.577 - 0.507ms returns FALSE
T0818 000:910.595 JLINK_HasError()
T0818 000:912.173 JLINK_IsHalted()
T0818 000:912.618 - 0.444ms returns FALSE
T0818 000:912.625 JLINK_HasError()
T0818 000:914.068 JLINK_IsHalted()
T0818 000:914.574 - 0.505ms returns FALSE
T0818 000:914.583 JLINK_HasError()
T0818 000:916.071 JLINK_IsHalted()
T0818 000:916.576 - 0.503ms returns FALSE
T0818 000:916.591 JLINK_HasError()
T0818 000:918.068 JLINK_IsHalted()
T0818 000:918.531 - 0.462ms returns FALSE
T0818 000:918.540 JLINK_HasError()
T0818 000:920.071 JLINK_IsHalted()
T0818 000:920.551 - 0.479ms returns FALSE
T0818 000:920.558 JLINK_HasError()
T0818 000:922.069 JLINK_IsHalted()
T0818 000:922.572 - 0.503ms returns FALSE
T0818 000:922.580 JLINK_HasError()
T0818 000:924.071 JLINK_IsHalted()
T0818 000:924.572 - 0.500ms returns FALSE
T0818 000:924.583 JLINK_HasError()
T0818 000:926.069 JLINK_IsHalted()
T0818 000:926.553 - 0.483ms returns FALSE
T0818 000:926.562 JLINK_HasError()
T0818 000:928.069 JLINK_IsHalted()
T0818 000:928.482 - 0.412ms returns FALSE
T0818 000:928.489 JLINK_HasError()
T0818 000:930.074 JLINK_IsHalted()
T0818 000:930.596 - 0.522ms returns FALSE
T0818 000:930.603 JLINK_HasError()
T0818 000:932.068 JLINK_IsHalted()
T0818 000:932.569 - 0.501ms returns FALSE
T0818 000:932.577 JLINK_HasError()
T0818 000:934.071 JLINK_IsHalted()
T0818 000:934.585 - 0.514ms returns FALSE
T0818 000:934.594 JLINK_HasError()
T0818 000:936.070 JLINK_IsHalted()
T0818 000:936.571 - 0.501ms returns FALSE
T0818 000:936.578 JLINK_HasError()
T0818 000:938.069 JLINK_IsHalted()
T0818 000:938.513 - 0.444ms returns FALSE
T0818 000:938.520 JLINK_HasError()
T0818 000:941.074 JLINK_IsHalted()
T0818 000:941.569 - 0.495ms returns FALSE
T0818 000:941.585 JLINK_HasError()
T0818 000:943.071 JLINK_IsHalted()
T0818 000:943.572 - 0.500ms returns FALSE
T0818 000:943.582 JLINK_HasError()
T0818 000:945.070 JLINK_IsHalted()
T0818 000:945.527 - 0.456ms returns FALSE
T0818 000:945.535 JLINK_HasError()
T0818 000:947.071 JLINK_IsHalted()
T0818 000:947.572 - 0.500ms returns FALSE
T0818 000:947.578 JLINK_HasError()
T0818 000:950.071 JLINK_IsHalted()
T0818 000:950.558 - 0.486ms returns FALSE
T0818 000:950.569 JLINK_HasError()
T0818 000:952.069 JLINK_IsHalted()
T0818 000:952.485 - 0.416ms returns FALSE
T0818 000:952.498 JLINK_HasError()
T0818 000:954.070 JLINK_IsHalted()
T0818 000:954.483 - 0.412ms returns FALSE
T0818 000:954.490 JLINK_HasError()
T0818 000:956.070 JLINK_IsHalted()
T0818 000:956.574 - 0.504ms returns FALSE
T0818 000:956.581 JLINK_HasError()
T0818 000:959.072 JLINK_IsHalted()
T0818 000:959.529 - 0.456ms returns FALSE
T0818 000:959.536 JLINK_HasError()
T0818 000:962.080 JLINK_IsHalted()
T0818 000:962.567 - 0.486ms returns FALSE
T0818 000:962.580 JLINK_HasError()
T0818 000:964.072 JLINK_IsHalted()
T0818 000:966.423   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:966.928 - 2.856ms returns TRUE
T0818 000:966.938 JLINK_ReadReg(R15 (PC))
T0818 000:966.943 - 0.005ms returns 0x20000000
T0818 000:966.947 JLINK_ClrBPEx(BPHandle = 0x00000003)
T0818 000:966.951 - 0.003ms returns 0x00
T0818 000:966.956 JLINK_ReadReg(R0)
T0818 000:966.959 - 0.003ms returns 0x00000000
T0818 000:967.350 JLINK_HasError()
T0818 000:967.361 JLINK_WriteReg(R0, 0x08004000)
T0818 000:967.366 - 0.004ms returns 0
T0818 000:967.370 JLINK_WriteReg(R1, 0x00004000)
T0818 000:967.374 - 0.003ms returns 0
T0818 000:967.378 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:967.382 - 0.003ms returns 0
T0818 000:967.386 JLINK_WriteReg(R3, 0x00000000)
T0818 000:967.389 - 0.003ms returns 0
T0818 000:967.393 JLINK_WriteReg(R4, 0x00000000)
T0818 000:967.397 - 0.003ms returns 0
T0818 000:967.401 JLINK_WriteReg(R5, 0x00000000)
T0818 000:967.404 - 0.003ms returns 0
T0818 000:967.408 JLINK_WriteReg(R6, 0x00000000)
T0818 000:967.412 - 0.003ms returns 0
T0818 000:967.416 JLINK_WriteReg(R7, 0x00000000)
T0818 000:967.420 - 0.003ms returns 0
T0818 000:967.425 JLINK_WriteReg(R8, 0x00000000)
T0818 000:967.428 - 0.003ms returns 0
T0818 000:967.432 JLINK_WriteReg(R9, 0x20000180)
T0818 000:967.435 - 0.003ms returns 0
T0818 000:967.439 JLINK_WriteReg(R10, 0x00000000)
T0818 000:967.443 - 0.003ms returns 0
T0818 000:967.447 JLINK_WriteReg(R11, 0x00000000)
T0818 000:967.450 - 0.003ms returns 0
T0818 000:967.454 JLINK_WriteReg(R12, 0x00000000)
T0818 000:967.458 - 0.003ms returns 0
T0818 000:967.462 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:967.466 - 0.003ms returns 0
T0818 000:967.471 JLINK_WriteReg(R14, 0x20000001)
T0818 000:967.474 - 0.003ms returns 0
T0818 000:967.478 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 000:967.482 - 0.003ms returns 0
T0818 000:967.486 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:967.489 - 0.003ms returns 0
T0818 000:967.493 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:967.497 - 0.003ms returns 0
T0818 000:967.501 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:967.504 - 0.003ms returns 0
T0818 000:967.508 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:967.511 - 0.003ms returns 0
T0818 000:967.516 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:967.520 - 0.004ms returns 0x00000004
T0818 000:967.524 JLINK_Go()
T0818 000:967.534   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:970.232 - 2.707ms 
T0818 000:970.241 JLINK_IsHalted()
T0818 000:972.619   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:973.131 - 2.888ms returns TRUE
T0818 000:973.138 JLINK_ReadReg(R15 (PC))
T0818 000:973.144 - 0.006ms returns 0x20000000
T0818 000:973.149 JLINK_ClrBPEx(BPHandle = 0x00000004)
T0818 000:973.153 - 0.003ms returns 0x00
T0818 000:973.157 JLINK_ReadReg(R0)
T0818 000:973.161 - 0.003ms returns 0x00000001
T0818 000:973.165 JLINK_HasError()
T0818 000:973.170 JLINK_WriteReg(R0, 0x08004000)
T0818 000:973.174 - 0.004ms returns 0
T0818 000:973.178 JLINK_WriteReg(R1, 0x00004000)
T0818 000:973.181 - 0.003ms returns 0
T0818 000:973.185 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:973.189 - 0.003ms returns 0
T0818 000:973.193 JLINK_WriteReg(R3, 0x00000000)
T0818 000:973.196 - 0.003ms returns 0
T0818 000:973.200 JLINK_WriteReg(R4, 0x00000000)
T0818 000:973.204 - 0.003ms returns 0
T0818 000:973.208 JLINK_WriteReg(R5, 0x00000000)
T0818 000:973.212 - 0.003ms returns 0
T0818 000:973.216 JLINK_WriteReg(R6, 0x00000000)
T0818 000:973.219 - 0.003ms returns 0
T0818 000:973.223 JLINK_WriteReg(R7, 0x00000000)
T0818 000:973.231 - 0.007ms returns 0
T0818 000:973.238 JLINK_WriteReg(R8, 0x00000000)
T0818 000:973.241 - 0.003ms returns 0
T0818 000:973.245 JLINK_WriteReg(R9, 0x20000180)
T0818 000:973.249 - 0.003ms returns 0
T0818 000:973.253 JLINK_WriteReg(R10, 0x00000000)
T0818 000:973.256 - 0.003ms returns 0
T0818 000:973.260 JLINK_WriteReg(R11, 0x00000000)
T0818 000:973.263 - 0.003ms returns 0
T0818 000:973.267 JLINK_WriteReg(R12, 0x00000000)
T0818 000:973.271 - 0.003ms returns 0
T0818 000:973.275 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:973.279 - 0.004ms returns 0
T0818 000:973.283 JLINK_WriteReg(R14, 0x20000001)
T0818 000:973.286 - 0.003ms returns 0
T0818 000:973.290 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 000:973.294 - 0.003ms returns 0
T0818 000:973.298 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:973.320 - 0.022ms returns 0
T0818 000:973.324 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:973.328 - 0.003ms returns 0
T0818 000:973.332 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:973.336 - 0.003ms returns 0
T0818 000:973.344 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:973.348 - 0.003ms returns 0
T0818 000:973.352 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:973.356 - 0.004ms returns 0x00000005
T0818 000:973.360 JLINK_Go()
T0818 000:973.368   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:976.188 - 2.827ms 
T0818 000:976.205 JLINK_IsHalted()
T0818 000:976.750 - 0.544ms returns FALSE
T0818 000:976.767 JLINK_HasError()
T0818 000:980.073 JLINK_IsHalted()
T0818 000:980.553 - 0.481ms returns FALSE
T0818 000:980.560 JLINK_HasError()
T0818 000:982.078 JLINK_IsHalted()
T0818 000:982.525 - 0.447ms returns FALSE
T0818 000:982.533 JLINK_HasError()
T0818 000:984.069 JLINK_IsHalted()
T0818 000:984.574 - 0.503ms returns FALSE
T0818 000:984.581 JLINK_HasError()
T0818 000:987.074 JLINK_IsHalted()
T0818 000:987.569 - 0.495ms returns FALSE
T0818 000:987.577 JLINK_HasError()
T0818 000:989.069 JLINK_IsHalted()
T0818 000:989.563 - 0.494ms returns FALSE
T0818 000:989.569 JLINK_HasError()
T0818 000:992.071 JLINK_IsHalted()
T0818 000:992.568 - 0.496ms returns FALSE
T0818 000:992.576 JLINK_HasError()
T0818 000:994.068 JLINK_IsHalted()
T0818 000:994.555 - 0.486ms returns FALSE
T0818 000:994.562 JLINK_HasError()
T0818 000:996.071 JLINK_IsHalted()
T0818 000:996.573 - 0.502ms returns FALSE
T0818 000:996.579 JLINK_HasError()
T0818 000:998.067 JLINK_IsHalted()
T0818 000:998.551 - 0.484ms returns FALSE
T0818 000:998.561 JLINK_HasError()
T0818 001:002.076 JLINK_IsHalted()
T0818 001:002.611 - 0.535ms returns FALSE
T0818 001:002.623 JLINK_HasError()
T0818 001:004.070 JLINK_IsHalted()
T0818 001:004.600 - 0.529ms returns FALSE
T0818 001:004.609 JLINK_HasError()
T0818 001:006.068 JLINK_IsHalted()
T0818 001:006.609 - 0.540ms returns FALSE
T0818 001:006.619 JLINK_HasError()
T0818 001:008.068 JLINK_IsHalted()
T0818 001:008.529 - 0.460ms returns FALSE
T0818 001:008.536 JLINK_HasError()
T0818 001:010.066 JLINK_IsHalted()
T0818 001:010.549 - 0.482ms returns FALSE
T0818 001:010.556 JLINK_HasError()
T0818 001:013.075 JLINK_IsHalted()
T0818 001:013.558 - 0.483ms returns FALSE
T0818 001:013.564 JLINK_HasError()
T0818 001:015.067 JLINK_IsHalted()
T0818 001:015.564 - 0.496ms returns FALSE
T0818 001:015.570 JLINK_HasError()
T0818 001:017.070 JLINK_IsHalted()
T0818 001:017.528 - 0.458ms returns FALSE
T0818 001:017.534 JLINK_HasError()
T0818 001:020.072 JLINK_IsHalted()
T0818 001:020.532 - 0.460ms returns FALSE
T0818 001:020.539 JLINK_HasError()
T0818 001:022.069 JLINK_IsHalted()
T0818 001:022.563 - 0.493ms returns FALSE
T0818 001:022.570 JLINK_HasError()
T0818 001:024.077 JLINK_IsHalted()
T0818 001:024.565 - 0.488ms returns FALSE
T0818 001:024.571 JLINK_HasError()
T0818 001:027.073 JLINK_IsHalted()
T0818 001:027.595 - 0.521ms returns FALSE
T0818 001:027.619 JLINK_HasError()
T0818 001:029.073 JLINK_IsHalted()
T0818 001:029.593 - 0.520ms returns FALSE
T0818 001:029.603 JLINK_HasError()
T0818 001:031.069 JLINK_IsHalted()
T0818 001:031.576 - 0.506ms returns FALSE
T0818 001:031.595 JLINK_HasError()
T0818 001:033.069 JLINK_IsHalted()
T0818 001:033.530 - 0.461ms returns FALSE
T0818 001:033.540 JLINK_HasError()
T0818 001:035.069 JLINK_IsHalted()
T0818 001:035.598 - 0.527ms returns FALSE
T0818 001:035.607 JLINK_HasError()
T0818 001:037.071 JLINK_IsHalted()
T0818 001:037.530 - 0.459ms returns FALSE
T0818 001:037.538 JLINK_HasError()
T0818 001:039.068 JLINK_IsHalted()
T0818 001:039.556 - 0.487ms returns FALSE
T0818 001:039.561 JLINK_HasError()
T0818 001:041.076 JLINK_IsHalted()
T0818 001:041.608 - 0.531ms returns FALSE
T0818 001:041.615 JLINK_HasError()
T0818 001:043.068 JLINK_IsHalted()
T0818 001:043.528 - 0.459ms returns FALSE
T0818 001:043.538 JLINK_HasError()
T0818 001:045.077 JLINK_IsHalted()
T0818 001:045.565 - 0.488ms returns FALSE
T0818 001:045.574 JLINK_HasError()
T0818 001:047.065 JLINK_IsHalted()
T0818 001:047.506 - 0.441ms returns FALSE
T0818 001:047.513 JLINK_HasError()
T0818 001:050.074 JLINK_IsHalted()
T0818 001:050.567 - 0.493ms returns FALSE
T0818 001:050.575 JLINK_HasError()
T0818 001:052.070 JLINK_IsHalted()
T0818 001:052.597 - 0.526ms returns FALSE
T0818 001:052.610 JLINK_HasError()
T0818 001:054.072 JLINK_IsHalted()
T0818 001:054.506 - 0.433ms returns FALSE
T0818 001:054.514 JLINK_HasError()
T0818 001:056.069 JLINK_IsHalted()
T0818 001:056.574 - 0.505ms returns FALSE
T0818 001:056.582 JLINK_HasError()
T0818 001:058.070 JLINK_IsHalted()
T0818 001:058.555 - 0.484ms returns FALSE
T0818 001:058.562 JLINK_HasError()
T0818 001:060.068 JLINK_IsHalted()
T0818 001:060.564 - 0.495ms returns FALSE
T0818 001:060.570 JLINK_HasError()
T0818 001:062.068 JLINK_IsHalted()
T0818 001:062.564 - 0.495ms returns FALSE
T0818 001:062.570 JLINK_HasError()
T0818 001:064.068 JLINK_IsHalted()
T0818 001:064.550 - 0.482ms returns FALSE
T0818 001:064.557 JLINK_HasError()
T0818 001:065.900 JLINK_IsHalted()
T0818 001:066.483 - 0.583ms returns FALSE
T0818 001:066.491 JLINK_HasError()
T0818 001:067.904 JLINK_IsHalted()
T0818 001:068.334 - 0.429ms returns FALSE
T0818 001:068.340 JLINK_HasError()
T0818 001:069.905 JLINK_IsHalted()
T0818 001:070.367 - 0.461ms returns FALSE
T0818 001:070.374 JLINK_HasError()
T0818 001:071.903 JLINK_IsHalted()
T0818 001:072.367 - 0.463ms returns FALSE
T0818 001:072.373 JLINK_HasError()
T0818 001:073.905 JLINK_IsHalted()
T0818 001:074.332 - 0.426ms returns FALSE
T0818 001:074.338 JLINK_HasError()
T0818 001:075.909 JLINK_IsHalted()
T0818 001:076.485 - 0.576ms returns FALSE
T0818 001:076.493 JLINK_HasError()
T0818 001:077.903 JLINK_IsHalted()
T0818 001:078.366 - 0.462ms returns FALSE
T0818 001:078.381 JLINK_HasError()
T0818 001:079.906 JLINK_IsHalted()
T0818 001:080.345 - 0.438ms returns FALSE
T0818 001:080.354 JLINK_HasError()
T0818 001:081.903 JLINK_IsHalted()
T0818 001:082.425 - 0.522ms returns FALSE
T0818 001:082.432 JLINK_HasError()
T0818 001:084.909 JLINK_IsHalted()
T0818 001:085.471 - 0.562ms returns FALSE
T0818 001:085.477 JLINK_HasError()
T0818 001:086.904 JLINK_IsHalted()
T0818 001:087.378 - 0.474ms returns FALSE
T0818 001:087.385 JLINK_HasError()
T0818 001:088.906 JLINK_IsHalted()
T0818 001:089.483 - 0.577ms returns FALSE
T0818 001:089.490 JLINK_HasError()
T0818 001:090.902 JLINK_IsHalted()
T0818 001:091.337 - 0.434ms returns FALSE
T0818 001:091.350 JLINK_HasError()
T0818 001:092.906 JLINK_IsHalted()
T0818 001:093.351 - 0.445ms returns FALSE
T0818 001:093.364 JLINK_HasError()
T0818 001:094.905 JLINK_IsHalted()
T0818 001:095.386 - 0.481ms returns FALSE
T0818 001:095.394 JLINK_HasError()
T0818 001:096.908 JLINK_IsHalted()
T0818 001:097.426 - 0.517ms returns FALSE
T0818 001:097.435 JLINK_HasError()
T0818 001:098.903 JLINK_IsHalted()
T0818 001:099.423 - 0.519ms returns FALSE
T0818 001:099.431 JLINK_HasError()
T0818 001:100.907 JLINK_IsHalted()
T0818 001:101.381 - 0.472ms returns FALSE
T0818 001:101.389 JLINK_HasError()
T0818 001:102.901 JLINK_IsHalted()
T0818 001:103.423 - 0.521ms returns FALSE
T0818 001:103.429 JLINK_HasError()
T0818 001:104.905 JLINK_IsHalted()
T0818 001:105.372 - 0.467ms returns FALSE
T0818 001:105.386 JLINK_HasError()
T0818 001:106.902 JLINK_IsHalted()
T0818 001:107.378 - 0.476ms returns FALSE
T0818 001:107.384 JLINK_HasError()
T0818 001:108.903 JLINK_IsHalted()
T0818 001:109.397 - 0.493ms returns FALSE
T0818 001:109.409 JLINK_HasError()
T0818 001:110.903 JLINK_IsHalted()
T0818 001:111.336 - 0.433ms returns FALSE
T0818 001:111.344 JLINK_HasError()
T0818 001:112.909 JLINK_IsHalted()
T0818 001:113.430 - 0.520ms returns FALSE
T0818 001:113.440 JLINK_HasError()
T0818 001:114.901 JLINK_IsHalted()
T0818 001:115.379 - 0.477ms returns FALSE
T0818 001:115.387 JLINK_HasError()
T0818 001:116.906 JLINK_IsHalted()
T0818 001:117.473 - 0.566ms returns FALSE
T0818 001:117.483 JLINK_HasError()
T0818 001:118.903 JLINK_IsHalted()
T0818 001:119.420 - 0.517ms returns FALSE
T0818 001:119.427 JLINK_HasError()
T0818 001:120.905 JLINK_IsHalted()
T0818 001:121.424 - 0.518ms returns FALSE
T0818 001:121.430 JLINK_HasError()
T0818 001:122.905 JLINK_IsHalted()
T0818 001:123.423 - 0.518ms returns FALSE
T0818 001:123.430 JLINK_HasError()
T0818 001:124.904 JLINK_IsHalted()
T0818 001:125.334 - 0.430ms returns FALSE
T0818 001:125.340 JLINK_HasError()
T0818 001:126.902 JLINK_IsHalted()
T0818 001:127.366 - 0.463ms returns FALSE
T0818 001:127.372 JLINK_HasError()
T0818 001:128.912 JLINK_IsHalted()
T0818 001:129.380 - 0.467ms returns FALSE
T0818 001:129.393 JLINK_HasError()
T0818 001:130.902 JLINK_IsHalted()
T0818 001:131.424 - 0.522ms returns FALSE
T0818 001:131.433 JLINK_HasError()
T0818 001:132.908 JLINK_IsHalted()
T0818 001:133.425 - 0.517ms returns FALSE
T0818 001:133.442 JLINK_HasError()
T0818 001:134.902 JLINK_IsHalted()
T0818 001:135.376 - 0.473ms returns FALSE
T0818 001:135.382 JLINK_HasError()
T0818 001:136.906 JLINK_IsHalted()
T0818 001:137.472 - 0.565ms returns FALSE
T0818 001:137.478 JLINK_HasError()
T0818 001:138.902 JLINK_IsHalted()
T0818 001:139.332 - 0.430ms returns FALSE
T0818 001:139.340 JLINK_HasError()
T0818 001:140.904 JLINK_IsHalted()
T0818 001:141.337 - 0.432ms returns FALSE
T0818 001:141.346 JLINK_HasError()
T0818 001:142.903 JLINK_IsHalted()
T0818 001:143.423 - 0.520ms returns FALSE
T0818 001:143.430 JLINK_HasError()
T0818 001:144.904 JLINK_IsHalted()
T0818 001:145.483 - 0.578ms returns FALSE
T0818 001:145.492 JLINK_HasError()
T0818 001:146.901 JLINK_IsHalted()
T0818 001:147.379 - 0.478ms returns FALSE
T0818 001:147.387 JLINK_HasError()
T0818 001:148.908 JLINK_IsHalted()
T0818 001:149.467 - 0.558ms returns FALSE
T0818 001:149.474 JLINK_HasError()
T0818 001:150.902 JLINK_IsHalted()
T0818 001:151.424 - 0.522ms returns FALSE
T0818 001:151.435 JLINK_HasError()
T0818 001:152.906 JLINK_IsHalted()
T0818 001:153.380 - 0.474ms returns FALSE
T0818 001:153.390 JLINK_HasError()
T0818 001:154.901 JLINK_IsHalted()
T0818 001:155.383 - 0.481ms returns FALSE
T0818 001:155.390 JLINK_HasError()
T0818 001:156.903 JLINK_IsHalted()
T0818 001:157.344 - 0.440ms returns FALSE
T0818 001:157.351 JLINK_HasError()
T0818 001:158.904 JLINK_IsHalted()
T0818 001:159.335 - 0.430ms returns FALSE
T0818 001:159.341 JLINK_HasError()
T0818 001:160.909 JLINK_IsHalted()
T0818 001:161.380 - 0.470ms returns FALSE
T0818 001:161.386 JLINK_HasError()
T0818 001:162.904 JLINK_IsHalted()
T0818 001:163.336 - 0.432ms returns FALSE
T0818 001:163.345 JLINK_HasError()
T0818 001:164.906 JLINK_IsHalted()
T0818 001:165.343 - 0.436ms returns FALSE
T0818 001:165.350 JLINK_HasError()
T0818 001:166.903 JLINK_IsHalted()
T0818 001:167.379 - 0.476ms returns FALSE
T0818 001:167.385 JLINK_HasError()
T0818 001:168.906 JLINK_IsHalted()
T0818 001:169.366 - 0.460ms returns FALSE
T0818 001:169.373 JLINK_HasError()
T0818 001:170.903 JLINK_IsHalted()
T0818 001:171.424 - 0.520ms returns FALSE
T0818 001:171.433 JLINK_HasError()
T0818 001:172.905 JLINK_IsHalted()
T0818 001:173.338 - 0.432ms returns FALSE
T0818 001:173.347 JLINK_HasError()
T0818 001:174.903 JLINK_IsHalted()
T0818 001:175.376 - 0.473ms returns FALSE
T0818 001:175.383 JLINK_HasError()
T0818 001:176.904 JLINK_IsHalted()
T0818 001:177.480 - 0.575ms returns FALSE
T0818 001:177.492 JLINK_HasError()
T0818 001:178.905 JLINK_IsHalted()
T0818 001:179.424 - 0.518ms returns FALSE
T0818 001:179.433 JLINK_HasError()
T0818 001:180.905 JLINK_IsHalted()
T0818 001:181.411 - 0.505ms returns FALSE
T0818 001:181.420 JLINK_HasError()
T0818 001:182.908 JLINK_IsHalted()
T0818 001:183.410 - 0.502ms returns FALSE
T0818 001:183.417 JLINK_HasError()
T0818 001:184.903 JLINK_IsHalted()
T0818 001:185.380 - 0.476ms returns FALSE
T0818 001:185.390 JLINK_HasError()
T0818 001:186.905 JLINK_IsHalted()
T0818 001:187.336 - 0.431ms returns FALSE
T0818 001:187.345 JLINK_HasError()
T0818 001:188.908 JLINK_IsHalted()
T0818 001:189.335 - 0.427ms returns FALSE
T0818 001:189.342 JLINK_HasError()
T0818 001:190.904 JLINK_IsHalted()
T0818 001:191.420 - 0.515ms returns FALSE
T0818 001:191.425 JLINK_HasError()
T0818 001:192.904 JLINK_IsHalted()
T0818 001:193.342 - 0.437ms returns FALSE
T0818 001:193.349 JLINK_HasError()
T0818 001:195.909 JLINK_IsHalted()
T0818 001:196.485 - 0.576ms returns FALSE
T0818 001:196.494 JLINK_HasError()
T0818 001:197.905 JLINK_IsHalted()
T0818 001:198.334 - 0.429ms returns FALSE
T0818 001:198.341 JLINK_HasError()
T0818 001:199.904 JLINK_IsHalted()
T0818 001:200.333 - 0.429ms returns FALSE
T0818 001:200.339 JLINK_HasError()
T0818 001:201.904 JLINK_IsHalted()
T0818 001:202.376 - 0.472ms returns FALSE
T0818 001:202.382 JLINK_HasError()
T0818 001:203.902 JLINK_IsHalted()
T0818 001:204.364 - 0.462ms returns FALSE
T0818 001:204.370 JLINK_HasError()
T0818 001:205.904 JLINK_IsHalted()
T0818 001:206.514 - 0.609ms returns FALSE
T0818 001:206.522 JLINK_HasError()
T0818 001:207.901 JLINK_IsHalted()
T0818 001:208.424 - 0.522ms returns FALSE
T0818 001:208.430 JLINK_HasError()
T0818 001:209.905 JLINK_IsHalted()
T0818 001:210.340 - 0.435ms returns FALSE
T0818 001:210.347 JLINK_HasError()
T0818 001:211.904 JLINK_IsHalted()
T0818 001:212.483 - 0.579ms returns FALSE
T0818 001:212.489 JLINK_HasError()
T0818 001:213.911 JLINK_IsHalted()
T0818 001:214.427 - 0.515ms returns FALSE
T0818 001:214.437 JLINK_HasError()
T0818 001:215.903 JLINK_IsHalted()
T0818 001:216.469 - 0.566ms returns FALSE
T0818 001:216.477 JLINK_HasError()
T0818 001:217.905 JLINK_IsHalted()
T0818 001:218.425 - 0.519ms returns FALSE
T0818 001:218.432 JLINK_HasError()
T0818 001:219.902 JLINK_IsHalted()
T0818 001:220.376 - 0.474ms returns FALSE
T0818 001:220.383 JLINK_HasError()
T0818 001:221.903 JLINK_IsHalted()
T0818 001:222.377 - 0.473ms returns FALSE
T0818 001:222.383 JLINK_HasError()
T0818 001:223.901 JLINK_IsHalted()
T0818 001:224.377 - 0.475ms returns FALSE
T0818 001:224.395 JLINK_HasError()
T0818 001:225.904 JLINK_IsHalted()
T0818 001:226.466 - 0.561ms returns FALSE
T0818 001:226.474 JLINK_HasError()
T0818 001:227.902 JLINK_IsHalted()
T0818 001:228.379 - 0.477ms returns FALSE
T0818 001:228.385 JLINK_HasError()
T0818 001:229.909 JLINK_IsHalted()
T0818 001:230.468 - 0.558ms returns FALSE
T0818 001:230.479 JLINK_HasError()
T0818 001:231.902 JLINK_IsHalted()
T0818 001:232.406 - 0.504ms returns FALSE
T0818 001:232.413 JLINK_HasError()
T0818 001:233.906 JLINK_IsHalted()
T0818 001:234.413 - 0.506ms returns FALSE
T0818 001:234.420 JLINK_HasError()
T0818 001:235.907 JLINK_IsHalted()
T0818 001:236.470 - 0.562ms returns FALSE
T0818 001:236.476 JLINK_HasError()
T0818 001:237.903 JLINK_IsHalted()
T0818 001:238.468 - 0.565ms returns FALSE
T0818 001:238.476 JLINK_HasError()
T0818 001:239.902 JLINK_IsHalted()
T0818 001:240.407 - 0.504ms returns FALSE
T0818 001:240.414 JLINK_HasError()
T0818 001:241.906 JLINK_IsHalted()
T0818 001:242.466 - 0.559ms returns FALSE
T0818 001:242.473 JLINK_HasError()
T0818 001:243.901 JLINK_IsHalted()
T0818 001:244.376 - 0.474ms returns FALSE
T0818 001:244.382 JLINK_HasError()
T0818 001:245.904 JLINK_IsHalted()
T0818 001:246.513 - 0.608ms returns FALSE
T0818 001:246.519 JLINK_HasError()
T0818 001:247.901 JLINK_IsHalted()
T0818 001:248.385 - 0.484ms returns FALSE
T0818 001:248.397 JLINK_HasError()
T0818 001:249.905 JLINK_IsHalted()
T0818 001:250.378 - 0.473ms returns FALSE
T0818 001:250.384 JLINK_HasError()
T0818 001:251.903 JLINK_IsHalted()
T0818 001:252.376 - 0.472ms returns FALSE
T0818 001:252.382 JLINK_HasError()
T0818 001:253.903 JLINK_IsHalted()
T0818 001:254.424 - 0.521ms returns FALSE
T0818 001:254.433 JLINK_HasError()
T0818 001:255.903 JLINK_IsHalted()
T0818 001:256.466 - 0.562ms returns FALSE
T0818 001:256.474 JLINK_HasError()
T0818 001:257.905 JLINK_IsHalted()
T0818 001:258.426 - 0.521ms returns FALSE
T0818 001:258.433 JLINK_HasError()
T0818 001:259.903 JLINK_IsHalted()
T0818 001:260.422 - 0.518ms returns FALSE
T0818 001:260.429 JLINK_HasError()
T0818 001:261.902 JLINK_IsHalted()
T0818 001:262.378 - 0.475ms returns FALSE
T0818 001:262.384 JLINK_HasError()
T0818 001:263.904 JLINK_IsHalted()
T0818 001:264.422 - 0.517ms returns FALSE
T0818 001:264.428 JLINK_HasError()
T0818 001:265.904 JLINK_IsHalted()
T0818 001:266.469 - 0.564ms returns FALSE
T0818 001:266.475 JLINK_HasError()
T0818 001:267.901 JLINK_IsHalted()
T0818 001:268.511 - 0.608ms returns FALSE
T0818 001:268.528 JLINK_HasError()
T0818 001:269.904 JLINK_IsHalted()
T0818 001:270.381 - 0.476ms returns FALSE
T0818 001:270.390 JLINK_HasError()
T0818 001:271.905 JLINK_IsHalted()
T0818 001:272.474 - 0.569ms returns FALSE
T0818 001:272.482 JLINK_HasError()
T0818 001:273.903 JLINK_IsHalted()
T0818 001:274.334 - 0.430ms returns FALSE
T0818 001:274.341 JLINK_HasError()
T0818 001:275.903 JLINK_IsHalted()
T0818 001:276.482 - 0.579ms returns FALSE
T0818 001:276.489 JLINK_HasError()
T0818 001:277.907 JLINK_IsHalted()
T0818 001:278.370 - 0.462ms returns FALSE
T0818 001:278.376 JLINK_HasError()
T0818 001:279.903 JLINK_IsHalted()
T0818 001:280.407 - 0.503ms returns FALSE
T0818 001:280.414 JLINK_HasError()
T0818 001:281.904 JLINK_IsHalted()
T0818 001:282.513 - 0.609ms returns FALSE
T0818 001:282.520 JLINK_HasError()
T0818 001:283.906 JLINK_IsHalted()
T0818 001:284.488 - 0.581ms returns FALSE
T0818 001:284.501 JLINK_HasError()
T0818 001:287.909 JLINK_IsHalted()
T0818 001:288.427 - 0.517ms returns FALSE
T0818 001:288.433 JLINK_HasError()
T0818 001:289.903 JLINK_IsHalted()
T0818 001:290.425 - 0.521ms returns FALSE
T0818 001:290.432 JLINK_HasError()
T0818 001:291.905 JLINK_IsHalted()
T0818 001:292.336 - 0.430ms returns FALSE
T0818 001:292.342 JLINK_HasError()
T0818 001:293.902 JLINK_IsHalted()
T0818 001:294.378 - 0.475ms returns FALSE
T0818 001:294.385 JLINK_HasError()
T0818 001:295.905 JLINK_IsHalted()
T0818 001:296.473 - 0.568ms returns FALSE
T0818 001:296.481 JLINK_HasError()
T0818 001:297.901 JLINK_IsHalted()
T0818 001:298.422 - 0.520ms returns FALSE
T0818 001:298.428 JLINK_HasError()
T0818 001:299.902 JLINK_IsHalted()
T0818 001:300.380 - 0.477ms returns FALSE
T0818 001:300.388 JLINK_HasError()
T0818 001:302.905 JLINK_IsHalted()
T0818 001:303.516 - 0.610ms returns FALSE
T0818 001:303.523 JLINK_HasError()
T0818 001:304.905 JLINK_IsHalted()
T0818 001:305.411 - 0.506ms returns FALSE
T0818 001:305.418 JLINK_HasError()
T0818 001:306.906 JLINK_IsHalted()
T0818 001:307.424 - 0.517ms returns FALSE
T0818 001:307.435 JLINK_HasError()
T0818 001:308.905 JLINK_IsHalted()
T0818 001:309.378 - 0.473ms returns FALSE
T0818 001:309.385 JLINK_HasError()
T0818 001:310.903 JLINK_IsHalted()
T0818 001:311.379 - 0.475ms returns FALSE
T0818 001:311.386 JLINK_HasError()
T0818 001:312.905 JLINK_IsHalted()
T0818 001:313.473 - 0.568ms returns FALSE
T0818 001:313.479 JLINK_HasError()
T0818 001:314.903 JLINK_IsHalted()
T0818 001:315.381 - 0.478ms returns FALSE
T0818 001:315.391 JLINK_HasError()
T0818 001:316.904 JLINK_IsHalted()
T0818 001:317.335 - 0.431ms returns FALSE
T0818 001:317.343 JLINK_HasError()
T0818 001:318.904 JLINK_IsHalted()
T0818 001:319.334 - 0.429ms returns FALSE
T0818 001:319.341 JLINK_HasError()
T0818 001:320.903 JLINK_IsHalted()
T0818 001:321.340 - 0.436ms returns FALSE
T0818 001:321.346 JLINK_HasError()
T0818 001:322.902 JLINK_IsHalted()
T0818 001:323.382 - 0.479ms returns FALSE
T0818 001:323.390 JLINK_HasError()
T0818 001:324.905 JLINK_IsHalted()
T0818 001:325.423 - 0.518ms returns FALSE
T0818 001:325.429 JLINK_HasError()
T0818 001:326.902 JLINK_IsHalted()
T0818 001:327.424 - 0.521ms returns FALSE
T0818 001:327.431 JLINK_HasError()
T0818 001:328.905 JLINK_IsHalted()
T0818 001:329.378 - 0.472ms returns FALSE
T0818 001:329.384 JLINK_HasError()
T0818 001:330.910 JLINK_IsHalted()
T0818 001:331.366 - 0.455ms returns FALSE
T0818 001:331.376 JLINK_HasError()
T0818 001:332.908 JLINK_IsHalted()
T0818 001:333.366 - 0.458ms returns FALSE
T0818 001:333.381 JLINK_HasError()
T0818 001:334.902 JLINK_IsHalted()
T0818 001:335.380 - 0.477ms returns FALSE
T0818 001:335.387 JLINK_HasError()
T0818 001:336.906 JLINK_IsHalted()
T0818 001:337.470 - 0.563ms returns FALSE
T0818 001:337.477 JLINK_HasError()
T0818 001:338.902 JLINK_IsHalted()
T0818 001:339.424 - 0.521ms returns FALSE
T0818 001:339.429 JLINK_HasError()
T0818 001:340.905 JLINK_IsHalted()
T0818 001:341.333 - 0.428ms returns FALSE
T0818 001:341.339 JLINK_HasError()
T0818 001:342.902 JLINK_IsHalted()
T0818 001:343.376 - 0.474ms returns FALSE
T0818 001:343.382 JLINK_HasError()
T0818 001:344.908 JLINK_IsHalted()
T0818 001:345.474 - 0.565ms returns FALSE
T0818 001:345.480 JLINK_HasError()
T0818 001:346.901 JLINK_IsHalted()
T0818 001:347.517 - 0.615ms returns FALSE
T0818 001:347.537 JLINK_HasError()
T0818 001:348.906 JLINK_IsHalted()
T0818 001:349.653 - 0.746ms returns FALSE
T0818 001:349.659 JLINK_HasError()
T0818 001:350.901 JLINK_IsHalted()
T0818 001:351.375 - 0.473ms returns FALSE
T0818 001:351.380 JLINK_HasError()
T0818 001:352.899 JLINK_IsHalted()
T0818 001:353.375 - 0.475ms returns FALSE
T0818 001:353.381 JLINK_HasError()
T0818 001:354.899 JLINK_IsHalted()
T0818 001:355.375 - 0.475ms returns FALSE
T0818 001:355.380 JLINK_HasError()
T0818 001:356.899 JLINK_IsHalted()
T0818 001:357.375 - 0.475ms returns FALSE
T0818 001:357.380 JLINK_HasError()
T0818 001:358.899 JLINK_IsHalted()
T0818 001:359.383 - 0.484ms returns FALSE
T0818 001:359.389 JLINK_HasError()
T0818 001:360.899 JLINK_IsHalted()
T0818 001:361.343 - 0.443ms returns FALSE
T0818 001:361.354 JLINK_HasError()
T0818 001:362.903 JLINK_IsHalted()
T0818 001:363.468 - 0.565ms returns FALSE
T0818 001:363.478 JLINK_HasError()
T0818 001:365.901 JLINK_IsHalted()
T0818 001:366.512 - 0.611ms returns FALSE
T0818 001:366.519 JLINK_HasError()
T0818 001:367.899 JLINK_IsHalted()
T0818 001:368.405 - 0.506ms returns FALSE
T0818 001:368.411 JLINK_HasError()
T0818 001:369.899 JLINK_IsHalted()
T0818 001:370.376 - 0.477ms returns FALSE
T0818 001:370.382 JLINK_HasError()
T0818 001:371.899 JLINK_IsHalted()
T0818 001:372.363 - 0.463ms returns FALSE
T0818 001:372.369 JLINK_HasError()
T0818 001:373.899 JLINK_IsHalted()
T0818 001:374.376 - 0.476ms returns FALSE
T0818 001:374.381 JLINK_HasError()
T0818 001:375.899 JLINK_IsHalted()
T0818 001:376.481 - 0.581ms returns FALSE
T0818 001:376.487 JLINK_HasError()
T0818 001:377.901 JLINK_IsHalted()
T0818 001:378.511 - 0.609ms returns FALSE
T0818 001:378.518 JLINK_HasError()
T0818 001:379.899 JLINK_IsHalted()
T0818 001:380.376 - 0.476ms returns FALSE
T0818 001:380.381 JLINK_HasError()
T0818 001:381.899 JLINK_IsHalted()
T0818 001:382.375 - 0.475ms returns FALSE
T0818 001:382.380 JLINK_HasError()
T0818 001:383.903 JLINK_IsHalted()
T0818 001:384.438 - 0.534ms returns FALSE
T0818 001:384.444 JLINK_HasError()
T0818 001:385.899 JLINK_IsHalted()
T0818 001:386.569 - 0.669ms returns FALSE
T0818 001:386.576 JLINK_HasError()
T0818 001:387.900 JLINK_IsHalted()
T0818 001:388.428 - 0.528ms returns FALSE
T0818 001:388.434 JLINK_HasError()
T0818 001:389.899 JLINK_IsHalted()
T0818 001:392.185   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:392.650 - 2.750ms returns TRUE
T0818 001:392.656 JLINK_ReadReg(R15 (PC))
T0818 001:392.661 - 0.004ms returns 0x20000000
T0818 001:392.665 JLINK_ClrBPEx(BPHandle = 0x00000005)
T0818 001:392.669 - 0.003ms returns 0x00
T0818 001:392.673 JLINK_ReadReg(R0)
T0818 001:392.682 - 0.008ms returns 0x00000000
T0818 001:393.033 JLINK_HasError()
T0818 001:393.041 JLINK_WriteReg(R0, 0x08008000)
T0818 001:393.045 - 0.004ms returns 0
T0818 001:393.049 JLINK_WriteReg(R1, 0x00004000)
T0818 001:393.053 - 0.003ms returns 0
T0818 001:393.057 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:393.060 - 0.003ms returns 0
T0818 001:393.064 JLINK_WriteReg(R3, 0x00000000)
T0818 001:393.068 - 0.003ms returns 0
T0818 001:393.072 JLINK_WriteReg(R4, 0x00000000)
T0818 001:393.075 - 0.003ms returns 0
T0818 001:393.079 JLINK_WriteReg(R5, 0x00000000)
T0818 001:393.083 - 0.003ms returns 0
T0818 001:393.087 JLINK_WriteReg(R6, 0x00000000)
T0818 001:393.090 - 0.003ms returns 0
T0818 001:393.094 JLINK_WriteReg(R7, 0x00000000)
T0818 001:393.098 - 0.003ms returns 0
T0818 001:393.102 JLINK_WriteReg(R8, 0x00000000)
T0818 001:393.105 - 0.003ms returns 0
T0818 001:393.109 JLINK_WriteReg(R9, 0x20000180)
T0818 001:393.112 - 0.003ms returns 0
T0818 001:393.117 JLINK_WriteReg(R10, 0x00000000)
T0818 001:393.120 - 0.003ms returns 0
T0818 001:393.124 JLINK_WriteReg(R11, 0x00000000)
T0818 001:393.127 - 0.003ms returns 0
T0818 001:393.131 JLINK_WriteReg(R12, 0x00000000)
T0818 001:393.135 - 0.003ms returns 0
T0818 001:393.139 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:393.142 - 0.003ms returns 0
T0818 001:393.146 JLINK_WriteReg(R14, 0x20000001)
T0818 001:393.150 - 0.003ms returns 0
T0818 001:393.154 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 001:393.157 - 0.003ms returns 0
T0818 001:393.161 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:393.165 - 0.003ms returns 0
T0818 001:393.169 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:393.172 - 0.003ms returns 0
T0818 001:393.176 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:393.180 - 0.003ms returns 0
T0818 001:393.184 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:393.187 - 0.003ms returns 0
T0818 001:393.192 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:393.196 - 0.004ms returns 0x00000006
T0818 001:393.200 JLINK_Go()
T0818 001:393.208   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:395.958 - 2.757ms 
T0818 001:395.966 JLINK_IsHalted()
T0818 001:398.347   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:398.843 - 2.877ms returns TRUE
T0818 001:398.850 JLINK_ReadReg(R15 (PC))
T0818 001:398.854 - 0.004ms returns 0x20000000
T0818 001:398.858 JLINK_ClrBPEx(BPHandle = 0x00000006)
T0818 001:398.862 - 0.003ms returns 0x00
T0818 001:398.866 JLINK_ReadReg(R0)
T0818 001:398.870 - 0.003ms returns 0x00000001
T0818 001:398.874 JLINK_HasError()
T0818 001:398.879 JLINK_WriteReg(R0, 0x08008000)
T0818 001:398.883 - 0.003ms returns 0
T0818 001:398.887 JLINK_WriteReg(R1, 0x00004000)
T0818 001:398.891 - 0.003ms returns 0
T0818 001:398.895 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:398.898 - 0.003ms returns 0
T0818 001:398.902 JLINK_WriteReg(R3, 0x00000000)
T0818 001:398.906 - 0.003ms returns 0
T0818 001:398.910 JLINK_WriteReg(R4, 0x00000000)
T0818 001:398.913 - 0.003ms returns 0
T0818 001:398.917 JLINK_WriteReg(R5, 0x00000000)
T0818 001:398.920 - 0.003ms returns 0
T0818 001:398.924 JLINK_WriteReg(R6, 0x00000000)
T0818 001:398.928 - 0.003ms returns 0
T0818 001:398.932 JLINK_WriteReg(R7, 0x00000000)
T0818 001:398.935 - 0.003ms returns 0
T0818 001:398.939 JLINK_WriteReg(R8, 0x00000000)
T0818 001:398.942 - 0.003ms returns 0
T0818 001:398.946 JLINK_WriteReg(R9, 0x20000180)
T0818 001:398.950 - 0.003ms returns 0
T0818 001:398.954 JLINK_WriteReg(R10, 0x00000000)
T0818 001:398.957 - 0.003ms returns 0
T0818 001:398.961 JLINK_WriteReg(R11, 0x00000000)
T0818 001:398.965 - 0.003ms returns 0
T0818 001:398.969 JLINK_WriteReg(R12, 0x00000000)
T0818 001:398.972 - 0.003ms returns 0
T0818 001:398.976 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:398.980 - 0.003ms returns 0
T0818 001:398.984 JLINK_WriteReg(R14, 0x20000001)
T0818 001:398.987 - 0.003ms returns 0
T0818 001:398.991 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 001:398.995 - 0.003ms returns 0
T0818 001:398.999 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:399.002 - 0.003ms returns 0
T0818 001:399.010 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:399.013 - 0.003ms returns 0
T0818 001:399.017 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:399.020 - 0.003ms returns 0
T0818 001:399.025 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:399.028 - 0.003ms returns 0
T0818 001:399.032 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:399.036 - 0.003ms returns 0x00000007
T0818 001:399.040 JLINK_Go()
T0818 001:399.046   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:401.795 - 2.755ms 
T0818 001:401.801 JLINK_IsHalted()
T0818 001:402.243 - 0.442ms returns FALSE
T0818 001:402.249 JLINK_HasError()
T0818 001:403.900 JLINK_IsHalted()
T0818 001:404.368 - 0.468ms returns FALSE
T0818 001:404.374 JLINK_HasError()
T0818 001:405.899 JLINK_IsHalted()
T0818 001:406.590 - 0.690ms returns FALSE
T0818 001:406.606 JLINK_HasError()
T0818 001:407.900 JLINK_IsHalted()
T0818 001:408.385 - 0.485ms returns FALSE
T0818 001:408.390 JLINK_HasError()
T0818 001:409.899 JLINK_IsHalted()
T0818 001:410.512 - 0.611ms returns FALSE
T0818 001:410.522 JLINK_HasError()
T0818 001:412.901 JLINK_IsHalted()
T0818 001:413.421 - 0.520ms returns FALSE
T0818 001:413.427 JLINK_HasError()
T0818 001:414.902 JLINK_IsHalted()
T0818 001:415.420 - 0.517ms returns FALSE
T0818 001:415.425 JLINK_HasError()
T0818 001:416.900 JLINK_IsHalted()
T0818 001:417.407 - 0.507ms returns FALSE
T0818 001:417.413 JLINK_HasError()
T0818 001:418.899 JLINK_IsHalted()
T0818 001:419.368 - 0.468ms returns FALSE
T0818 001:419.374 JLINK_HasError()
T0818 001:420.900 JLINK_IsHalted()
T0818 001:421.502 - 0.601ms returns FALSE
T0818 001:421.508 JLINK_HasError()
T0818 001:422.904 JLINK_IsHalted()
T0818 001:423.583 - 0.678ms returns FALSE
T0818 001:423.589 JLINK_HasError()
T0818 001:424.900 JLINK_IsHalted()
T0818 001:425.651 - 0.751ms returns FALSE
T0818 001:425.661 JLINK_HasError()
T0818 001:426.900 JLINK_IsHalted()
T0818 001:427.407 - 0.506ms returns FALSE
T0818 001:427.413 JLINK_HasError()
T0818 001:428.912 JLINK_IsHalted()
T0818 001:429.511 - 0.598ms returns FALSE
T0818 001:429.516 JLINK_HasError()
T0818 001:430.899 JLINK_IsHalted()
T0818 001:431.375 - 0.475ms returns FALSE
T0818 001:431.381 JLINK_HasError()
T0818 001:432.899 JLINK_IsHalted()
T0818 001:433.375 - 0.475ms returns FALSE
T0818 001:433.381 JLINK_HasError()
T0818 001:434.900 JLINK_IsHalted()
T0818 001:435.422 - 0.521ms returns FALSE
T0818 001:435.427 JLINK_HasError()
T0818 001:436.900 JLINK_IsHalted()
T0818 001:437.374 - 0.474ms returns FALSE
T0818 001:437.380 JLINK_HasError()
T0818 001:438.899 JLINK_IsHalted()
T0818 001:439.367 - 0.467ms returns FALSE
T0818 001:439.372 JLINK_HasError()
T0818 001:440.901 JLINK_IsHalted()
T0818 001:441.376 - 0.475ms returns FALSE
T0818 001:441.382 JLINK_HasError()
T0818 001:442.899 JLINK_IsHalted()
T0818 001:443.368 - 0.468ms returns FALSE
T0818 001:443.374 JLINK_HasError()
T0818 001:444.899 JLINK_IsHalted()
T0818 001:445.371 - 0.471ms returns FALSE
T0818 001:445.377 JLINK_HasError()
T0818 001:446.900 JLINK_IsHalted()
T0818 001:447.407 - 0.506ms returns FALSE
T0818 001:447.413 JLINK_HasError()
T0818 001:448.899 JLINK_IsHalted()
T0818 001:449.454 - 0.554ms returns FALSE
T0818 001:449.460 JLINK_HasError()
T0818 001:450.899 JLINK_IsHalted()
T0818 001:451.426 - 0.526ms returns FALSE
T0818 001:451.432 JLINK_HasError()
T0818 001:452.899 JLINK_IsHalted()
T0818 001:453.375 - 0.475ms returns FALSE
T0818 001:453.381 JLINK_HasError()
T0818 001:454.899 JLINK_IsHalted()
T0818 001:455.363 - 0.464ms returns FALSE
T0818 001:455.369 JLINK_HasError()
T0818 001:456.903 JLINK_IsHalted()
T0818 001:457.467 - 0.564ms returns FALSE
T0818 001:457.473 JLINK_HasError()
T0818 001:459.900 JLINK_IsHalted()
T0818 001:460.377 - 0.476ms returns FALSE
T0818 001:460.383 JLINK_HasError()
T0818 001:461.899 JLINK_IsHalted()
T0818 001:462.421 - 0.522ms returns FALSE
T0818 001:462.427 JLINK_HasError()
T0818 001:463.899 JLINK_IsHalted()
T0818 001:464.405 - 0.505ms returns FALSE
T0818 001:464.411 JLINK_HasError()
T0818 001:466.363 JLINK_IsHalted()
T0818 001:468.155 - 1.792ms returns FALSE
T0818 001:468.166 JLINK_HasError()
T0818 001:469.899 JLINK_IsHalted()
T0818 001:470.375 - 0.475ms returns FALSE
T0818 001:470.381 JLINK_HasError()
T0818 001:471.900 JLINK_IsHalted()
T0818 001:472.655 - 0.755ms returns FALSE
T0818 001:472.667 JLINK_HasError()
T0818 001:473.902 JLINK_IsHalted()
T0818 001:474.511 - 0.608ms returns FALSE
T0818 001:474.517 JLINK_HasError()
T0818 001:475.899 JLINK_IsHalted()
T0818 001:476.614 - 0.714ms returns FALSE
T0818 001:476.622 JLINK_HasError()
T0818 001:478.154 JLINK_IsHalted()
T0818 001:478.651 - 0.496ms returns FALSE
T0818 001:478.657 JLINK_HasError()
T0818 001:479.899 JLINK_IsHalted()
T0818 001:480.375 - 0.476ms returns FALSE
T0818 001:480.381 JLINK_HasError()
T0818 001:481.457 JLINK_IsHalted()
T0818 001:481.922 - 0.465ms returns FALSE
T0818 001:481.928 JLINK_HasError()
T0818 001:483.456 JLINK_IsHalted()
T0818 001:483.966 - 0.509ms returns FALSE
T0818 001:483.971 JLINK_HasError()
T0818 001:485.456 JLINK_IsHalted()
T0818 001:485.927 - 0.471ms returns FALSE
T0818 001:485.933 JLINK_HasError()
T0818 001:487.457 JLINK_IsHalted()
T0818 001:487.982 - 0.524ms returns FALSE
T0818 001:487.990 JLINK_HasError()
T0818 001:489.462 JLINK_IsHalted()
T0818 001:490.005 - 0.543ms returns FALSE
T0818 001:490.018 JLINK_HasError()
T0818 001:491.461 JLINK_IsHalted()
T0818 001:491.958 - 0.497ms returns FALSE
T0818 001:491.966 JLINK_HasError()
T0818 001:493.456 JLINK_IsHalted()
T0818 001:493.964 - 0.508ms returns FALSE
T0818 001:493.970 JLINK_HasError()
T0818 001:495.456 JLINK_IsHalted()
T0818 001:495.926 - 0.469ms returns FALSE
T0818 001:495.931 JLINK_HasError()
T0818 001:497.464 JLINK_IsHalted()
T0818 001:497.937 - 0.473ms returns FALSE
T0818 001:497.944 JLINK_HasError()
T0818 001:499.463 JLINK_IsHalted()
T0818 001:499.968 - 0.504ms returns FALSE
T0818 001:499.976 JLINK_HasError()
T0818 001:501.459 JLINK_IsHalted()
T0818 001:501.959 - 0.500ms returns FALSE
T0818 001:501.965 JLINK_HasError()
T0818 001:503.456 JLINK_IsHalted()
T0818 001:503.991 - 0.534ms returns FALSE
T0818 001:504.001 JLINK_HasError()
T0818 001:505.457 JLINK_IsHalted()
T0818 001:505.900 - 0.442ms returns FALSE
T0818 001:505.906 JLINK_HasError()
T0818 001:507.456 JLINK_IsHalted()
T0818 001:507.944 - 0.487ms returns FALSE
T0818 001:507.949 JLINK_HasError()
T0818 001:509.456 JLINK_IsHalted()
T0818 001:509.921 - 0.464ms returns FALSE
T0818 001:509.926 JLINK_HasError()
T0818 001:511.456 JLINK_IsHalted()
T0818 001:511.967 - 0.510ms returns FALSE
T0818 001:511.973 JLINK_HasError()
T0818 001:513.456 JLINK_IsHalted()
T0818 001:513.966 - 0.509ms returns FALSE
T0818 001:513.971 JLINK_HasError()
T0818 001:515.456 JLINK_IsHalted()
T0818 001:515.927 - 0.471ms returns FALSE
T0818 001:515.933 JLINK_HasError()
T0818 001:517.456 JLINK_IsHalted()
T0818 001:517.927 - 0.471ms returns FALSE
T0818 001:517.932 JLINK_HasError()
T0818 001:519.456 JLINK_IsHalted()
T0818 001:519.924 - 0.467ms returns FALSE
T0818 001:519.937 JLINK_HasError()
T0818 001:522.458 JLINK_IsHalted()
T0818 001:522.958 - 0.500ms returns FALSE
T0818 001:522.964 JLINK_HasError()
T0818 001:524.456 JLINK_IsHalted()
T0818 001:524.927 - 0.470ms returns FALSE
T0818 001:524.932 JLINK_HasError()
T0818 001:526.457 JLINK_IsHalted()
T0818 001:526.956 - 0.499ms returns FALSE
T0818 001:526.962 JLINK_HasError()
T0818 001:528.456 JLINK_IsHalted()
T0818 001:528.921 - 0.464ms returns FALSE
T0818 001:528.927 JLINK_HasError()
T0818 001:530.456 JLINK_IsHalted()
T0818 001:530.926 - 0.470ms returns FALSE
T0818 001:530.932 JLINK_HasError()
T0818 001:532.459 JLINK_IsHalted()
T0818 001:532.937 - 0.477ms returns FALSE
T0818 001:532.942 JLINK_HasError()
T0818 001:534.456 JLINK_IsHalted()
T0818 001:534.927 - 0.471ms returns FALSE
T0818 001:534.933 JLINK_HasError()
T0818 001:536.458 JLINK_IsHalted()
T0818 001:536.936 - 0.477ms returns FALSE
T0818 001:536.942 JLINK_HasError()
T0818 001:538.457 JLINK_IsHalted()
T0818 001:538.923 - 0.466ms returns FALSE
T0818 001:538.934 JLINK_HasError()
T0818 001:540.456 JLINK_IsHalted()
T0818 001:540.964 - 0.507ms returns FALSE
T0818 001:540.969 JLINK_HasError()
T0818 001:542.456 JLINK_IsHalted()
T0818 001:542.928 - 0.471ms returns FALSE
T0818 001:542.934 JLINK_HasError()
T0818 001:544.456 JLINK_IsHalted()
T0818 001:544.926 - 0.470ms returns FALSE
T0818 001:544.932 JLINK_HasError()
T0818 001:546.457 JLINK_IsHalted()
T0818 001:546.921 - 0.464ms returns FALSE
T0818 001:546.927 JLINK_HasError()
T0818 001:548.457 JLINK_IsHalted()
T0818 001:548.966 - 0.509ms returns FALSE
T0818 001:548.973 JLINK_HasError()
T0818 001:550.456 JLINK_IsHalted()
T0818 001:550.927 - 0.470ms returns FALSE
T0818 001:550.933 JLINK_HasError()
T0818 001:552.458 JLINK_IsHalted()
T0818 001:552.981 - 0.522ms returns FALSE
T0818 001:552.987 JLINK_HasError()
T0818 001:554.456 JLINK_IsHalted()
T0818 001:554.927 - 0.470ms returns FALSE
T0818 001:554.932 JLINK_HasError()
T0818 001:556.458 JLINK_IsHalted()
T0818 001:556.966 - 0.508ms returns FALSE
T0818 001:556.972 JLINK_HasError()
T0818 001:558.456 JLINK_IsHalted()
T0818 001:558.923 - 0.466ms returns FALSE
T0818 001:558.928 JLINK_HasError()
T0818 001:560.456 JLINK_IsHalted()
T0818 001:560.927 - 0.470ms returns FALSE
T0818 001:560.932 JLINK_HasError()
T0818 001:562.456 JLINK_IsHalted()
T0818 001:562.928 - 0.472ms returns FALSE
T0818 001:562.934 JLINK_HasError()
T0818 001:564.476 JLINK_IsHalted()
T0818 001:565.084 - 0.608ms returns FALSE
T0818 001:565.092 JLINK_HasError()
T0818 001:567.458 JLINK_IsHalted()
T0818 001:568.092 - 0.633ms returns FALSE
T0818 001:568.102 JLINK_HasError()
T0818 001:569.456 JLINK_IsHalted()
T0818 001:569.928 - 0.471ms returns FALSE
T0818 001:569.934 JLINK_HasError()
T0818 001:571.456 JLINK_IsHalted()
T0818 001:571.991 - 0.534ms returns FALSE
T0818 001:571.996 JLINK_HasError()
T0818 001:573.456 JLINK_IsHalted()
T0818 001:573.922 - 0.466ms returns FALSE
T0818 001:573.928 JLINK_HasError()
T0818 001:575.456 JLINK_IsHalted()
T0818 001:575.920 - 0.463ms returns FALSE
T0818 001:575.925 JLINK_HasError()
T0818 001:577.462 JLINK_IsHalted()
T0818 001:578.884 - 1.421ms returns FALSE
T0818 001:578.890 JLINK_HasError()
T0818 001:580.456 JLINK_IsHalted()
T0818 001:580.922 - 0.466ms returns FALSE
T0818 001:580.928 JLINK_HasError()
T0818 001:582.456 JLINK_IsHalted()
T0818 001:582.921 - 0.464ms returns FALSE
T0818 001:582.926 JLINK_HasError()
T0818 001:584.458 JLINK_IsHalted()
T0818 001:584.957 - 0.499ms returns FALSE
T0818 001:584.964 JLINK_HasError()
T0818 001:586.457 JLINK_IsHalted()
T0818 001:586.944 - 0.487ms returns FALSE
T0818 001:586.950 JLINK_HasError()
T0818 001:588.457 JLINK_IsHalted()
T0818 001:588.936 - 0.479ms returns FALSE
T0818 001:588.945 JLINK_HasError()
T0818 001:590.456 JLINK_IsHalted()
T0818 001:590.922 - 0.465ms returns FALSE
T0818 001:590.928 JLINK_HasError()
T0818 001:592.456 JLINK_IsHalted()
T0818 001:592.921 - 0.465ms returns FALSE
T0818 001:592.927 JLINK_HasError()
T0818 001:594.456 JLINK_IsHalted()
T0818 001:594.937 - 0.480ms returns FALSE
T0818 001:594.943 JLINK_HasError()
T0818 001:596.456 JLINK_IsHalted()
T0818 001:596.966 - 0.510ms returns FALSE
T0818 001:596.972 JLINK_HasError()
T0818 001:598.456 JLINK_IsHalted()
T0818 001:598.921 - 0.465ms returns FALSE
T0818 001:598.927 JLINK_HasError()
T0818 001:600.457 JLINK_IsHalted()
T0818 001:600.922 - 0.465ms returns FALSE
T0818 001:600.928 JLINK_HasError()
T0818 001:602.458 JLINK_IsHalted()
T0818 001:602.956 - 0.497ms returns FALSE
T0818 001:602.962 JLINK_HasError()
T0818 001:604.456 JLINK_IsHalted()
T0818 001:604.927 - 0.471ms returns FALSE
T0818 001:604.933 JLINK_HasError()
T0818 001:606.456 JLINK_IsHalted()
T0818 001:606.926 - 0.469ms returns FALSE
T0818 001:606.932 JLINK_HasError()
T0818 001:608.456 JLINK_IsHalted()
T0818 001:608.922 - 0.465ms returns FALSE
T0818 001:608.927 JLINK_HasError()
T0818 001:610.457 JLINK_IsHalted()
T0818 001:611.025 - 0.568ms returns FALSE
T0818 001:611.031 JLINK_HasError()
T0818 001:612.456 JLINK_IsHalted()
T0818 001:612.964 - 0.508ms returns FALSE
T0818 001:612.971 JLINK_HasError()
T0818 001:614.458 JLINK_IsHalted()
T0818 001:614.928 - 0.470ms returns FALSE
T0818 001:614.937 JLINK_HasError()
T0818 001:616.458 JLINK_IsHalted()
T0818 001:616.980 - 0.521ms returns FALSE
T0818 001:616.986 JLINK_HasError()
T0818 001:618.456 JLINK_IsHalted()
T0818 001:618.920 - 0.464ms returns FALSE
T0818 001:618.926 JLINK_HasError()
T0818 001:620.458 JLINK_IsHalted()
T0818 001:620.937 - 0.478ms returns FALSE
T0818 001:620.943 JLINK_HasError()
T0818 001:622.456 JLINK_IsHalted()
T0818 001:622.928 - 0.471ms returns FALSE
T0818 001:622.933 JLINK_HasError()
T0818 001:624.456 JLINK_IsHalted()
T0818 001:624.928 - 0.471ms returns FALSE
T0818 001:624.933 JLINK_HasError()
T0818 001:626.457 JLINK_IsHalted()
T0818 001:626.926 - 0.469ms returns FALSE
T0818 001:626.932 JLINK_HasError()
T0818 001:628.458 JLINK_IsHalted()
T0818 001:628.937 - 0.479ms returns FALSE
T0818 001:628.943 JLINK_HasError()
T0818 001:630.456 JLINK_IsHalted()
T0818 001:630.923 - 0.466ms returns FALSE
T0818 001:630.928 JLINK_HasError()
T0818 001:632.458 JLINK_IsHalted()
T0818 001:632.956 - 0.498ms returns FALSE
T0818 001:632.962 JLINK_HasError()
T0818 001:635.457 JLINK_IsHalted()
T0818 001:635.911 - 0.454ms returns FALSE
T0818 001:635.917 JLINK_HasError()
T0818 001:637.456 JLINK_IsHalted()
T0818 001:637.921 - 0.465ms returns FALSE
T0818 001:637.927 JLINK_HasError()
T0818 001:639.457 JLINK_IsHalted()
T0818 001:639.936 - 0.478ms returns FALSE
T0818 001:639.945 JLINK_HasError()
T0818 001:641.456 JLINK_IsHalted()
T0818 001:641.928 - 0.471ms returns FALSE
T0818 001:641.933 JLINK_HasError()
T0818 001:643.459 JLINK_IsHalted()
T0818 001:643.927 - 0.467ms returns FALSE
T0818 001:643.932 JLINK_HasError()
T0818 001:645.456 JLINK_IsHalted()
T0818 001:645.922 - 0.466ms returns FALSE
T0818 001:645.928 JLINK_HasError()
T0818 001:647.465 JLINK_IsHalted()
T0818 001:647.957 - 0.491ms returns FALSE
T0818 001:647.963 JLINK_HasError()
T0818 001:649.458 JLINK_IsHalted()
T0818 001:649.944 - 0.486ms returns FALSE
T0818 001:649.951 JLINK_HasError()
T0818 001:651.456 JLINK_IsHalted()
T0818 001:651.938 - 0.481ms returns FALSE
T0818 001:651.944 JLINK_HasError()
T0818 001:653.456 JLINK_IsHalted()
T0818 001:653.926 - 0.469ms returns FALSE
T0818 001:653.931 JLINK_HasError()
T0818 001:655.456 JLINK_IsHalted()
T0818 001:655.922 - 0.466ms returns FALSE
T0818 001:655.928 JLINK_HasError()
T0818 001:657.457 JLINK_IsHalted()
T0818 001:657.957 - 0.499ms returns FALSE
T0818 001:657.963 JLINK_HasError()
T0818 001:659.456 JLINK_IsHalted()
T0818 001:659.926 - 0.470ms returns FALSE
T0818 001:659.932 JLINK_HasError()
T0818 001:661.456 JLINK_IsHalted()
T0818 001:661.988 - 0.531ms returns FALSE
T0818 001:661.996 JLINK_HasError()
T0818 001:663.456 JLINK_IsHalted()
T0818 001:663.921 - 0.464ms returns FALSE
T0818 001:663.927 JLINK_HasError()
T0818 001:665.457 JLINK_IsHalted()
T0818 001:665.922 - 0.465ms returns FALSE
T0818 001:665.931 JLINK_HasError()
T0818 001:667.456 JLINK_IsHalted()
T0818 001:667.964 - 0.507ms returns FALSE
T0818 001:667.970 JLINK_HasError()
T0818 001:669.456 JLINK_IsHalted()
T0818 001:669.936 - 0.479ms returns FALSE
T0818 001:669.941 JLINK_HasError()
T0818 001:671.456 JLINK_IsHalted()
T0818 001:671.928 - 0.471ms returns FALSE
T0818 001:671.933 JLINK_HasError()
T0818 001:673.456 JLINK_IsHalted()
T0818 001:673.927 - 0.471ms returns FALSE
T0818 001:673.933 JLINK_HasError()
T0818 001:675.456 JLINK_IsHalted()
T0818 001:675.921 - 0.464ms returns FALSE
T0818 001:675.926 JLINK_HasError()
T0818 001:677.473 JLINK_IsHalted()
T0818 001:678.075 - 0.601ms returns FALSE
T0818 001:678.086 JLINK_HasError()
T0818 001:679.457 JLINK_IsHalted()
T0818 001:679.936 - 0.479ms returns FALSE
T0818 001:679.942 JLINK_HasError()
T0818 001:681.457 JLINK_IsHalted()
T0818 001:681.928 - 0.471ms returns FALSE
T0818 001:681.934 JLINK_HasError()
T0818 001:683.456 JLINK_IsHalted()
T0818 001:683.921 - 0.465ms returns FALSE
T0818 001:683.927 JLINK_HasError()
T0818 001:685.456 JLINK_IsHalted()
T0818 001:685.922 - 0.465ms returns FALSE
T0818 001:685.927 JLINK_HasError()
T0818 001:687.457 JLINK_IsHalted()
T0818 001:687.964 - 0.507ms returns FALSE
T0818 001:687.971 JLINK_HasError()
T0818 001:689.456 JLINK_IsHalted()
T0818 001:689.928 - 0.471ms returns FALSE
T0818 001:689.933 JLINK_HasError()
T0818 001:691.457 JLINK_IsHalted()
T0818 001:691.944 - 0.487ms returns FALSE
T0818 001:691.954 JLINK_HasError()
T0818 001:693.456 JLINK_IsHalted()
T0818 001:693.967 - 0.510ms returns FALSE
T0818 001:693.979 JLINK_HasError()
T0818 001:695.457 JLINK_IsHalted()
T0818 001:695.958 - 0.500ms returns FALSE
T0818 001:695.964 JLINK_HasError()
T0818 001:697.459 JLINK_IsHalted()
T0818 001:697.957 - 0.497ms returns FALSE
T0818 001:697.963 JLINK_HasError()
T0818 001:699.456 JLINK_IsHalted()
T0818 001:699.942 - 0.485ms returns FALSE
T0818 001:699.948 JLINK_HasError()
T0818 001:701.458 JLINK_IsHalted()
T0818 001:701.928 - 0.470ms returns FALSE
T0818 001:701.934 JLINK_HasError()
T0818 001:703.457 JLINK_IsHalted()
T0818 001:703.936 - 0.478ms returns FALSE
T0818 001:703.941 JLINK_HasError()
T0818 001:705.456 JLINK_IsHalted()
T0818 001:705.922 - 0.466ms returns FALSE
T0818 001:705.928 JLINK_HasError()
T0818 001:707.461 JLINK_IsHalted()
T0818 001:707.902 - 0.441ms returns FALSE
T0818 001:707.908 JLINK_HasError()
T0818 001:709.460 JLINK_IsHalted()
T0818 001:709.938 - 0.478ms returns FALSE
T0818 001:709.946 JLINK_HasError()
T0818 001:711.456 JLINK_IsHalted()
T0818 001:711.929 - 0.472ms returns FALSE
T0818 001:711.935 JLINK_HasError()
T0818 001:713.456 JLINK_IsHalted()
T0818 001:713.920 - 0.464ms returns FALSE
T0818 001:713.926 JLINK_HasError()
T0818 001:715.456 JLINK_IsHalted()
T0818 001:715.921 - 0.465ms returns FALSE
T0818 001:715.927 JLINK_HasError()
T0818 001:717.458 JLINK_IsHalted()
T0818 001:717.965 - 0.506ms returns FALSE
T0818 001:717.971 JLINK_HasError()
T0818 001:719.456 JLINK_IsHalted()
T0818 001:719.927 - 0.471ms returns FALSE
T0818 001:719.933 JLINK_HasError()
T0818 001:721.457 JLINK_IsHalted()
T0818 001:721.937 - 0.480ms returns FALSE
T0818 001:721.943 JLINK_HasError()
T0818 001:723.456 JLINK_IsHalted()
T0818 001:723.921 - 0.464ms returns FALSE
T0818 001:723.926 JLINK_HasError()
T0818 001:725.456 JLINK_IsHalted()
T0818 001:725.960 - 0.503ms returns FALSE
T0818 001:725.972 JLINK_HasError()
T0818 001:727.456 JLINK_IsHalted()
T0818 001:727.944 - 0.487ms returns FALSE
T0818 001:727.951 JLINK_HasError()
T0818 001:729.457 JLINK_IsHalted()
T0818 001:729.927 - 0.470ms returns FALSE
T0818 001:729.932 JLINK_HasError()
T0818 001:731.456 JLINK_IsHalted()
T0818 001:731.928 - 0.472ms returns FALSE
T0818 001:731.934 JLINK_HasError()
T0818 001:733.456 JLINK_IsHalted()
T0818 001:733.921 - 0.464ms returns FALSE
T0818 001:733.926 JLINK_HasError()
T0818 001:735.457 JLINK_IsHalted()
T0818 001:735.967 - 0.509ms returns FALSE
T0818 001:735.973 JLINK_HasError()
T0818 001:737.458 JLINK_IsHalted()
T0818 001:737.937 - 0.479ms returns FALSE
T0818 001:737.944 JLINK_HasError()
T0818 001:739.457 JLINK_IsHalted()
T0818 001:739.926 - 0.469ms returns FALSE
T0818 001:739.932 JLINK_HasError()
T0818 001:741.460 JLINK_IsHalted()
T0818 001:741.924 - 0.464ms returns FALSE
T0818 001:741.936 JLINK_HasError()
T0818 001:744.458 JLINK_IsHalted()
T0818 001:744.957 - 0.499ms returns FALSE
T0818 001:744.963 JLINK_HasError()
T0818 001:746.457 JLINK_IsHalted()
T0818 001:746.945 - 0.488ms returns FALSE
T0818 001:746.950 JLINK_HasError()
T0818 001:748.456 JLINK_IsHalted()
T0818 001:748.980 - 0.523ms returns FALSE
T0818 001:748.986 JLINK_HasError()
T0818 001:750.456 JLINK_IsHalted()
T0818 001:750.919 - 0.462ms returns FALSE
T0818 001:750.924 JLINK_HasError()
T0818 001:752.458 JLINK_IsHalted()
T0818 001:752.966 - 0.508ms returns FALSE
T0818 001:752.972 JLINK_HasError()
T0818 001:754.456 JLINK_IsHalted()
T0818 001:754.928 - 0.471ms returns FALSE
T0818 001:754.933 JLINK_HasError()
T0818 001:756.456 JLINK_IsHalted()
T0818 001:756.926 - 0.469ms returns FALSE
T0818 001:756.937 JLINK_HasError()
T0818 001:758.457 JLINK_IsHalted()
T0818 001:758.922 - 0.464ms returns FALSE
T0818 001:758.928 JLINK_HasError()
T0818 001:760.457 JLINK_IsHalted()
T0818 001:760.920 - 0.463ms returns FALSE
T0818 001:760.926 JLINK_HasError()
T0818 001:762.456 JLINK_IsHalted()
T0818 001:762.922 - 0.465ms returns FALSE
T0818 001:762.928 JLINK_HasError()
T0818 001:764.456 JLINK_IsHalted()
T0818 001:764.927 - 0.470ms returns FALSE
T0818 001:764.932 JLINK_HasError()
T0818 001:766.458 JLINK_IsHalted()
T0818 001:766.980 - 0.521ms returns FALSE
T0818 001:766.989 JLINK_HasError()
T0818 001:768.457 JLINK_IsHalted()
T0818 001:768.911 - 0.453ms returns FALSE
T0818 001:768.917 JLINK_HasError()
T0818 001:770.456 JLINK_IsHalted()
T0818 001:770.920 - 0.463ms returns FALSE
T0818 001:770.926 JLINK_HasError()
T0818 001:772.458 JLINK_IsHalted()
T0818 001:772.922 - 0.464ms returns FALSE
T0818 001:772.928 JLINK_HasError()
T0818 001:774.457 JLINK_IsHalted()
T0818 001:774.943 - 0.485ms returns FALSE
T0818 001:774.949 JLINK_HasError()
T0818 001:776.456 JLINK_IsHalted()
T0818 001:776.943 - 0.486ms returns FALSE
T0818 001:776.949 JLINK_HasError()
T0818 001:778.456 JLINK_IsHalted()
T0818 001:778.921 - 0.464ms returns FALSE
T0818 001:778.927 JLINK_HasError()
T0818 001:780.456 JLINK_IsHalted()
T0818 001:780.921 - 0.465ms returns FALSE
T0818 001:780.927 JLINK_HasError()
T0818 001:782.457 JLINK_IsHalted()
T0818 001:782.966 - 0.509ms returns FALSE
T0818 001:782.972 JLINK_HasError()
T0818 001:784.456 JLINK_IsHalted()
T0818 001:784.926 - 0.469ms returns FALSE
T0818 001:784.931 JLINK_HasError()
T0818 001:786.458 JLINK_IsHalted()
T0818 001:786.938 - 0.479ms returns FALSE
T0818 001:786.945 JLINK_HasError()
T0818 001:788.457 JLINK_IsHalted()
T0818 001:788.969 - 0.511ms returns FALSE
T0818 001:788.976 JLINK_HasError()
T0818 001:790.459 JLINK_IsHalted()
T0818 001:792.724   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:793.239 - 2.780ms returns TRUE
T0818 001:793.249 JLINK_ReadReg(R15 (PC))
T0818 001:793.254 - 0.005ms returns 0x20000000
T0818 001:793.279 JLINK_ClrBPEx(BPHandle = 0x00000007)
T0818 001:793.284 - 0.005ms returns 0x00
T0818 001:793.288 JLINK_ReadReg(R0)
T0818 001:793.292 - 0.003ms returns 0x00000000
T0818 001:793.689 JLINK_HasError()
T0818 001:793.699 JLINK_WriteReg(R0, 0x0800C000)
T0818 001:793.704 - 0.004ms returns 0
T0818 001:793.708 JLINK_WriteReg(R1, 0x00004000)
T0818 001:793.711 - 0.003ms returns 0
T0818 001:793.715 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:793.719 - 0.003ms returns 0
T0818 001:793.723 JLINK_WriteReg(R3, 0x00000000)
T0818 001:793.726 - 0.003ms returns 0
T0818 001:793.730 JLINK_WriteReg(R4, 0x00000000)
T0818 001:793.733 - 0.003ms returns 0
T0818 001:793.738 JLINK_WriteReg(R5, 0x00000000)
T0818 001:793.741 - 0.003ms returns 0
T0818 001:793.745 JLINK_WriteReg(R6, 0x00000000)
T0818 001:793.748 - 0.003ms returns 0
T0818 001:793.752 JLINK_WriteReg(R7, 0x00000000)
T0818 001:793.756 - 0.003ms returns 0
T0818 001:793.760 JLINK_WriteReg(R8, 0x00000000)
T0818 001:793.763 - 0.003ms returns 0
T0818 001:793.767 JLINK_WriteReg(R9, 0x20000180)
T0818 001:793.771 - 0.003ms returns 0
T0818 001:793.775 JLINK_WriteReg(R10, 0x00000000)
T0818 001:793.778 - 0.003ms returns 0
T0818 001:793.782 JLINK_WriteReg(R11, 0x00000000)
T0818 001:793.786 - 0.003ms returns 0
T0818 001:793.790 JLINK_WriteReg(R12, 0x00000000)
T0818 001:793.793 - 0.003ms returns 0
T0818 001:793.797 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:793.801 - 0.003ms returns 0
T0818 001:793.805 JLINK_WriteReg(R14, 0x20000001)
T0818 001:793.808 - 0.003ms returns 0
T0818 001:793.812 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 001:793.816 - 0.003ms returns 0
T0818 001:793.820 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:793.823 - 0.003ms returns 0
T0818 001:793.827 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:793.831 - 0.003ms returns 0
T0818 001:793.835 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:793.838 - 0.003ms returns 0
T0818 001:793.842 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:793.848 - 0.005ms returns 0
T0818 001:793.855 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:793.859 - 0.004ms returns 0x00000008
T0818 001:793.863 JLINK_Go()
T0818 001:793.873   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:796.598 - 2.734ms 
T0818 001:796.612 JLINK_IsHalted()
T0818 001:798.939   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:799.377 - 2.764ms returns TRUE
T0818 001:799.384 JLINK_ReadReg(R15 (PC))
T0818 001:799.388 - 0.004ms returns 0x20000000
T0818 001:799.393 JLINK_ClrBPEx(BPHandle = 0x00000008)
T0818 001:799.396 - 0.003ms returns 0x00
T0818 001:799.401 JLINK_ReadReg(R0)
T0818 001:799.404 - 0.003ms returns 0x00000001
T0818 001:799.408 JLINK_HasError()
T0818 001:799.413 JLINK_WriteReg(R0, 0x0800C000)
T0818 001:799.417 - 0.003ms returns 0
T0818 001:799.421 JLINK_WriteReg(R1, 0x00004000)
T0818 001:799.424 - 0.003ms returns 0
T0818 001:799.428 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:799.432 - 0.003ms returns 0
T0818 001:799.436 JLINK_WriteReg(R3, 0x00000000)
T0818 001:799.439 - 0.003ms returns 0
T0818 001:799.443 JLINK_WriteReg(R4, 0x00000000)
T0818 001:799.446 - 0.003ms returns 0
T0818 001:799.450 JLINK_WriteReg(R5, 0x00000000)
T0818 001:799.454 - 0.003ms returns 0
T0818 001:799.458 JLINK_WriteReg(R6, 0x00000000)
T0818 001:799.461 - 0.003ms returns 0
T0818 001:799.465 JLINK_WriteReg(R7, 0x00000000)
T0818 001:799.469 - 0.003ms returns 0
T0818 001:799.473 JLINK_WriteReg(R8, 0x00000000)
T0818 001:799.476 - 0.003ms returns 0
T0818 001:799.480 JLINK_WriteReg(R9, 0x20000180)
T0818 001:799.483 - 0.003ms returns 0
T0818 001:799.487 JLINK_WriteReg(R10, 0x00000000)
T0818 001:799.491 - 0.003ms returns 0
T0818 001:799.495 JLINK_WriteReg(R11, 0x00000000)
T0818 001:799.498 - 0.003ms returns 0
T0818 001:799.502 JLINK_WriteReg(R12, 0x00000000)
T0818 001:799.505 - 0.003ms returns 0
T0818 001:799.509 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:799.513 - 0.003ms returns 0
T0818 001:799.517 JLINK_WriteReg(R14, 0x20000001)
T0818 001:799.520 - 0.003ms returns 0
T0818 001:799.524 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 001:799.528 - 0.003ms returns 0
T0818 001:799.532 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:799.535 - 0.003ms returns 0
T0818 001:799.539 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:799.543 - 0.003ms returns 0
T0818 001:799.547 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:799.550 - 0.003ms returns 0
T0818 001:799.554 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:799.558 - 0.003ms returns 0
T0818 001:799.562 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:799.566 - 0.003ms returns 0x00000009
T0818 001:799.570 JLINK_Go()
T0818 001:799.580   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:802.333 - 2.762ms 
T0818 001:802.338 JLINK_IsHalted()
T0818 001:802.777 - 0.438ms returns FALSE
T0818 001:802.783 JLINK_HasError()
T0818 001:806.459 JLINK_IsHalted()
T0818 001:806.905 - 0.445ms returns FALSE
T0818 001:806.917 JLINK_HasError()
T0818 001:808.459 JLINK_IsHalted()
T0818 001:808.958 - 0.498ms returns FALSE
T0818 001:808.964 JLINK_HasError()
T0818 001:810.456 JLINK_IsHalted()
T0818 001:810.944 - 0.487ms returns FALSE
T0818 001:810.949 JLINK_HasError()
T0818 001:812.459 JLINK_IsHalted()
T0818 001:813.003 - 0.544ms returns FALSE
T0818 001:813.010 JLINK_HasError()
T0818 001:814.462 JLINK_IsHalted()
T0818 001:814.937 - 0.475ms returns FALSE
T0818 001:814.944 JLINK_HasError()
T0818 001:816.457 JLINK_IsHalted()
T0818 001:816.921 - 0.464ms returns FALSE
T0818 001:816.926 JLINK_HasError()
T0818 001:818.457 JLINK_IsHalted()
T0818 001:818.920 - 0.463ms returns FALSE
T0818 001:818.925 JLINK_HasError()
T0818 001:820.456 JLINK_IsHalted()
T0818 001:820.925 - 0.469ms returns FALSE
T0818 001:820.931 JLINK_HasError()
T0818 001:822.457 JLINK_IsHalted()
T0818 001:822.927 - 0.470ms returns FALSE
T0818 001:822.933 JLINK_HasError()
T0818 001:824.457 JLINK_IsHalted()
T0818 001:824.926 - 0.469ms returns FALSE
T0818 001:824.932 JLINK_HasError()
T0818 001:826.456 JLINK_IsHalted()
T0818 001:826.920 - 0.463ms returns FALSE
T0818 001:826.928 JLINK_HasError()
T0818 001:828.456 JLINK_IsHalted()
T0818 001:828.920 - 0.464ms returns FALSE
T0818 001:828.926 JLINK_HasError()
T0818 001:830.456 JLINK_IsHalted()
T0818 001:830.926 - 0.469ms returns FALSE
T0818 001:830.931 JLINK_HasError()
T0818 001:832.456 JLINK_IsHalted()
T0818 001:832.926 - 0.469ms returns FALSE
T0818 001:832.932 JLINK_HasError()
T0818 001:834.456 JLINK_IsHalted()
T0818 001:834.920 - 0.463ms returns FALSE
T0818 001:834.925 JLINK_HasError()
T0818 001:836.456 JLINK_IsHalted()
T0818 001:836.921 - 0.464ms returns FALSE
T0818 001:836.926 JLINK_HasError()
T0818 001:838.457 JLINK_IsHalted()
T0818 001:838.884 - 0.426ms returns FALSE
T0818 001:838.890 JLINK_HasError()
T0818 001:840.456 JLINK_IsHalted()
T0818 001:840.925 - 0.468ms returns FALSE
T0818 001:840.930 JLINK_HasError()
T0818 001:842.459 JLINK_IsHalted()
T0818 001:842.937 - 0.478ms returns FALSE
T0818 001:842.947 JLINK_HasError()
T0818 001:844.456 JLINK_IsHalted()
T0818 001:844.934 - 0.478ms returns FALSE
T0818 001:844.940 JLINK_HasError()
T0818 001:846.456 JLINK_IsHalted()
T0818 001:846.920 - 0.463ms returns FALSE
T0818 001:846.925 JLINK_HasError()
T0818 001:848.456 JLINK_IsHalted()
T0818 001:848.920 - 0.463ms returns FALSE
T0818 001:848.925 JLINK_HasError()
T0818 001:850.456 JLINK_IsHalted()
T0818 001:850.926 - 0.469ms returns FALSE
T0818 001:850.931 JLINK_HasError()
T0818 001:852.456 JLINK_IsHalted()
T0818 001:852.922 - 0.465ms returns FALSE
T0818 001:852.928 JLINK_HasError()
T0818 001:855.458 JLINK_IsHalted()
T0818 001:855.956 - 0.497ms returns FALSE
T0818 001:855.962 JLINK_HasError()
T0818 001:857.458 JLINK_IsHalted()
T0818 001:857.982 - 0.523ms returns FALSE
T0818 001:857.988 JLINK_HasError()
T0818 001:859.456 JLINK_IsHalted()
T0818 001:859.927 - 0.471ms returns FALSE
T0818 001:859.933 JLINK_HasError()
T0818 001:861.456 JLINK_IsHalted()
T0818 001:861.921 - 0.465ms returns FALSE
T0818 001:861.927 JLINK_HasError()
T0818 001:863.456 JLINK_IsHalted()
T0818 001:863.922 - 0.466ms returns FALSE
T0818 001:863.928 JLINK_HasError()
T0818 001:865.456 JLINK_IsHalted()
T0818 001:865.921 - 0.465ms returns FALSE
T0818 001:865.927 JLINK_HasError()
T0818 001:867.456 JLINK_IsHalted()
T0818 001:867.929 - 0.472ms returns FALSE
T0818 001:867.935 JLINK_HasError()
T0818 001:869.458 JLINK_IsHalted()
T0818 001:869.936 - 0.477ms returns FALSE
T0818 001:869.942 JLINK_HasError()
T0818 001:871.456 JLINK_IsHalted()
T0818 001:871.905 - 0.448ms returns FALSE
T0818 001:871.910 JLINK_HasError()
T0818 001:873.456 JLINK_IsHalted()
T0818 001:873.923 - 0.466ms returns FALSE
T0818 001:873.928 JLINK_HasError()
T0818 001:875.456 JLINK_IsHalted()
T0818 001:875.936 - 0.479ms returns FALSE
T0818 001:875.941 JLINK_HasError()
T0818 001:877.456 JLINK_IsHalted()
T0818 001:878.878 - 1.421ms returns FALSE
T0818 001:878.884 JLINK_HasError()
T0818 001:880.456 JLINK_IsHalted()
T0818 001:880.920 - 0.463ms returns FALSE
T0818 001:880.925 JLINK_HasError()
T0818 001:882.456 JLINK_IsHalted()
T0818 001:882.927 - 0.470ms returns FALSE
T0818 001:882.932 JLINK_HasError()
T0818 001:884.456 JLINK_IsHalted()
T0818 001:886.037 - 1.580ms returns FALSE
T0818 001:886.047 JLINK_HasError()
T0818 001:887.456 JLINK_IsHalted()
T0818 001:887.965 - 0.508ms returns FALSE
T0818 001:887.971 JLINK_HasError()
T0818 001:889.456 JLINK_IsHalted()
T0818 001:889.967 - 0.510ms returns FALSE
T0818 001:889.972 JLINK_HasError()
T0818 001:891.456 JLINK_IsHalted()
T0818 001:891.927 - 0.471ms returns FALSE
T0818 001:891.933 JLINK_HasError()
T0818 001:893.456 JLINK_IsHalted()
T0818 001:893.925 - 0.468ms returns FALSE
T0818 001:893.934 JLINK_HasError()
T0818 001:895.456 JLINK_IsHalted()
T0818 001:895.922 - 0.466ms returns FALSE
T0818 001:895.928 JLINK_HasError()
T0818 001:897.458 JLINK_IsHalted()
T0818 001:897.964 - 0.505ms returns FALSE
T0818 001:897.970 JLINK_HasError()
T0818 001:899.456 JLINK_IsHalted()
T0818 001:899.928 - 0.471ms returns FALSE
T0818 001:899.933 JLINK_HasError()
T0818 001:905.391 JLINK_IsHalted()
T0818 001:905.876 - 0.484ms returns FALSE
T0818 001:905.882 JLINK_HasError()
T0818 001:907.456 JLINK_IsHalted()
T0818 001:907.956 - 0.499ms returns FALSE
T0818 001:907.962 JLINK_HasError()
T0818 001:909.456 JLINK_IsHalted()
T0818 001:909.928 - 0.471ms returns FALSE
T0818 001:909.933 JLINK_HasError()
T0818 001:911.456 JLINK_IsHalted()
T0818 001:912.209 - 0.752ms returns FALSE
T0818 001:912.214 JLINK_HasError()
T0818 001:913.456 JLINK_IsHalted()
T0818 001:913.928 - 0.471ms returns FALSE
T0818 001:913.933 JLINK_HasError()
T0818 001:915.456 JLINK_IsHalted()
T0818 001:915.921 - 0.465ms returns FALSE
T0818 001:915.927 JLINK_HasError()
T0818 001:917.458 JLINK_IsHalted()
T0818 001:917.956 - 0.498ms returns FALSE
T0818 001:917.963 JLINK_HasError()
T0818 001:919.456 JLINK_IsHalted()
T0818 001:919.937 - 0.480ms returns FALSE
T0818 001:919.943 JLINK_HasError()
T0818 001:921.456 JLINK_IsHalted()
T0818 001:921.936 - 0.480ms returns FALSE
T0818 001:921.942 JLINK_HasError()
T0818 001:923.456 JLINK_IsHalted()
T0818 001:923.922 - 0.466ms returns FALSE
T0818 001:923.928 JLINK_HasError()
T0818 001:925.457 JLINK_IsHalted()
T0818 001:925.922 - 0.464ms returns FALSE
T0818 001:925.928 JLINK_HasError()
T0818 001:927.458 JLINK_IsHalted()
T0818 001:927.936 - 0.478ms returns FALSE
T0818 001:927.943 JLINK_HasError()
T0818 001:929.459 JLINK_IsHalted()
T0818 001:929.957 - 0.498ms returns FALSE
T0818 001:929.963 JLINK_HasError()
T0818 001:931.456 JLINK_IsHalted()
T0818 001:931.979 - 0.523ms returns FALSE
T0818 001:931.985 JLINK_HasError()
T0818 001:933.456 JLINK_IsHalted()
T0818 001:933.921 - 0.464ms returns FALSE
T0818 001:933.927 JLINK_HasError()
T0818 001:935.456 JLINK_IsHalted()
T0818 001:935.920 - 0.463ms returns FALSE
T0818 001:935.925 JLINK_HasError()
T0818 001:937.456 JLINK_IsHalted()
T0818 001:937.965 - 0.508ms returns FALSE
T0818 001:937.970 JLINK_HasError()
T0818 001:939.456 JLINK_IsHalted()
T0818 001:939.929 - 0.472ms returns FALSE
T0818 001:939.934 JLINK_HasError()
T0818 001:941.456 JLINK_IsHalted()
T0818 001:941.927 - 0.470ms returns FALSE
T0818 001:941.932 JLINK_HasError()
T0818 001:943.456 JLINK_IsHalted()
T0818 001:943.921 - 0.465ms returns FALSE
T0818 001:943.927 JLINK_HasError()
T0818 001:945.456 JLINK_IsHalted()
T0818 001:945.922 - 0.465ms returns FALSE
T0818 001:945.927 JLINK_HasError()
T0818 001:947.456 JLINK_IsHalted()
T0818 001:947.927 - 0.470ms returns FALSE
T0818 001:947.933 JLINK_HasError()
T0818 001:949.458 JLINK_IsHalted()
T0818 001:949.957 - 0.499ms returns FALSE
T0818 001:949.963 JLINK_HasError()
T0818 001:951.456 JLINK_IsHalted()
T0818 001:951.926 - 0.469ms returns FALSE
T0818 001:951.932 JLINK_HasError()
T0818 001:953.456 JLINK_IsHalted()
T0818 001:953.922 - 0.466ms returns FALSE
T0818 001:953.928 JLINK_HasError()
T0818 001:955.456 JLINK_IsHalted()
T0818 001:955.919 - 0.463ms returns FALSE
T0818 001:955.925 JLINK_HasError()
T0818 001:957.456 JLINK_IsHalted()
T0818 001:957.936 - 0.479ms returns FALSE
T0818 001:957.941 JLINK_HasError()
T0818 001:959.456 JLINK_IsHalted()
T0818 001:959.928 - 0.472ms returns FALSE
T0818 001:959.934 JLINK_HasError()
T0818 001:961.456 JLINK_IsHalted()
T0818 001:961.943 - 0.487ms returns FALSE
T0818 001:961.949 JLINK_HasError()
T0818 001:963.456 JLINK_IsHalted()
T0818 001:964.014 - 0.557ms returns FALSE
T0818 001:964.027 JLINK_HasError()
T0818 001:966.458 JLINK_IsHalted()
T0818 001:966.936 - 0.477ms returns FALSE
T0818 001:966.942 JLINK_HasError()
T0818 001:968.456 JLINK_IsHalted()
T0818 001:968.924 - 0.467ms returns FALSE
T0818 001:968.929 JLINK_HasError()
T0818 001:970.456 JLINK_IsHalted()
T0818 001:970.923 - 0.466ms returns FALSE
T0818 001:970.928 JLINK_HasError()
T0818 001:972.456 JLINK_IsHalted()
T0818 001:972.922 - 0.465ms returns FALSE
T0818 001:972.927 JLINK_HasError()
T0818 001:974.456 JLINK_IsHalted()
T0818 001:974.937 - 0.481ms returns FALSE
T0818 001:974.943 JLINK_HasError()
T0818 001:976.456 JLINK_IsHalted()
T0818 001:976.924 - 0.468ms returns FALSE
T0818 001:976.930 JLINK_HasError()
T0818 001:978.456 JLINK_IsHalted()
T0818 001:978.968 - 0.511ms returns FALSE
T0818 001:978.973 JLINK_HasError()
T0818 001:980.461 JLINK_IsHalted()
T0818 001:981.015 - 0.553ms returns FALSE
T0818 001:981.028 JLINK_HasError()
T0818 001:982.457 JLINK_IsHalted()
T0818 001:982.924 - 0.466ms returns FALSE
T0818 001:982.931 JLINK_HasError()
T0818 001:984.462 JLINK_IsHalted()
T0818 001:984.904 - 0.441ms returns FALSE
T0818 001:984.912 JLINK_HasError()
T0818 001:986.461 JLINK_IsHalted()
T0818 001:986.925 - 0.464ms returns FALSE
T0818 001:986.932 JLINK_HasError()
T0818 001:988.460 JLINK_IsHalted()
T0818 001:988.968 - 0.508ms returns FALSE
T0818 001:988.974 JLINK_HasError()
T0818 001:990.457 JLINK_IsHalted()
T0818 001:990.920 - 0.463ms returns FALSE
T0818 001:990.926 JLINK_HasError()
T0818 001:992.457 JLINK_IsHalted()
T0818 001:992.925 - 0.468ms returns FALSE
T0818 001:992.932 JLINK_HasError()
T0818 001:994.460 JLINK_IsHalted()
T0818 001:994.939 - 0.478ms returns FALSE
T0818 001:994.952 JLINK_HasError()
T0818 001:996.460 JLINK_IsHalted()
T0818 001:996.884 - 0.424ms returns FALSE
T0818 001:996.891 JLINK_HasError()
T0818 001:998.458 JLINK_IsHalted()
T0818 001:998.921 - 0.463ms returns FALSE
T0818 001:998.927 JLINK_HasError()
T0818 002:000.457 JLINK_IsHalted()
T0818 002:000.922 - 0.465ms returns FALSE
T0818 002:000.928 JLINK_HasError()
T0818 002:002.464 JLINK_IsHalted()
T0818 002:002.926 - 0.462ms returns FALSE
T0818 002:002.941 JLINK_HasError()
T0818 002:004.457 JLINK_IsHalted()
T0818 002:004.938 - 0.480ms returns FALSE
T0818 002:004.944 JLINK_HasError()
T0818 002:006.457 JLINK_IsHalted()
T0818 002:006.929 - 0.471ms returns FALSE
T0818 002:006.940 JLINK_HasError()
T0818 002:008.458 JLINK_IsHalted()
T0818 002:008.926 - 0.467ms returns FALSE
T0818 002:008.931 JLINK_HasError()
T0818 002:010.456 JLINK_IsHalted()
T0818 002:010.921 - 0.465ms returns FALSE
T0818 002:010.927 JLINK_HasError()
T0818 002:012.462 JLINK_IsHalted()
T0818 002:012.924 - 0.461ms returns FALSE
T0818 002:012.930 JLINK_HasError()
T0818 002:014.459 JLINK_IsHalted()
T0818 002:014.868 - 0.408ms returns FALSE
T0818 002:014.879 JLINK_HasError()
T0818 002:016.457 JLINK_IsHalted()
T0818 002:016.942 - 0.485ms returns FALSE
T0818 002:016.948 JLINK_HasError()
T0818 002:018.456 JLINK_IsHalted()
T0818 002:018.920 - 0.463ms returns FALSE
T0818 002:018.925 JLINK_HasError()
T0818 002:020.458 JLINK_IsHalted()
T0818 002:020.921 - 0.463ms returns FALSE
T0818 002:020.930 JLINK_HasError()
T0818 002:022.456 JLINK_IsHalted()
T0818 002:022.925 - 0.469ms returns FALSE
T0818 002:022.931 JLINK_HasError()
T0818 002:024.456 JLINK_IsHalted()
T0818 002:024.925 - 0.468ms returns FALSE
T0818 002:024.930 JLINK_HasError()
T0818 002:026.456 JLINK_IsHalted()
T0818 002:026.939 - 0.482ms returns FALSE
T0818 002:026.946 JLINK_HasError()
T0818 002:028.459 JLINK_IsHalted()
T0818 002:028.935 - 0.476ms returns FALSE
T0818 002:028.941 JLINK_HasError()
T0818 002:030.457 JLINK_IsHalted()
T0818 002:030.920 - 0.463ms returns FALSE
T0818 002:030.926 JLINK_HasError()
T0818 002:032.456 JLINK_IsHalted()
T0818 002:032.919 - 0.462ms returns FALSE
T0818 002:032.924 JLINK_HasError()
T0818 002:034.456 JLINK_IsHalted()
T0818 002:034.927 - 0.470ms returns FALSE
T0818 002:034.932 JLINK_HasError()
T0818 002:036.456 JLINK_IsHalted()
T0818 002:036.934 - 0.477ms returns FALSE
T0818 002:036.940 JLINK_HasError()
T0818 002:038.456 JLINK_IsHalted()
T0818 002:038.918 - 0.461ms returns FALSE
T0818 002:038.923 JLINK_HasError()
T0818 002:040.456 JLINK_IsHalted()
T0818 002:040.925 - 0.468ms returns FALSE
T0818 002:040.934 JLINK_HasError()
T0818 002:042.459 JLINK_IsHalted()
T0818 002:042.912 - 0.453ms returns FALSE
T0818 002:042.919 JLINK_HasError()
T0818 002:044.457 JLINK_IsHalted()
T0818 002:044.928 - 0.470ms returns FALSE
T0818 002:044.934 JLINK_HasError()
T0818 002:046.459 JLINK_IsHalted()
T0818 002:046.937 - 0.478ms returns FALSE
T0818 002:046.947 JLINK_HasError()
T0818 002:048.456 JLINK_IsHalted()
T0818 002:048.941 - 0.485ms returns FALSE
T0818 002:048.951 JLINK_HasError()
T0818 002:050.456 JLINK_IsHalted()
T0818 002:050.920 - 0.464ms returns FALSE
T0818 002:050.926 JLINK_HasError()
T0818 002:052.456 JLINK_IsHalted()
T0818 002:052.878 - 0.421ms returns FALSE
T0818 002:052.884 JLINK_HasError()
T0818 002:054.460 JLINK_IsHalted()
T0818 002:054.927 - 0.467ms returns FALSE
T0818 002:054.933 JLINK_HasError()
T0818 002:056.456 JLINK_IsHalted()
T0818 002:056.928 - 0.471ms returns FALSE
T0818 002:056.933 JLINK_HasError()
T0818 002:058.460 JLINK_IsHalted()
T0818 002:058.958 - 0.497ms returns FALSE
T0818 002:058.964 JLINK_HasError()
T0818 002:060.456 JLINK_IsHalted()
T0818 002:060.923 - 0.466ms returns FALSE
T0818 002:060.928 JLINK_HasError()
T0818 002:062.456 JLINK_IsHalted()
T0818 002:062.928 - 0.471ms returns FALSE
T0818 002:062.933 JLINK_HasError()
T0818 002:064.456 JLINK_IsHalted()
T0818 002:064.928 - 0.471ms returns FALSE
T0818 002:064.934 JLINK_HasError()
T0818 002:066.456 JLINK_IsHalted()
T0818 002:066.943 - 0.486ms returns FALSE
T0818 002:066.949 JLINK_HasError()
T0818 002:068.466 JLINK_IsHalted()
T0818 002:068.958 - 0.492ms returns FALSE
T0818 002:068.964 JLINK_HasError()
T0818 002:070.458 JLINK_IsHalted()
T0818 002:070.957 - 0.499ms returns FALSE
T0818 002:070.966 JLINK_HasError()
T0818 002:072.456 JLINK_IsHalted()
T0818 002:072.926 - 0.469ms returns FALSE
T0818 002:072.932 JLINK_HasError()
T0818 002:074.174 JLINK_IsHalted()
T0818 002:074.651 - 0.477ms returns FALSE
T0818 002:074.657 JLINK_HasError()
T0818 002:077.175 JLINK_IsHalted()
T0818 002:077.654 - 0.478ms returns FALSE
T0818 002:077.660 JLINK_HasError()
T0818 002:079.174 JLINK_IsHalted()
T0818 002:079.669 - 0.494ms returns FALSE
T0818 002:079.679 JLINK_HasError()
T0818 002:081.175 JLINK_IsHalted()
T0818 002:081.661 - 0.486ms returns FALSE
T0818 002:081.667 JLINK_HasError()
T0818 002:083.175 JLINK_IsHalted()
T0818 002:083.655 - 0.479ms returns FALSE
T0818 002:083.664 JLINK_HasError()
T0818 002:085.174 JLINK_IsHalted()
T0818 002:085.637 - 0.462ms returns FALSE
T0818 002:085.643 JLINK_HasError()
T0818 002:087.176 JLINK_IsHalted()
T0818 002:087.661 - 0.485ms returns FALSE
T0818 002:087.666 JLINK_HasError()
T0818 002:089.174 JLINK_IsHalted()
T0818 002:089.639 - 0.464ms returns FALSE
T0818 002:089.649 JLINK_HasError()
T0818 002:091.176 JLINK_IsHalted()
T0818 002:091.659 - 0.483ms returns FALSE
T0818 002:091.665 JLINK_HasError()
T0818 002:093.174 JLINK_IsHalted()
T0818 002:093.650 - 0.476ms returns FALSE
T0818 002:093.656 JLINK_HasError()
T0818 002:095.174 JLINK_IsHalted()
T0818 002:095.620 - 0.445ms returns FALSE
T0818 002:095.625 JLINK_HasError()
T0818 002:097.175 JLINK_IsHalted()
T0818 002:097.669 - 0.494ms returns FALSE
T0818 002:097.675 JLINK_HasError()
T0818 002:099.174 JLINK_IsHalted()
T0818 002:099.659 - 0.485ms returns FALSE
T0818 002:099.665 JLINK_HasError()
T0818 002:101.174 JLINK_IsHalted()
T0818 002:101.650 - 0.476ms returns FALSE
T0818 002:101.655 JLINK_HasError()
T0818 002:103.174 JLINK_IsHalted()
T0818 002:103.616 - 0.442ms returns FALSE
T0818 002:103.622 JLINK_HasError()
T0818 002:105.177 JLINK_IsHalted()
T0818 002:105.756 - 0.579ms returns FALSE
T0818 002:105.765 JLINK_HasError()
T0818 002:107.178 JLINK_IsHalted()
T0818 002:107.640 - 0.462ms returns FALSE
T0818 002:107.646 JLINK_HasError()
T0818 002:109.178 JLINK_IsHalted()
T0818 002:111.605 - 2.427ms returns FALSE
T0818 002:111.613 JLINK_HasError()
T0818 002:113.174 JLINK_IsHalted()
T0818 002:113.661 - 0.486ms returns FALSE
T0818 002:113.666 JLINK_HasError()
T0818 002:115.189 JLINK_IsHalted()
T0818 002:115.650 - 0.460ms returns FALSE
T0818 002:115.655 JLINK_HasError()
T0818 002:117.174 JLINK_IsHalted()
T0818 002:117.639 - 0.464ms returns FALSE
T0818 002:117.645 JLINK_HasError()
T0818 002:119.174 JLINK_IsHalted()
T0818 002:119.617 - 0.442ms returns FALSE
T0818 002:119.622 JLINK_HasError()
T0818 002:121.176 JLINK_IsHalted()
T0818 002:121.654 - 0.478ms returns FALSE
T0818 002:121.662 JLINK_HasError()
T0818 002:123.174 JLINK_IsHalted()
T0818 002:123.659 - 0.485ms returns FALSE
T0818 002:123.665 JLINK_HasError()
T0818 002:125.174 JLINK_IsHalted()
T0818 002:125.649 - 0.475ms returns FALSE
T0818 002:125.655 JLINK_HasError()
T0818 002:127.175 JLINK_IsHalted()
T0818 002:127.637 - 0.461ms returns FALSE
T0818 002:127.643 JLINK_HasError()
T0818 002:129.174 JLINK_IsHalted()
T0818 002:129.625 - 0.450ms returns FALSE
T0818 002:129.631 JLINK_HasError()
T0818 002:131.174 JLINK_IsHalted()
T0818 002:131.660 - 0.485ms returns FALSE
T0818 002:131.665 JLINK_HasError()
T0818 002:133.174 JLINK_IsHalted()
T0818 002:133.672 - 0.497ms returns FALSE
T0818 002:133.678 JLINK_HasError()
T0818 002:135.174 JLINK_IsHalted()
T0818 002:135.696 - 0.521ms returns FALSE
T0818 002:135.705 JLINK_HasError()
T0818 002:137.175 JLINK_IsHalted()
T0818 002:137.636 - 0.461ms returns FALSE
T0818 002:137.641 JLINK_HasError()
T0818 002:139.174 JLINK_IsHalted()
T0818 002:139.660 - 0.486ms returns FALSE
T0818 002:139.666 JLINK_HasError()
T0818 002:141.174 JLINK_IsHalted()
T0818 002:141.660 - 0.485ms returns FALSE
T0818 002:141.665 JLINK_HasError()
T0818 002:143.174 JLINK_IsHalted()
T0818 002:143.651 - 0.477ms returns FALSE
T0818 002:143.657 JLINK_HasError()
T0818 002:145.174 JLINK_IsHalted()
T0818 002:145.670 - 0.496ms returns FALSE
T0818 002:145.675 JLINK_HasError()
T0818 002:147.174 JLINK_IsHalted()
T0818 002:147.638 - 0.463ms returns FALSE
T0818 002:147.644 JLINK_HasError()
T0818 002:149.174 JLINK_IsHalted()
T0818 002:149.617 - 0.443ms returns FALSE
T0818 002:149.623 JLINK_HasError()
T0818 002:151.178 JLINK_IsHalted()
T0818 002:151.660 - 0.482ms returns FALSE
T0818 002:151.666 JLINK_HasError()
T0818 002:153.175 JLINK_IsHalted()
T0818 002:153.670 - 0.495ms returns FALSE
T0818 002:153.676 JLINK_HasError()
T0818 002:155.174 JLINK_IsHalted()
T0818 002:155.649 - 0.475ms returns FALSE
T0818 002:155.654 JLINK_HasError()
T0818 002:157.175 JLINK_IsHalted()
T0818 002:157.625 - 0.450ms returns FALSE
T0818 002:157.632 JLINK_HasError()
T0818 002:159.176 JLINK_IsHalted()
T0818 002:159.706 - 0.530ms returns FALSE
T0818 002:159.711 JLINK_HasError()
T0818 002:161.175 JLINK_IsHalted()
T0818 002:161.659 - 0.484ms returns FALSE
T0818 002:161.665 JLINK_HasError()
T0818 002:163.174 JLINK_IsHalted()
T0818 002:163.651 - 0.476ms returns FALSE
T0818 002:163.656 JLINK_HasError()
T0818 002:165.174 JLINK_IsHalted()
T0818 002:165.693 - 0.518ms returns FALSE
T0818 002:165.701 JLINK_HasError()
T0818 002:167.174 JLINK_IsHalted()
T0818 002:167.671 - 0.497ms returns FALSE
T0818 002:167.677 JLINK_HasError()
T0818 002:169.174 JLINK_IsHalted()
T0818 002:169.660 - 0.486ms returns FALSE
T0818 002:169.666 JLINK_HasError()
T0818 002:171.176 JLINK_IsHalted()
T0818 002:171.650 - 0.474ms returns FALSE
T0818 002:171.659 JLINK_HasError()
T0818 002:173.174 JLINK_IsHalted()
T0818 002:173.661 - 0.486ms returns FALSE
T0818 002:173.666 JLINK_HasError()
T0818 002:175.174 JLINK_IsHalted()
T0818 002:175.650 - 0.475ms returns FALSE
T0818 002:175.655 JLINK_HasError()
T0818 002:177.174 JLINK_IsHalted()
T0818 002:177.618 - 0.444ms returns FALSE
T0818 002:177.624 JLINK_HasError()
T0818 002:179.174 JLINK_IsHalted()
T0818 002:179.659 - 0.485ms returns FALSE
T0818 002:179.664 JLINK_HasError()
T0818 002:181.174 JLINK_IsHalted()
T0818 002:181.710 - 0.535ms returns FALSE
T0818 002:181.720 JLINK_HasError()
T0818 002:184.175 JLINK_IsHalted()
T0818 002:184.694 - 0.519ms returns FALSE
T0818 002:184.700 JLINK_HasError()
T0818 002:186.208 JLINK_IsHalted()
T0818 002:186.753 - 0.545ms returns FALSE
T0818 002:186.760 JLINK_HasError()
T0818 002:188.174 JLINK_IsHalted()
T0818 002:188.688 - 0.513ms returns FALSE
T0818 002:188.696 JLINK_HasError()
T0818 002:190.174 JLINK_IsHalted()
T0818 002:190.659 - 0.485ms returns FALSE
T0818 002:190.665 JLINK_HasError()
T0818 002:192.174 JLINK_IsHalted()
T0818 002:192.670 - 0.495ms returns FALSE
T0818 002:192.675 JLINK_HasError()
T0818 002:194.174 JLINK_IsHalted()
T0818 002:194.627 - 0.452ms returns FALSE
T0818 002:194.634 JLINK_HasError()
T0818 002:196.192 JLINK_IsHalted()
T0818 002:196.708 - 0.515ms returns FALSE
T0818 002:196.714 JLINK_HasError()
T0818 002:198.176 JLINK_IsHalted()
T0818 002:198.672 - 0.495ms returns FALSE
T0818 002:198.678 JLINK_HasError()
T0818 002:200.180 JLINK_IsHalted()
T0818 002:200.653 - 0.473ms returns FALSE
T0818 002:200.659 JLINK_HasError()
T0818 002:202.174 JLINK_IsHalted()
T0818 002:202.626 - 0.451ms returns FALSE
T0818 002:202.631 JLINK_HasError()
T0818 002:204.174 JLINK_IsHalted()
T0818 002:204.618 - 0.444ms returns FALSE
T0818 002:204.624 JLINK_HasError()
T0818 002:206.206 JLINK_IsHalted()
T0818 002:206.751 - 0.545ms returns FALSE
T0818 002:206.757 JLINK_HasError()
T0818 002:208.174 JLINK_IsHalted()
T0818 002:208.661 - 0.487ms returns FALSE
T0818 002:208.666 JLINK_HasError()
T0818 002:210.175 JLINK_IsHalted()
T0818 002:210.670 - 0.494ms returns FALSE
T0818 002:210.676 JLINK_HasError()
T0818 002:212.174 JLINK_IsHalted()
T0818 002:212.660 - 0.485ms returns FALSE
T0818 002:212.665 JLINK_HasError()
T0818 002:214.175 JLINK_IsHalted()
T0818 002:214.688 - 0.513ms returns FALSE
T0818 002:214.694 JLINK_HasError()
T0818 002:216.204 JLINK_IsHalted()
T0818 002:216.707 - 0.502ms returns FALSE
T0818 002:216.714 JLINK_HasError()
T0818 002:218.174 JLINK_IsHalted()
T0818 002:220.453   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:220.922 - 2.747ms returns TRUE
T0818 002:220.928 JLINK_ReadReg(R15 (PC))
T0818 002:220.933 - 0.004ms returns 0x20000000
T0818 002:220.937 JLINK_ClrBPEx(BPHandle = 0x00000009)
T0818 002:220.941 - 0.003ms returns 0x00
T0818 002:220.945 JLINK_ReadReg(R0)
T0818 002:220.949 - 0.003ms returns 0x00000000
T0818 002:221.207 JLINK_HasError()
T0818 002:221.215 JLINK_WriteReg(R0, 0x00000001)
T0818 002:221.220 - 0.004ms returns 0
T0818 002:221.224 JLINK_WriteReg(R1, 0x00004000)
T0818 002:221.228 - 0.003ms returns 0
T0818 002:221.232 JLINK_WriteReg(R2, 0x000000FF)
T0818 002:221.235 - 0.003ms returns 0
T0818 002:221.239 JLINK_WriteReg(R3, 0x00000000)
T0818 002:221.243 - 0.003ms returns 0
T0818 002:221.247 JLINK_WriteReg(R4, 0x00000000)
T0818 002:221.250 - 0.003ms returns 0
T0818 002:221.254 JLINK_WriteReg(R5, 0x00000000)
T0818 002:221.258 - 0.003ms returns 0
T0818 002:221.262 JLINK_WriteReg(R6, 0x00000000)
T0818 002:221.265 - 0.003ms returns 0
T0818 002:221.269 JLINK_WriteReg(R7, 0x00000000)
T0818 002:221.272 - 0.003ms returns 0
T0818 002:221.276 JLINK_WriteReg(R8, 0x00000000)
T0818 002:221.280 - 0.003ms returns 0
T0818 002:221.284 JLINK_WriteReg(R9, 0x20000180)
T0818 002:221.287 - 0.003ms returns 0
T0818 002:221.291 JLINK_WriteReg(R10, 0x00000000)
T0818 002:221.295 - 0.003ms returns 0
T0818 002:221.299 JLINK_WriteReg(R11, 0x00000000)
T0818 002:221.302 - 0.003ms returns 0
T0818 002:221.306 JLINK_WriteReg(R12, 0x00000000)
T0818 002:221.309 - 0.003ms returns 0
T0818 002:221.314 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:221.317 - 0.003ms returns 0
T0818 002:221.321 JLINK_WriteReg(R14, 0x20000001)
T0818 002:221.325 - 0.003ms returns 0
T0818 002:221.329 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 002:221.332 - 0.003ms returns 0
T0818 002:221.336 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:221.340 - 0.003ms returns 0
T0818 002:221.344 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:221.347 - 0.003ms returns 0
T0818 002:221.351 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:221.354 - 0.003ms returns 0
T0818 002:221.358 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:221.367 - 0.008ms returns 0
T0818 002:221.372 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:221.376 - 0.004ms returns 0x0000000A
T0818 002:221.380 JLINK_Go()
T0818 002:221.389   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:224.187 - 2.806ms 
T0818 002:224.195 JLINK_IsHalted()
T0818 002:226.455   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:227.690 - 3.494ms returns TRUE
T0818 002:227.704 JLINK_ReadReg(R15 (PC))
T0818 002:227.709 - 0.004ms returns 0x20000000
T0818 002:227.713 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T0818 002:227.717 - 0.004ms returns 0x00
T0818 002:227.724 JLINK_ReadReg(R0)
T0818 002:227.729 - 0.004ms returns 0x00000000
T0818 002:281.635 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 002:281.649   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 002:281.663   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 002:283.516 - 1.880ms returns 0x184
T0818 002:283.549 JLINK_HasError()
T0818 002:283.555 JLINK_WriteReg(R0, 0x08000000)
T0818 002:283.560 - 0.004ms returns 0
T0818 002:283.564 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 002:283.568 - 0.003ms returns 0
T0818 002:283.572 JLINK_WriteReg(R2, 0x00000002)
T0818 002:283.575 - 0.003ms returns 0
T0818 002:283.579 JLINK_WriteReg(R3, 0x00000000)
T0818 002:283.583 - 0.003ms returns 0
T0818 002:283.587 JLINK_WriteReg(R4, 0x00000000)
T0818 002:283.590 - 0.003ms returns 0
T0818 002:283.594 JLINK_WriteReg(R5, 0x00000000)
T0818 002:283.598 - 0.003ms returns 0
T0818 002:283.602 JLINK_WriteReg(R6, 0x00000000)
T0818 002:283.605 - 0.003ms returns 0
T0818 002:283.609 JLINK_WriteReg(R7, 0x00000000)
T0818 002:283.613 - 0.003ms returns 0
T0818 002:283.617 JLINK_WriteReg(R8, 0x00000000)
T0818 002:283.620 - 0.003ms returns 0
T0818 002:283.624 JLINK_WriteReg(R9, 0x20000180)
T0818 002:283.628 - 0.003ms returns 0
T0818 002:283.632 JLINK_WriteReg(R10, 0x00000000)
T0818 002:283.635 - 0.003ms returns 0
T0818 002:283.639 JLINK_WriteReg(R11, 0x00000000)
T0818 002:283.642 - 0.003ms returns 0
T0818 002:283.646 JLINK_WriteReg(R12, 0x00000000)
T0818 002:283.650 - 0.003ms returns 0
T0818 002:283.654 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:283.658 - 0.003ms returns 0
T0818 002:283.662 JLINK_WriteReg(R14, 0x20000001)
T0818 002:283.665 - 0.003ms returns 0
T0818 002:283.669 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 002:283.672 - 0.003ms returns 0
T0818 002:283.676 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:283.680 - 0.003ms returns 0
T0818 002:283.684 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:283.687 - 0.003ms returns 0
T0818 002:283.691 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:283.694 - 0.003ms returns 0
T0818 002:283.698 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:283.702 - 0.003ms returns 0
T0818 002:283.707 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:283.714   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:284.189 - 0.482ms returns 0x0000000B
T0818 002:284.195 JLINK_Go()
T0818 002:284.199   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 002:284.664   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:287.340 - 3.145ms 
T0818 002:287.350 JLINK_IsHalted()
T0818 002:289.657   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:290.110 - 2.759ms returns TRUE
T0818 002:290.116 JLINK_ReadReg(R15 (PC))
T0818 002:290.120 - 0.004ms returns 0x20000000
T0818 002:290.124 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T0818 002:290.128 - 0.003ms returns 0x00
T0818 002:290.132 JLINK_ReadReg(R0)
T0818 002:290.135 - 0.003ms returns 0x00000000
T0818 002:290.322 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:290.328   Data:  78 17 00 20 C1 01 00 08 55 28 00 08 39 25 00 08 ...
T0818 002:290.338   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:292.924 - 2.601ms returns 0x27C
T0818 002:292.932 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:292.936   Data:  4F F0 00 0B 23 F0 00 43 50 EA 01 04 5E D0 52 EA ...
T0818 002:292.946   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:294.776 - 1.843ms returns 0x184
T0818 002:294.782 JLINK_HasError()
T0818 002:294.786 JLINK_WriteReg(R0, 0x08000000)
T0818 002:294.790 - 0.004ms returns 0
T0818 002:294.795 JLINK_WriteReg(R1, 0x00000400)
T0818 002:294.798 - 0.003ms returns 0
T0818 002:294.802 JLINK_WriteReg(R2, 0x20000184)
T0818 002:294.806 - 0.003ms returns 0
T0818 002:294.810 JLINK_WriteReg(R3, 0x00000000)
T0818 002:294.813 - 0.003ms returns 0
T0818 002:294.817 JLINK_WriteReg(R4, 0x00000000)
T0818 002:294.820 - 0.003ms returns 0
T0818 002:294.824 JLINK_WriteReg(R5, 0x00000000)
T0818 002:294.828 - 0.003ms returns 0
T0818 002:294.832 JLINK_WriteReg(R6, 0x00000000)
T0818 002:294.835 - 0.003ms returns 0
T0818 002:294.839 JLINK_WriteReg(R7, 0x00000000)
T0818 002:294.876 - 0.036ms returns 0
T0818 002:294.880 JLINK_WriteReg(R8, 0x00000000)
T0818 002:294.883 - 0.003ms returns 0
T0818 002:294.887 JLINK_WriteReg(R9, 0x20000180)
T0818 002:294.890 - 0.003ms returns 0
T0818 002:294.895 JLINK_WriteReg(R10, 0x00000000)
T0818 002:294.908 - 0.013ms returns 0
T0818 002:294.912 JLINK_WriteReg(R11, 0x00000000)
T0818 002:294.916 - 0.003ms returns 0
T0818 002:294.920 JLINK_WriteReg(R12, 0x00000000)
T0818 002:294.923 - 0.003ms returns 0
T0818 002:294.927 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:294.931 - 0.003ms returns 0
T0818 002:294.935 JLINK_WriteReg(R14, 0x20000001)
T0818 002:294.938 - 0.003ms returns 0
T0818 002:294.942 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:294.946 - 0.003ms returns 0
T0818 002:294.950 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:294.953 - 0.003ms returns 0
T0818 002:294.957 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:294.960 - 0.003ms returns 0
T0818 002:294.964 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:294.968 - 0.003ms returns 0
T0818 002:294.972 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:294.975 - 0.003ms returns 0
T0818 002:294.980 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:294.983 - 0.003ms returns 0x0000000C
T0818 002:294.987 JLINK_Go()
T0818 002:294.994   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:297.745 - 2.757ms 
T0818 002:297.756 JLINK_IsHalted()
T0818 002:298.230 - 0.474ms returns FALSE
T0818 002:298.236 JLINK_HasError()
T0818 002:302.176 JLINK_IsHalted()
T0818 002:304.482   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:304.938 - 2.761ms returns TRUE
T0818 002:304.948 JLINK_ReadReg(R15 (PC))
T0818 002:304.952 - 0.004ms returns 0x20000000
T0818 002:304.978 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T0818 002:304.983 - 0.005ms returns 0x00
T0818 002:304.987 JLINK_ReadReg(R0)
T0818 002:304.991 - 0.003ms returns 0x00000000
T0818 002:305.280 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:305.287   Data:  20 FA 03 F3 19 43 90 40 70 47 20 2A 04 DB 20 3A ...
T0818 002:305.296   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:307.892 - 2.611ms returns 0x27C
T0818 002:307.906 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:307.910   Data:  00 45 14 46 4F F0 00 0A 23 F0 00 41 50 EA 05 02 ...
T0818 002:307.919   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:309.675 - 1.768ms returns 0x184
T0818 002:309.681 JLINK_HasError()
T0818 002:309.685 JLINK_WriteReg(R0, 0x08000400)
T0818 002:309.690 - 0.005ms returns 0
T0818 002:309.694 JLINK_WriteReg(R1, 0x00000400)
T0818 002:309.698 - 0.003ms returns 0
T0818 002:309.702 JLINK_WriteReg(R2, 0x20000184)
T0818 002:309.706 - 0.003ms returns 0
T0818 002:309.710 JLINK_WriteReg(R3, 0x00000000)
T0818 002:309.713 - 0.003ms returns 0
T0818 002:309.717 JLINK_WriteReg(R4, 0x00000000)
T0818 002:309.721 - 0.003ms returns 0
T0818 002:309.725 JLINK_WriteReg(R5, 0x00000000)
T0818 002:309.728 - 0.003ms returns 0
T0818 002:309.732 JLINK_WriteReg(R6, 0x00000000)
T0818 002:309.736 - 0.003ms returns 0
T0818 002:309.740 JLINK_WriteReg(R7, 0x00000000)
T0818 002:309.743 - 0.003ms returns 0
T0818 002:309.747 JLINK_WriteReg(R8, 0x00000000)
T0818 002:309.750 - 0.003ms returns 0
T0818 002:309.754 JLINK_WriteReg(R9, 0x20000180)
T0818 002:309.758 - 0.003ms returns 0
T0818 002:309.762 JLINK_WriteReg(R10, 0x00000000)
T0818 002:309.765 - 0.003ms returns 0
T0818 002:309.769 JLINK_WriteReg(R11, 0x00000000)
T0818 002:309.772 - 0.003ms returns 0
T0818 002:309.776 JLINK_WriteReg(R12, 0x00000000)
T0818 002:309.780 - 0.003ms returns 0
T0818 002:309.784 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:309.788 - 0.004ms returns 0
T0818 002:309.792 JLINK_WriteReg(R14, 0x20000001)
T0818 002:309.795 - 0.003ms returns 0
T0818 002:309.800 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:309.803 - 0.003ms returns 0
T0818 002:309.807 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:309.810 - 0.003ms returns 0
T0818 002:309.814 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:309.818 - 0.003ms returns 0
T0818 002:309.822 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:309.853 - 0.031ms returns 0
T0818 002:309.858 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:309.862 - 0.003ms returns 0
T0818 002:309.866 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:309.870 - 0.004ms returns 0x0000000D
T0818 002:309.874 JLINK_Go()
T0818 002:309.882   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:312.636 - 2.761ms 
T0818 002:312.644 JLINK_IsHalted()
T0818 002:313.126 - 0.482ms returns FALSE
T0818 002:313.132 JLINK_HasError()
T0818 002:314.174 JLINK_IsHalted()
T0818 002:314.672 - 0.497ms returns FALSE
T0818 002:314.677 JLINK_HasError()
T0818 002:316.193 JLINK_IsHalted()
T0818 002:316.850 - 0.656ms returns FALSE
T0818 002:316.858 JLINK_HasError()
T0818 002:318.176 JLINK_IsHalted()
T0818 002:320.450   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:320.944 - 2.768ms returns TRUE
T0818 002:320.950 JLINK_ReadReg(R15 (PC))
T0818 002:320.954 - 0.004ms returns 0x20000000
T0818 002:320.958 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T0818 002:320.962 - 0.003ms returns 0x00
T0818 002:320.966 JLINK_ReadReg(R0)
T0818 002:320.970 - 0.003ms returns 0x00000000
T0818 002:321.298 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:321.306   Data:  00 29 A8 BF 70 47 40 1C 49 00 08 BF 20 F0 01 00 ...
T0818 002:321.314   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:323.890 - 2.592ms returns 0x27C
T0818 002:323.900 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:323.903   Data:  00 00 00 00 2D E9 F0 4F 81 B0 41 F6 00 49 C4 F2 ...
T0818 002:323.912   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:325.812 - 1.912ms returns 0x184
T0818 002:325.818 JLINK_HasError()
T0818 002:325.822 JLINK_WriteReg(R0, 0x08000800)
T0818 002:325.826 - 0.004ms returns 0
T0818 002:325.830 JLINK_WriteReg(R1, 0x00000400)
T0818 002:325.834 - 0.003ms returns 0
T0818 002:325.838 JLINK_WriteReg(R2, 0x20000184)
T0818 002:325.841 - 0.003ms returns 0
T0818 002:325.846 JLINK_WriteReg(R3, 0x00000000)
T0818 002:325.849 - 0.003ms returns 0
T0818 002:325.853 JLINK_WriteReg(R4, 0x00000000)
T0818 002:325.857 - 0.003ms returns 0
T0818 002:325.861 JLINK_WriteReg(R5, 0x00000000)
T0818 002:325.864 - 0.003ms returns 0
T0818 002:325.868 JLINK_WriteReg(R6, 0x00000000)
T0818 002:325.871 - 0.003ms returns 0
T0818 002:325.875 JLINK_WriteReg(R7, 0x00000000)
T0818 002:325.879 - 0.003ms returns 0
T0818 002:325.883 JLINK_WriteReg(R8, 0x00000000)
T0818 002:325.886 - 0.003ms returns 0
T0818 002:325.890 JLINK_WriteReg(R9, 0x20000180)
T0818 002:325.894 - 0.003ms returns 0
T0818 002:325.898 JLINK_WriteReg(R10, 0x00000000)
T0818 002:325.901 - 0.003ms returns 0
T0818 002:325.905 JLINK_WriteReg(R11, 0x00000000)
T0818 002:325.908 - 0.003ms returns 0
T0818 002:325.912 JLINK_WriteReg(R12, 0x00000000)
T0818 002:325.916 - 0.003ms returns 0
T0818 002:325.920 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:325.924 - 0.003ms returns 0
T0818 002:325.928 JLINK_WriteReg(R14, 0x20000001)
T0818 002:325.931 - 0.003ms returns 0
T0818 002:325.935 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:325.939 - 0.003ms returns 0
T0818 002:325.943 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:325.946 - 0.003ms returns 0
T0818 002:325.950 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:325.954 - 0.003ms returns 0
T0818 002:325.958 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:325.961 - 0.003ms returns 0
T0818 002:325.965 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:325.968 - 0.003ms returns 0
T0818 002:325.973 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:325.977 - 0.004ms returns 0x0000000E
T0818 002:325.981 JLINK_Go()
T0818 002:325.987   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:328.734 - 2.752ms 
T0818 002:328.746 JLINK_IsHalted()
T0818 002:329.232 - 0.485ms returns FALSE
T0818 002:329.241 JLINK_HasError()
T0818 002:331.174 JLINK_IsHalted()
T0818 002:331.625 - 0.450ms returns FALSE
T0818 002:331.630 JLINK_HasError()
T0818 002:333.174 JLINK_IsHalted()
T0818 002:335.451   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:335.937 - 2.762ms returns TRUE
T0818 002:335.943 JLINK_ReadReg(R15 (PC))
T0818 002:335.970 - 0.027ms returns 0x20000000
T0818 002:335.976 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T0818 002:335.979 - 0.003ms returns 0x00
T0818 002:335.984 JLINK_ReadReg(R0)
T0818 002:335.987 - 0.003ms returns 0x00000000
T0818 002:336.481 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:336.489   Data:  02 04 04 F5 A0 56 05 46 C2 09 30 46 4F F4 80 51 ...
T0818 002:336.499   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:339.336 - 2.854ms returns 0x27C
T0818 002:339.349 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:339.353   Data:  FF F7 DA FF 94 ED 02 0A 9F ED 31 8A 9F ED 31 9A ...
T0818 002:339.362   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:341.232 - 1.883ms returns 0x184
T0818 002:341.242 JLINK_HasError()
T0818 002:341.248 JLINK_WriteReg(R0, 0x08000C00)
T0818 002:341.252 - 0.005ms returns 0
T0818 002:341.257 JLINK_WriteReg(R1, 0x00000400)
T0818 002:341.261 - 0.003ms returns 0
T0818 002:341.265 JLINK_WriteReg(R2, 0x20000184)
T0818 002:341.268 - 0.003ms returns 0
T0818 002:341.272 JLINK_WriteReg(R3, 0x00000000)
T0818 002:341.275 - 0.003ms returns 0
T0818 002:341.279 JLINK_WriteReg(R4, 0x00000000)
T0818 002:341.283 - 0.003ms returns 0
T0818 002:341.287 JLINK_WriteReg(R5, 0x00000000)
T0818 002:341.290 - 0.003ms returns 0
T0818 002:341.294 JLINK_WriteReg(R6, 0x00000000)
T0818 002:341.298 - 0.003ms returns 0
T0818 002:341.302 JLINK_WriteReg(R7, 0x00000000)
T0818 002:341.305 - 0.003ms returns 0
T0818 002:341.309 JLINK_WriteReg(R8, 0x00000000)
T0818 002:341.312 - 0.003ms returns 0
T0818 002:341.316 JLINK_WriteReg(R9, 0x20000180)
T0818 002:341.320 - 0.003ms returns 0
T0818 002:341.324 JLINK_WriteReg(R10, 0x00000000)
T0818 002:341.327 - 0.003ms returns 0
T0818 002:341.331 JLINK_WriteReg(R11, 0x00000000)
T0818 002:341.334 - 0.003ms returns 0
T0818 002:341.338 JLINK_WriteReg(R12, 0x00000000)
T0818 002:341.342 - 0.003ms returns 0
T0818 002:341.346 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:341.350 - 0.003ms returns 0
T0818 002:341.354 JLINK_WriteReg(R14, 0x20000001)
T0818 002:341.357 - 0.003ms returns 0
T0818 002:341.361 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:341.365 - 0.003ms returns 0
T0818 002:341.369 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:341.372 - 0.003ms returns 0
T0818 002:341.376 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:341.380 - 0.003ms returns 0
T0818 002:341.384 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:341.387 - 0.003ms returns 0
T0818 002:341.391 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:341.394 - 0.003ms returns 0
T0818 002:341.399 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:341.403 - 0.004ms returns 0x0000000F
T0818 002:341.412 JLINK_Go()
T0818 002:341.419   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:344.171 - 2.759ms 
T0818 002:344.178 JLINK_IsHalted()
T0818 002:344.636 - 0.457ms returns FALSE
T0818 002:344.643 JLINK_HasError()
T0818 002:346.330 JLINK_IsHalted()
T0818 002:347.240 - 0.909ms returns FALSE
T0818 002:347.252 JLINK_HasError()
T0818 002:349.177 JLINK_IsHalted()
T0818 002:351.517   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:351.990 - 2.812ms returns TRUE
T0818 002:351.996 JLINK_ReadReg(R15 (PC))
T0818 002:352.000 - 0.004ms returns 0x20000000
T0818 002:352.005 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T0818 002:352.009 - 0.004ms returns 0x00
T0818 002:352.013 JLINK_ReadReg(R0)
T0818 002:352.017 - 0.003ms returns 0x00000000
T0818 002:352.518 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:352.529   Data:  18 01 00 EB 40 00 C2 F2 00 01 01 EB 80 00 80 ED ...
T0818 002:352.539   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:356.347 - 3.828ms returns 0x27C
T0818 002:356.360 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:356.364   Data:  C0 07 0E D0 00 F0 14 F9 40 1B 06 28 F6 D3 20 20 ...
T0818 002:356.373   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:358.339 - 1.979ms returns 0x184
T0818 002:358.357 JLINK_HasError()
T0818 002:358.363 JLINK_WriteReg(R0, 0x08001000)
T0818 002:358.368 - 0.005ms returns 0
T0818 002:358.398 JLINK_WriteReg(R1, 0x00000400)
T0818 002:358.433 - 0.034ms returns 0
T0818 002:358.439 JLINK_WriteReg(R2, 0x20000184)
T0818 002:358.442 - 0.003ms returns 0
T0818 002:358.447 JLINK_WriteReg(R3, 0x00000000)
T0818 002:358.450 - 0.003ms returns 0
T0818 002:358.454 JLINK_WriteReg(R4, 0x00000000)
T0818 002:358.458 - 0.003ms returns 0
T0818 002:358.462 JLINK_WriteReg(R5, 0x00000000)
T0818 002:358.465 - 0.003ms returns 0
T0818 002:358.469 JLINK_WriteReg(R6, 0x00000000)
T0818 002:358.472 - 0.003ms returns 0
T0818 002:358.476 JLINK_WriteReg(R7, 0x00000000)
T0818 002:358.480 - 0.003ms returns 0
T0818 002:358.484 JLINK_WriteReg(R8, 0x00000000)
T0818 002:358.487 - 0.003ms returns 0
T0818 002:358.491 JLINK_WriteReg(R9, 0x20000180)
T0818 002:358.495 - 0.003ms returns 0
T0818 002:358.499 JLINK_WriteReg(R10, 0x00000000)
T0818 002:358.502 - 0.003ms returns 0
T0818 002:358.506 JLINK_WriteReg(R11, 0x00000000)
T0818 002:358.509 - 0.003ms returns 0
T0818 002:358.513 JLINK_WriteReg(R12, 0x00000000)
T0818 002:358.517 - 0.003ms returns 0
T0818 002:358.521 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:358.525 - 0.004ms returns 0
T0818 002:358.529 JLINK_WriteReg(R14, 0x20000001)
T0818 002:358.532 - 0.003ms returns 0
T0818 002:358.536 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:358.540 - 0.003ms returns 0
T0818 002:358.544 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:358.547 - 0.003ms returns 0
T0818 002:358.551 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:358.555 - 0.003ms returns 0
T0818 002:358.559 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:358.562 - 0.003ms returns 0
T0818 002:358.566 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:358.570 - 0.003ms returns 0
T0818 002:358.574 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:358.579 - 0.004ms returns 0x00000010
T0818 002:358.583 JLINK_Go()
T0818 002:358.592   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:361.347 - 2.763ms 
T0818 002:361.370 JLINK_IsHalted()
T0818 002:361.858 - 0.487ms returns FALSE
T0818 002:361.864 JLINK_HasError()
T0818 002:363.178 JLINK_IsHalted()
T0818 002:363.672 - 0.493ms returns FALSE
T0818 002:363.679 JLINK_HasError()
T0818 002:365.180 JLINK_IsHalted()
T0818 002:365.654 - 0.474ms returns FALSE
T0818 002:365.661 JLINK_HasError()
T0818 002:367.178 JLINK_IsHalted()
T0818 002:369.508   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:370.000 - 2.822ms returns TRUE
T0818 002:370.008 JLINK_ReadReg(R15 (PC))
T0818 002:370.013 - 0.005ms returns 0x20000000
T0818 002:370.018 JLINK_ClrBPEx(BPHandle = 0x00000010)
T0818 002:370.022 - 0.004ms returns 0x00
T0818 002:370.026 JLINK_ReadReg(R0)
T0818 002:370.030 - 0.003ms returns 0x00000000
T0818 002:370.421 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:370.430   Data:  1A 68 43 F6 00 44 42 F4 80 42 1A 60 1A 68 C4 F2 ...
T0818 002:370.453   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:373.075 - 2.653ms returns 0x27C
T0818 002:373.084 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:373.088   Data:  F6 D9 0E E0 01 20 70 60 FF F7 12 FF 04 46 00 BF ...
T0818 002:373.096   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:374.971 - 1.886ms returns 0x184
T0818 002:374.978 JLINK_HasError()
T0818 002:374.983 JLINK_WriteReg(R0, 0x08001400)
T0818 002:374.988 - 0.005ms returns 0
T0818 002:374.992 JLINK_WriteReg(R1, 0x00000400)
T0818 002:374.995 - 0.003ms returns 0
T0818 002:374.999 JLINK_WriteReg(R2, 0x20000184)
T0818 002:375.003 - 0.003ms returns 0
T0818 002:375.007 JLINK_WriteReg(R3, 0x00000000)
T0818 002:375.010 - 0.003ms returns 0
T0818 002:375.015 JLINK_WriteReg(R4, 0x00000000)
T0818 002:375.018 - 0.003ms returns 0
T0818 002:375.022 JLINK_WriteReg(R5, 0x00000000)
T0818 002:375.025 - 0.003ms returns 0
T0818 002:375.029 JLINK_WriteReg(R6, 0x00000000)
T0818 002:375.033 - 0.003ms returns 0
T0818 002:375.037 JLINK_WriteReg(R7, 0x00000000)
T0818 002:375.040 - 0.003ms returns 0
T0818 002:375.044 JLINK_WriteReg(R8, 0x00000000)
T0818 002:375.048 - 0.003ms returns 0
T0818 002:375.052 JLINK_WriteReg(R9, 0x20000180)
T0818 002:375.055 - 0.003ms returns 0
T0818 002:375.059 JLINK_WriteReg(R10, 0x00000000)
T0818 002:375.109 - 0.049ms returns 0
T0818 002:375.116 JLINK_WriteReg(R11, 0x00000000)
T0818 002:375.119 - 0.003ms returns 0
T0818 002:375.123 JLINK_WriteReg(R12, 0x00000000)
T0818 002:375.127 - 0.003ms returns 0
T0818 002:375.131 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:375.135 - 0.004ms returns 0
T0818 002:375.139 JLINK_WriteReg(R14, 0x20000001)
T0818 002:375.142 - 0.003ms returns 0
T0818 002:375.146 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:375.150 - 0.003ms returns 0
T0818 002:375.154 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:375.158 - 0.003ms returns 0
T0818 002:375.162 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:375.165 - 0.003ms returns 0
T0818 002:375.169 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:375.172 - 0.003ms returns 0
T0818 002:375.176 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:375.180 - 0.003ms returns 0
T0818 002:375.188 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:375.192 - 0.004ms returns 0x00000011
T0818 002:375.196 JLINK_Go()
T0818 002:375.205   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:377.971 - 2.773ms 
T0818 002:377.984 JLINK_IsHalted()
T0818 002:378.481 - 0.497ms returns FALSE
T0818 002:378.487 JLINK_HasError()
T0818 002:380.185 JLINK_IsHalted()
T0818 002:380.653 - 0.468ms returns FALSE
T0818 002:380.659 JLINK_HasError()
T0818 002:382.180 JLINK_IsHalted()
T0818 002:384.516   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:385.034 - 2.853ms returns TRUE
T0818 002:385.052 JLINK_ReadReg(R15 (PC))
T0818 002:385.058 - 0.006ms returns 0x20000000
T0818 002:385.672 JLINK_ClrBPEx(BPHandle = 0x00000011)
T0818 002:385.684 - 0.012ms returns 0x00
T0818 002:385.689 JLINK_ReadReg(R0)
T0818 002:385.694 - 0.004ms returns 0x00000000
T0818 002:386.076 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:386.084   Data:  FF F7 54 FE 80 1B B8 42 F2 D9 03 20 BD E8 F0 81 ...
T0818 002:386.366   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:388.968 - 2.892ms returns 0x27C
T0818 002:388.995 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:388.999   Data:  B0 E0 30 6C 5F EA C0 08 0A D4 00 20 01 90 30 6C ...
T0818 002:389.011   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:390.860 - 1.865ms returns 0x184
T0818 002:390.869 JLINK_HasError()
T0818 002:390.874 JLINK_WriteReg(R0, 0x08001800)
T0818 002:390.879 - 0.005ms returns 0
T0818 002:390.884 JLINK_WriteReg(R1, 0x00000400)
T0818 002:390.887 - 0.003ms returns 0
T0818 002:390.891 JLINK_WriteReg(R2, 0x20000184)
T0818 002:390.894 - 0.003ms returns 0
T0818 002:390.899 JLINK_WriteReg(R3, 0x00000000)
T0818 002:390.902 - 0.003ms returns 0
T0818 002:390.906 JLINK_WriteReg(R4, 0x00000000)
T0818 002:390.909 - 0.003ms returns 0
T0818 002:390.914 JLINK_WriteReg(R5, 0x00000000)
T0818 002:390.918 - 0.003ms returns 0
T0818 002:390.925 JLINK_WriteReg(R6, 0x00000000)
T0818 002:390.930 - 0.005ms returns 0
T0818 002:390.934 JLINK_WriteReg(R7, 0x00000000)
T0818 002:390.938 - 0.003ms returns 0
T0818 002:390.942 JLINK_WriteReg(R8, 0x00000000)
T0818 002:390.945 - 0.003ms returns 0
T0818 002:390.950 JLINK_WriteReg(R9, 0x20000180)
T0818 002:390.953 - 0.003ms returns 0
T0818 002:390.957 JLINK_WriteReg(R10, 0x00000000)
T0818 002:390.960 - 0.003ms returns 0
T0818 002:390.964 JLINK_WriteReg(R11, 0x00000000)
T0818 002:390.968 - 0.003ms returns 0
T0818 002:390.972 JLINK_WriteReg(R12, 0x00000000)
T0818 002:390.983 - 0.010ms returns 0
T0818 002:390.987 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:390.991 - 0.003ms returns 0
T0818 002:390.995 JLINK_WriteReg(R14, 0x20000001)
T0818 002:390.998 - 0.003ms returns 0
T0818 002:391.002 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:391.006 - 0.003ms returns 0
T0818 002:391.010 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:391.013 - 0.003ms returns 0
T0818 002:391.017 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:391.021 - 0.003ms returns 0
T0818 002:391.025 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:391.028 - 0.003ms returns 0
T0818 002:391.032 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:391.036 - 0.003ms returns 0
T0818 002:391.040 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:391.052 - 0.011ms returns 0x00000012
T0818 002:391.056 JLINK_Go()
T0818 002:391.064   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:393.912 - 2.855ms 
T0818 002:393.924 JLINK_IsHalted()
T0818 002:394.504 - 0.579ms returns FALSE
T0818 002:394.510 JLINK_HasError()
T0818 002:396.180 JLINK_IsHalted()
T0818 002:396.662 - 0.482ms returns FALSE
T0818 002:396.670 JLINK_HasError()
T0818 002:397.800 JLINK_IsHalted()
T0818 002:398.256 - 0.456ms returns FALSE
T0818 002:398.262 JLINK_HasError()
T0818 002:400.349 JLINK_IsHalted()
T0818 002:402.694   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:403.190 - 2.841ms returns TRUE
T0818 002:403.197 JLINK_ReadReg(R15 (PC))
T0818 002:403.202 - 0.005ms returns 0x20000000
T0818 002:403.206 JLINK_ClrBPEx(BPHandle = 0x00000012)
T0818 002:403.211 - 0.004ms returns 0x00
T0818 002:403.216 JLINK_ReadReg(R0)
T0818 002:403.219 - 0.003ms returns 0x00000000
T0818 002:403.582 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:403.590   Data:  3F F5 F6 AE 30 6C 20 F0 80 50 30 64 F0 E6 04 F1 ...
T0818 002:403.601   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:406.118 - 2.536ms returns 0x27C
T0818 002:406.128 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:406.132   Data:  00 F0 5A BB 81 6B 0A 68 52 68 93 B2 00 2B B9 D0 ...
T0818 002:406.140   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:408.077 - 1.947ms returns 0x184
T0818 002:408.098 JLINK_HasError()
T0818 002:408.104 JLINK_WriteReg(R0, 0x08001C00)
T0818 002:408.111 - 0.007ms returns 0
T0818 002:408.116 JLINK_WriteReg(R1, 0x00000400)
T0818 002:408.119 - 0.003ms returns 0
T0818 002:408.123 JLINK_WriteReg(R2, 0x20000184)
T0818 002:408.127 - 0.003ms returns 0
T0818 002:408.131 JLINK_WriteReg(R3, 0x00000000)
T0818 002:408.134 - 0.003ms returns 0
T0818 002:408.138 JLINK_WriteReg(R4, 0x00000000)
T0818 002:408.142 - 0.004ms returns 0
T0818 002:408.146 JLINK_WriteReg(R5, 0x00000000)
T0818 002:408.150 - 0.003ms returns 0
T0818 002:408.154 JLINK_WriteReg(R6, 0x00000000)
T0818 002:408.157 - 0.003ms returns 0
T0818 002:408.161 JLINK_WriteReg(R7, 0x00000000)
T0818 002:408.164 - 0.003ms returns 0
T0818 002:408.168 JLINK_WriteReg(R8, 0x00000000)
T0818 002:408.172 - 0.003ms returns 0
T0818 002:408.176 JLINK_WriteReg(R9, 0x20000180)
T0818 002:408.180 - 0.004ms returns 0
T0818 002:408.192 JLINK_WriteReg(R10, 0x00000000)
T0818 002:408.196 - 0.003ms returns 0
T0818 002:408.200 JLINK_WriteReg(R11, 0x00000000)
T0818 002:408.203 - 0.003ms returns 0
T0818 002:408.207 JLINK_WriteReg(R12, 0x00000000)
T0818 002:408.210 - 0.003ms returns 0
T0818 002:408.214 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:408.218 - 0.003ms returns 0
T0818 002:408.222 JLINK_WriteReg(R14, 0x20000001)
T0818 002:408.226 - 0.003ms returns 0
T0818 002:408.230 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:408.238 - 0.008ms returns 0
T0818 002:408.242 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:408.245 - 0.003ms returns 0
T0818 002:408.249 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:408.253 - 0.003ms returns 0
T0818 002:408.257 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:408.260 - 0.003ms returns 0
T0818 002:408.264 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:408.268 - 0.003ms returns 0
T0818 002:408.272 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:408.277 - 0.004ms returns 0x00000013
T0818 002:408.281 JLINK_Go()
T0818 002:408.292   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:411.051 - 2.770ms 
T0818 002:411.067 JLINK_IsHalted()
T0818 002:411.547 - 0.480ms returns FALSE
T0818 002:411.555 JLINK_HasError()
T0818 002:413.347 JLINK_IsHalted()
T0818 002:413.800 - 0.453ms returns FALSE
T0818 002:413.808 JLINK_HasError()
T0818 002:415.348 JLINK_IsHalted()
T0818 002:417.830   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:418.338 - 2.990ms returns TRUE
T0818 002:418.347 JLINK_ReadReg(R15 (PC))
T0818 002:418.352 - 0.005ms returns 0x20000000
T0818 002:418.356 JLINK_ClrBPEx(BPHandle = 0x00000013)
T0818 002:418.360 - 0.004ms returns 0x00
T0818 002:418.365 JLINK_ReadReg(R0)
T0818 002:418.377 - 0.011ms returns 0x00000000
T0818 002:418.760 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:418.768   Data:  01 6D 02 B0 BD E8 B0 40 08 47 04 46 FF F7 C0 FE ...
T0818 002:418.780   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:421.417 - 2.657ms returns 0x27C
T0818 002:421.427 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:421.431   Data:  C2 F2 00 00 C1 88 7E 29 12 D8 C3 78 40 F2 F0 1C ...
T0818 002:421.438   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:423.345 - 1.917ms returns 0x184
T0818 002:423.353 JLINK_HasError()
T0818 002:423.358 JLINK_WriteReg(R0, 0x08002000)
T0818 002:423.362 - 0.004ms returns 0
T0818 002:423.366 JLINK_WriteReg(R1, 0x00000400)
T0818 002:423.370 - 0.003ms returns 0
T0818 002:423.374 JLINK_WriteReg(R2, 0x20000184)
T0818 002:423.378 - 0.003ms returns 0
T0818 002:423.382 JLINK_WriteReg(R3, 0x00000000)
T0818 002:423.385 - 0.003ms returns 0
T0818 002:423.389 JLINK_WriteReg(R4, 0x00000000)
T0818 002:423.392 - 0.003ms returns 0
T0818 002:423.396 JLINK_WriteReg(R5, 0x00000000)
T0818 002:423.400 - 0.003ms returns 0
T0818 002:423.404 JLINK_WriteReg(R6, 0x00000000)
T0818 002:423.407 - 0.003ms returns 0
T0818 002:423.411 JLINK_WriteReg(R7, 0x00000000)
T0818 002:423.415 - 0.003ms returns 0
T0818 002:423.419 JLINK_WriteReg(R8, 0x00000000)
T0818 002:423.422 - 0.003ms returns 0
T0818 002:423.426 JLINK_WriteReg(R9, 0x20000180)
T0818 002:423.430 - 0.003ms returns 0
T0818 002:423.434 JLINK_WriteReg(R10, 0x00000000)
T0818 002:423.437 - 0.003ms returns 0
T0818 002:423.441 JLINK_WriteReg(R11, 0x00000000)
T0818 002:423.444 - 0.003ms returns 0
T0818 002:423.448 JLINK_WriteReg(R12, 0x00000000)
T0818 002:423.452 - 0.003ms returns 0
T0818 002:423.456 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:423.460 - 0.004ms returns 0
T0818 002:423.476 JLINK_WriteReg(R14, 0x20000001)
T0818 002:423.479 - 0.003ms returns 0
T0818 002:423.484 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:423.487 - 0.003ms returns 0
T0818 002:423.491 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:423.494 - 0.003ms returns 0
T0818 002:423.498 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:423.502 - 0.003ms returns 0
T0818 002:423.506 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:423.509 - 0.003ms returns 0
T0818 002:423.513 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:423.516 - 0.003ms returns 0
T0818 002:423.521 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:423.525 - 0.004ms returns 0x00000014
T0818 002:423.529 JLINK_Go()
T0818 002:423.537   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:426.346 - 2.816ms 
T0818 002:426.356 JLINK_IsHalted()
T0818 002:426.862 - 0.505ms returns FALSE
T0818 002:426.878 JLINK_HasError()
T0818 002:428.348 JLINK_IsHalted()
T0818 002:428.815 - 0.466ms returns FALSE
T0818 002:428.822 JLINK_HasError()
T0818 002:430.349 JLINK_IsHalted()
T0818 002:432.703   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:433.200 - 2.850ms returns TRUE
T0818 002:433.208 JLINK_ReadReg(R15 (PC))
T0818 002:433.213 - 0.005ms returns 0x20000000
T0818 002:433.217 JLINK_ClrBPEx(BPHandle = 0x00000014)
T0818 002:433.221 - 0.004ms returns 0x00
T0818 002:433.226 JLINK_ReadReg(R0)
T0818 002:433.229 - 0.003ms returns 0x00000000
T0818 002:433.624 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:433.633   Data:  FF F7 54 F8 A5 84 E5 84 A1 68 80 46 B1 F5 80 5F ...
T0818 002:433.644   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:436.343 - 2.719ms returns 0x27C
T0818 002:436.362 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:436.366   Data:  01 68 41 F0 10 01 01 60 01 68 01 F0 10 01 01 91 ...
T0818 002:436.377   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:438.264 - 1.901ms returns 0x184
T0818 002:438.290 JLINK_HasError()
T0818 002:438.328 JLINK_WriteReg(R0, 0x08002400)
T0818 002:438.336 - 0.008ms returns 0
T0818 002:438.340 JLINK_WriteReg(R1, 0x00000400)
T0818 002:438.344 - 0.003ms returns 0
T0818 002:438.348 JLINK_WriteReg(R2, 0x20000184)
T0818 002:438.351 - 0.003ms returns 0
T0818 002:438.355 JLINK_WriteReg(R3, 0x00000000)
T0818 002:438.364 - 0.009ms returns 0
T0818 002:438.370 JLINK_WriteReg(R4, 0x00000000)
T0818 002:438.374 - 0.003ms returns 0
T0818 002:438.378 JLINK_WriteReg(R5, 0x00000000)
T0818 002:438.382 - 0.003ms returns 0
T0818 002:438.385 JLINK_WriteReg(R6, 0x00000000)
T0818 002:438.389 - 0.003ms returns 0
T0818 002:438.393 JLINK_WriteReg(R7, 0x00000000)
T0818 002:438.396 - 0.003ms returns 0
T0818 002:438.400 JLINK_WriteReg(R8, 0x00000000)
T0818 002:438.404 - 0.003ms returns 0
T0818 002:438.408 JLINK_WriteReg(R9, 0x20000180)
T0818 002:438.412 - 0.003ms returns 0
T0818 002:438.416 JLINK_WriteReg(R10, 0x00000000)
T0818 002:438.419 - 0.003ms returns 0
T0818 002:438.423 JLINK_WriteReg(R11, 0x00000000)
T0818 002:438.426 - 0.003ms returns 0
T0818 002:438.431 JLINK_WriteReg(R12, 0x00000000)
T0818 002:438.435 - 0.003ms returns 0
T0818 002:438.439 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:438.443 - 0.004ms returns 0
T0818 002:438.447 JLINK_WriteReg(R14, 0x20000001)
T0818 002:438.450 - 0.003ms returns 0
T0818 002:438.455 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:438.458 - 0.003ms returns 0
T0818 002:438.462 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:438.466 - 0.003ms returns 0
T0818 002:438.470 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:438.473 - 0.003ms returns 0
T0818 002:438.477 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:438.480 - 0.003ms returns 0
T0818 002:438.485 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:438.488 - 0.003ms returns 0
T0818 002:438.493 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:438.497 - 0.004ms returns 0x00000015
T0818 002:438.501 JLINK_Go()
T0818 002:438.512   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:441.265 - 2.763ms 
T0818 002:441.274 JLINK_IsHalted()
T0818 002:441.758 - 0.483ms returns FALSE
T0818 002:441.774 JLINK_HasError()
T0818 002:443.350 JLINK_IsHalted()
T0818 002:443.885 - 0.534ms returns FALSE
T0818 002:443.896 JLINK_HasError()
T0818 002:445.348 JLINK_IsHalted()
T0818 002:447.621   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:448.168 - 2.819ms returns TRUE
T0818 002:448.177 JLINK_ReadReg(R15 (PC))
T0818 002:448.182 - 0.005ms returns 0x20000000
T0818 002:448.187 JLINK_ClrBPEx(BPHandle = 0x00000015)
T0818 002:448.191 - 0.003ms returns 0x00
T0818 002:448.195 JLINK_ReadReg(R0)
T0818 002:448.199 - 0.003ms returns 0x00000000
T0818 002:448.602 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:448.613   Data:  C3 61 FF F7 43 FC 00 28 08 BF 80 BD BD E8 80 40 ...
T0818 002:448.624   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:451.211 - 2.609ms returns 0x27C
T0818 002:451.221 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:451.225   Data:  10 80 72 B6 FE E7 00 BF 0F 20 01 90 02 20 02 90 ...
T0818 002:451.234   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:453.074 - 1.852ms returns 0x184
T0818 002:453.083 JLINK_HasError()
T0818 002:453.088 JLINK_WriteReg(R0, 0x08002800)
T0818 002:453.093 - 0.005ms returns 0
T0818 002:453.098 JLINK_WriteReg(R1, 0x00000400)
T0818 002:453.101 - 0.003ms returns 0
T0818 002:453.105 JLINK_WriteReg(R2, 0x20000184)
T0818 002:453.109 - 0.003ms returns 0
T0818 002:453.113 JLINK_WriteReg(R3, 0x00000000)
T0818 002:453.116 - 0.003ms returns 0
T0818 002:453.120 JLINK_WriteReg(R4, 0x00000000)
T0818 002:453.123 - 0.003ms returns 0
T0818 002:453.127 JLINK_WriteReg(R5, 0x00000000)
T0818 002:453.131 - 0.003ms returns 0
T0818 002:453.135 JLINK_WriteReg(R6, 0x00000000)
T0818 002:453.138 - 0.003ms returns 0
T0818 002:453.142 JLINK_WriteReg(R7, 0x00000000)
T0818 002:453.145 - 0.003ms returns 0
T0818 002:453.150 JLINK_WriteReg(R8, 0x00000000)
T0818 002:453.153 - 0.003ms returns 0
T0818 002:453.157 JLINK_WriteReg(R9, 0x20000180)
T0818 002:453.160 - 0.003ms returns 0
T0818 002:453.164 JLINK_WriteReg(R10, 0x00000000)
T0818 002:453.168 - 0.003ms returns 0
T0818 002:453.172 JLINK_WriteReg(R11, 0x00000000)
T0818 002:453.176 - 0.003ms returns 0
T0818 002:453.180 JLINK_WriteReg(R12, 0x00000000)
T0818 002:453.183 - 0.003ms returns 0
T0818 002:453.187 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:453.196 - 0.009ms returns 0
T0818 002:453.203 JLINK_WriteReg(R14, 0x20000001)
T0818 002:453.207 - 0.003ms returns 0
T0818 002:453.211 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:453.214 - 0.003ms returns 0
T0818 002:453.218 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:453.232 - 0.013ms returns 0
T0818 002:453.236 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:453.239 - 0.003ms returns 0
T0818 002:453.243 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:453.246 - 0.003ms returns 0
T0818 002:453.250 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:453.254 - 0.003ms returns 0
T0818 002:453.258 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:453.263 - 0.004ms returns 0x00000016
T0818 002:453.267 JLINK_Go()
T0818 002:453.275   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:455.948 - 2.681ms 
T0818 002:455.956 JLINK_IsHalted()
T0818 002:456.511 - 0.555ms returns FALSE
T0818 002:456.519 JLINK_HasError()
T0818 002:458.353 JLINK_IsHalted()
T0818 002:458.882 - 0.529ms returns FALSE
T0818 002:458.889 JLINK_HasError()
T0818 002:460.345 JLINK_IsHalted()
T0818 002:462.659   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:463.178 - 2.833ms returns TRUE
T0818 002:463.192 JLINK_ReadReg(R15 (PC))
T0818 002:463.198 - 0.006ms returns 0x20000000
T0818 002:463.239 JLINK_ClrBPEx(BPHandle = 0x00000016)
T0818 002:463.256 - 0.017ms returns 0x00
T0818 002:463.261 JLINK_ReadReg(R0)
T0818 002:463.265 - 0.004ms returns 0x00000000
T0818 002:463.671 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:463.678   Data:  F1 FA 48 F2 1F 51 C5 F2 EB 11 A0 FB 01 23 5A 09 ...
T0818 002:463.691   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:466.231 - 2.560ms returns 0x27C
T0818 002:466.244 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:466.248   Data:  10 0A FD F7 8C FA 02 46 40 F2 AC 10 0B 46 C2 F2 ...
T0818 002:466.260   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:468.162 - 1.916ms returns 0x184
T0818 002:468.234 JLINK_HasError()
T0818 002:468.242 JLINK_WriteReg(R0, 0x08002C00)
T0818 002:468.250 - 0.008ms returns 0
T0818 002:468.254 JLINK_WriteReg(R1, 0x00000400)
T0818 002:468.258 - 0.003ms returns 0
T0818 002:468.262 JLINK_WriteReg(R2, 0x20000184)
T0818 002:468.265 - 0.003ms returns 0
T0818 002:468.270 JLINK_WriteReg(R3, 0x00000000)
T0818 002:468.273 - 0.003ms returns 0
T0818 002:468.277 JLINK_WriteReg(R4, 0x00000000)
T0818 002:468.280 - 0.003ms returns 0
T0818 002:468.284 JLINK_WriteReg(R5, 0x00000000)
T0818 002:468.288 - 0.003ms returns 0
T0818 002:468.292 JLINK_WriteReg(R6, 0x00000000)
T0818 002:468.295 - 0.003ms returns 0
T0818 002:468.299 JLINK_WriteReg(R7, 0x00000000)
T0818 002:468.302 - 0.003ms returns 0
T0818 002:468.306 JLINK_WriteReg(R8, 0x00000000)
T0818 002:468.310 - 0.003ms returns 0
T0818 002:468.314 JLINK_WriteReg(R9, 0x20000180)
T0818 002:468.317 - 0.003ms returns 0
T0818 002:468.321 JLINK_WriteReg(R10, 0x00000000)
T0818 002:468.325 - 0.003ms returns 0
T0818 002:468.329 JLINK_WriteReg(R11, 0x00000000)
T0818 002:468.332 - 0.003ms returns 0
T0818 002:468.336 JLINK_WriteReg(R12, 0x00000000)
T0818 002:468.339 - 0.003ms returns 0
T0818 002:468.344 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:468.348 - 0.004ms returns 0
T0818 002:468.361 JLINK_WriteReg(R14, 0x20000001)
T0818 002:468.365 - 0.003ms returns 0
T0818 002:468.369 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:468.373 - 0.003ms returns 0
T0818 002:468.377 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:468.380 - 0.003ms returns 0
T0818 002:468.384 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:468.387 - 0.003ms returns 0
T0818 002:468.391 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:468.395 - 0.003ms returns 0
T0818 002:468.399 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:468.402 - 0.003ms returns 0
T0818 002:468.407 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:468.412 - 0.005ms returns 0x00000017
T0818 002:468.416 JLINK_Go()
T0818 002:468.426   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:471.095 - 2.674ms 
T0818 002:471.111 JLINK_IsHalted()
T0818 002:471.566 - 0.454ms returns FALSE
T0818 002:471.582 JLINK_HasError()
T0818 002:473.348 JLINK_IsHalted()
T0818 002:473.880 - 0.531ms returns FALSE
T0818 002:473.887 JLINK_HasError()
T0818 002:475.347 JLINK_IsHalted()
T0818 002:477.837   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:478.344 - 2.996ms returns TRUE
T0818 002:478.358 JLINK_ReadReg(R15 (PC))
T0818 002:478.365 - 0.006ms returns 0x20000000
T0818 002:478.369 JLINK_ClrBPEx(BPHandle = 0x00000017)
T0818 002:478.373 - 0.004ms returns 0x00
T0818 002:478.378 JLINK_ReadReg(R0)
T0818 002:478.381 - 0.003ms returns 0x00000000
T0818 002:478.780 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:478.789   Data:  FF F7 FE F8 40 F2 68 10 40 F2 43 01 C2 F2 00 00 ...
T0818 002:478.800   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:481.434 - 2.654ms returns 0x27C
T0818 002:481.449 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:481.453   Data:  BD E8 F0 9F CB F1 00 00 DF E7 04 46 00 21 40 4A ...
T0818 002:481.465   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:483.383 - 1.932ms returns 0x184
T0818 002:483.397 JLINK_HasError()
T0818 002:483.403 JLINK_WriteReg(R0, 0x08003000)
T0818 002:483.409 - 0.006ms returns 0
T0818 002:483.413 JLINK_WriteReg(R1, 0x00000400)
T0818 002:483.417 - 0.004ms returns 0
T0818 002:483.421 JLINK_WriteReg(R2, 0x20000184)
T0818 002:483.425 - 0.003ms returns 0
T0818 002:483.429 JLINK_WriteReg(R3, 0x00000000)
T0818 002:483.432 - 0.003ms returns 0
T0818 002:483.437 JLINK_WriteReg(R4, 0x00000000)
T0818 002:483.440 - 0.003ms returns 0
T0818 002:483.444 JLINK_WriteReg(R5, 0x00000000)
T0818 002:483.448 - 0.003ms returns 0
T0818 002:483.452 JLINK_WriteReg(R6, 0x00000000)
T0818 002:483.455 - 0.003ms returns 0
T0818 002:483.460 JLINK_WriteReg(R7, 0x00000000)
T0818 002:483.463 - 0.003ms returns 0
T0818 002:483.467 JLINK_WriteReg(R8, 0x00000000)
T0818 002:483.471 - 0.003ms returns 0
T0818 002:483.475 JLINK_WriteReg(R9, 0x20000180)
T0818 002:483.478 - 0.003ms returns 0
T0818 002:483.482 JLINK_WriteReg(R10, 0x00000000)
T0818 002:483.486 - 0.003ms returns 0
T0818 002:483.490 JLINK_WriteReg(R11, 0x00000000)
T0818 002:483.493 - 0.003ms returns 0
T0818 002:483.497 JLINK_WriteReg(R12, 0x00000000)
T0818 002:483.500 - 0.003ms returns 0
T0818 002:483.505 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:483.509 - 0.004ms returns 0
T0818 002:483.513 JLINK_WriteReg(R14, 0x20000001)
T0818 002:483.517 - 0.004ms returns 0
T0818 002:483.521 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:483.525 - 0.003ms returns 0
T0818 002:483.529 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:483.532 - 0.003ms returns 0
T0818 002:483.536 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:483.540 - 0.003ms returns 0
T0818 002:483.544 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:483.548 - 0.003ms returns 0
T0818 002:483.552 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:483.555 - 0.003ms returns 0
T0818 002:483.560 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:483.564 - 0.004ms returns 0x00000018
T0818 002:483.569 JLINK_Go()
T0818 002:483.579   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:486.369 - 2.798ms 
T0818 002:486.386 JLINK_IsHalted()
T0818 002:486.886 - 0.499ms returns FALSE
T0818 002:486.899 JLINK_HasError()
T0818 002:488.365 JLINK_IsHalted()
T0818 002:488.840 - 0.474ms returns FALSE
T0818 002:488.852 JLINK_HasError()
T0818 002:490.361 JLINK_IsHalted()
T0818 002:492.716   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:493.212 - 2.850ms returns TRUE
T0818 002:493.228 JLINK_ReadReg(R15 (PC))
T0818 002:493.234 - 0.005ms returns 0x20000000
T0818 002:493.238 JLINK_ClrBPEx(BPHandle = 0x00000018)
T0818 002:493.242 - 0.004ms returns 0x00
T0818 002:493.247 JLINK_ReadReg(R0)
T0818 002:493.250 - 0.003ms returns 0x00000000
T0818 002:493.658 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:493.667   Data:  50 42 44 F4 00 54 05 90 44 F0 02 04 76 1C 30 78 ...
T0818 002:493.679   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:496.361 - 2.702ms returns 0x27C
T0818 002:496.378 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:496.386   Data:  10 03 53 EA 0A 03 05 D0 0E E0 40 22 8D F8 04 20 ...
T0818 002:496.406   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:498.258 - 1.880ms returns 0x184
T0818 002:498.279 JLINK_HasError()
T0818 002:498.285 JLINK_WriteReg(R0, 0x08003400)
T0818 002:498.292 - 0.007ms returns 0
T0818 002:498.296 JLINK_WriteReg(R1, 0x00000400)
T0818 002:498.299 - 0.003ms returns 0
T0818 002:498.303 JLINK_WriteReg(R2, 0x20000184)
T0818 002:498.307 - 0.003ms returns 0
T0818 002:498.311 JLINK_WriteReg(R3, 0x00000000)
T0818 002:498.314 - 0.003ms returns 0
T0818 002:498.318 JLINK_WriteReg(R4, 0x00000000)
T0818 002:498.322 - 0.003ms returns 0
T0818 002:498.326 JLINK_WriteReg(R5, 0x00000000)
T0818 002:498.329 - 0.003ms returns 0
T0818 002:498.333 JLINK_WriteReg(R6, 0x00000000)
T0818 002:498.336 - 0.003ms returns 0
T0818 002:498.340 JLINK_WriteReg(R7, 0x00000000)
T0818 002:498.344 - 0.003ms returns 0
T0818 002:498.348 JLINK_WriteReg(R8, 0x00000000)
T0818 002:498.351 - 0.003ms returns 0
T0818 002:498.355 JLINK_WriteReg(R9, 0x20000180)
T0818 002:498.359 - 0.003ms returns 0
T0818 002:498.362 JLINK_WriteReg(R10, 0x00000000)
T0818 002:498.366 - 0.003ms returns 0
T0818 002:498.370 JLINK_WriteReg(R11, 0x00000000)
T0818 002:498.374 - 0.003ms returns 0
T0818 002:498.378 JLINK_WriteReg(R12, 0x00000000)
T0818 002:498.381 - 0.003ms returns 0
T0818 002:498.385 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:498.389 - 0.003ms returns 0
T0818 002:498.393 JLINK_WriteReg(R14, 0x20000001)
T0818 002:498.397 - 0.003ms returns 0
T0818 002:498.401 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:498.404 - 0.003ms returns 0
T0818 002:498.408 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:498.412 - 0.003ms returns 0
T0818 002:498.416 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:498.419 - 0.003ms returns 0
T0818 002:498.424 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:498.427 - 0.003ms returns 0
T0818 002:498.431 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:498.434 - 0.003ms returns 0
T0818 002:498.439 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:498.444 - 0.005ms returns 0x00000019
T0818 002:498.448 JLINK_Go()
T0818 002:498.458   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:501.202 - 2.752ms 
T0818 002:501.212 JLINK_IsHalted()
T0818 002:501.697 - 0.484ms returns FALSE
T0818 002:501.705 JLINK_HasError()
T0818 002:503.361 JLINK_IsHalted()
T0818 002:503.814 - 0.453ms returns FALSE
T0818 002:503.820 JLINK_HasError()
T0818 002:505.364 JLINK_IsHalted()
T0818 002:507.742   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:508.234 - 2.869ms returns TRUE
T0818 002:508.243 JLINK_ReadReg(R15 (PC))
T0818 002:508.248 - 0.005ms returns 0x20000000
T0818 002:508.252 JLINK_ClrBPEx(BPHandle = 0x00000019)
T0818 002:508.256 - 0.003ms returns 0x00
T0818 002:508.261 JLINK_ReadReg(R0)
T0818 002:508.264 - 0.003ms returns 0x00000000
T0818 002:508.662 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:508.673   Data:  5F EA 04 5C 02 D5 0F F2 68 2C 07 E0 5F EA C4 7C ...
T0818 002:508.684   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:511.221 - 2.558ms returns 0x27C
T0818 002:511.235 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:511.238   Data:  04 46 00 25 1E 46 17 46 88 04 04 D4 05 E0 39 46 ...
T0818 002:511.248   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:513.109 - 1.873ms returns 0x184
T0818 002:513.125 JLINK_HasError()
T0818 002:513.163 JLINK_WriteReg(R0, 0x08003800)
T0818 002:513.170 - 0.006ms returns 0
T0818 002:513.174 JLINK_WriteReg(R1, 0x00000400)
T0818 002:513.178 - 0.003ms returns 0
T0818 002:513.182 JLINK_WriteReg(R2, 0x20000184)
T0818 002:513.185 - 0.003ms returns 0
T0818 002:513.189 JLINK_WriteReg(R3, 0x00000000)
T0818 002:513.193 - 0.003ms returns 0
T0818 002:513.197 JLINK_WriteReg(R4, 0x00000000)
T0818 002:513.200 - 0.003ms returns 0
T0818 002:513.204 JLINK_WriteReg(R5, 0x00000000)
T0818 002:513.208 - 0.003ms returns 0
T0818 002:513.212 JLINK_WriteReg(R6, 0x00000000)
T0818 002:513.216 - 0.004ms returns 0
T0818 002:513.220 JLINK_WriteReg(R7, 0x00000000)
T0818 002:513.223 - 0.003ms returns 0
T0818 002:513.234 JLINK_WriteReg(R8, 0x00000000)
T0818 002:513.238 - 0.003ms returns 0
T0818 002:513.242 JLINK_WriteReg(R9, 0x20000180)
T0818 002:513.245 - 0.003ms returns 0
T0818 002:513.249 JLINK_WriteReg(R10, 0x00000000)
T0818 002:513.252 - 0.003ms returns 0
T0818 002:513.256 JLINK_WriteReg(R11, 0x00000000)
T0818 002:513.260 - 0.003ms returns 0
T0818 002:513.264 JLINK_WriteReg(R12, 0x00000000)
T0818 002:513.268 - 0.003ms returns 0
T0818 002:513.272 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:513.276 - 0.004ms returns 0
T0818 002:513.280 JLINK_WriteReg(R14, 0x20000001)
T0818 002:513.283 - 0.003ms returns 0
T0818 002:513.288 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:513.291 - 0.003ms returns 0
T0818 002:513.295 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:513.298 - 0.003ms returns 0
T0818 002:513.302 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:513.306 - 0.003ms returns 0
T0818 002:513.310 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:513.313 - 0.003ms returns 0
T0818 002:513.317 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:513.321 - 0.003ms returns 0
T0818 002:513.325 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:513.330 - 0.004ms returns 0x0000001A
T0818 002:513.334 JLINK_Go()
T0818 002:513.342   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:516.109 - 2.775ms 
T0818 002:516.119 JLINK_IsHalted()
T0818 002:516.612 - 0.492ms returns FALSE
T0818 002:516.626 JLINK_HasError()
T0818 002:519.362 JLINK_IsHalted()
T0818 002:519.845 - 0.484ms returns FALSE
T0818 002:519.856 JLINK_HasError()
T0818 002:521.365 JLINK_IsHalted()
T0818 002:523.693   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:524.234 - 2.869ms returns TRUE
T0818 002:524.248 JLINK_ReadReg(R15 (PC))
T0818 002:524.253 - 0.005ms returns 0x20000000
T0818 002:524.258 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T0818 002:524.262 - 0.004ms returns 0x00
T0818 002:524.266 JLINK_ReadReg(R0)
T0818 002:524.272 - 0.005ms returns 0x00000000
T0818 002:524.647 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:524.654   Data:  F0 00 F0 04 F0 02 F0 06 F0 01 F0 05 F0 03 F0 07 ...
T0818 002:524.664   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:527.234 - 2.587ms returns 0x27C
T0818 002:527.252 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:527.256   Data:  6C 03 6C 07 EC 00 EC 04 EC 02 EC 06 EC 01 EC 05 ...
T0818 002:527.269   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:529.116 - 1.864ms returns 0x184
T0818 002:529.130 JLINK_HasError()
T0818 002:529.136 JLINK_WriteReg(R0, 0x08003C00)
T0818 002:529.143 - 0.006ms returns 0
T0818 002:529.147 JLINK_WriteReg(R1, 0x00000400)
T0818 002:529.150 - 0.003ms returns 0
T0818 002:529.154 JLINK_WriteReg(R2, 0x20000184)
T0818 002:529.158 - 0.003ms returns 0
T0818 002:529.162 JLINK_WriteReg(R3, 0x00000000)
T0818 002:529.165 - 0.003ms returns 0
T0818 002:529.169 JLINK_WriteReg(R4, 0x00000000)
T0818 002:529.172 - 0.003ms returns 0
T0818 002:529.176 JLINK_WriteReg(R5, 0x00000000)
T0818 002:529.180 - 0.003ms returns 0
T0818 002:529.184 JLINK_WriteReg(R6, 0x00000000)
T0818 002:529.187 - 0.003ms returns 0
T0818 002:529.192 JLINK_WriteReg(R7, 0x00000000)
T0818 002:529.195 - 0.003ms returns 0
T0818 002:529.199 JLINK_WriteReg(R8, 0x00000000)
T0818 002:529.202 - 0.003ms returns 0
T0818 002:529.206 JLINK_WriteReg(R9, 0x20000180)
T0818 002:529.210 - 0.003ms returns 0
T0818 002:529.214 JLINK_WriteReg(R10, 0x00000000)
T0818 002:529.217 - 0.003ms returns 0
T0818 002:529.221 JLINK_WriteReg(R11, 0x00000000)
T0818 002:529.225 - 0.003ms returns 0
T0818 002:529.229 JLINK_WriteReg(R12, 0x00000000)
T0818 002:529.232 - 0.003ms returns 0
T0818 002:529.236 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:529.240 - 0.003ms returns 0
T0818 002:529.244 JLINK_WriteReg(R14, 0x20000001)
T0818 002:529.248 - 0.003ms returns 0
T0818 002:529.252 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:529.255 - 0.003ms returns 0
T0818 002:529.259 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:529.263 - 0.003ms returns 0
T0818 002:529.267 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:529.275 - 0.007ms returns 0
T0818 002:529.282 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:529.285 - 0.003ms returns 0
T0818 002:529.289 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:529.292 - 0.003ms returns 0
T0818 002:529.297 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:529.302 - 0.004ms returns 0x0000001B
T0818 002:529.306 JLINK_Go()
T0818 002:529.315   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:532.016 - 2.710ms 
T0818 002:532.031 JLINK_IsHalted()
T0818 002:532.527 - 0.495ms returns FALSE
T0818 002:532.535 JLINK_HasError()
T0818 002:534.363 JLINK_IsHalted()
T0818 002:534.821 - 0.457ms returns FALSE
T0818 002:534.828 JLINK_HasError()
T0818 002:536.360 JLINK_IsHalted()
T0818 002:538.674   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:539.176 - 2.815ms returns TRUE
T0818 002:539.190 JLINK_ReadReg(R15 (PC))
T0818 002:539.195 - 0.006ms returns 0x20000000
T0818 002:539.235 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T0818 002:539.252 - 0.016ms returns 0x00
T0818 002:539.258 JLINK_ReadReg(R0)
T0818 002:539.262 - 0.004ms returns 0x00000000
T0818 002:539.666 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:539.674   Data:  F2 00 F2 04 F2 02 F2 06 F2 01 F2 05 F2 03 F2 07 ...
T0818 002:539.685   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:542.357 - 2.691ms returns 0x27C
T0818 002:542.371 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:542.375   Data:  6E 03 6E 07 EE 00 EE 04 EE 02 EE 06 EE 01 EE 05 ...
T0818 002:542.387   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:544.348 - 1.977ms returns 0x184
T0818 002:544.359 JLINK_HasError()
T0818 002:544.365 JLINK_WriteReg(R0, 0x08004000)
T0818 002:544.370 - 0.005ms returns 0
T0818 002:544.375 JLINK_WriteReg(R1, 0x00000400)
T0818 002:544.378 - 0.003ms returns 0
T0818 002:544.382 JLINK_WriteReg(R2, 0x20000184)
T0818 002:544.386 - 0.003ms returns 0
T0818 002:544.390 JLINK_WriteReg(R3, 0x00000000)
T0818 002:544.394 - 0.003ms returns 0
T0818 002:544.398 JLINK_WriteReg(R4, 0x00000000)
T0818 002:544.401 - 0.003ms returns 0
T0818 002:544.405 JLINK_WriteReg(R5, 0x00000000)
T0818 002:544.409 - 0.003ms returns 0
T0818 002:544.412 JLINK_WriteReg(R6, 0x00000000)
T0818 002:544.416 - 0.003ms returns 0
T0818 002:544.420 JLINK_WriteReg(R7, 0x00000000)
T0818 002:544.424 - 0.003ms returns 0
T0818 002:544.428 JLINK_WriteReg(R8, 0x00000000)
T0818 002:544.431 - 0.003ms returns 0
T0818 002:544.435 JLINK_WriteReg(R9, 0x20000180)
T0818 002:544.438 - 0.003ms returns 0
T0818 002:544.442 JLINK_WriteReg(R10, 0x00000000)
T0818 002:544.446 - 0.003ms returns 0
T0818 002:544.450 JLINK_WriteReg(R11, 0x00000000)
T0818 002:544.453 - 0.003ms returns 0
T0818 002:544.457 JLINK_WriteReg(R12, 0x00000000)
T0818 002:544.461 - 0.003ms returns 0
T0818 002:544.465 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:544.469 - 0.004ms returns 0
T0818 002:544.473 JLINK_WriteReg(R14, 0x20000001)
T0818 002:544.476 - 0.003ms returns 0
T0818 002:544.480 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:544.484 - 0.003ms returns 0
T0818 002:544.488 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:544.491 - 0.003ms returns 0
T0818 002:544.496 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:544.499 - 0.003ms returns 0
T0818 002:544.503 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:544.506 - 0.003ms returns 0
T0818 002:544.510 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:544.514 - 0.003ms returns 0
T0818 002:544.519 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:544.524 - 0.004ms returns 0x0000001C
T0818 002:544.528 JLINK_Go()
T0818 002:544.536   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:547.349 - 2.821ms 
T0818 002:547.370 JLINK_IsHalted()
T0818 002:547.882 - 0.511ms returns FALSE
T0818 002:547.897 JLINK_HasError()
T0818 002:549.362 JLINK_IsHalted()
T0818 002:549.811 - 0.448ms returns FALSE
T0818 002:549.821 JLINK_HasError()
T0818 002:551.364 JLINK_IsHalted()
T0818 002:553.609   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:554.112 - 2.748ms returns TRUE
T0818 002:554.121 JLINK_ReadReg(R15 (PC))
T0818 002:554.126 - 0.005ms returns 0x20000000
T0818 002:554.136 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T0818 002:554.143 - 0.006ms returns 0x00
T0818 002:554.148 JLINK_ReadReg(R0)
T0818 002:554.151 - 0.003ms returns 0x00000000
T0818 002:554.546 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:554.557   Data:  25 D2 24 3F 56 36 27 3F 15 94 29 3F 4A EB 2B 3F ...
T0818 002:554.568   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:557.174 - 2.627ms returns 0x27C
T0818 002:557.191 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:557.195   Data:  EA 5A F1 3E BB CB EB 3E 75 33 E6 3E 4F 92 E0 3E ...
T0818 002:557.320   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:559.202 - 2.010ms returns 0x184
T0818 002:559.222 JLINK_HasError()
T0818 002:559.228 JLINK_WriteReg(R0, 0x08004400)
T0818 002:559.234 - 0.005ms returns 0
T0818 002:559.239 JLINK_WriteReg(R1, 0x00000400)
T0818 002:559.243 - 0.003ms returns 0
T0818 002:559.247 JLINK_WriteReg(R2, 0x20000184)
T0818 002:559.251 - 0.003ms returns 0
T0818 002:559.255 JLINK_WriteReg(R3, 0x00000000)
T0818 002:559.258 - 0.003ms returns 0
T0818 002:559.262 JLINK_WriteReg(R4, 0x00000000)
T0818 002:559.266 - 0.003ms returns 0
T0818 002:559.270 JLINK_WriteReg(R5, 0x00000000)
T0818 002:559.274 - 0.003ms returns 0
T0818 002:559.278 JLINK_WriteReg(R6, 0x00000000)
T0818 002:559.281 - 0.003ms returns 0
T0818 002:559.285 JLINK_WriteReg(R7, 0x00000000)
T0818 002:559.288 - 0.003ms returns 0
T0818 002:559.292 JLINK_WriteReg(R8, 0x00000000)
T0818 002:559.296 - 0.003ms returns 0
T0818 002:559.300 JLINK_WriteReg(R9, 0x20000180)
T0818 002:559.303 - 0.003ms returns 0
T0818 002:559.307 JLINK_WriteReg(R10, 0x00000000)
T0818 002:559.311 - 0.003ms returns 0
T0818 002:559.315 JLINK_WriteReg(R11, 0x00000000)
T0818 002:559.318 - 0.003ms returns 0
T0818 002:559.322 JLINK_WriteReg(R12, 0x00000000)
T0818 002:559.326 - 0.003ms returns 0
T0818 002:559.330 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:559.334 - 0.004ms returns 0
T0818 002:559.338 JLINK_WriteReg(R14, 0x20000001)
T0818 002:559.342 - 0.003ms returns 0
T0818 002:559.346 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:559.349 - 0.003ms returns 0
T0818 002:559.353 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:559.357 - 0.003ms returns 0
T0818 002:559.361 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:559.364 - 0.003ms returns 0
T0818 002:559.369 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:559.372 - 0.003ms returns 0
T0818 002:559.376 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:559.380 - 0.003ms returns 0
T0818 002:559.384 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:559.388 - 0.004ms returns 0x0000001D
T0818 002:559.392 JLINK_Go()
T0818 002:559.402   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:562.156 - 2.762ms 
T0818 002:562.167 JLINK_IsHalted()
T0818 002:562.654 - 0.486ms returns FALSE
T0818 002:562.663 JLINK_HasError()
T0818 002:564.361 JLINK_IsHalted()
T0818 002:564.816 - 0.455ms returns FALSE
T0818 002:564.823 JLINK_HasError()
T0818 002:566.362 JLINK_IsHalted()
T0818 002:568.651   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:569.179 - 2.816ms returns TRUE
T0818 002:569.186 JLINK_ReadReg(R15 (PC))
T0818 002:569.192 - 0.005ms returns 0x20000000
T0818 002:569.196 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T0818 002:569.200 - 0.004ms returns 0x00
T0818 002:569.204 JLINK_ReadReg(R0)
T0818 002:569.208 - 0.003ms returns 0x00000000
T0818 002:569.573 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:569.582   Data:  25 D2 24 BF 56 36 27 BF 15 94 29 BF 4A EB 2B BF ...
T0818 002:569.594   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:572.202 - 2.629ms returns 0x27C
T0818 002:572.212 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:572.216   Data:  EA 5A F1 BE BB CB EB BE 75 33 E6 BE 4F 92 E0 BE ...
T0818 002:572.223   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:574.099 - 1.887ms returns 0x184
T0818 002:574.109 JLINK_HasError()
T0818 002:574.115 JLINK_WriteReg(R0, 0x08004800)
T0818 002:574.120 - 0.005ms returns 0
T0818 002:574.125 JLINK_WriteReg(R1, 0x00000400)
T0818 002:574.129 - 0.003ms returns 0
T0818 002:574.137 JLINK_WriteReg(R2, 0x20000184)
T0818 002:574.144 - 0.006ms returns 0
T0818 002:574.148 JLINK_WriteReg(R3, 0x00000000)
T0818 002:574.151 - 0.003ms returns 0
T0818 002:574.155 JLINK_WriteReg(R4, 0x00000000)
T0818 002:574.158 - 0.003ms returns 0
T0818 002:574.162 JLINK_WriteReg(R5, 0x00000000)
T0818 002:574.166 - 0.003ms returns 0
T0818 002:574.170 JLINK_WriteReg(R6, 0x00000000)
T0818 002:574.173 - 0.003ms returns 0
T0818 002:574.178 JLINK_WriteReg(R7, 0x00000000)
T0818 002:574.181 - 0.003ms returns 0
T0818 002:574.185 JLINK_WriteReg(R8, 0x00000000)
T0818 002:574.188 - 0.003ms returns 0
T0818 002:574.193 JLINK_WriteReg(R9, 0x20000180)
T0818 002:574.196 - 0.003ms returns 0
T0818 002:574.200 JLINK_WriteReg(R10, 0x00000000)
T0818 002:574.203 - 0.003ms returns 0
T0818 002:574.208 JLINK_WriteReg(R11, 0x00000000)
T0818 002:574.211 - 0.003ms returns 0
T0818 002:574.215 JLINK_WriteReg(R12, 0x00000000)
T0818 002:574.218 - 0.003ms returns 0
T0818 002:574.222 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:574.226 - 0.004ms returns 0
T0818 002:574.231 JLINK_WriteReg(R14, 0x20000001)
T0818 002:574.235 - 0.003ms returns 0
T0818 002:574.239 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:574.242 - 0.003ms returns 0
T0818 002:574.246 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:574.250 - 0.003ms returns 0
T0818 002:574.254 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:574.258 - 0.004ms returns 0
T0818 002:574.262 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:574.265 - 0.003ms returns 0
T0818 002:574.270 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:574.273 - 0.003ms returns 0
T0818 002:574.278 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:574.282 - 0.004ms returns 0x0000001E
T0818 002:574.286 JLINK_Go()
T0818 002:574.294   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:577.133 - 2.845ms 
T0818 002:577.150 JLINK_IsHalted()
T0818 002:577.661 - 0.511ms returns FALSE
T0818 002:577.668 JLINK_HasError()
T0818 002:579.370 JLINK_IsHalted()
T0818 002:579.835 - 0.465ms returns FALSE
T0818 002:579.843 JLINK_HasError()
T0818 002:581.361 JLINK_IsHalted()
T0818 002:583.684   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:584.177 - 2.815ms returns TRUE
T0818 002:584.185 JLINK_ReadReg(R15 (PC))
T0818 002:584.190 - 0.005ms returns 0x20000000
T0818 002:584.194 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T0818 002:584.198 - 0.004ms returns 0x00
T0818 002:584.203 JLINK_ReadReg(R0)
T0818 002:584.207 - 0.003ms returns 0x00000000
T0818 002:584.745 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:584.757   Data:  8F C3 7F 3F 07 E0 2F 3D 2A BF 7F 3F FC 26 36 3D ...
T0818 002:584.770   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:587.427 - 2.681ms returns 0x27C
T0818 002:587.441 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:587.446   Data:  51 52 27 3E B0 7E 7C 3F FC DE 28 3E 08 6E 7C 3F ...
T0818 002:587.459   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:589.351 - 1.909ms returns 0x184
T0818 002:589.370 JLINK_HasError()
T0818 002:589.415 JLINK_WriteReg(R0, 0x08004C00)
T0818 002:589.426 - 0.007ms returns 0
T0818 002:589.431 JLINK_WriteReg(R1, 0x00000400)
T0818 002:589.434 - 0.003ms returns 0
T0818 002:589.439 JLINK_WriteReg(R2, 0x20000184)
T0818 002:589.443 - 0.004ms returns 0
T0818 002:589.447 JLINK_WriteReg(R3, 0x00000000)
T0818 002:589.450 - 0.003ms returns 0
T0818 002:589.454 JLINK_WriteReg(R4, 0x00000000)
T0818 002:589.458 - 0.003ms returns 0
T0818 002:589.463 JLINK_WriteReg(R5, 0x00000000)
T0818 002:589.466 - 0.003ms returns 0
T0818 002:589.470 JLINK_WriteReg(R6, 0x00000000)
T0818 002:589.474 - 0.003ms returns 0
T0818 002:589.478 JLINK_WriteReg(R7, 0x00000000)
T0818 002:589.482 - 0.003ms returns 0
T0818 002:589.486 JLINK_WriteReg(R8, 0x00000000)
T0818 002:589.489 - 0.003ms returns 0
T0818 002:589.493 JLINK_WriteReg(R9, 0x20000180)
T0818 002:589.496 - 0.003ms returns 0
T0818 002:589.500 JLINK_WriteReg(R10, 0x00000000)
T0818 002:589.504 - 0.003ms returns 0
T0818 002:589.508 JLINK_WriteReg(R11, 0x00000000)
T0818 002:589.511 - 0.003ms returns 0
T0818 002:589.519 JLINK_WriteReg(R12, 0x00000000)
T0818 002:589.526 - 0.007ms returns 0
T0818 002:589.531 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:589.535 - 0.004ms returns 0
T0818 002:589.539 JLINK_WriteReg(R14, 0x20000001)
T0818 002:589.543 - 0.003ms returns 0
T0818 002:589.547 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:589.551 - 0.003ms returns 0
T0818 002:589.555 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:589.558 - 0.003ms returns 0
T0818 002:589.562 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:589.566 - 0.003ms returns 0
T0818 002:589.570 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:589.574 - 0.003ms returns 0
T0818 002:589.578 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:589.581 - 0.003ms returns 0
T0818 002:589.586 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:589.590 - 0.004ms returns 0x0000001F
T0818 002:589.595 JLINK_Go()
T0818 002:589.605   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:592.372 - 2.776ms 
T0818 002:592.395 JLINK_IsHalted()
T0818 002:592.902 - 0.507ms returns FALSE
T0818 002:592.908 JLINK_HasError()
T0818 002:594.362 JLINK_IsHalted()
T0818 002:594.843 - 0.479ms returns FALSE
T0818 002:594.856 JLINK_HasError()
T0818 002:596.360 JLINK_IsHalted()
T0818 002:598.758   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:599.211 - 2.850ms returns TRUE
T0818 002:599.220 JLINK_ReadReg(R15 (PC))
T0818 002:599.226 - 0.006ms returns 0x20000000
T0818 002:599.230 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T0818 002:599.235 - 0.004ms returns 0x00
T0818 002:599.239 JLINK_ReadReg(R0)
T0818 002:599.243 - 0.003ms returns 0x00000000
T0818 002:599.666 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:599.678   Data:  7B B4 78 3F 51 B6 72 3E 93 9C 78 3F E8 3C 74 3E ...
T0818 002:599.692   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:602.338 - 2.672ms returns 0x27C
T0818 002:602.349 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:602.353   Data:  D6 98 B4 3E 30 68 6F 3F EC 54 B5 3E 83 44 6F 3F ...
T0818 002:602.363   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:604.215 - 1.865ms returns 0x184
T0818 002:604.225 JLINK_HasError()
T0818 002:604.230 JLINK_WriteReg(R0, 0x08005000)
T0818 002:604.235 - 0.005ms returns 0
T0818 002:604.239 JLINK_WriteReg(R1, 0x00000400)
T0818 002:604.243 - 0.003ms returns 0
T0818 002:604.248 JLINK_WriteReg(R2, 0x20000184)
T0818 002:604.252 - 0.003ms returns 0
T0818 002:604.256 JLINK_WriteReg(R3, 0x00000000)
T0818 002:604.260 - 0.004ms returns 0
T0818 002:604.264 JLINK_WriteReg(R4, 0x00000000)
T0818 002:604.268 - 0.003ms returns 0
T0818 002:604.272 JLINK_WriteReg(R5, 0x00000000)
T0818 002:604.275 - 0.003ms returns 0
T0818 002:604.279 JLINK_WriteReg(R6, 0x00000000)
T0818 002:604.282 - 0.003ms returns 0
T0818 002:604.286 JLINK_WriteReg(R7, 0x00000000)
T0818 002:604.290 - 0.003ms returns 0
T0818 002:604.294 JLINK_WriteReg(R8, 0x00000000)
T0818 002:604.298 - 0.004ms returns 0
T0818 002:604.302 JLINK_WriteReg(R9, 0x20000180)
T0818 002:604.306 - 0.003ms returns 0
T0818 002:604.310 JLINK_WriteReg(R10, 0x00000000)
T0818 002:604.313 - 0.003ms returns 0
T0818 002:604.317 JLINK_WriteReg(R11, 0x00000000)
T0818 002:604.320 - 0.003ms returns 0
T0818 002:604.324 JLINK_WriteReg(R12, 0x00000000)
T0818 002:604.328 - 0.003ms returns 0
T0818 002:604.332 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:604.336 - 0.004ms returns 0
T0818 002:604.340 JLINK_WriteReg(R14, 0x20000001)
T0818 002:604.343 - 0.003ms returns 0
T0818 002:604.348 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:604.351 - 0.003ms returns 0
T0818 002:604.355 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:604.358 - 0.003ms returns 0
T0818 002:604.362 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:604.366 - 0.003ms returns 0
T0818 002:604.370 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:604.373 - 0.003ms returns 0
T0818 002:604.377 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:604.381 - 0.003ms returns 0
T0818 002:604.385 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:604.390 - 0.004ms returns 0x00000020
T0818 002:604.394 JLINK_Go()
T0818 002:604.402   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:607.176 - 2.781ms 
T0818 002:607.190 JLINK_IsHalted()
T0818 002:607.754 - 0.564ms returns FALSE
T0818 002:607.762 JLINK_HasError()
T0818 002:609.360 JLINK_IsHalted()
T0818 002:609.883 - 0.522ms returns FALSE
T0818 002:609.892 JLINK_HasError()
T0818 002:611.370 JLINK_IsHalted()
T0818 002:613.688   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:614.214 - 2.843ms returns TRUE
T0818 002:614.223 JLINK_ReadReg(R15 (PC))
T0818 002:614.228 - 0.004ms returns 0x20000000
T0818 002:614.232 JLINK_ClrBPEx(BPHandle = 0x00000020)
T0818 002:614.236 - 0.003ms returns 0x00
T0818 002:614.240 JLINK_ReadReg(R0)
T0818 002:614.244 - 0.003ms returns 0x00000000
T0818 002:614.630 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:614.638   Data:  A8 16 68 3F 6B 10 D8 3E 2A EC 67 3F A3 C6 D8 3E ...
T0818 002:614.649   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:617.254 - 2.623ms returns 0x27C
T0818 002:617.276 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:617.281   Data:  E8 4B 07 3F 6A 1E 59 3F 36 A1 07 3F 17 E9 58 3F ...
T0818 002:617.294   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:619.186 - 1.909ms returns 0x184
T0818 002:619.204 JLINK_HasError()
T0818 002:619.210 JLINK_WriteReg(R0, 0x08005400)
T0818 002:619.216 - 0.006ms returns 0
T0818 002:619.220 JLINK_WriteReg(R1, 0x00000400)
T0818 002:619.224 - 0.003ms returns 0
T0818 002:619.228 JLINK_WriteReg(R2, 0x20000184)
T0818 002:619.232 - 0.003ms returns 0
T0818 002:619.236 JLINK_WriteReg(R3, 0x00000000)
T0818 002:619.239 - 0.003ms returns 0
T0818 002:619.243 JLINK_WriteReg(R4, 0x00000000)
T0818 002:619.247 - 0.003ms returns 0
T0818 002:619.251 JLINK_WriteReg(R5, 0x00000000)
T0818 002:619.254 - 0.003ms returns 0
T0818 002:619.258 JLINK_WriteReg(R6, 0x00000000)
T0818 002:619.262 - 0.003ms returns 0
T0818 002:619.266 JLINK_WriteReg(R7, 0x00000000)
T0818 002:619.269 - 0.003ms returns 0
T0818 002:619.273 JLINK_WriteReg(R8, 0x00000000)
T0818 002:619.277 - 0.003ms returns 0
T0818 002:619.281 JLINK_WriteReg(R9, 0x20000180)
T0818 002:619.284 - 0.003ms returns 0
T0818 002:619.288 JLINK_WriteReg(R10, 0x00000000)
T0818 002:619.292 - 0.003ms returns 0
T0818 002:619.296 JLINK_WriteReg(R11, 0x00000000)
T0818 002:619.299 - 0.003ms returns 0
T0818 002:619.303 JLINK_WriteReg(R12, 0x00000000)
T0818 002:619.307 - 0.003ms returns 0
T0818 002:619.311 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:619.315 - 0.004ms returns 0
T0818 002:619.319 JLINK_WriteReg(R14, 0x20000001)
T0818 002:619.322 - 0.003ms returns 0
T0818 002:619.326 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:619.330 - 0.003ms returns 0
T0818 002:619.334 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:619.337 - 0.003ms returns 0
T0818 002:619.341 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:619.345 - 0.003ms returns 0
T0818 002:619.349 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:619.352 - 0.003ms returns 0
T0818 002:619.356 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:619.360 - 0.003ms returns 0
T0818 002:619.364 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:619.368 - 0.004ms returns 0x00000021
T0818 002:619.373 JLINK_Go()
T0818 002:619.382   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:622.176 - 2.803ms 
T0818 002:622.191 JLINK_IsHalted()
T0818 002:622.700 - 0.508ms returns FALSE
T0818 002:622.709 JLINK_HasError()
T0818 002:625.368 JLINK_IsHalted()
T0818 002:625.887 - 0.519ms returns FALSE
T0818 002:625.894 JLINK_HasError()
T0818 002:627.362 JLINK_IsHalted()
T0818 002:629.686   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:630.196 - 2.833ms returns TRUE
T0818 002:630.202 JLINK_ReadReg(R15 (PC))
T0818 002:630.208 - 0.005ms returns 0x20000000
T0818 002:630.212 JLINK_ClrBPEx(BPHandle = 0x00000021)
T0818 002:630.216 - 0.004ms returns 0x00
T0818 002:630.221 JLINK_ReadReg(R0)
T0818 002:630.224 - 0.003ms returns 0x00000000
T0818 002:630.590 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:630.599   Data:  90 8D 4E 3F 07 3C 17 3F 1C 52 4E 3F 18 8D 17 3F ...
T0818 002:630.611   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:633.202 - 2.611ms returns 0x27C
T0818 002:633.216 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:633.221   Data:  5B 18 2F 3F A4 7C 3A 3F A5 61 2F 3F B7 37 3A 3F ...
T0818 002:633.232   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:635.069 - 1.852ms returns 0x184
T0818 002:635.081 JLINK_HasError()
T0818 002:635.086 JLINK_WriteReg(R0, 0x08005800)
T0818 002:635.091 - 0.005ms returns 0
T0818 002:635.096 JLINK_WriteReg(R1, 0x00000400)
T0818 002:635.100 - 0.003ms returns 0
T0818 002:635.104 JLINK_WriteReg(R2, 0x20000184)
T0818 002:635.107 - 0.003ms returns 0
T0818 002:635.112 JLINK_WriteReg(R3, 0x00000000)
T0818 002:635.115 - 0.003ms returns 0
T0818 002:635.120 JLINK_WriteReg(R4, 0x00000000)
T0818 002:635.123 - 0.003ms returns 0
T0818 002:635.128 JLINK_WriteReg(R5, 0x00000000)
T0818 002:635.131 - 0.003ms returns 0
T0818 002:635.135 JLINK_WriteReg(R6, 0x00000000)
T0818 002:635.138 - 0.003ms returns 0
T0818 002:635.142 JLINK_WriteReg(R7, 0x00000000)
T0818 002:635.146 - 0.003ms returns 0
T0818 002:635.150 JLINK_WriteReg(R8, 0x00000000)
T0818 002:635.153 - 0.003ms returns 0
T0818 002:635.158 JLINK_WriteReg(R9, 0x20000180)
T0818 002:635.161 - 0.003ms returns 0
T0818 002:635.165 JLINK_WriteReg(R10, 0x00000000)
T0818 002:635.168 - 0.003ms returns 0
T0818 002:635.174 JLINK_WriteReg(R11, 0x00000000)
T0818 002:635.177 - 0.003ms returns 0
T0818 002:635.182 JLINK_WriteReg(R12, 0x00000000)
T0818 002:635.185 - 0.003ms returns 0
T0818 002:635.189 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:635.193 - 0.004ms returns 0
T0818 002:635.198 JLINK_WriteReg(R14, 0x20000001)
T0818 002:635.201 - 0.003ms returns 0
T0818 002:635.205 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:635.209 - 0.003ms returns 0
T0818 002:635.213 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:635.216 - 0.003ms returns 0
T0818 002:635.220 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:635.224 - 0.003ms returns 0
T0818 002:635.228 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:635.232 - 0.003ms returns 0
T0818 002:635.236 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:635.239 - 0.003ms returns 0
T0818 002:635.244 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:635.248 - 0.004ms returns 0x00000022
T0818 002:635.252 JLINK_Go()
T0818 002:635.260   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:637.993 - 2.740ms 
T0818 002:638.013 JLINK_IsHalted()
T0818 002:638.505 - 0.492ms returns FALSE
T0818 002:638.511 JLINK_HasError()
T0818 002:640.366 JLINK_IsHalted()
T0818 002:640.858 - 0.492ms returns FALSE
T0818 002:640.866 JLINK_HasError()
T0818 002:642.362 JLINK_IsHalted()
T0818 002:644.658   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:645.147 - 2.785ms returns TRUE
T0818 002:645.153 JLINK_ReadReg(R15 (PC))
T0818 002:645.158 - 0.004ms returns 0x20000000
T0818 002:645.162 JLINK_ClrBPEx(BPHandle = 0x00000022)
T0818 002:645.166 - 0.003ms returns 0x00
T0818 002:645.171 JLINK_ReadReg(R0)
T0818 002:645.174 - 0.003ms returns 0x00000000
T0818 002:645.562 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:645.571   Data:  69 14 2D 3F 03 A0 3C 3F 49 CA 2C 3F EC E3 3C 3F ...
T0818 002:645.582   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:648.141 - 2.578ms returns 0x27C
T0818 002:648.164 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:648.168   Data:  3B 2A 50 3F 39 B0 14 3F AF 64 50 3F 58 5E 14 3F ...
T0818 002:648.181   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:650.078 - 1.913ms returns 0x184
T0818 002:650.092 JLINK_HasError()
T0818 002:650.098 JLINK_WriteReg(R0, 0x08005C00)
T0818 002:650.104 - 0.005ms returns 0
T0818 002:650.108 JLINK_WriteReg(R1, 0x00000400)
T0818 002:650.112 - 0.003ms returns 0
T0818 002:650.116 JLINK_WriteReg(R2, 0x20000184)
T0818 002:650.119 - 0.003ms returns 0
T0818 002:650.123 JLINK_WriteReg(R3, 0x00000000)
T0818 002:650.127 - 0.003ms returns 0
T0818 002:650.131 JLINK_WriteReg(R4, 0x00000000)
T0818 002:650.134 - 0.003ms returns 0
T0818 002:650.138 JLINK_WriteReg(R5, 0x00000000)
T0818 002:650.141 - 0.003ms returns 0
T0818 002:650.146 JLINK_WriteReg(R6, 0x00000000)
T0818 002:650.154 - 0.008ms returns 0
T0818 002:650.161 JLINK_WriteReg(R7, 0x00000000)
T0818 002:650.165 - 0.003ms returns 0
T0818 002:650.169 JLINK_WriteReg(R8, 0x00000000)
T0818 002:650.172 - 0.003ms returns 0
T0818 002:650.176 JLINK_WriteReg(R9, 0x20000180)
T0818 002:650.179 - 0.003ms returns 0
T0818 002:650.183 JLINK_WriteReg(R10, 0x00000000)
T0818 002:650.187 - 0.003ms returns 0
T0818 002:650.191 JLINK_WriteReg(R11, 0x00000000)
T0818 002:650.194 - 0.003ms returns 0
T0818 002:650.198 JLINK_WriteReg(R12, 0x00000000)
T0818 002:650.201 - 0.003ms returns 0
T0818 002:650.206 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:650.209 - 0.003ms returns 0
T0818 002:650.214 JLINK_WriteReg(R14, 0x20000001)
T0818 002:650.217 - 0.003ms returns 0
T0818 002:650.221 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:650.225 - 0.003ms returns 0
T0818 002:650.229 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:650.232 - 0.003ms returns 0
T0818 002:650.236 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:650.240 - 0.003ms returns 0
T0818 002:650.244 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:650.247 - 0.003ms returns 0
T0818 002:650.251 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:650.255 - 0.003ms returns 0
T0818 002:650.259 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:650.264 - 0.004ms returns 0x00000023
T0818 002:650.268 JLINK_Go()
T0818 002:650.278   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:652.961 - 2.692ms 
T0818 002:652.970 JLINK_IsHalted()
T0818 002:653.506 - 0.535ms returns FALSE
T0818 002:653.513 JLINK_HasError()
T0818 002:655.361 JLINK_IsHalted()
T0818 002:655.783 - 0.422ms returns FALSE
T0818 002:655.789 JLINK_HasError()
T0818 002:657.361 JLINK_IsHalted()
T0818 002:659.666   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:660.191 - 2.830ms returns TRUE
T0818 002:660.197 JLINK_ReadReg(R15 (PC))
T0818 002:660.202 - 0.004ms returns 0x20000000
T0818 002:660.206 JLINK_ClrBPEx(BPHandle = 0x00000023)
T0818 002:660.210 - 0.003ms returns 0x00
T0818 002:660.214 JLINK_ReadReg(R0)
T0818 002:660.218 - 0.003ms returns 0x00000000
T0818 002:660.604 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:660.613   Data:  84 F4 04 3F 50 C4 5A 3F 91 9E 04 3F 75 F8 5A 3F ...
T0818 002:660.638   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:663.344 - 2.739ms returns 0x27C
T0818 002:663.354 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:663.357   Data:  32 3C 69 3F 09 5A D2 3E 91 65 69 3F AA A2 D1 3E ...
T0818 002:663.365   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:665.262 - 1.908ms returns 0x184
T0818 002:665.272 JLINK_HasError()
T0818 002:665.278 JLINK_WriteReg(R0, 0x08006000)
T0818 002:665.284 - 0.005ms returns 0
T0818 002:665.288 JLINK_WriteReg(R1, 0x00000400)
T0818 002:665.292 - 0.003ms returns 0
T0818 002:665.296 JLINK_WriteReg(R2, 0x20000184)
T0818 002:665.299 - 0.003ms returns 0
T0818 002:665.303 JLINK_WriteReg(R3, 0x00000000)
T0818 002:665.307 - 0.004ms returns 0
T0818 002:665.311 JLINK_WriteReg(R4, 0x00000000)
T0818 002:665.315 - 0.003ms returns 0
T0818 002:665.319 JLINK_WriteReg(R5, 0x00000000)
T0818 002:665.322 - 0.003ms returns 0
T0818 002:665.326 JLINK_WriteReg(R6, 0x00000000)
T0818 002:665.330 - 0.003ms returns 0
T0818 002:665.334 JLINK_WriteReg(R7, 0x00000000)
T0818 002:665.337 - 0.003ms returns 0
T0818 002:665.341 JLINK_WriteReg(R8, 0x00000000)
T0818 002:665.344 - 0.003ms returns 0
T0818 002:665.348 JLINK_WriteReg(R9, 0x20000180)
T0818 002:665.352 - 0.003ms returns 0
T0818 002:665.356 JLINK_WriteReg(R10, 0x00000000)
T0818 002:665.359 - 0.003ms returns 0
T0818 002:665.392 JLINK_WriteReg(R11, 0x00000000)
T0818 002:665.407 - 0.015ms returns 0
T0818 002:665.412 JLINK_WriteReg(R12, 0x00000000)
T0818 002:665.416 - 0.003ms returns 0
T0818 002:665.420 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:665.424 - 0.004ms returns 0
T0818 002:665.429 JLINK_WriteReg(R14, 0x20000001)
T0818 002:665.432 - 0.003ms returns 0
T0818 002:665.437 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:665.440 - 0.004ms returns 0
T0818 002:665.444 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:665.455 - 0.010ms returns 0
T0818 002:665.459 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:665.462 - 0.003ms returns 0
T0818 002:665.470 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:665.474 - 0.004ms returns 0
T0818 002:665.478 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:665.482 - 0.003ms returns 0
T0818 002:665.487 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:665.492 - 0.005ms returns 0x00000024
T0818 002:665.496 JLINK_Go()
T0818 002:665.507   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:668.343 - 2.846ms 
T0818 002:668.366 JLINK_IsHalted()
T0818 002:668.881 - 0.514ms returns FALSE
T0818 002:668.888 JLINK_HasError()
T0818 002:670.364 JLINK_IsHalted()
T0818 002:670.787 - 0.422ms returns FALSE
T0818 002:670.794 JLINK_HasError()
T0818 002:672.375 JLINK_IsHalted()
T0818 002:674.649   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:675.119 - 2.743ms returns TRUE
T0818 002:675.126 JLINK_ReadReg(R15 (PC))
T0818 002:675.131 - 0.004ms returns 0x20000000
T0818 002:675.135 JLINK_ClrBPEx(BPHandle = 0x00000024)
T0818 002:675.139 - 0.004ms returns 0x00
T0818 002:675.144 JLINK_ReadReg(R0)
T0818 002:675.147 - 0.003ms returns 0x00000000
T0818 002:675.532 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:675.541   Data:  3A 71 AF 3E 66 80 70 3F 49 B4 AE 3E C6 A2 70 3F ...
T0818 002:675.553   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:678.122 - 2.589ms returns 0x27C
T0818 002:678.144 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:678.148   Data:  9A 57 79 3F 66 7C 66 3E 4E 6E 79 3F 86 F4 64 3E ...
T0818 002:678.169   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:679.988 - 1.844ms returns 0x184
T0818 002:679.997 JLINK_HasError()
T0818 002:680.003 JLINK_WriteReg(R0, 0x08006400)
T0818 002:680.007 - 0.004ms returns 0
T0818 002:680.012 JLINK_WriteReg(R1, 0x00000400)
T0818 002:680.016 - 0.004ms returns 0
T0818 002:680.020 JLINK_WriteReg(R2, 0x20000184)
T0818 002:680.023 - 0.003ms returns 0
T0818 002:680.028 JLINK_WriteReg(R3, 0x00000000)
T0818 002:680.031 - 0.003ms returns 0
T0818 002:680.035 JLINK_WriteReg(R4, 0x00000000)
T0818 002:680.039 - 0.003ms returns 0
T0818 002:680.043 JLINK_WriteReg(R5, 0x00000000)
T0818 002:680.047 - 0.004ms returns 0
T0818 002:680.051 JLINK_WriteReg(R6, 0x00000000)
T0818 002:680.054 - 0.003ms returns 0
T0818 002:680.058 JLINK_WriteReg(R7, 0x00000000)
T0818 002:680.062 - 0.003ms returns 0
T0818 002:680.066 JLINK_WriteReg(R8, 0x00000000)
T0818 002:680.069 - 0.003ms returns 0
T0818 002:680.073 JLINK_WriteReg(R9, 0x20000180)
T0818 002:680.077 - 0.003ms returns 0
T0818 002:680.081 JLINK_WriteReg(R10, 0x00000000)
T0818 002:680.084 - 0.003ms returns 0
T0818 002:680.088 JLINK_WriteReg(R11, 0x00000000)
T0818 002:680.092 - 0.003ms returns 0
T0818 002:680.096 JLINK_WriteReg(R12, 0x00000000)
T0818 002:680.099 - 0.003ms returns 0
T0818 002:680.103 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:680.107 - 0.004ms returns 0
T0818 002:680.111 JLINK_WriteReg(R14, 0x20000001)
T0818 002:680.114 - 0.003ms returns 0
T0818 002:680.119 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:680.122 - 0.003ms returns 0
T0818 002:680.127 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:680.130 - 0.003ms returns 0
T0818 002:680.134 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:680.138 - 0.003ms returns 0
T0818 002:680.142 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:680.145 - 0.003ms returns 0
T0818 002:680.149 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:680.153 - 0.003ms returns 0
T0818 002:680.157 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:680.162 - 0.005ms returns 0x00000025
T0818 002:680.166 JLINK_Go()
T0818 002:680.174   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:682.925 - 2.759ms 
T0818 002:682.934 JLINK_IsHalted()
T0818 002:683.484 - 0.549ms returns FALSE
T0818 002:683.490 JLINK_HasError()
T0818 002:685.372 JLINK_IsHalted()
T0818 002:685.836 - 0.463ms returns FALSE
T0818 002:685.843 JLINK_HasError()
T0818 002:687.360 JLINK_IsHalted()
T0818 002:689.697   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:690.200 - 2.840ms returns TRUE
T0818 002:690.209 JLINK_ReadReg(R15 (PC))
T0818 002:690.214 - 0.005ms returns 0x20000000
T0818 002:690.219 JLINK_ClrBPEx(BPHandle = 0x00000025)
T0818 002:690.223 - 0.003ms returns 0x00
T0818 002:690.227 JLINK_ReadReg(R0)
T0818 002:690.231 - 0.003ms returns 0x00000000
T0818 002:690.607 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:690.616   Data:  DE 76 1C 3E 73 FE 7C 3F 6B E9 1A 3E BC 0D 7D 3F ...
T0818 002:690.638   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:693.229 - 2.622ms returns 0x27C
T0818 002:693.244 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:693.248   Data:  00 DE 7F 3F B9 49 FB 3C 29 E1 7F 3F 2D BA EE 3C ...
T0818 002:693.256   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:695.110 - 1.865ms returns 0x184
T0818 002:695.120 JLINK_HasError()
T0818 002:695.161 JLINK_WriteReg(R0, 0x08006800)
T0818 002:695.174 - 0.013ms returns 0
T0818 002:695.179 JLINK_WriteReg(R1, 0x00000400)
T0818 002:695.182 - 0.003ms returns 0
T0818 002:695.187 JLINK_WriteReg(R2, 0x20000184)
T0818 002:695.190 - 0.003ms returns 0
T0818 002:695.194 JLINK_WriteReg(R3, 0x00000000)
T0818 002:695.198 - 0.003ms returns 0
T0818 002:695.202 JLINK_WriteReg(R4, 0x00000000)
T0818 002:695.205 - 0.003ms returns 0
T0818 002:695.209 JLINK_WriteReg(R5, 0x00000000)
T0818 002:695.213 - 0.003ms returns 0
T0818 002:695.217 JLINK_WriteReg(R6, 0x00000000)
T0818 002:695.220 - 0.003ms returns 0
T0818 002:695.224 JLINK_WriteReg(R7, 0x00000000)
T0818 002:695.228 - 0.004ms returns 0
T0818 002:695.232 JLINK_WriteReg(R8, 0x00000000)
T0818 002:695.236 - 0.003ms returns 0
T0818 002:695.240 JLINK_WriteReg(R9, 0x20000180)
T0818 002:695.243 - 0.003ms returns 0
T0818 002:695.247 JLINK_WriteReg(R10, 0x00000000)
T0818 002:695.250 - 0.003ms returns 0
T0818 002:695.254 JLINK_WriteReg(R11, 0x00000000)
T0818 002:695.258 - 0.003ms returns 0
T0818 002:695.262 JLINK_WriteReg(R12, 0x00000000)
T0818 002:695.265 - 0.003ms returns 0
T0818 002:695.269 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:695.273 - 0.004ms returns 0
T0818 002:695.277 JLINK_WriteReg(R14, 0x20000001)
T0818 002:695.281 - 0.004ms returns 0
T0818 002:695.285 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:695.289 - 0.003ms returns 0
T0818 002:695.293 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:695.296 - 0.003ms returns 0
T0818 002:695.300 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:695.305 - 0.004ms returns 0
T0818 002:695.309 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:695.312 - 0.003ms returns 0
T0818 002:695.316 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:695.319 - 0.003ms returns 0
T0818 002:695.324 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:695.329 - 0.005ms returns 0x00000026
T0818 002:695.334 JLINK_Go()
T0818 002:695.343   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:698.245 - 2.910ms 
T0818 002:698.267 JLINK_IsHalted()
T0818 002:698.755 - 0.488ms returns FALSE
T0818 002:698.762 JLINK_HasError()
T0818 002:700.364 JLINK_IsHalted()
T0818 002:700.858 - 0.493ms returns FALSE
T0818 002:700.867 JLINK_HasError()
T0818 002:702.361 JLINK_IsHalted()
T0818 002:704.708   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:705.211 - 2.849ms returns TRUE
T0818 002:705.219 JLINK_ReadReg(R15 (PC))
T0818 002:705.224 - 0.005ms returns 0x20000000
T0818 002:705.229 JLINK_ClrBPEx(BPHandle = 0x00000026)
T0818 002:705.233 - 0.004ms returns 0x00
T0818 002:705.237 JLINK_ReadReg(R0)
T0818 002:705.241 - 0.003ms returns 0x00000000
T0818 002:705.649 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:705.658   Data:  07 E0 2F BD 8F C3 7F 3F FC 26 36 BD 2A BF 7F 3F ...
T0818 002:705.669   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:708.357 - 2.707ms returns 0x27C
T0818 002:708.382 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:708.386   Data:  31 8F 7C 3F FC DE 28 BE B0 7E 7C 3F 8D 6B 2A BE ...
T0818 002:708.401   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:710.270 - 1.888ms returns 0x184
T0818 002:710.280 JLINK_HasError()
T0818 002:710.286 JLINK_WriteReg(R0, 0x08006C00)
T0818 002:710.296 - 0.010ms returns 0
T0818 002:710.302 JLINK_WriteReg(R1, 0x00000400)
T0818 002:710.306 - 0.003ms returns 0
T0818 002:710.310 JLINK_WriteReg(R2, 0x20000184)
T0818 002:710.314 - 0.003ms returns 0
T0818 002:710.318 JLINK_WriteReg(R3, 0x00000000)
T0818 002:710.321 - 0.003ms returns 0
T0818 002:710.325 JLINK_WriteReg(R4, 0x00000000)
T0818 002:710.328 - 0.003ms returns 0
T0818 002:710.332 JLINK_WriteReg(R5, 0x00000000)
T0818 002:710.336 - 0.003ms returns 0
T0818 002:710.340 JLINK_WriteReg(R6, 0x00000000)
T0818 002:710.343 - 0.003ms returns 0
T0818 002:710.347 JLINK_WriteReg(R7, 0x00000000)
T0818 002:710.351 - 0.003ms returns 0
T0818 002:710.356 JLINK_WriteReg(R8, 0x00000000)
T0818 002:710.359 - 0.003ms returns 0
T0818 002:710.363 JLINK_WriteReg(R9, 0x20000180)
T0818 002:710.367 - 0.003ms returns 0
T0818 002:710.371 JLINK_WriteReg(R10, 0x00000000)
T0818 002:710.374 - 0.003ms returns 0
T0818 002:710.378 JLINK_WriteReg(R11, 0x00000000)
T0818 002:710.382 - 0.003ms returns 0
T0818 002:710.386 JLINK_WriteReg(R12, 0x00000000)
T0818 002:710.424 - 0.038ms returns 0
T0818 002:710.429 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:710.437 - 0.007ms returns 0
T0818 002:710.441 JLINK_WriteReg(R14, 0x20000001)
T0818 002:710.444 - 0.003ms returns 0
T0818 002:710.448 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:710.452 - 0.003ms returns 0
T0818 002:710.456 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:710.460 - 0.003ms returns 0
T0818 002:710.464 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:710.467 - 0.003ms returns 0
T0818 002:710.471 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:710.474 - 0.003ms returns 0
T0818 002:710.478 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:710.482 - 0.003ms returns 0
T0818 002:710.487 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:710.492 - 0.004ms returns 0x00000027
T0818 002:710.496 JLINK_Go()
T0818 002:710.505   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:713.278 - 2.781ms 
T0818 002:713.295 JLINK_IsHalted()
T0818 002:713.764 - 0.469ms returns FALSE
T0818 002:713.771 JLINK_HasError()
T0818 002:715.364 JLINK_IsHalted()
T0818 002:715.800 - 0.436ms returns FALSE
T0818 002:715.806 JLINK_HasError()
T0818 002:717.367 JLINK_IsHalted()
T0818 002:719.710   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:720.207 - 2.839ms returns TRUE
T0818 002:720.259 JLINK_ReadReg(R15 (PC))
T0818 002:720.266 - 0.006ms returns 0x20000000
T0818 002:720.271 JLINK_ClrBPEx(BPHandle = 0x00000027)
T0818 002:720.275 - 0.004ms returns 0x00
T0818 002:720.280 JLINK_ReadReg(R0)
T0818 002:720.283 - 0.003ms returns 0x00000000
T0818 002:720.680 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:720.687   Data:  51 B6 72 BE 7B B4 78 3F E8 3C 74 BE 93 9C 78 3F ...
T0818 002:720.698   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:723.352 - 2.672ms returns 0x27C
T0818 002:723.369 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:723.373   Data:  B8 8B 6F 3F EC 54 B5 BE 30 68 6F 3F E6 10 B6 BE ...
T0818 002:723.387   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:725.271 - 1.902ms returns 0x184
T0818 002:725.282 JLINK_HasError()
T0818 002:725.288 JLINK_WriteReg(R0, 0x08007000)
T0818 002:725.294 - 0.006ms returns 0
T0818 002:725.298 JLINK_WriteReg(R1, 0x00000400)
T0818 002:725.301 - 0.003ms returns 0
T0818 002:725.306 JLINK_WriteReg(R2, 0x20000184)
T0818 002:725.309 - 0.003ms returns 0
T0818 002:725.313 JLINK_WriteReg(R3, 0x00000000)
T0818 002:725.316 - 0.003ms returns 0
T0818 002:725.321 JLINK_WriteReg(R4, 0x00000000)
T0818 002:725.324 - 0.003ms returns 0
T0818 002:725.328 JLINK_WriteReg(R5, 0x00000000)
T0818 002:725.331 - 0.003ms returns 0
T0818 002:725.335 JLINK_WriteReg(R6, 0x00000000)
T0818 002:725.339 - 0.003ms returns 0
T0818 002:725.343 JLINK_WriteReg(R7, 0x00000000)
T0818 002:725.347 - 0.003ms returns 0
T0818 002:725.351 JLINK_WriteReg(R8, 0x00000000)
T0818 002:725.355 - 0.003ms returns 0
T0818 002:725.359 JLINK_WriteReg(R9, 0x20000180)
T0818 002:725.363 - 0.003ms returns 0
T0818 002:725.367 JLINK_WriteReg(R10, 0x00000000)
T0818 002:725.370 - 0.003ms returns 0
T0818 002:725.382 JLINK_WriteReg(R11, 0x00000000)
T0818 002:725.385 - 0.003ms returns 0
T0818 002:725.389 JLINK_WriteReg(R12, 0x00000000)
T0818 002:725.392 - 0.003ms returns 0
T0818 002:725.397 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:725.401 - 0.004ms returns 0
T0818 002:725.405 JLINK_WriteReg(R14, 0x20000001)
T0818 002:725.409 - 0.003ms returns 0
T0818 002:725.413 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:725.416 - 0.003ms returns 0
T0818 002:725.420 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:725.424 - 0.003ms returns 0
T0818 002:725.428 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:725.432 - 0.003ms returns 0
T0818 002:725.436 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:725.439 - 0.003ms returns 0
T0818 002:725.443 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:725.446 - 0.003ms returns 0
T0818 002:725.452 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:725.456 - 0.004ms returns 0x00000028
T0818 002:725.460 JLINK_Go()
T0818 002:725.469   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:728.392 - 2.931ms 
T0818 002:728.415 JLINK_IsHalted()
T0818 002:728.913 - 0.497ms returns FALSE
T0818 002:728.925 JLINK_HasError()
T0818 002:731.365 JLINK_IsHalted()
T0818 002:731.849 - 0.483ms returns FALSE
T0818 002:731.856 JLINK_HasError()
T0818 002:733.362 JLINK_IsHalted()
T0818 002:735.704   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:736.226 - 2.863ms returns TRUE
T0818 002:736.236 JLINK_ReadReg(R15 (PC))
T0818 002:736.242 - 0.005ms returns 0x20000000
T0818 002:736.247 JLINK_ClrBPEx(BPHandle = 0x00000028)
T0818 002:736.251 - 0.004ms returns 0x00
T0818 002:736.255 JLINK_ReadReg(R0)
T0818 002:736.259 - 0.003ms returns 0x00000000
T0818 002:736.700 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:736.709   Data:  6B 10 D8 BE A8 16 68 3F A3 C6 D8 BE 2A EC 67 3F ...
T0818 002:736.725   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:739.365 - 2.664ms returns 0x27C
T0818 002:739.389 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:739.393   Data:  9C 53 59 3F 36 A1 07 BF 6A 1E 59 3F 6F F6 07 BF ...
T0818 002:739.408   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:741.337 - 1.947ms returns 0x184
T0818 002:741.346 JLINK_HasError()
T0818 002:741.352 JLINK_WriteReg(R0, 0x08007400)
T0818 002:741.357 - 0.005ms returns 0
T0818 002:741.362 JLINK_WriteReg(R1, 0x00000400)
T0818 002:741.365 - 0.003ms returns 0
T0818 002:741.377 JLINK_WriteReg(R2, 0x20000184)
T0818 002:741.381 - 0.004ms returns 0
T0818 002:741.386 JLINK_WriteReg(R3, 0x00000000)
T0818 002:741.389 - 0.003ms returns 0
T0818 002:741.393 JLINK_WriteReg(R4, 0x00000000)
T0818 002:741.396 - 0.003ms returns 0
T0818 002:741.400 JLINK_WriteReg(R5, 0x00000000)
T0818 002:741.404 - 0.003ms returns 0
T0818 002:741.408 JLINK_WriteReg(R6, 0x00000000)
T0818 002:741.411 - 0.003ms returns 0
T0818 002:741.416 JLINK_WriteReg(R7, 0x00000000)
T0818 002:741.419 - 0.003ms returns 0
T0818 002:741.423 JLINK_WriteReg(R8, 0x00000000)
T0818 002:741.427 - 0.003ms returns 0
T0818 002:741.431 JLINK_WriteReg(R9, 0x20000180)
T0818 002:741.434 - 0.003ms returns 0
T0818 002:741.439 JLINK_WriteReg(R10, 0x00000000)
T0818 002:741.442 - 0.003ms returns 0
T0818 002:741.446 JLINK_WriteReg(R11, 0x00000000)
T0818 002:741.449 - 0.003ms returns 0
T0818 002:741.453 JLINK_WriteReg(R12, 0x00000000)
T0818 002:741.457 - 0.003ms returns 0
T0818 002:741.461 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:741.465 - 0.004ms returns 0
T0818 002:741.469 JLINK_WriteReg(R14, 0x20000001)
T0818 002:741.472 - 0.003ms returns 0
T0818 002:741.476 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:741.480 - 0.003ms returns 0
T0818 002:741.484 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:741.488 - 0.003ms returns 0
T0818 002:741.492 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:741.495 - 0.003ms returns 0
T0818 002:741.499 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:741.502 - 0.003ms returns 0
T0818 002:741.506 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:741.510 - 0.003ms returns 0
T0818 002:741.514 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:741.522 - 0.008ms returns 0x00000029
T0818 002:741.529 JLINK_Go()
T0818 002:741.537   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:744.261 - 2.731ms 
T0818 002:744.282 JLINK_IsHalted()
T0818 002:744.780 - 0.497ms returns FALSE
T0818 002:744.787 JLINK_HasError()
T0818 002:746.361 JLINK_IsHalted()
T0818 002:746.825 - 0.463ms returns FALSE
T0818 002:746.838 JLINK_HasError()
T0818 002:748.366 JLINK_IsHalted()
T0818 002:750.654   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:751.181 - 2.814ms returns TRUE
T0818 002:751.192 JLINK_ReadReg(R15 (PC))
T0818 002:751.197 - 0.005ms returns 0x20000000
T0818 002:751.202 JLINK_ClrBPEx(BPHandle = 0x00000029)
T0818 002:751.205 - 0.003ms returns 0x00
T0818 002:751.210 JLINK_ReadReg(R0)
T0818 002:751.214 - 0.003ms returns 0x00000000
T0818 002:751.610 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:751.620   Data:  07 3C 17 BF 90 8D 4E 3F 18 8D 17 BF 1C 52 4E 3F ...
T0818 002:751.629   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:754.267 - 2.655ms returns 0x27C
T0818 002:754.281 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:754.285   Data:  75 C1 3A 3F A5 61 2F BF A4 7C 3A 3F D3 AA 2F BF ...
T0818 002:754.297   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:756.182 - 1.900ms returns 0x184
T0818 002:756.193 JLINK_HasError()
T0818 002:756.199 JLINK_WriteReg(R0, 0x08007800)
T0818 002:756.206 - 0.006ms returns 0
T0818 002:756.210 JLINK_WriteReg(R1, 0x00000400)
T0818 002:756.214 - 0.003ms returns 0
T0818 002:756.218 JLINK_WriteReg(R2, 0x20000184)
T0818 002:756.221 - 0.003ms returns 0
T0818 002:756.226 JLINK_WriteReg(R3, 0x00000000)
T0818 002:756.229 - 0.003ms returns 0
T0818 002:756.233 JLINK_WriteReg(R4, 0x00000000)
T0818 002:756.237 - 0.003ms returns 0
T0818 002:756.242 JLINK_WriteReg(R5, 0x00000000)
T0818 002:756.245 - 0.003ms returns 0
T0818 002:756.249 JLINK_WriteReg(R6, 0x00000000)
T0818 002:756.253 - 0.004ms returns 0
T0818 002:756.257 JLINK_WriteReg(R7, 0x00000000)
T0818 002:756.261 - 0.003ms returns 0
T0818 002:756.265 JLINK_WriteReg(R8, 0x00000000)
T0818 002:756.268 - 0.003ms returns 0
T0818 002:756.272 JLINK_WriteReg(R9, 0x20000180)
T0818 002:756.276 - 0.003ms returns 0
T0818 002:756.280 JLINK_WriteReg(R10, 0x00000000)
T0818 002:756.283 - 0.003ms returns 0
T0818 002:756.287 JLINK_WriteReg(R11, 0x00000000)
T0818 002:756.291 - 0.003ms returns 0
T0818 002:756.295 JLINK_WriteReg(R12, 0x00000000)
T0818 002:756.298 - 0.003ms returns 0
T0818 002:756.303 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:756.307 - 0.004ms returns 0
T0818 002:756.311 JLINK_WriteReg(R14, 0x20000001)
T0818 002:756.315 - 0.003ms returns 0
T0818 002:756.319 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:756.323 - 0.004ms returns 0
T0818 002:756.327 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:756.330 - 0.003ms returns 0
T0818 002:756.335 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:756.338 - 0.003ms returns 0
T0818 002:756.342 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:756.346 - 0.003ms returns 0
T0818 002:756.350 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:756.353 - 0.003ms returns 0
T0818 002:756.358 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:756.362 - 0.004ms returns 0x0000002A
T0818 002:756.367 JLINK_Go()
T0818 002:756.386   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:759.193 - 2.826ms 
T0818 002:759.204 JLINK_IsHalted()
T0818 002:759.702 - 0.497ms returns FALSE
T0818 002:759.710 JLINK_HasError()
T0818 002:761.362 JLINK_IsHalted()
T0818 002:761.866 - 0.504ms returns FALSE
T0818 002:761.874 JLINK_HasError()
T0818 002:763.364 JLINK_IsHalted()
T0818 002:765.644   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:766.118 - 2.753ms returns TRUE
T0818 002:766.124 JLINK_ReadReg(R15 (PC))
T0818 002:766.129 - 0.005ms returns 0x20000000
T0818 002:766.134 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T0818 002:766.137 - 0.003ms returns 0x00
T0818 002:766.142 JLINK_ReadReg(R0)
T0818 002:766.146 - 0.003ms returns 0x00000000
T0818 002:766.541 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:766.552   Data:  03 A0 3C BF 69 14 2D 3F EC E3 3C BF 49 CA 2C 3F ...
T0818 002:766.570   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:769.162 - 2.619ms returns 0x27C
T0818 002:769.171 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:769.174   Data:  04 02 15 3F AF 64 50 BF 39 B0 14 3F 03 9F 50 BF ...
T0818 002:769.193   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:771.099 - 1.928ms returns 0x184
T0818 002:771.108 JLINK_HasError()
T0818 002:771.113 JLINK_WriteReg(R0, 0x08007C00)
T0818 002:771.118 - 0.004ms returns 0
T0818 002:771.122 JLINK_WriteReg(R1, 0x00000400)
T0818 002:771.126 - 0.003ms returns 0
T0818 002:771.130 JLINK_WriteReg(R2, 0x20000184)
T0818 002:771.134 - 0.003ms returns 0
T0818 002:771.138 JLINK_WriteReg(R3, 0x00000000)
T0818 002:771.141 - 0.003ms returns 0
T0818 002:771.145 JLINK_WriteReg(R4, 0x00000000)
T0818 002:771.148 - 0.003ms returns 0
T0818 002:771.152 JLINK_WriteReg(R5, 0x00000000)
T0818 002:771.156 - 0.003ms returns 0
T0818 002:771.160 JLINK_WriteReg(R6, 0x00000000)
T0818 002:771.163 - 0.003ms returns 0
T0818 002:771.168 JLINK_WriteReg(R7, 0x00000000)
T0818 002:771.171 - 0.003ms returns 0
T0818 002:771.175 JLINK_WriteReg(R8, 0x00000000)
T0818 002:771.178 - 0.003ms returns 0
T0818 002:771.182 JLINK_WriteReg(R9, 0x20000180)
T0818 002:771.186 - 0.003ms returns 0
T0818 002:771.190 JLINK_WriteReg(R10, 0x00000000)
T0818 002:771.193 - 0.003ms returns 0
T0818 002:771.197 JLINK_WriteReg(R11, 0x00000000)
T0818 002:771.200 - 0.003ms returns 0
T0818 002:771.205 JLINK_WriteReg(R12, 0x00000000)
T0818 002:771.208 - 0.003ms returns 0
T0818 002:771.212 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:771.216 - 0.004ms returns 0
T0818 002:771.220 JLINK_WriteReg(R14, 0x20000001)
T0818 002:771.224 - 0.003ms returns 0
T0818 002:771.228 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:771.231 - 0.003ms returns 0
T0818 002:771.236 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:771.239 - 0.003ms returns 0
T0818 002:771.243 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:771.246 - 0.003ms returns 0
T0818 002:771.250 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:771.254 - 0.003ms returns 0
T0818 002:771.258 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:771.262 - 0.003ms returns 0
T0818 002:771.266 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:771.270 - 0.004ms returns 0x0000002B
T0818 002:771.275 JLINK_Go()
T0818 002:771.283   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:774.016 - 2.741ms 
T0818 002:774.030 JLINK_IsHalted()
T0818 002:774.519 - 0.489ms returns FALSE
T0818 002:774.528 JLINK_HasError()
T0818 002:776.362 JLINK_IsHalted()
T0818 002:776.819 - 0.456ms returns FALSE
T0818 002:776.836 JLINK_HasError()
T0818 002:778.367 JLINK_IsHalted()
T0818 002:780.594   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:781.056 - 2.688ms returns TRUE
T0818 002:781.073 JLINK_ReadReg(R15 (PC))
T0818 002:781.079 - 0.006ms returns 0x20000000
T0818 002:781.084 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T0818 002:781.088 - 0.004ms returns 0x00
T0818 002:781.092 JLINK_ReadReg(R0)
T0818 002:781.096 - 0.003ms returns 0x00000000
T0818 002:781.480 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:781.492   Data:  50 C4 5A BF 84 F4 04 3F 75 F8 5A BF 91 9E 04 3F ...
T0818 002:781.506   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:784.089 - 2.608ms returns 0x27C
T0818 002:784.115 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:784.119   Data:  48 11 D3 3E 91 65 69 BF 09 5A D2 3E CC 8E 69 BF ...
T0818 002:784.134   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:786.063 - 1.947ms returns 0x184
T0818 002:786.087 JLINK_HasError()
T0818 002:786.094 JLINK_WriteReg(R0, 0x08008000)
T0818 002:786.100 - 0.007ms returns 0
T0818 002:786.105 JLINK_WriteReg(R1, 0x00000400)
T0818 002:786.108 - 0.003ms returns 0
T0818 002:786.112 JLINK_WriteReg(R2, 0x20000184)
T0818 002:786.116 - 0.003ms returns 0
T0818 002:786.120 JLINK_WriteReg(R3, 0x00000000)
T0818 002:786.124 - 0.003ms returns 0
T0818 002:786.128 JLINK_WriteReg(R4, 0x00000000)
T0818 002:786.131 - 0.003ms returns 0
T0818 002:786.138 JLINK_WriteReg(R5, 0x00000000)
T0818 002:786.144 - 0.006ms returns 0
T0818 002:786.148 JLINK_WriteReg(R6, 0x00000000)
T0818 002:786.152 - 0.003ms returns 0
T0818 002:786.156 JLINK_WriteReg(R7, 0x00000000)
T0818 002:786.159 - 0.003ms returns 0
T0818 002:786.163 JLINK_WriteReg(R8, 0x00000000)
T0818 002:786.166 - 0.003ms returns 0
T0818 002:786.170 JLINK_WriteReg(R9, 0x20000180)
T0818 002:786.174 - 0.003ms returns 0
T0818 002:786.178 JLINK_WriteReg(R10, 0x00000000)
T0818 002:786.181 - 0.003ms returns 0
T0818 002:786.185 JLINK_WriteReg(R11, 0x00000000)
T0818 002:786.189 - 0.003ms returns 0
T0818 002:786.193 JLINK_WriteReg(R12, 0x00000000)
T0818 002:786.196 - 0.003ms returns 0
T0818 002:786.201 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:786.205 - 0.004ms returns 0
T0818 002:786.209 JLINK_WriteReg(R14, 0x20000001)
T0818 002:786.212 - 0.003ms returns 0
T0818 002:786.216 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:786.220 - 0.003ms returns 0
T0818 002:786.224 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:786.227 - 0.003ms returns 0
T0818 002:786.232 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:786.235 - 0.003ms returns 0
T0818 002:786.239 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:786.242 - 0.003ms returns 0
T0818 002:786.246 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:786.250 - 0.003ms returns 0
T0818 002:786.254 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:786.259 - 0.004ms returns 0x0000002C
T0818 002:786.263 JLINK_Go()
T0818 002:786.274   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:789.146 - 2.882ms 
T0818 002:789.155 JLINK_IsHalted()
T0818 002:789.653 - 0.497ms returns FALSE
T0818 002:789.665 JLINK_HasError()
T0818 002:793.367 JLINK_IsHalted()
T0818 002:795.649   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:796.119 - 2.751ms returns TRUE
T0818 002:796.127 JLINK_ReadReg(R15 (PC))
T0818 002:796.133 - 0.005ms returns 0x20000000
T0818 002:796.138 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T0818 002:796.141 - 0.003ms returns 0x00
T0818 002:796.146 JLINK_ReadReg(R0)
T0818 002:796.150 - 0.003ms returns 0x00000000
T0818 002:796.542 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:796.551   Data:  66 80 70 BF 3A 71 AF 3E C6 A2 70 BF 49 B4 AE 3E ...
T0818 002:796.562   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:799.196 - 2.653ms returns 0x27C
T0818 002:799.222 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:799.227   Data:  22 04 68 3E 4E 6E 79 BF 66 7C 66 3E DC 84 79 BF ...
T0818 002:799.241   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:801.120 - 1.897ms returns 0x184
T0818 002:801.136 JLINK_HasError()
T0818 002:801.177 JLINK_WriteReg(R0, 0x08008400)
T0818 002:801.192 - 0.016ms returns 0
T0818 002:801.197 JLINK_WriteReg(R1, 0x00000400)
T0818 002:801.201 - 0.003ms returns 0
T0818 002:801.205 JLINK_WriteReg(R2, 0x20000184)
T0818 002:801.209 - 0.004ms returns 0
T0818 002:801.213 JLINK_WriteReg(R3, 0x00000000)
T0818 002:801.216 - 0.003ms returns 0
T0818 002:801.221 JLINK_WriteReg(R4, 0x00000000)
T0818 002:801.224 - 0.003ms returns 0
T0818 002:801.228 JLINK_WriteReg(R5, 0x00000000)
T0818 002:801.231 - 0.003ms returns 0
T0818 002:801.236 JLINK_WriteReg(R6, 0x00000000)
T0818 002:801.239 - 0.003ms returns 0
T0818 002:801.243 JLINK_WriteReg(R7, 0x00000000)
T0818 002:801.246 - 0.003ms returns 0
T0818 002:801.250 JLINK_WriteReg(R8, 0x00000000)
T0818 002:801.254 - 0.003ms returns 0
T0818 002:801.258 JLINK_WriteReg(R9, 0x20000180)
T0818 002:801.261 - 0.003ms returns 0
T0818 002:801.265 JLINK_WriteReg(R10, 0x00000000)
T0818 002:801.269 - 0.003ms returns 0
T0818 002:801.273 JLINK_WriteReg(R11, 0x00000000)
T0818 002:801.276 - 0.003ms returns 0
T0818 002:801.280 JLINK_WriteReg(R12, 0x00000000)
T0818 002:801.283 - 0.003ms returns 0
T0818 002:801.287 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:801.291 - 0.004ms returns 0
T0818 002:801.295 JLINK_WriteReg(R14, 0x20000001)
T0818 002:801.299 - 0.003ms returns 0
T0818 002:801.303 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:801.306 - 0.003ms returns 0
T0818 002:801.310 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:801.322 - 0.011ms returns 0
T0818 002:801.327 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:801.330 - 0.003ms returns 0
T0818 002:801.334 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:801.338 - 0.003ms returns 0
T0818 002:801.342 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:801.345 - 0.003ms returns 0
T0818 002:801.350 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:801.356 - 0.005ms returns 0x0000002D
T0818 002:801.360 JLINK_Go()
T0818 002:801.371   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:804.067 - 2.707ms 
T0818 002:804.080 JLINK_IsHalted()
T0818 002:804.576 - 0.496ms returns FALSE
T0818 002:804.586 JLINK_HasError()
T0818 002:806.363 JLINK_IsHalted()
T0818 002:806.822 - 0.458ms returns FALSE
T0818 002:806.829 JLINK_HasError()
T0818 002:808.366 JLINK_IsHalted()
T0818 002:810.743   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:811.243 - 2.877ms returns TRUE
T0818 002:811.251 JLINK_ReadReg(R15 (PC))
T0818 002:811.257 - 0.005ms returns 0x20000000
T0818 002:811.261 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T0818 002:811.265 - 0.004ms returns 0x00
T0818 002:811.270 JLINK_ReadReg(R0)
T0818 002:811.273 - 0.003ms returns 0x00000000
T0818 002:811.648 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:811.657   Data:  73 FE 7C BF DE 76 1C 3E BC 0D 7D BF 6B E9 1A 3E ...
T0818 002:811.668   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:814.280 - 2.631ms returns 0x27C
T0818 002:814.289 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:814.292   Data:  90 EC 03 3D 29 E1 7F BF B9 49 FB 3C 2B E4 7F BF ...
T0818 002:814.302   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:816.155 - 1.865ms returns 0x184
T0818 002:816.162 JLINK_HasError()
T0818 002:816.167 JLINK_WriteReg(R0, 0x08008800)
T0818 002:816.171 - 0.004ms returns 0
T0818 002:816.175 JLINK_WriteReg(R1, 0x00000400)
T0818 002:816.179 - 0.003ms returns 0
T0818 002:816.183 JLINK_WriteReg(R2, 0x20000184)
T0818 002:816.186 - 0.003ms returns 0
T0818 002:816.190 JLINK_WriteReg(R3, 0x00000000)
T0818 002:816.194 - 0.003ms returns 0
T0818 002:816.198 JLINK_WriteReg(R4, 0x00000000)
T0818 002:816.201 - 0.003ms returns 0
T0818 002:816.205 JLINK_WriteReg(R5, 0x00000000)
T0818 002:816.209 - 0.003ms returns 0
T0818 002:816.213 JLINK_WriteReg(R6, 0x00000000)
T0818 002:816.216 - 0.003ms returns 0
T0818 002:816.220 JLINK_WriteReg(R7, 0x00000000)
T0818 002:816.223 - 0.003ms returns 0
T0818 002:816.228 JLINK_WriteReg(R8, 0x00000000)
T0818 002:816.231 - 0.003ms returns 0
T0818 002:816.235 JLINK_WriteReg(R9, 0x20000180)
T0818 002:816.239 - 0.003ms returns 0
T0818 002:816.243 JLINK_WriteReg(R10, 0x00000000)
T0818 002:816.246 - 0.003ms returns 0
T0818 002:816.250 JLINK_WriteReg(R11, 0x00000000)
T0818 002:816.253 - 0.003ms returns 0
T0818 002:816.257 JLINK_WriteReg(R12, 0x00000000)
T0818 002:816.261 - 0.003ms returns 0
T0818 002:816.265 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:816.269 - 0.004ms returns 0
T0818 002:816.273 JLINK_WriteReg(R14, 0x20000001)
T0818 002:816.276 - 0.003ms returns 0
T0818 002:816.281 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:816.284 - 0.003ms returns 0
T0818 002:816.288 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:816.292 - 0.003ms returns 0
T0818 002:816.296 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:816.299 - 0.003ms returns 0
T0818 002:816.303 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:816.307 - 0.003ms returns 0
T0818 002:816.311 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:816.314 - 0.003ms returns 0
T0818 002:816.319 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:816.323 - 0.004ms returns 0x0000002E
T0818 002:816.327 JLINK_Go()
T0818 002:816.336   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:819.110 - 2.781ms 
T0818 002:819.134 JLINK_IsHalted()
T0818 002:819.564 - 0.430ms returns FALSE
T0818 002:819.570 JLINK_HasError()
T0818 002:821.365 JLINK_IsHalted()
T0818 002:821.816 - 0.450ms returns FALSE
T0818 002:821.822 JLINK_HasError()
T0818 002:823.362 JLINK_IsHalted()
T0818 002:825.734   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:826.269 - 2.905ms returns TRUE
T0818 002:826.292 JLINK_ReadReg(R15 (PC))
T0818 002:826.299 - 0.006ms returns 0x20000000
T0818 002:826.335 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T0818 002:826.349 - 0.013ms returns 0x00
T0818 002:826.354 JLINK_ReadReg(R0)
T0818 002:826.358 - 0.004ms returns 0x00000000
T0818 002:826.925 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:826.934   Data:  8F C3 7F BF 07 E0 2F BD 2A BF 7F BF FC 26 36 BD ...
T0818 002:826.947   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:829.529 - 2.604ms returns 0x27C
T0818 002:829.553 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:829.558   Data:  51 52 27 BE B0 7E 7C BF FC DE 28 BE 08 6E 7C BF ...
T0818 002:829.570   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:831.428 - 1.875ms returns 0x184
T0818 002:831.441 JLINK_HasError()
T0818 002:831.447 JLINK_WriteReg(R0, 0x08008C00)
T0818 002:831.452 - 0.005ms returns 0
T0818 002:831.457 JLINK_WriteReg(R1, 0x00000400)
T0818 002:831.460 - 0.003ms returns 0
T0818 002:831.464 JLINK_WriteReg(R2, 0x20000184)
T0818 002:831.468 - 0.003ms returns 0
T0818 002:831.472 JLINK_WriteReg(R3, 0x00000000)
T0818 002:831.475 - 0.003ms returns 0
T0818 002:831.479 JLINK_WriteReg(R4, 0x00000000)
T0818 002:831.483 - 0.004ms returns 0
T0818 002:831.487 JLINK_WriteReg(R5, 0x00000000)
T0818 002:831.491 - 0.003ms returns 0
T0818 002:831.495 JLINK_WriteReg(R6, 0x00000000)
T0818 002:831.498 - 0.003ms returns 0
T0818 002:831.502 JLINK_WriteReg(R7, 0x00000000)
T0818 002:831.505 - 0.003ms returns 0
T0818 002:831.509 JLINK_WriteReg(R8, 0x00000000)
T0818 002:831.513 - 0.003ms returns 0
T0818 002:831.518 JLINK_WriteReg(R9, 0x20000180)
T0818 002:831.521 - 0.003ms returns 0
T0818 002:831.526 JLINK_WriteReg(R10, 0x00000000)
T0818 002:831.529 - 0.003ms returns 0
T0818 002:831.533 JLINK_WriteReg(R11, 0x00000000)
T0818 002:831.537 - 0.003ms returns 0
T0818 002:831.541 JLINK_WriteReg(R12, 0x00000000)
T0818 002:831.544 - 0.003ms returns 0
T0818 002:831.548 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:831.552 - 0.003ms returns 0
T0818 002:831.556 JLINK_WriteReg(R14, 0x20000001)
T0818 002:831.560 - 0.003ms returns 0
T0818 002:831.564 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:831.568 - 0.004ms returns 0
T0818 002:831.572 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:831.575 - 0.003ms returns 0
T0818 002:831.579 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:831.582 - 0.003ms returns 0
T0818 002:831.586 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:831.590 - 0.003ms returns 0
T0818 002:831.594 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:831.597 - 0.003ms returns 0
T0818 002:831.602 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:831.606 - 0.004ms returns 0x0000002F
T0818 002:831.610 JLINK_Go()
T0818 002:831.619   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:834.567 - 2.955ms 
T0818 002:834.579 JLINK_IsHalted()
T0818 002:835.051 - 0.471ms returns FALSE
T0818 002:835.058 JLINK_HasError()
T0818 002:837.366 JLINK_IsHalted()
T0818 002:837.836 - 0.469ms returns FALSE
T0818 002:837.843 JLINK_HasError()
T0818 002:839.362 JLINK_IsHalted()
T0818 002:841.745   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:842.243 - 2.880ms returns TRUE
T0818 002:842.250 JLINK_ReadReg(R15 (PC))
T0818 002:842.256 - 0.006ms returns 0x20000000
T0818 002:842.260 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T0818 002:842.264 - 0.003ms returns 0x00
T0818 002:842.269 JLINK_ReadReg(R0)
T0818 002:842.273 - 0.004ms returns 0x00000000
T0818 002:842.654 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:842.662   Data:  7B B4 78 BF 51 B6 72 BE 93 9C 78 BF E8 3C 74 BE ...
T0818 002:842.674   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:845.277 - 2.623ms returns 0x27C
T0818 002:845.286 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:845.290   Data:  D6 98 B4 BE 30 68 6F BF EC 54 B5 BE 83 44 6F BF ...
T0818 002:845.298   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:847.286 - 1.999ms returns 0x184
T0818 002:847.305 JLINK_HasError()
T0818 002:847.320 JLINK_WriteReg(R0, 0x08009000)
T0818 002:847.332 - 0.011ms returns 0
T0818 002:847.338 JLINK_WriteReg(R1, 0x00000400)
T0818 002:847.342 - 0.003ms returns 0
T0818 002:847.346 JLINK_WriteReg(R2, 0x20000184)
T0818 002:847.349 - 0.003ms returns 0
T0818 002:847.353 JLINK_WriteReg(R3, 0x00000000)
T0818 002:847.357 - 0.003ms returns 0
T0818 002:847.361 JLINK_WriteReg(R4, 0x00000000)
T0818 002:847.364 - 0.003ms returns 0
T0818 002:847.368 JLINK_WriteReg(R5, 0x00000000)
T0818 002:847.372 - 0.003ms returns 0
T0818 002:847.376 JLINK_WriteReg(R6, 0x00000000)
T0818 002:847.380 - 0.004ms returns 0
T0818 002:847.384 JLINK_WriteReg(R7, 0x00000000)
T0818 002:847.387 - 0.003ms returns 0
T0818 002:847.391 JLINK_WriteReg(R8, 0x00000000)
T0818 002:847.394 - 0.003ms returns 0
T0818 002:847.399 JLINK_WriteReg(R9, 0x20000180)
T0818 002:847.402 - 0.003ms returns 0
T0818 002:847.406 JLINK_WriteReg(R10, 0x00000000)
T0818 002:847.409 - 0.003ms returns 0
T0818 002:847.413 JLINK_WriteReg(R11, 0x00000000)
T0818 002:847.417 - 0.003ms returns 0
T0818 002:847.421 JLINK_WriteReg(R12, 0x00000000)
T0818 002:847.425 - 0.003ms returns 0
T0818 002:847.429 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:847.433 - 0.004ms returns 0
T0818 002:847.437 JLINK_WriteReg(R14, 0x20000001)
T0818 002:847.440 - 0.003ms returns 0
T0818 002:847.445 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:847.448 - 0.003ms returns 0
T0818 002:847.452 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:847.456 - 0.003ms returns 0
T0818 002:847.460 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:847.463 - 0.003ms returns 0
T0818 002:847.467 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:847.470 - 0.003ms returns 0
T0818 002:847.475 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:847.478 - 0.003ms returns 0
T0818 002:847.483 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:847.487 - 0.004ms returns 0x00000030
T0818 002:847.491 JLINK_Go()
T0818 002:847.502   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:850.349 - 2.857ms 
T0818 002:850.370 JLINK_IsHalted()
T0818 002:851.170 - 0.799ms returns FALSE
T0818 002:851.177 JLINK_HasError()
T0818 002:852.360 JLINK_IsHalted()
T0818 002:852.878 - 0.517ms returns FALSE
T0818 002:852.884 JLINK_HasError()
T0818 002:854.358 JLINK_IsHalted()
T0818 002:856.691   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:857.153 - 2.794ms returns TRUE
T0818 002:857.160 JLINK_ReadReg(R15 (PC))
T0818 002:857.165 - 0.004ms returns 0x20000000
T0818 002:857.170 JLINK_ClrBPEx(BPHandle = 0x00000030)
T0818 002:857.173 - 0.003ms returns 0x00
T0818 002:857.178 JLINK_ReadReg(R0)
T0818 002:857.181 - 0.003ms returns 0x00000000
T0818 002:857.525 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:857.533   Data:  A8 16 68 BF 6B 10 D8 BE 2A EC 67 BF A3 C6 D8 BE ...
T0818 002:857.545   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:860.176 - 2.651ms returns 0x27C
T0818 002:860.183 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:860.186   Data:  E8 4B 07 BF 6A 1E 59 BF 36 A1 07 BF 17 E9 58 BF ...
T0818 002:860.193   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:862.079 - 1.895ms returns 0x184
T0818 002:862.092 JLINK_HasError()
T0818 002:862.098 JLINK_WriteReg(R0, 0x08009400)
T0818 002:862.103 - 0.005ms returns 0
T0818 002:862.107 JLINK_WriteReg(R1, 0x00000400)
T0818 002:862.111 - 0.003ms returns 0
T0818 002:862.115 JLINK_WriteReg(R2, 0x20000184)
T0818 002:862.119 - 0.003ms returns 0
T0818 002:862.123 JLINK_WriteReg(R3, 0x00000000)
T0818 002:862.126 - 0.003ms returns 0
T0818 002:862.130 JLINK_WriteReg(R4, 0x00000000)
T0818 002:862.133 - 0.003ms returns 0
T0818 002:862.137 JLINK_WriteReg(R5, 0x00000000)
T0818 002:862.141 - 0.003ms returns 0
T0818 002:862.145 JLINK_WriteReg(R6, 0x00000000)
T0818 002:862.148 - 0.003ms returns 0
T0818 002:862.152 JLINK_WriteReg(R7, 0x00000000)
T0818 002:862.156 - 0.003ms returns 0
T0818 002:862.159 JLINK_WriteReg(R8, 0x00000000)
T0818 002:862.163 - 0.003ms returns 0
T0818 002:862.167 JLINK_WriteReg(R9, 0x20000180)
T0818 002:862.170 - 0.003ms returns 0
T0818 002:862.174 JLINK_WriteReg(R10, 0x00000000)
T0818 002:862.178 - 0.003ms returns 0
T0818 002:862.186 JLINK_WriteReg(R11, 0x00000000)
T0818 002:862.189 - 0.003ms returns 0
T0818 002:862.193 JLINK_WriteReg(R12, 0x00000000)
T0818 002:862.197 - 0.003ms returns 0
T0818 002:862.201 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:862.205 - 0.003ms returns 0
T0818 002:862.209 JLINK_WriteReg(R14, 0x20000001)
T0818 002:862.212 - 0.003ms returns 0
T0818 002:862.216 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:862.220 - 0.003ms returns 0
T0818 002:862.224 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:862.227 - 0.003ms returns 0
T0818 002:862.231 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:862.234 - 0.003ms returns 0
T0818 002:862.238 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:862.242 - 0.003ms returns 0
T0818 002:862.246 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:862.252 - 0.006ms returns 0
T0818 002:862.257 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:862.261 - 0.004ms returns 0x00000031
T0818 002:862.265 JLINK_Go()
T0818 002:862.273   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:865.053 - 2.787ms 
T0818 002:865.059 JLINK_IsHalted()
T0818 002:865.524 - 0.465ms returns FALSE
T0818 002:865.530 JLINK_HasError()
T0818 002:867.359 JLINK_IsHalted()
T0818 002:867.855 - 0.496ms returns FALSE
T0818 002:867.861 JLINK_HasError()
T0818 002:869.358 JLINK_IsHalted()
T0818 002:871.659   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:872.151 - 2.792ms returns TRUE
T0818 002:872.157 JLINK_ReadReg(R15 (PC))
T0818 002:872.161 - 0.004ms returns 0x20000000
T0818 002:872.165 JLINK_ClrBPEx(BPHandle = 0x00000031)
T0818 002:872.169 - 0.003ms returns 0x00
T0818 002:872.173 JLINK_ReadReg(R0)
T0818 002:872.176 - 0.003ms returns 0x00000000
T0818 002:872.591 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:872.600   Data:  90 8D 4E BF 07 3C 17 BF 1C 52 4E BF 18 8D 17 BF ...
T0818 002:872.609   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:875.235 - 2.644ms returns 0x27C
T0818 002:875.247 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:875.251   Data:  5B 18 2F BF A4 7C 3A BF A5 61 2F BF B7 37 3A BF ...
T0818 002:875.261   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:877.198 - 1.950ms returns 0x184
T0818 002:877.212 JLINK_HasError()
T0818 002:877.217 JLINK_WriteReg(R0, 0x08009800)
T0818 002:877.222 - 0.005ms returns 0
T0818 002:877.226 JLINK_WriteReg(R1, 0x00000400)
T0818 002:877.230 - 0.003ms returns 0
T0818 002:877.234 JLINK_WriteReg(R2, 0x20000184)
T0818 002:877.238 - 0.003ms returns 0
T0818 002:877.242 JLINK_WriteReg(R3, 0x00000000)
T0818 002:877.245 - 0.003ms returns 0
T0818 002:877.249 JLINK_WriteReg(R4, 0x00000000)
T0818 002:877.252 - 0.003ms returns 0
T0818 002:877.256 JLINK_WriteReg(R5, 0x00000000)
T0818 002:877.260 - 0.003ms returns 0
T0818 002:877.264 JLINK_WriteReg(R6, 0x00000000)
T0818 002:877.267 - 0.003ms returns 0
T0818 002:877.271 JLINK_WriteReg(R7, 0x00000000)
T0818 002:877.275 - 0.003ms returns 0
T0818 002:877.279 JLINK_WriteReg(R8, 0x00000000)
T0818 002:877.282 - 0.003ms returns 0
T0818 002:877.286 JLINK_WriteReg(R9, 0x20000180)
T0818 002:877.290 - 0.003ms returns 0
T0818 002:877.294 JLINK_WriteReg(R10, 0x00000000)
T0818 002:877.297 - 0.003ms returns 0
T0818 002:877.301 JLINK_WriteReg(R11, 0x00000000)
T0818 002:877.305 - 0.003ms returns 0
T0818 002:877.309 JLINK_WriteReg(R12, 0x00000000)
T0818 002:877.312 - 0.003ms returns 0
T0818 002:877.316 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:877.320 - 0.003ms returns 0
T0818 002:877.324 JLINK_WriteReg(R14, 0x20000001)
T0818 002:877.327 - 0.003ms returns 0
T0818 002:877.331 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:877.335 - 0.003ms returns 0
T0818 002:877.339 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:877.342 - 0.003ms returns 0
T0818 002:877.346 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:877.350 - 0.003ms returns 0
T0818 002:877.354 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:877.357 - 0.003ms returns 0
T0818 002:877.388 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:877.392 - 0.004ms returns 0
T0818 002:877.397 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:877.405 - 0.008ms returns 0x00000032
T0818 002:877.411 JLINK_Go()
T0818 002:877.420   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:880.187 - 2.775ms 
T0818 002:880.194 JLINK_IsHalted()
T0818 002:880.656 - 0.461ms returns FALSE
T0818 002:880.662 JLINK_HasError()
T0818 002:882.363 JLINK_IsHalted()
T0818 002:882.855 - 0.491ms returns FALSE
T0818 002:882.861 JLINK_HasError()
T0818 002:884.358 JLINK_IsHalted()
T0818 002:886.681   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:887.151 - 2.793ms returns TRUE
T0818 002:887.157 JLINK_ReadReg(R15 (PC))
T0818 002:887.161 - 0.004ms returns 0x20000000
T0818 002:887.166 JLINK_ClrBPEx(BPHandle = 0x00000032)
T0818 002:887.169 - 0.003ms returns 0x00
T0818 002:887.174 JLINK_ReadReg(R0)
T0818 002:887.178 - 0.003ms returns 0x00000000
T0818 002:887.561 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:887.568   Data:  69 14 2D BF 03 A0 3C BF 49 CA 2C BF EC E3 3C BF ...
T0818 002:887.577   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:890.178 - 2.616ms returns 0x27C
T0818 002:890.188 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:890.192   Data:  3B 2A 50 BF 39 B0 14 BF AF 64 50 BF 58 5E 14 BF ...
T0818 002:890.201   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:892.062 - 1.874ms returns 0x184
T0818 002:892.068 JLINK_HasError()
T0818 002:892.073 JLINK_WriteReg(R0, 0x08009C00)
T0818 002:892.078 - 0.004ms returns 0
T0818 002:892.082 JLINK_WriteReg(R1, 0x00000400)
T0818 002:892.085 - 0.003ms returns 0
T0818 002:892.089 JLINK_WriteReg(R2, 0x20000184)
T0818 002:892.093 - 0.003ms returns 0
T0818 002:892.097 JLINK_WriteReg(R3, 0x00000000)
T0818 002:892.100 - 0.003ms returns 0
T0818 002:892.104 JLINK_WriteReg(R4, 0x00000000)
T0818 002:892.108 - 0.003ms returns 0
T0818 002:892.112 JLINK_WriteReg(R5, 0x00000000)
T0818 002:892.115 - 0.003ms returns 0
T0818 002:892.119 JLINK_WriteReg(R6, 0x00000000)
T0818 002:892.123 - 0.003ms returns 0
T0818 002:892.127 JLINK_WriteReg(R7, 0x00000000)
T0818 002:892.196 - 0.069ms returns 0
T0818 002:892.201 JLINK_WriteReg(R8, 0x00000000)
T0818 002:892.204 - 0.003ms returns 0
T0818 002:892.208 JLINK_WriteReg(R9, 0x20000180)
T0818 002:892.211 - 0.003ms returns 0
T0818 002:892.216 JLINK_WriteReg(R10, 0x00000000)
T0818 002:892.219 - 0.003ms returns 0
T0818 002:892.224 JLINK_WriteReg(R11, 0x00000000)
T0818 002:892.228 - 0.004ms returns 0
T0818 002:892.232 JLINK_WriteReg(R12, 0x00000000)
T0818 002:892.236 - 0.003ms returns 0
T0818 002:892.240 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:892.244 - 0.003ms returns 0
T0818 002:892.248 JLINK_WriteReg(R14, 0x20000001)
T0818 002:892.251 - 0.003ms returns 0
T0818 002:892.255 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:892.258 - 0.003ms returns 0
T0818 002:892.263 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:892.266 - 0.003ms returns 0
T0818 002:892.270 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:892.274 - 0.003ms returns 0
T0818 002:892.278 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:892.281 - 0.003ms returns 0
T0818 002:892.285 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:892.288 - 0.003ms returns 0
T0818 002:892.293 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:892.297 - 0.004ms returns 0x00000033
T0818 002:892.301 JLINK_Go()
T0818 002:892.308   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:895.011 - 2.709ms 
T0818 002:895.017 JLINK_IsHalted()
T0818 002:895.482 - 0.464ms returns FALSE
T0818 002:895.488 JLINK_HasError()
T0818 002:897.359 JLINK_IsHalted()
T0818 002:897.855 - 0.496ms returns FALSE
T0818 002:897.861 JLINK_HasError()
T0818 002:899.358 JLINK_IsHalted()
T0818 002:901.660   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:902.136 - 2.777ms returns TRUE
T0818 002:902.141 JLINK_ReadReg(R15 (PC))
T0818 002:902.146 - 0.004ms returns 0x20000000
T0818 002:902.150 JLINK_ClrBPEx(BPHandle = 0x00000033)
T0818 002:902.154 - 0.003ms returns 0x00
T0818 002:902.158 JLINK_ReadReg(R0)
T0818 002:902.162 - 0.003ms returns 0x00000000
T0818 002:902.564 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:902.575   Data:  84 F4 04 BF 50 C4 5A BF 91 9E 04 BF 75 F8 5A BF ...
T0818 002:902.585   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:905.190 - 2.626ms returns 0x27C
T0818 002:905.208 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:905.212   Data:  32 3C 69 BF 09 5A D2 BE 91 65 69 BF AA A2 D1 BE ...
T0818 002:905.221   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:907.076 - 1.867ms returns 0x184
T0818 002:907.091 JLINK_HasError()
T0818 002:907.096 JLINK_WriteReg(R0, 0x0800A000)
T0818 002:907.101 - 0.005ms returns 0
T0818 002:907.105 JLINK_WriteReg(R1, 0x00000400)
T0818 002:907.109 - 0.003ms returns 0
T0818 002:907.113 JLINK_WriteReg(R2, 0x20000184)
T0818 002:907.116 - 0.003ms returns 0
T0818 002:907.120 JLINK_WriteReg(R3, 0x00000000)
T0818 002:907.124 - 0.003ms returns 0
T0818 002:907.128 JLINK_WriteReg(R4, 0x00000000)
T0818 002:907.131 - 0.003ms returns 0
T0818 002:907.135 JLINK_WriteReg(R5, 0x00000000)
T0818 002:907.211 - 0.075ms returns 0
T0818 002:907.215 JLINK_WriteReg(R6, 0x00000000)
T0818 002:907.219 - 0.003ms returns 0
T0818 002:907.454 JLINK_WriteReg(R7, 0x00000000)
T0818 002:907.458 - 0.003ms returns 0
T0818 002:907.462 JLINK_WriteReg(R8, 0x00000000)
T0818 002:907.465 - 0.003ms returns 0
T0818 002:907.470 JLINK_WriteReg(R9, 0x20000180)
T0818 002:907.473 - 0.003ms returns 0
T0818 002:907.477 JLINK_WriteReg(R10, 0x00000000)
T0818 002:907.481 - 0.003ms returns 0
T0818 002:907.485 JLINK_WriteReg(R11, 0x00000000)
T0818 002:907.488 - 0.003ms returns 0
T0818 002:907.492 JLINK_WriteReg(R12, 0x00000000)
T0818 002:907.496 - 0.003ms returns 0
T0818 002:907.500 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:907.503 - 0.003ms returns 0
T0818 002:907.507 JLINK_WriteReg(R14, 0x20000001)
T0818 002:907.511 - 0.003ms returns 0
T0818 002:907.515 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:907.518 - 0.003ms returns 0
T0818 002:907.522 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:907.526 - 0.003ms returns 0
T0818 002:907.530 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:907.534 - 0.003ms returns 0
T0818 002:907.538 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:907.541 - 0.003ms returns 0
T0818 002:907.545 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:907.548 - 0.003ms returns 0
T0818 002:907.553 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:907.557 - 0.004ms returns 0x00000034
T0818 002:907.561 JLINK_Go()
T0818 002:907.569   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:910.414 - 2.852ms 
T0818 002:910.425 JLINK_IsHalted()
T0818 002:910.885 - 0.459ms returns FALSE
T0818 002:910.893 JLINK_HasError()
T0818 002:912.358 JLINK_IsHalted()
T0818 002:912.855 - 0.497ms returns FALSE
T0818 002:912.861 JLINK_HasError()
T0818 002:914.358 JLINK_IsHalted()
T0818 002:916.647   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:917.129 - 2.771ms returns TRUE
T0818 002:917.134 JLINK_ReadReg(R15 (PC))
T0818 002:917.138 - 0.004ms returns 0x20000000
T0818 002:917.143 JLINK_ClrBPEx(BPHandle = 0x00000034)
T0818 002:917.146 - 0.003ms returns 0x00
T0818 002:917.151 JLINK_ReadReg(R0)
T0818 002:917.154 - 0.003ms returns 0x00000000
T0818 002:917.457 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:917.464   Data:  3A 71 AF BE 66 80 70 BF 49 B4 AE BE C6 A2 70 BF ...
T0818 002:917.473   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:920.065 - 2.608ms returns 0x27C
T0818 002:920.074 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:920.078   Data:  9A 57 79 BF 66 7C 66 BE 4E 6E 79 BF 86 F4 64 BE ...
T0818 002:920.086   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:921.957 - 1.882ms returns 0x184
T0818 002:921.963 JLINK_HasError()
T0818 002:921.967 JLINK_WriteReg(R0, 0x0800A400)
T0818 002:921.972 - 0.004ms returns 0
T0818 002:921.976 JLINK_WriteReg(R1, 0x00000400)
T0818 002:921.979 - 0.003ms returns 0
T0818 002:921.983 JLINK_WriteReg(R2, 0x20000184)
T0818 002:921.987 - 0.003ms returns 0
T0818 002:921.991 JLINK_WriteReg(R3, 0x00000000)
T0818 002:921.994 - 0.003ms returns 0
T0818 002:921.998 JLINK_WriteReg(R4, 0x00000000)
T0818 002:922.002 - 0.003ms returns 0
T0818 002:922.059 JLINK_WriteReg(R5, 0x00000000)
T0818 002:922.063 - 0.003ms returns 0
T0818 002:922.067 JLINK_WriteReg(R6, 0x00000000)
T0818 002:922.070 - 0.003ms returns 0
T0818 002:922.074 JLINK_WriteReg(R7, 0x00000000)
T0818 002:922.078 - 0.003ms returns 0
T0818 002:922.082 JLINK_WriteReg(R8, 0x00000000)
T0818 002:922.095 - 0.013ms returns 0
T0818 002:922.099 JLINK_WriteReg(R9, 0x20000180)
T0818 002:922.102 - 0.003ms returns 0
T0818 002:922.106 JLINK_WriteReg(R10, 0x00000000)
T0818 002:922.110 - 0.003ms returns 0
T0818 002:922.114 JLINK_WriteReg(R11, 0x00000000)
T0818 002:922.117 - 0.003ms returns 0
T0818 002:922.121 JLINK_WriteReg(R12, 0x00000000)
T0818 002:922.125 - 0.003ms returns 0
T0818 002:922.129 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:922.133 - 0.003ms returns 0
T0818 002:922.137 JLINK_WriteReg(R14, 0x20000001)
T0818 002:922.140 - 0.003ms returns 0
T0818 002:922.144 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:922.147 - 0.003ms returns 0
T0818 002:922.152 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:922.155 - 0.003ms returns 0
T0818 002:922.159 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:922.162 - 0.003ms returns 0
T0818 002:922.166 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:922.170 - 0.003ms returns 0
T0818 002:922.174 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:922.177 - 0.003ms returns 0
T0818 002:922.182 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:922.185 - 0.004ms returns 0x00000035
T0818 002:922.189 JLINK_Go()
T0818 002:922.196   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:924.964 - 2.774ms 
T0818 002:924.970 JLINK_IsHalted()
T0818 002:925.481 - 0.511ms returns FALSE
T0818 002:925.487 JLINK_HasError()
T0818 002:927.359 JLINK_IsHalted()
T0818 002:927.811 - 0.452ms returns FALSE
T0818 002:927.817 JLINK_HasError()
T0818 002:929.358 JLINK_IsHalted()
T0818 002:931.647   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:932.140 - 2.782ms returns TRUE
T0818 002:932.146 JLINK_ReadReg(R15 (PC))
T0818 002:932.150 - 0.004ms returns 0x20000000
T0818 002:932.155 JLINK_ClrBPEx(BPHandle = 0x00000035)
T0818 002:932.159 - 0.003ms returns 0x00
T0818 002:932.163 JLINK_ReadReg(R0)
T0818 002:932.167 - 0.003ms returns 0x00000000
T0818 002:932.468 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:932.475   Data:  DE 76 1C BE 73 FE 7C BF 6B E9 1A BE BC 0D 7D BF ...
T0818 002:932.483   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:935.120 - 2.651ms returns 0x27C
T0818 002:935.131 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:935.135   Data:  00 DE 7F BF B9 49 FB BC 29 E1 7F BF 2D BA EE BC ...
T0818 002:935.184   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:937.114 - 1.982ms returns 0x184
T0818 002:937.132 JLINK_HasError()
T0818 002:937.137 JLINK_WriteReg(R0, 0x0800A800)
T0818 002:937.143 - 0.005ms returns 0
T0818 002:937.147 JLINK_WriteReg(R1, 0x00000400)
T0818 002:937.150 - 0.003ms returns 0
T0818 002:937.154 JLINK_WriteReg(R2, 0x20000184)
T0818 002:937.158 - 0.003ms returns 0
T0818 002:937.162 JLINK_WriteReg(R3, 0x00000000)
T0818 002:937.165 - 0.003ms returns 0
T0818 002:937.169 JLINK_WriteReg(R4, 0x00000000)
T0818 002:937.173 - 0.003ms returns 0
T0818 002:937.177 JLINK_WriteReg(R5, 0x00000000)
T0818 002:937.180 - 0.003ms returns 0
T0818 002:937.184 JLINK_WriteReg(R6, 0x00000000)
T0818 002:937.188 - 0.003ms returns 0
T0818 002:937.192 JLINK_WriteReg(R7, 0x00000000)
T0818 002:937.195 - 0.003ms returns 0
T0818 002:937.199 JLINK_WriteReg(R8, 0x00000000)
T0818 002:937.202 - 0.003ms returns 0
T0818 002:937.206 JLINK_WriteReg(R9, 0x20000180)
T0818 002:937.210 - 0.003ms returns 0
T0818 002:937.214 JLINK_WriteReg(R10, 0x00000000)
T0818 002:937.217 - 0.003ms returns 0
T0818 002:937.221 JLINK_WriteReg(R11, 0x00000000)
T0818 002:937.225 - 0.003ms returns 0
T0818 002:937.229 JLINK_WriteReg(R12, 0x00000000)
T0818 002:937.232 - 0.003ms returns 0
T0818 002:937.236 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:937.240 - 0.003ms returns 0
T0818 002:937.244 JLINK_WriteReg(R14, 0x20000001)
T0818 002:937.247 - 0.003ms returns 0
T0818 002:937.254 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:937.257 - 0.003ms returns 0
T0818 002:937.261 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:937.265 - 0.003ms returns 0
T0818 002:937.269 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:937.272 - 0.003ms returns 0
T0818 002:937.276 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:937.280 - 0.003ms returns 0
T0818 002:937.284 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:937.287 - 0.003ms returns 0
T0818 002:937.291 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:937.295 - 0.004ms returns 0x00000036
T0818 002:937.300 JLINK_Go()
T0818 002:937.308   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:940.090 - 2.790ms 
T0818 002:940.102 JLINK_IsHalted()
T0818 002:940.709 - 0.607ms returns FALSE
T0818 002:940.717 JLINK_HasError()
T0818 002:943.359 JLINK_IsHalted()
T0818 002:943.834 - 0.474ms returns FALSE
T0818 002:943.840 JLINK_HasError()
T0818 002:945.358 JLINK_IsHalted()
T0818 002:947.732   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:948.218 - 2.860ms returns TRUE
T0818 002:948.224 JLINK_ReadReg(R15 (PC))
T0818 002:948.228 - 0.004ms returns 0x20000000
T0818 002:948.233 JLINK_ClrBPEx(BPHandle = 0x00000036)
T0818 002:948.236 - 0.003ms returns 0x00
T0818 002:948.241 JLINK_ReadReg(R0)
T0818 002:948.244 - 0.003ms returns 0x00000000
T0818 002:948.712 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:948.726   Data:  07 E0 2F 3D 8F C3 7F BF FC 26 36 3D 2A BF 7F BF ...
T0818 002:948.737   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:951.610 - 2.897ms returns 0x27C
T0818 002:951.619 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:951.623   Data:  31 8F 7C BF FC DE 28 3E B0 7E 7C BF 8D 6B 2A 3E ...
T0818 002:951.632   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:953.499 - 1.879ms returns 0x184
T0818 002:953.509 JLINK_HasError()
T0818 002:953.514 JLINK_WriteReg(R0, 0x0800AC00)
T0818 002:953.519 - 0.005ms returns 0
T0818 002:953.523 JLINK_WriteReg(R1, 0x00000400)
T0818 002:953.527 - 0.003ms returns 0
T0818 002:953.531 JLINK_WriteReg(R2, 0x20000184)
T0818 002:953.534 - 0.003ms returns 0
T0818 002:953.538 JLINK_WriteReg(R3, 0x00000000)
T0818 002:953.542 - 0.003ms returns 0
T0818 002:953.546 JLINK_WriteReg(R4, 0x00000000)
T0818 002:953.549 - 0.003ms returns 0
T0818 002:953.553 JLINK_WriteReg(R5, 0x00000000)
T0818 002:953.556 - 0.003ms returns 0
T0818 002:953.560 JLINK_WriteReg(R6, 0x00000000)
T0818 002:953.564 - 0.003ms returns 0
T0818 002:953.568 JLINK_WriteReg(R7, 0x00000000)
T0818 002:953.571 - 0.003ms returns 0
T0818 002:953.575 JLINK_WriteReg(R8, 0x00000000)
T0818 002:953.578 - 0.003ms returns 0
T0818 002:953.583 JLINK_WriteReg(R9, 0x20000180)
T0818 002:953.586 - 0.003ms returns 0
T0818 002:953.590 JLINK_WriteReg(R10, 0x00000000)
T0818 002:953.594 - 0.003ms returns 0
T0818 002:953.598 JLINK_WriteReg(R11, 0x00000000)
T0818 002:953.601 - 0.003ms returns 0
T0818 002:953.605 JLINK_WriteReg(R12, 0x00000000)
T0818 002:953.608 - 0.003ms returns 0
T0818 002:953.612 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:953.616 - 0.003ms returns 0
T0818 002:953.620 JLINK_WriteReg(R14, 0x20000001)
T0818 002:953.623 - 0.003ms returns 0
T0818 002:953.627 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:953.631 - 0.003ms returns 0
T0818 002:953.635 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:953.638 - 0.003ms returns 0
T0818 002:953.642 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:953.646 - 0.003ms returns 0
T0818 002:953.650 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:953.653 - 0.003ms returns 0
T0818 002:953.657 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:953.660 - 0.003ms returns 0
T0818 002:953.665 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:953.669 - 0.004ms returns 0x00000037
T0818 002:953.673 JLINK_Go()
T0818 002:953.680   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:956.571 - 2.897ms 
T0818 002:956.578 JLINK_IsHalted()
T0818 002:957.066 - 0.487ms returns FALSE
T0818 002:957.074 JLINK_HasError()
T0818 002:959.367 JLINK_IsHalted()
T0818 002:959.864 - 0.503ms returns FALSE
T0818 002:959.873 JLINK_HasError()
T0818 002:961.358 JLINK_IsHalted()
T0818 002:963.647   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:964.126 - 2.768ms returns TRUE
T0818 002:964.132 JLINK_ReadReg(R15 (PC))
T0818 002:964.136 - 0.004ms returns 0x20000000
T0818 002:964.141 JLINK_ClrBPEx(BPHandle = 0x00000037)
T0818 002:964.144 - 0.003ms returns 0x00
T0818 002:964.149 JLINK_ReadReg(R0)
T0818 002:964.152 - 0.003ms returns 0x00000000
T0818 002:964.452 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:964.459   Data:  51 B6 72 3E 7B B4 78 BF E8 3C 74 3E 93 9C 78 BF ...
T0818 002:964.468   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:967.087 - 2.635ms returns 0x27C
T0818 002:967.102 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:967.106   Data:  B8 8B 6F BF EC 54 B5 3E 30 68 6F BF E6 10 B6 3E ...
T0818 002:967.114   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:968.982 - 1.880ms returns 0x184
T0818 002:968.996 JLINK_HasError()
T0818 002:969.026 JLINK_WriteReg(R0, 0x0800B000)
T0818 002:969.031 - 0.005ms returns 0
T0818 002:969.036 JLINK_WriteReg(R1, 0x00000400)
T0818 002:969.039 - 0.003ms returns 0
T0818 002:969.043 JLINK_WriteReg(R2, 0x20000184)
T0818 002:969.047 - 0.003ms returns 0
T0818 002:969.051 JLINK_WriteReg(R3, 0x00000000)
T0818 002:969.054 - 0.003ms returns 0
T0818 002:969.058 JLINK_WriteReg(R4, 0x00000000)
T0818 002:969.062 - 0.003ms returns 0
T0818 002:969.066 JLINK_WriteReg(R5, 0x00000000)
T0818 002:969.069 - 0.003ms returns 0
T0818 002:969.073 JLINK_WriteReg(R6, 0x00000000)
T0818 002:969.077 - 0.003ms returns 0
T0818 002:969.081 JLINK_WriteReg(R7, 0x00000000)
T0818 002:969.084 - 0.003ms returns 0
T0818 002:969.088 JLINK_WriteReg(R8, 0x00000000)
T0818 002:969.091 - 0.003ms returns 0
T0818 002:969.095 JLINK_WriteReg(R9, 0x20000180)
T0818 002:969.099 - 0.003ms returns 0
T0818 002:969.103 JLINK_WriteReg(R10, 0x00000000)
T0818 002:969.106 - 0.003ms returns 0
T0818 002:969.110 JLINK_WriteReg(R11, 0x00000000)
T0818 002:969.114 - 0.003ms returns 0
T0818 002:969.118 JLINK_WriteReg(R12, 0x00000000)
T0818 002:969.121 - 0.003ms returns 0
T0818 002:969.125 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:969.129 - 0.003ms returns 0
T0818 002:969.133 JLINK_WriteReg(R14, 0x20000001)
T0818 002:969.136 - 0.003ms returns 0
T0818 002:969.140 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:969.144 - 0.003ms returns 0
T0818 002:969.148 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:969.151 - 0.003ms returns 0
T0818 002:969.155 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:969.159 - 0.003ms returns 0
T0818 002:969.162 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:969.166 - 0.003ms returns 0
T0818 002:969.170 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:969.173 - 0.003ms returns 0
T0818 002:969.178 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:969.182 - 0.004ms returns 0x00000038
T0818 002:969.186 JLINK_Go()
T0818 002:969.193   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:971.963 - 2.776ms 
T0818 002:971.969 JLINK_IsHalted()
T0818 002:972.473 - 0.503ms returns FALSE
T0818 002:972.478 JLINK_HasError()
T0818 002:974.358 JLINK_IsHalted()
T0818 002:974.812 - 0.454ms returns FALSE
T0818 002:974.818 JLINK_HasError()
T0818 002:976.359 JLINK_IsHalted()
T0818 002:978.681   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:979.159 - 2.799ms returns TRUE
T0818 002:979.165 JLINK_ReadReg(R15 (PC))
T0818 002:979.170 - 0.004ms returns 0x20000000
T0818 002:979.174 JLINK_ClrBPEx(BPHandle = 0x00000038)
T0818 002:979.178 - 0.003ms returns 0x00
T0818 002:979.182 JLINK_ReadReg(R0)
T0818 002:979.185 - 0.003ms returns 0x00000000
T0818 002:979.461 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:979.468   Data:  6B 10 D8 3E A8 16 68 BF A3 C6 D8 3E 2A EC 67 BF ...
T0818 002:979.477   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:982.064 - 2.603ms returns 0x27C
T0818 002:982.071 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:982.075   Data:  9C 53 59 BF 36 A1 07 3F 6A 1E 59 BF 6F F6 07 3F ...
T0818 002:982.082   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:983.972 - 1.900ms returns 0x184
T0818 002:983.986 JLINK_HasError()
T0818 002:983.991 JLINK_WriteReg(R0, 0x0800B400)
T0818 002:983.996 - 0.005ms returns 0
T0818 002:984.000 JLINK_WriteReg(R1, 0x00000400)
T0818 002:984.004 - 0.003ms returns 0
T0818 002:984.008 JLINK_WriteReg(R2, 0x20000184)
T0818 002:984.011 - 0.003ms returns 0
T0818 002:984.015 JLINK_WriteReg(R3, 0x00000000)
T0818 002:984.018 - 0.003ms returns 0
T0818 002:984.022 JLINK_WriteReg(R4, 0x00000000)
T0818 002:984.026 - 0.003ms returns 0
T0818 002:984.030 JLINK_WriteReg(R5, 0x00000000)
T0818 002:984.033 - 0.003ms returns 0
T0818 002:984.037 JLINK_WriteReg(R6, 0x00000000)
T0818 002:984.041 - 0.003ms returns 0
T0818 002:984.045 JLINK_WriteReg(R7, 0x00000000)
T0818 002:984.048 - 0.003ms returns 0
T0818 002:984.052 JLINK_WriteReg(R8, 0x00000000)
T0818 002:984.056 - 0.003ms returns 0
T0818 002:984.060 JLINK_WriteReg(R9, 0x20000180)
T0818 002:984.063 - 0.003ms returns 0
T0818 002:984.067 JLINK_WriteReg(R10, 0x00000000)
T0818 002:984.070 - 0.003ms returns 0
T0818 002:984.074 JLINK_WriteReg(R11, 0x00000000)
T0818 002:984.078 - 0.003ms returns 0
T0818 002:984.082 JLINK_WriteReg(R12, 0x00000000)
T0818 002:984.085 - 0.003ms returns 0
T0818 002:984.089 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:984.093 - 0.003ms returns 0
T0818 002:984.096 JLINK_WriteReg(R14, 0x20000001)
T0818 002:984.100 - 0.003ms returns 0
T0818 002:984.104 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:984.108 - 0.003ms returns 0
T0818 002:984.112 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:984.115 - 0.003ms returns 0
T0818 002:984.119 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:984.122 - 0.003ms returns 0
T0818 002:984.127 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:984.130 - 0.003ms returns 0
T0818 002:984.134 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:984.137 - 0.003ms returns 0
T0818 002:984.142 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:984.146 - 0.004ms returns 0x00000039
T0818 002:984.150 JLINK_Go()
T0818 002:984.157   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:986.909 - 2.759ms 
T0818 002:987.023 JLINK_IsHalted()
T0818 002:987.513 - 0.489ms returns FALSE
T0818 002:987.519 JLINK_HasError()
T0818 002:989.358 JLINK_IsHalted()
T0818 002:989.845 - 0.486ms returns FALSE
T0818 002:989.850 JLINK_HasError()
T0818 002:991.358 JLINK_IsHalted()
T0818 002:993.682   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:994.135 - 2.777ms returns TRUE
T0818 002:994.141 JLINK_ReadReg(R15 (PC))
T0818 002:994.146 - 0.004ms returns 0x20000000
T0818 002:994.150 JLINK_ClrBPEx(BPHandle = 0x00000039)
T0818 002:994.153 - 0.003ms returns 0x00
T0818 002:994.158 JLINK_ReadReg(R0)
T0818 002:994.161 - 0.003ms returns 0x00000000
T0818 002:994.447 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:994.455   Data:  07 3C 17 3F 90 8D 4E BF 18 8D 17 3F 1C 52 4E BF ...
T0818 002:994.464   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:997.067 - 2.620ms returns 0x27C
T0818 002:997.077 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:997.081   Data:  75 C1 3A BF A5 61 2F 3F A4 7C 3A BF D3 AA 2F 3F ...
T0818 002:997.090   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:998.957 - 1.880ms returns 0x184
T0818 002:998.963 JLINK_HasError()
T0818 002:998.968 JLINK_WriteReg(R0, 0x0800B800)
T0818 002:998.972 - 0.004ms returns 0
T0818 002:998.976 JLINK_WriteReg(R1, 0x00000400)
T0818 002:998.980 - 0.003ms returns 0
T0818 002:998.984 JLINK_WriteReg(R2, 0x20000184)
T0818 002:998.987 - 0.003ms returns 0
T0818 002:998.991 JLINK_WriteReg(R3, 0x00000000)
T0818 002:998.995 - 0.003ms returns 0
T0818 002:998.999 JLINK_WriteReg(R4, 0x00000000)
T0818 002:999.002 - 0.003ms returns 0
T0818 002:999.006 JLINK_WriteReg(R5, 0x00000000)
T0818 002:999.009 - 0.003ms returns 0
T0818 002:999.013 JLINK_WriteReg(R6, 0x00000000)
T0818 002:999.017 - 0.003ms returns 0
T0818 002:999.021 JLINK_WriteReg(R7, 0x00000000)
T0818 002:999.024 - 0.003ms returns 0
T0818 002:999.028 JLINK_WriteReg(R8, 0x00000000)
T0818 002:999.032 - 0.003ms returns 0
T0818 002:999.036 JLINK_WriteReg(R9, 0x20000180)
T0818 002:999.041 - 0.005ms returns 0
T0818 002:999.045 JLINK_WriteReg(R10, 0x00000000)
T0818 002:999.048 - 0.003ms returns 0
T0818 002:999.052 JLINK_WriteReg(R11, 0x00000000)
T0818 002:999.056 - 0.003ms returns 0
T0818 002:999.060 JLINK_WriteReg(R12, 0x00000000)
T0818 002:999.063 - 0.003ms returns 0
T0818 002:999.067 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:999.071 - 0.003ms returns 0
T0818 002:999.075 JLINK_WriteReg(R14, 0x20000001)
T0818 002:999.078 - 0.003ms returns 0
T0818 002:999.082 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:999.086 - 0.003ms returns 0
T0818 002:999.090 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:999.093 - 0.003ms returns 0
T0818 002:999.097 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:999.101 - 0.003ms returns 0
T0818 002:999.105 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:999.108 - 0.003ms returns 0
T0818 002:999.112 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:999.116 - 0.003ms returns 0
T0818 002:999.120 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:999.124 - 0.003ms returns 0x0000003A
T0818 002:999.128 JLINK_Go()
T0818 002:999.134   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:001.967 - 2.838ms 
T0818 003:001.980 JLINK_IsHalted()
T0818 003:002.424 - 0.444ms returns FALSE
T0818 003:002.435 JLINK_HasError()
T0818 003:004.366 JLINK_IsHalted()
T0818 003:004.859 - 0.492ms returns FALSE
T0818 003:004.867 JLINK_HasError()
T0818 003:006.362 JLINK_IsHalted()
T0818 003:008.736   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:009.217 - 2.854ms returns TRUE
T0818 003:009.224 JLINK_ReadReg(R15 (PC))
T0818 003:009.230 - 0.005ms returns 0x20000000
T0818 003:009.234 JLINK_ClrBPEx(BPHandle = 0x0000003A)
T0818 003:009.238 - 0.004ms returns 0x00
T0818 003:009.243 JLINK_ReadReg(R0)
T0818 003:009.247 - 0.004ms returns 0x00000000
T0818 003:009.645 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:009.654   Data:  03 A0 3C 3F 69 14 2D BF EC E3 3C 3F 49 CA 2C BF ...
T0818 003:009.666   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:012.266 - 2.621ms returns 0x27C
T0818 003:012.274 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:012.278   Data:  04 02 15 BF AF 64 50 3F 39 B0 14 BF 03 9F 50 3F ...
T0818 003:012.285   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:014.151 - 1.876ms returns 0x184
T0818 003:014.158 JLINK_HasError()
T0818 003:014.163 JLINK_WriteReg(R0, 0x0800BC00)
T0818 003:014.168 - 0.004ms returns 0
T0818 003:014.172 JLINK_WriteReg(R1, 0x00000400)
T0818 003:014.175 - 0.003ms returns 0
T0818 003:014.179 JLINK_WriteReg(R2, 0x20000184)
T0818 003:014.183 - 0.003ms returns 0
T0818 003:014.187 JLINK_WriteReg(R3, 0x00000000)
T0818 003:014.190 - 0.003ms returns 0
T0818 003:014.194 JLINK_WriteReg(R4, 0x00000000)
T0818 003:014.198 - 0.003ms returns 0
T0818 003:014.202 JLINK_WriteReg(R5, 0x00000000)
T0818 003:014.205 - 0.003ms returns 0
T0818 003:014.209 JLINK_WriteReg(R6, 0x00000000)
T0818 003:014.212 - 0.003ms returns 0
T0818 003:014.216 JLINK_WriteReg(R7, 0x00000000)
T0818 003:014.220 - 0.003ms returns 0
T0818 003:014.224 JLINK_WriteReg(R8, 0x00000000)
T0818 003:014.228 - 0.003ms returns 0
T0818 003:014.232 JLINK_WriteReg(R9, 0x20000180)
T0818 003:014.235 - 0.003ms returns 0
T0818 003:014.239 JLINK_WriteReg(R10, 0x00000000)
T0818 003:014.242 - 0.003ms returns 0
T0818 003:014.246 JLINK_WriteReg(R11, 0x00000000)
T0818 003:014.250 - 0.003ms returns 0
T0818 003:014.254 JLINK_WriteReg(R12, 0x00000000)
T0818 003:014.257 - 0.003ms returns 0
T0818 003:014.261 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:014.265 - 0.003ms returns 0
T0818 003:014.269 JLINK_WriteReg(R14, 0x20000001)
T0818 003:014.272 - 0.003ms returns 0
T0818 003:014.276 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:014.280 - 0.003ms returns 0
T0818 003:014.284 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:014.287 - 0.003ms returns 0
T0818 003:014.291 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:014.295 - 0.003ms returns 0
T0818 003:014.299 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:014.302 - 0.003ms returns 0
T0818 003:014.306 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:014.314 - 0.007ms returns 0
T0818 003:014.318 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:014.323 - 0.004ms returns 0x0000003B
T0818 003:014.327 JLINK_Go()
T0818 003:014.334   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:017.138 - 2.810ms 
T0818 003:017.153 JLINK_IsHalted()
T0818 003:017.629 - 0.475ms returns FALSE
T0818 003:017.635 JLINK_HasError()
T0818 003:019.360 JLINK_IsHalted()
T0818 003:019.832 - 0.471ms returns FALSE
T0818 003:019.842 JLINK_HasError()
T0818 003:021.360 JLINK_IsHalted()
T0818 003:023.682   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:024.153 - 2.792ms returns TRUE
T0818 003:024.158 JLINK_ReadReg(R15 (PC))
T0818 003:024.163 - 0.004ms returns 0x20000000
T0818 003:024.168 JLINK_ClrBPEx(BPHandle = 0x0000003B)
T0818 003:024.172 - 0.003ms returns 0x00
T0818 003:024.176 JLINK_ReadReg(R0)
T0818 003:024.180 - 0.003ms returns 0x00000000
T0818 003:024.518 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:024.525   Data:  50 C4 5A 3F 84 F4 04 BF 75 F8 5A 3F 91 9E 04 BF ...
T0818 003:024.535   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:027.144 - 2.626ms returns 0x27C
T0818 003:027.154 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:027.158   Data:  48 11 D3 BE 91 65 69 3F 09 5A D2 BE CC 8E 69 3F ...
T0818 003:027.166   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:029.016 - 1.862ms returns 0x184
T0818 003:029.031 JLINK_HasError()
T0818 003:029.037 JLINK_WriteReg(R0, 0x0800C000)
T0818 003:029.042 - 0.005ms returns 0
T0818 003:029.046 JLINK_WriteReg(R1, 0x00000400)
T0818 003:029.050 - 0.003ms returns 0
T0818 003:029.054 JLINK_WriteReg(R2, 0x20000184)
T0818 003:029.057 - 0.003ms returns 0
T0818 003:029.061 JLINK_WriteReg(R3, 0x00000000)
T0818 003:029.064 - 0.003ms returns 0
T0818 003:029.068 JLINK_WriteReg(R4, 0x00000000)
T0818 003:029.072 - 0.003ms returns 0
T0818 003:029.076 JLINK_WriteReg(R5, 0x00000000)
T0818 003:029.079 - 0.003ms returns 0
T0818 003:029.083 JLINK_WriteReg(R6, 0x00000000)
T0818 003:029.086 - 0.003ms returns 0
T0818 003:029.090 JLINK_WriteReg(R7, 0x00000000)
T0818 003:029.094 - 0.003ms returns 0
T0818 003:029.098 JLINK_WriteReg(R8, 0x00000000)
T0818 003:029.102 - 0.003ms returns 0
T0818 003:029.106 JLINK_WriteReg(R9, 0x20000180)
T0818 003:029.109 - 0.003ms returns 0
T0818 003:029.113 JLINK_WriteReg(R10, 0x00000000)
T0818 003:029.116 - 0.003ms returns 0
T0818 003:029.120 JLINK_WriteReg(R11, 0x00000000)
T0818 003:029.124 - 0.003ms returns 0
T0818 003:029.128 JLINK_WriteReg(R12, 0x00000000)
T0818 003:029.131 - 0.003ms returns 0
T0818 003:029.135 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:029.139 - 0.003ms returns 0
T0818 003:029.143 JLINK_WriteReg(R14, 0x20000001)
T0818 003:029.146 - 0.003ms returns 0
T0818 003:029.150 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:029.154 - 0.003ms returns 0
T0818 003:029.158 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:029.161 - 0.003ms returns 0
T0818 003:029.165 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:029.169 - 0.003ms returns 0
T0818 003:029.173 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:029.176 - 0.003ms returns 0
T0818 003:029.180 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:029.184 - 0.003ms returns 0
T0818 003:029.189 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:029.193 - 0.004ms returns 0x0000003C
T0818 003:029.197 JLINK_Go()
T0818 003:029.205   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:031.947 - 2.749ms 
T0818 003:031.959 JLINK_IsHalted()
T0818 003:032.512 - 0.553ms returns FALSE
T0818 003:032.518 JLINK_HasError()
T0818 003:034.363 JLINK_IsHalted()
T0818 003:034.814 - 0.450ms returns FALSE
T0818 003:034.820 JLINK_HasError()
T0818 003:036.358 JLINK_IsHalted()
T0818 003:038.666   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:039.198 - 2.839ms returns TRUE
T0818 003:039.210 JLINK_ReadReg(R15 (PC))
T0818 003:039.216 - 0.006ms returns 0x20000000
T0818 003:039.221 JLINK_ClrBPEx(BPHandle = 0x0000003C)
T0818 003:039.225 - 0.004ms returns 0x00
T0818 003:039.230 JLINK_ReadReg(R0)
T0818 003:039.239 - 0.009ms returns 0x00000000
T0818 003:039.617 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:039.625   Data:  66 80 70 3F 3A 71 AF BE C6 A2 70 3F 49 B4 AE BE ...
T0818 003:039.636   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:042.177 - 2.560ms returns 0x27C
T0818 003:042.186 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:042.190   Data:  22 04 68 BE 4E 6E 79 3F 66 7C 66 BE DC 84 79 3F ...
T0818 003:042.200   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:044.062 - 1.875ms returns 0x184
T0818 003:044.068 JLINK_HasError()
T0818 003:044.073 JLINK_WriteReg(R0, 0x0800C400)
T0818 003:044.078 - 0.004ms returns 0
T0818 003:044.082 JLINK_WriteReg(R1, 0x00000400)
T0818 003:044.085 - 0.003ms returns 0
T0818 003:044.089 JLINK_WriteReg(R2, 0x20000184)
T0818 003:044.093 - 0.003ms returns 0
T0818 003:044.097 JLINK_WriteReg(R3, 0x00000000)
T0818 003:044.100 - 0.003ms returns 0
T0818 003:044.110 JLINK_WriteReg(R4, 0x00000000)
T0818 003:044.114 - 0.003ms returns 0
T0818 003:044.118 JLINK_WriteReg(R5, 0x00000000)
T0818 003:044.121 - 0.003ms returns 0
T0818 003:044.126 JLINK_WriteReg(R6, 0x00000000)
T0818 003:044.129 - 0.003ms returns 0
T0818 003:044.133 JLINK_WriteReg(R7, 0x00000000)
T0818 003:044.136 - 0.003ms returns 0
T0818 003:044.140 JLINK_WriteReg(R8, 0x00000000)
T0818 003:044.144 - 0.003ms returns 0
T0818 003:044.148 JLINK_WriteReg(R9, 0x20000180)
T0818 003:044.151 - 0.003ms returns 0
T0818 003:044.155 JLINK_WriteReg(R10, 0x00000000)
T0818 003:044.159 - 0.003ms returns 0
T0818 003:044.163 JLINK_WriteReg(R11, 0x00000000)
T0818 003:044.166 - 0.003ms returns 0
T0818 003:044.170 JLINK_WriteReg(R12, 0x00000000)
T0818 003:044.173 - 0.003ms returns 0
T0818 003:044.177 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:044.181 - 0.003ms returns 0
T0818 003:044.185 JLINK_WriteReg(R14, 0x20000001)
T0818 003:044.188 - 0.003ms returns 0
T0818 003:044.193 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:044.196 - 0.003ms returns 0
T0818 003:044.200 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:044.204 - 0.003ms returns 0
T0818 003:044.208 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:044.211 - 0.003ms returns 0
T0818 003:044.215 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:044.219 - 0.003ms returns 0
T0818 003:044.223 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:044.226 - 0.003ms returns 0
T0818 003:044.231 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:044.235 - 0.004ms returns 0x0000003D
T0818 003:044.239 JLINK_Go()
T0818 003:044.246   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:046.950 - 2.710ms 
T0818 003:046.968 JLINK_IsHalted()
T0818 003:047.531 - 0.563ms returns FALSE
T0818 003:047.547 JLINK_HasError()
T0818 003:049.359 JLINK_IsHalted()
T0818 003:049.856 - 0.497ms returns FALSE
T0818 003:049.862 JLINK_HasError()
T0818 003:051.358 JLINK_IsHalted()
T0818 003:053.680   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:054.130 - 2.772ms returns TRUE
T0818 003:054.137 JLINK_ReadReg(R15 (PC))
T0818 003:054.141 - 0.004ms returns 0x20000000
T0818 003:054.145 JLINK_ClrBPEx(BPHandle = 0x0000003D)
T0818 003:054.149 - 0.004ms returns 0x00
T0818 003:054.153 JLINK_ReadReg(R0)
T0818 003:054.157 - 0.003ms returns 0x00000000
T0818 003:054.536 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:054.543   Data:  73 FE 7C 3F DE 76 1C BE BC 0D 7D 3F 6B E9 1A BE ...
T0818 003:054.552   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:057.110 - 2.574ms returns 0x27C
T0818 003:057.121 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:057.125   Data:  90 EC 03 BD 29 E1 7F 3F B9 49 FB BC 2B E4 7F 3F ...
T0818 003:057.134   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:059.001 - 1.880ms returns 0x184
T0818 003:059.007 JLINK_HasError()
T0818 003:059.012 JLINK_WriteReg(R0, 0x0800C800)
T0818 003:059.016 - 0.004ms returns 0
T0818 003:059.020 JLINK_WriteReg(R1, 0x00000358)
T0818 003:059.024 - 0.003ms returns 0
T0818 003:059.028 JLINK_WriteReg(R2, 0x20000184)
T0818 003:059.031 - 0.003ms returns 0
T0818 003:059.035 JLINK_WriteReg(R3, 0x00000000)
T0818 003:059.041 - 0.005ms returns 0
T0818 003:059.045 JLINK_WriteReg(R4, 0x00000000)
T0818 003:059.049 - 0.003ms returns 0
T0818 003:059.053 JLINK_WriteReg(R5, 0x00000000)
T0818 003:059.056 - 0.003ms returns 0
T0818 003:059.060 JLINK_WriteReg(R6, 0x00000000)
T0818 003:059.064 - 0.003ms returns 0
T0818 003:059.068 JLINK_WriteReg(R7, 0x00000000)
T0818 003:059.071 - 0.003ms returns 0
T0818 003:059.075 JLINK_WriteReg(R8, 0x00000000)
T0818 003:059.078 - 0.003ms returns 0
T0818 003:059.082 JLINK_WriteReg(R9, 0x20000180)
T0818 003:059.086 - 0.003ms returns 0
T0818 003:059.090 JLINK_WriteReg(R10, 0x00000000)
T0818 003:059.093 - 0.003ms returns 0
T0818 003:059.097 JLINK_WriteReg(R11, 0x00000000)
T0818 003:059.100 - 0.003ms returns 0
T0818 003:059.105 JLINK_WriteReg(R12, 0x00000000)
T0818 003:059.108 - 0.003ms returns 0
T0818 003:059.112 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:059.116 - 0.003ms returns 0
T0818 003:059.120 JLINK_WriteReg(R14, 0x20000001)
T0818 003:059.123 - 0.003ms returns 0
T0818 003:059.127 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:059.130 - 0.003ms returns 0
T0818 003:059.135 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:059.138 - 0.003ms returns 0
T0818 003:059.142 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:059.146 - 0.003ms returns 0
T0818 003:059.150 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:059.153 - 0.003ms returns 0
T0818 003:059.157 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:059.160 - 0.003ms returns 0
T0818 003:059.165 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:059.169 - 0.004ms returns 0x0000003E
T0818 003:059.173 JLINK_Go()
T0818 003:059.180   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:061.915 - 2.741ms 
T0818 003:061.927 JLINK_IsHalted()
T0818 003:062.382 - 0.455ms returns FALSE
T0818 003:062.388 JLINK_HasError()
T0818 003:064.358 JLINK_IsHalted()
T0818 003:064.803 - 0.445ms returns FALSE
T0818 003:064.809 JLINK_HasError()
T0818 003:066.358 JLINK_IsHalted()
T0818 003:068.704   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:069.179 - 2.820ms returns TRUE
T0818 003:069.184 JLINK_ReadReg(R15 (PC))
T0818 003:069.188 - 0.004ms returns 0x20000000
T0818 003:069.193 JLINK_ClrBPEx(BPHandle = 0x0000003E)
T0818 003:069.196 - 0.003ms returns 0x00
T0818 003:069.201 JLINK_ReadReg(R0)
T0818 003:069.205 - 0.003ms returns 0x00000000
T0818 003:069.209 JLINK_HasError()
T0818 003:069.214 JLINK_WriteReg(R0, 0x00000002)
T0818 003:069.217 - 0.003ms returns 0
T0818 003:069.221 JLINK_WriteReg(R1, 0x00000358)
T0818 003:069.225 - 0.003ms returns 0
T0818 003:069.229 JLINK_WriteReg(R2, 0x20000184)
T0818 003:069.232 - 0.003ms returns 0
T0818 003:069.236 JLINK_WriteReg(R3, 0x00000000)
T0818 003:069.240 - 0.003ms returns 0
T0818 003:069.244 JLINK_WriteReg(R4, 0x00000000)
T0818 003:069.247 - 0.003ms returns 0
T0818 003:069.251 JLINK_WriteReg(R5, 0x00000000)
T0818 003:069.254 - 0.003ms returns 0
T0818 003:069.258 JLINK_WriteReg(R6, 0x00000000)
T0818 003:069.262 - 0.003ms returns 0
T0818 003:069.266 JLINK_WriteReg(R7, 0x00000000)
T0818 003:069.269 - 0.003ms returns 0
T0818 003:069.273 JLINK_WriteReg(R8, 0x00000000)
T0818 003:069.276 - 0.003ms returns 0
T0818 003:069.280 JLINK_WriteReg(R9, 0x20000180)
T0818 003:069.284 - 0.003ms returns 0
T0818 003:069.288 JLINK_WriteReg(R10, 0x00000000)
T0818 003:069.291 - 0.003ms returns 0
T0818 003:069.295 JLINK_WriteReg(R11, 0x00000000)
T0818 003:069.299 - 0.003ms returns 0
T0818 003:069.303 JLINK_WriteReg(R12, 0x00000000)
T0818 003:069.306 - 0.003ms returns 0
T0818 003:069.310 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:069.314 - 0.003ms returns 0
T0818 003:069.318 JLINK_WriteReg(R14, 0x20000001)
T0818 003:069.321 - 0.003ms returns 0
T0818 003:069.325 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 003:069.335 - 0.009ms returns 0
T0818 003:069.339 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:069.342 - 0.003ms returns 0
T0818 003:069.346 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:069.350 - 0.003ms returns 0
T0818 003:069.354 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:069.357 - 0.003ms returns 0
T0818 003:069.363 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:069.366 - 0.003ms returns 0
T0818 003:069.370 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:069.374 - 0.004ms returns 0x0000003F
T0818 003:069.378 JLINK_Go()
T0818 003:069.385   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:072.172 - 2.793ms 
T0818 003:072.189 JLINK_IsHalted()
T0818 003:074.497   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:074.981 - 2.792ms returns TRUE
T0818 003:074.989 JLINK_ReadReg(R15 (PC))
T0818 003:074.994 - 0.004ms returns 0x20000000
T0818 003:074.998 JLINK_ClrBPEx(BPHandle = 0x0000003F)
T0818 003:075.002 - 0.003ms returns 0x00
T0818 003:075.006 JLINK_ReadReg(R0)
T0818 003:075.010 - 0.004ms returns 0x00000000
T0818 003:129.445 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 003:129.456   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 003:129.468   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 003:131.382 - 1.936ms returns 0x184
T0818 003:131.412 JLINK_HasError()
T0818 003:131.418 JLINK_WriteReg(R0, 0x08000000)
T0818 003:131.423 - 0.005ms returns 0
T0818 003:131.427 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 003:131.430 - 0.003ms returns 0
T0818 003:131.434 JLINK_WriteReg(R2, 0x00000003)
T0818 003:131.438 - 0.003ms returns 0
T0818 003:131.442 JLINK_WriteReg(R3, 0x00000000)
T0818 003:131.445 - 0.003ms returns 0
T0818 003:131.449 JLINK_WriteReg(R4, 0x00000000)
T0818 003:131.453 - 0.003ms returns 0
T0818 003:131.457 JLINK_WriteReg(R5, 0x00000000)
T0818 003:131.460 - 0.003ms returns 0
T0818 003:131.464 JLINK_WriteReg(R6, 0x00000000)
T0818 003:131.468 - 0.003ms returns 0
T0818 003:131.472 JLINK_WriteReg(R7, 0x00000000)
T0818 003:131.475 - 0.003ms returns 0
T0818 003:131.479 JLINK_WriteReg(R8, 0x00000000)
T0818 003:131.482 - 0.003ms returns 0
T0818 003:131.486 JLINK_WriteReg(R9, 0x20000180)
T0818 003:131.490 - 0.003ms returns 0
T0818 003:131.494 JLINK_WriteReg(R10, 0x00000000)
T0818 003:131.500 - 0.006ms returns 0
T0818 003:131.505 JLINK_WriteReg(R11, 0x00000000)
T0818 003:131.508 - 0.003ms returns 0
T0818 003:131.512 JLINK_WriteReg(R12, 0x00000000)
T0818 003:131.515 - 0.003ms returns 0
T0818 003:131.520 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:131.523 - 0.003ms returns 0
T0818 003:131.527 JLINK_WriteReg(R14, 0x20000001)
T0818 003:131.531 - 0.003ms returns 0
T0818 003:131.535 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 003:131.538 - 0.003ms returns 0
T0818 003:131.542 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:131.545 - 0.003ms returns 0
T0818 003:131.550 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:131.553 - 0.003ms returns 0
T0818 003:131.557 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:131.560 - 0.003ms returns 0
T0818 003:131.564 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:131.568 - 0.003ms returns 0
T0818 003:131.572 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:131.579   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:132.092 - 0.520ms returns 0x00000040
T0818 003:132.098 JLINK_Go()
T0818 003:132.103   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 003:132.586   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:135.342 - 3.244ms 
T0818 003:135.348 JLINK_IsHalted()
T0818 003:137.979   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:138.482 - 3.134ms returns TRUE
T0818 003:138.489 JLINK_ReadReg(R15 (PC))
T0818 003:138.493 - 0.004ms returns 0x20000000
T0818 003:138.498 JLINK_ClrBPEx(BPHandle = 0x00000040)
T0818 003:138.502 - 0.003ms returns 0x00
T0818 003:138.506 JLINK_ReadReg(R0)
T0818 003:138.509 - 0.003ms returns 0x00000000
T0818 003:138.514 JLINK_HasError()
T0818 003:138.518 JLINK_WriteReg(R0, 0xFFFFFFFF)
T0818 003:138.522 - 0.003ms returns 0
T0818 003:138.526 JLINK_WriteReg(R1, 0x08000000)
T0818 003:138.530 - 0.003ms returns 0
T0818 003:138.534 JLINK_WriteReg(R2, 0x0000CB58)
T0818 003:138.537 - 0.003ms returns 0
T0818 003:138.541 JLINK_WriteReg(R3, 0x04C11DB7)
T0818 003:138.544 - 0.003ms returns 0
T0818 003:138.548 JLINK_WriteReg(R4, 0x00000000)
T0818 003:138.552 - 0.003ms returns 0
T0818 003:138.556 JLINK_WriteReg(R5, 0x00000000)
T0818 003:138.562 - 0.005ms returns 0
T0818 003:138.566 JLINK_WriteReg(R6, 0x00000000)
T0818 003:138.569 - 0.003ms returns 0
T0818 003:138.573 JLINK_WriteReg(R7, 0x00000000)
T0818 003:138.576 - 0.003ms returns 0
T0818 003:138.580 JLINK_WriteReg(R8, 0x00000000)
T0818 003:138.584 - 0.003ms returns 0
T0818 003:138.588 JLINK_WriteReg(R9, 0x20000180)
T0818 003:138.591 - 0.003ms returns 0
T0818 003:138.595 JLINK_WriteReg(R10, 0x00000000)
T0818 003:138.598 - 0.003ms returns 0
T0818 003:138.602 JLINK_WriteReg(R11, 0x00000000)
T0818 003:138.606 - 0.003ms returns 0
T0818 003:138.610 JLINK_WriteReg(R12, 0x00000000)
T0818 003:138.613 - 0.003ms returns 0
T0818 003:138.617 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:138.621 - 0.003ms returns 0
T0818 003:138.625 JLINK_WriteReg(R14, 0x20000001)
T0818 003:138.628 - 0.003ms returns 0
T0818 003:138.632 JLINK_WriteReg(R15 (PC), 0x20000002)
T0818 003:138.636 - 0.003ms returns 0
T0818 003:138.640 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:138.643 - 0.003ms returns 0
T0818 003:138.648 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:138.651 - 0.003ms returns 0
T0818 003:138.655 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:138.658 - 0.003ms returns 0
T0818 003:138.662 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:138.666 - 0.003ms returns 0
T0818 003:138.670 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:138.674 - 0.003ms returns 0x00000041
T0818 003:138.678 JLINK_Go()
T0818 003:138.684   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:141.381 - 2.703ms 
T0818 003:141.391 JLINK_IsHalted()
T0818 003:141.924 - 0.532ms returns FALSE
T0818 003:141.936 JLINK_HasError()
T0818 003:144.135 JLINK_IsHalted()
T0818 003:144.606 - 0.470ms returns FALSE
T0818 003:144.612 JLINK_HasError()
T0818 003:146.324 JLINK_IsHalted()
T0818 003:146.820 - 0.495ms returns FALSE
T0818 003:146.832 JLINK_HasError()
T0818 003:148.137 JLINK_IsHalted()
T0818 003:148.650 - 0.513ms returns FALSE
T0818 003:148.656 JLINK_HasError()
T0818 003:150.136 JLINK_IsHalted()
T0818 003:150.651 - 0.515ms returns FALSE
T0818 003:150.657 JLINK_HasError()
T0818 003:152.134 JLINK_IsHalted()
T0818 003:152.609 - 0.474ms returns FALSE
T0818 003:152.615 JLINK_HasError()
T0818 003:154.134 JLINK_IsHalted()
T0818 003:154.610 - 0.476ms returns FALSE
T0818 003:154.616 JLINK_HasError()
T0818 003:156.164 JLINK_IsHalted()
T0818 003:156.688 - 0.523ms returns FALSE
T0818 003:156.694 JLINK_HasError()
T0818 003:158.137 JLINK_IsHalted()
T0818 003:158.651 - 0.514ms returns FALSE
T0818 003:158.657 JLINK_HasError()
T0818 003:161.135 JLINK_IsHalted()
T0818 003:161.672 - 0.536ms returns FALSE
T0818 003:161.678 JLINK_HasError()
T0818 003:163.134 JLINK_IsHalted()
T0818 003:163.605 - 0.470ms returns FALSE
T0818 003:163.610 JLINK_HasError()
T0818 003:165.134 JLINK_IsHalted()
T0818 003:165.606 - 0.471ms returns FALSE
T0818 003:165.611 JLINK_HasError()
T0818 003:167.134 JLINK_IsHalted()
T0818 003:167.608 - 0.474ms returns FALSE
T0818 003:167.614 JLINK_HasError()
T0818 003:169.136 JLINK_IsHalted()
T0818 003:169.626 - 0.491ms returns FALSE
T0818 003:169.632 JLINK_HasError()
T0818 003:171.134 JLINK_IsHalted()
T0818 003:171.609 - 0.474ms returns FALSE
T0818 003:171.614 JLINK_HasError()
T0818 003:173.134 JLINK_IsHalted()
T0818 003:173.653 - 0.518ms returns FALSE
T0818 003:173.663 JLINK_HasError()
T0818 003:175.134 JLINK_IsHalted()
T0818 003:175.606 - 0.471ms returns FALSE
T0818 003:175.612 JLINK_HasError()
T0818 003:177.135 JLINK_IsHalted()
T0818 003:177.625 - 0.489ms returns FALSE
T0818 003:177.633 JLINK_HasError()
T0818 003:179.520 JLINK_IsHalted()
T0818 003:180.025 - 0.504ms returns FALSE
T0818 003:180.031 JLINK_HasError()
T0818 003:181.137 JLINK_IsHalted()
T0818 003:181.592 - 0.455ms returns FALSE
T0818 003:181.598 JLINK_HasError()
T0818 003:183.134 JLINK_IsHalted()
T0818 003:183.604 - 0.470ms returns FALSE
T0818 003:183.610 JLINK_HasError()
T0818 003:185.134 JLINK_IsHalted()
T0818 003:185.605 - 0.470ms returns FALSE
T0818 003:185.610 JLINK_HasError()
T0818 003:187.135 JLINK_IsHalted()
T0818 003:187.612 - 0.477ms returns FALSE
T0818 003:187.618 JLINK_HasError()
T0818 003:189.134 JLINK_IsHalted()
T0818 003:190.003 - 0.867ms returns FALSE
T0818 003:190.016 JLINK_HasError()
T0818 003:191.151 JLINK_IsHalted()
T0818 003:191.652 - 0.500ms returns FALSE
T0818 003:191.659 JLINK_HasError()
T0818 003:193.135 JLINK_IsHalted()
T0818 003:193.650 - 0.514ms returns FALSE
T0818 003:193.657 JLINK_HasError()
T0818 003:195.135 JLINK_IsHalted()
T0818 003:195.650 - 0.515ms returns FALSE
T0818 003:195.657 JLINK_HasError()
T0818 003:197.135 JLINK_IsHalted()
T0818 003:197.626 - 0.490ms returns FALSE
T0818 003:197.633 JLINK_HasError()
T0818 003:199.139 JLINK_IsHalted()
T0818 003:199.630 - 0.490ms returns FALSE
T0818 003:199.640 JLINK_HasError()
T0818 003:201.137 JLINK_IsHalted()
T0818 003:201.651 - 0.514ms returns FALSE
T0818 003:201.657 JLINK_HasError()
T0818 003:203.135 JLINK_IsHalted()
T0818 003:203.591 - 0.456ms returns FALSE
T0818 003:203.600 JLINK_HasError()
T0818 003:205.134 JLINK_IsHalted()
T0818 003:205.764 - 0.629ms returns FALSE
T0818 003:205.775 JLINK_HasError()
T0818 003:207.135 JLINK_IsHalted()
T0818 003:207.626 - 0.490ms returns FALSE
T0818 003:207.632 JLINK_HasError()
T0818 003:209.134 JLINK_IsHalted()
T0818 003:209.609 - 0.474ms returns FALSE
T0818 003:209.614 JLINK_HasError()
T0818 003:211.134 JLINK_IsHalted()
T0818 003:211.606 - 0.471ms returns FALSE
T0818 003:211.612 JLINK_HasError()
T0818 003:213.134 JLINK_IsHalted()
T0818 003:213.605 - 0.470ms returns FALSE
T0818 003:213.610 JLINK_HasError()
T0818 003:215.138 JLINK_IsHalted()
T0818 003:215.615 - 0.477ms returns FALSE
T0818 003:215.622 JLINK_HasError()
T0818 003:217.134 JLINK_IsHalted()
T0818 003:217.635 - 0.500ms returns FALSE
T0818 003:217.641 JLINK_HasError()
T0818 003:219.134 JLINK_IsHalted()
T0818 003:219.608 - 0.473ms returns FALSE
T0818 003:219.613 JLINK_HasError()
T0818 003:221.134 JLINK_IsHalted()
T0818 003:221.608 - 0.473ms returns FALSE
T0818 003:221.618 JLINK_HasError()
T0818 003:223.135 JLINK_IsHalted()
T0818 003:223.605 - 0.469ms returns FALSE
T0818 003:223.611 JLINK_HasError()
T0818 003:225.134 JLINK_IsHalted()
T0818 003:225.605 - 0.471ms returns FALSE
T0818 003:225.611 JLINK_HasError()
T0818 003:227.134 JLINK_IsHalted()
T0818 003:227.607 - 0.472ms returns FALSE
T0818 003:227.612 JLINK_HasError()
T0818 003:229.135 JLINK_IsHalted()
T0818 003:229.628 - 0.492ms returns FALSE
T0818 003:229.634 JLINK_HasError()
T0818 003:231.134 JLINK_IsHalted()
T0818 003:231.605 - 0.471ms returns FALSE
T0818 003:231.611 JLINK_HasError()
T0818 003:233.136 JLINK_IsHalted()
T0818 003:233.593 - 0.457ms returns FALSE
T0818 003:233.598 JLINK_HasError()
T0818 003:235.134 JLINK_IsHalted()
T0818 003:235.636 - 0.501ms returns FALSE
T0818 003:235.641 JLINK_HasError()
T0818 003:237.137 JLINK_IsHalted()
T0818 003:237.651 - 0.514ms returns FALSE
T0818 003:237.659 JLINK_HasError()
T0818 003:239.134 JLINK_IsHalted()
T0818 003:239.626 - 0.492ms returns FALSE
T0818 003:239.632 JLINK_HasError()
T0818 003:241.134 JLINK_IsHalted()
T0818 003:241.606 - 0.471ms returns FALSE
T0818 003:241.611 JLINK_HasError()
T0818 003:243.134 JLINK_IsHalted()
T0818 003:243.605 - 0.470ms returns FALSE
T0818 003:243.610 JLINK_HasError()
T0818 003:245.134 JLINK_IsHalted()
T0818 003:245.606 - 0.472ms returns FALSE
T0818 003:245.612 JLINK_HasError()
T0818 003:247.135 JLINK_IsHalted()
T0818 003:247.608 - 0.472ms returns FALSE
T0818 003:247.613 JLINK_HasError()
T0818 003:249.134 JLINK_IsHalted()
T0818 003:249.609 - 0.474ms returns FALSE
T0818 003:249.614 JLINK_HasError()
T0818 003:251.134 JLINK_IsHalted()
T0818 003:251.660 - 0.525ms returns FALSE
T0818 003:251.666 JLINK_HasError()
T0818 003:253.136 JLINK_IsHalted()
T0818 003:253.651 - 0.514ms returns FALSE
T0818 003:253.660 JLINK_HasError()
T0818 003:255.134 JLINK_IsHalted()
T0818 003:255.606 - 0.471ms returns FALSE
T0818 003:255.612 JLINK_HasError()
T0818 003:257.136 JLINK_IsHalted()
T0818 003:257.627 - 0.491ms returns FALSE
T0818 003:257.634 JLINK_HasError()
T0818 003:259.134 JLINK_IsHalted()
T0818 003:259.612 - 0.478ms returns FALSE
T0818 003:259.619 JLINK_HasError()
T0818 003:261.134 JLINK_IsHalted()
T0818 003:261.641 - 0.506ms returns FALSE
T0818 003:261.648 JLINK_HasError()
T0818 003:263.134 JLINK_IsHalted()
T0818 003:263.592 - 0.457ms returns FALSE
T0818 003:263.598 JLINK_HasError()
T0818 003:265.134 JLINK_IsHalted()
T0818 003:265.636 - 0.501ms returns FALSE
T0818 003:265.642 JLINK_HasError()
T0818 003:267.136 JLINK_IsHalted()
T0818 003:267.672 - 0.535ms returns FALSE
T0818 003:267.680 JLINK_HasError()
T0818 003:270.136 JLINK_IsHalted()
T0818 003:270.650 - 0.514ms returns FALSE
T0818 003:270.656 JLINK_HasError()
T0818 003:272.134 JLINK_IsHalted()
T0818 003:272.610 - 0.475ms returns FALSE
T0818 003:272.615 JLINK_HasError()
T0818 003:274.134 JLINK_IsHalted()
T0818 003:274.610 - 0.475ms returns FALSE
T0818 003:274.615 JLINK_HasError()
T0818 003:276.165 JLINK_IsHalted()
T0818 003:276.689 - 0.524ms returns FALSE
T0818 003:276.695 JLINK_HasError()
T0818 003:278.134 JLINK_IsHalted()
T0818 003:278.605 - 0.470ms returns FALSE
T0818 003:278.610 JLINK_HasError()
T0818 003:280.134 JLINK_IsHalted()
T0818 003:280.605 - 0.470ms returns FALSE
T0818 003:280.610 JLINK_HasError()
T0818 003:282.134 JLINK_IsHalted()
T0818 003:282.608 - 0.473ms returns FALSE
T0818 003:282.617 JLINK_HasError()
T0818 003:286.248 JLINK_IsHalted()
T0818 003:286.755 - 0.506ms returns FALSE
T0818 003:286.767 JLINK_HasError()
T0818 003:288.137 JLINK_IsHalted()
T0818 003:288.612 - 0.474ms returns FALSE
T0818 003:288.617 JLINK_HasError()
T0818 003:290.136 JLINK_IsHalted()
T0818 003:290.606 - 0.469ms returns FALSE
T0818 003:290.612 JLINK_HasError()
T0818 003:292.134 JLINK_IsHalted()
T0818 003:292.605 - 0.471ms returns FALSE
T0818 003:292.611 JLINK_HasError()
T0818 003:294.134 JLINK_IsHalted()
T0818 003:294.605 - 0.471ms returns FALSE
T0818 003:294.611 JLINK_HasError()
T0818 003:296.163 JLINK_IsHalted()
T0818 003:296.708 - 0.545ms returns FALSE
T0818 003:296.715 JLINK_HasError()
T0818 003:298.136 JLINK_IsHalted()
T0818 003:298.627 - 0.491ms returns FALSE
T0818 003:298.633 JLINK_HasError()
T0818 003:300.134 JLINK_IsHalted()
T0818 003:300.604 - 0.470ms returns FALSE
T0818 003:300.610 JLINK_HasError()
T0818 003:302.134 JLINK_IsHalted()
T0818 003:302.605 - 0.470ms returns FALSE
T0818 003:302.610 JLINK_HasError()
T0818 003:304.135 JLINK_IsHalted()
T0818 003:304.604 - 0.469ms returns FALSE
T0818 003:304.613 JLINK_HasError()
T0818 003:306.158 JLINK_IsHalted()
T0818 003:306.708 - 0.550ms returns FALSE
T0818 003:306.714 JLINK_HasError()
T0818 003:308.134 JLINK_IsHalted()
T0818 003:308.605 - 0.471ms returns FALSE
T0818 003:308.611 JLINK_HasError()
T0818 003:310.134 JLINK_IsHalted()
T0818 003:310.609 - 0.474ms returns FALSE
T0818 003:310.614 JLINK_HasError()
T0818 003:312.134 JLINK_IsHalted()
T0818 003:312.924 - 0.789ms returns FALSE
T0818 003:312.934 JLINK_HasError()
T0818 003:314.134 JLINK_IsHalted()
T0818 003:314.592 - 0.458ms returns FALSE
T0818 003:314.598 JLINK_HasError()
T0818 003:316.289 JLINK_IsHalted()
T0818 003:316.831 - 0.542ms returns FALSE
T0818 003:316.838 JLINK_HasError()
T0818 003:318.134 JLINK_IsHalted()
T0818 003:318.606 - 0.471ms returns FALSE
T0818 003:318.612 JLINK_HasError()
T0818 003:320.134 JLINK_IsHalted()
T0818 003:320.604 - 0.470ms returns FALSE
T0818 003:320.610 JLINK_HasError()
T0818 003:322.134 JLINK_IsHalted()
T0818 003:322.604 - 0.470ms returns FALSE
T0818 003:322.610 JLINK_HasError()
T0818 003:324.134 JLINK_IsHalted()
T0818 003:324.606 - 0.471ms returns FALSE
T0818 003:324.611 JLINK_HasError()
T0818 003:326.296 JLINK_IsHalted()
T0818 003:326.796 - 0.499ms returns FALSE
T0818 003:326.802 JLINK_HasError()
T0818 003:328.137 JLINK_IsHalted()
T0818 003:328.618 - 0.480ms returns FALSE
T0818 003:328.624 JLINK_HasError()
T0818 003:330.134 JLINK_IsHalted()
T0818 003:330.605 - 0.470ms returns FALSE
T0818 003:330.610 JLINK_HasError()
T0818 003:332.134 JLINK_IsHalted()
T0818 003:332.605 - 0.470ms returns FALSE
T0818 003:332.613 JLINK_HasError()
T0818 003:334.134 JLINK_IsHalted()
T0818 003:334.606 - 0.471ms returns FALSE
T0818 003:334.612 JLINK_HasError()
T0818 003:336.285 JLINK_IsHalted()
T0818 003:336.831 - 0.545ms returns FALSE
T0818 003:336.838 JLINK_HasError()
T0818 003:338.134 JLINK_IsHalted()
T0818 003:338.606 - 0.471ms returns FALSE
T0818 003:338.611 JLINK_HasError()
T0818 003:340.134 JLINK_IsHalted()
T0818 003:340.604 - 0.470ms returns FALSE
T0818 003:340.611 JLINK_HasError()
T0818 003:342.134 JLINK_IsHalted()
T0818 003:342.605 - 0.470ms returns FALSE
T0818 003:342.610 JLINK_HasError()
T0818 003:344.138 JLINK_IsHalted()
T0818 003:344.618 - 0.480ms returns FALSE
T0818 003:344.627 JLINK_HasError()
T0818 003:346.305 JLINK_IsHalted()
T0818 003:346.845 - 0.539ms returns FALSE
T0818 003:346.855 JLINK_HasError()
T0818 003:348.137 JLINK_IsHalted()
T0818 003:348.695 - 0.558ms returns FALSE
T0818 003:348.704 JLINK_HasError()
T0818 003:350.135 JLINK_IsHalted()
T0818 003:350.605 - 0.470ms returns FALSE
T0818 003:350.610 JLINK_HasError()
T0818 003:352.135 JLINK_IsHalted()
T0818 003:352.606 - 0.470ms returns FALSE
T0818 003:352.612 JLINK_HasError()
T0818 003:354.140 JLINK_IsHalted()
T0818 003:354.629 - 0.488ms returns FALSE
T0818 003:354.639 JLINK_HasError()
T0818 003:356.328 JLINK_IsHalted()
T0818 003:356.840 - 0.512ms returns FALSE
T0818 003:356.846 JLINK_HasError()
T0818 003:358.134 JLINK_IsHalted()
T0818 003:358.605 - 0.471ms returns FALSE
T0818 003:358.611 JLINK_HasError()
T0818 003:360.136 JLINK_IsHalted()
T0818 003:360.605 - 0.469ms returns FALSE
T0818 003:360.611 JLINK_HasError()
T0818 003:362.134 JLINK_IsHalted()
T0818 003:362.604 - 0.470ms returns FALSE
T0818 003:362.610 JLINK_HasError()
T0818 003:364.135 JLINK_IsHalted()
T0818 003:364.611 - 0.475ms returns FALSE
T0818 003:364.617 JLINK_HasError()
T0818 003:366.166 JLINK_IsHalted()
T0818 003:366.708 - 0.541ms returns FALSE
T0818 003:366.716 JLINK_HasError()
T0818 003:368.134 JLINK_IsHalted()
T0818 003:368.598 - 0.463ms returns FALSE
T0818 003:368.604 JLINK_HasError()
T0818 003:370.134 JLINK_IsHalted()
T0818 003:370.605 - 0.471ms returns FALSE
T0818 003:370.611 JLINK_HasError()
T0818 003:372.134 JLINK_IsHalted()
T0818 003:372.604 - 0.470ms returns FALSE
T0818 003:372.610 JLINK_HasError()
T0818 003:374.134 JLINK_IsHalted()
T0818 003:374.608 - 0.473ms returns FALSE
T0818 003:374.619 JLINK_HasError()
T0818 003:377.136 JLINK_IsHalted()
T0818 003:377.605 - 0.468ms returns FALSE
T0818 003:377.611 JLINK_HasError()
T0818 003:379.134 JLINK_IsHalted()
T0818 003:379.605 - 0.471ms returns FALSE
T0818 003:379.611 JLINK_HasError()
T0818 003:381.134 JLINK_IsHalted()
T0818 003:381.605 - 0.470ms returns FALSE
T0818 003:381.610 JLINK_HasError()
T0818 003:383.134 JLINK_IsHalted()
T0818 003:383.607 - 0.473ms returns FALSE
T0818 003:383.613 JLINK_HasError()
T0818 003:385.134 JLINK_IsHalted()
T0818 003:385.608 - 0.474ms returns FALSE
T0818 003:385.614 JLINK_HasError()
T0818 003:387.135 JLINK_IsHalted()
T0818 003:387.604 - 0.469ms returns FALSE
T0818 003:387.610 JLINK_HasError()
T0818 003:389.136 JLINK_IsHalted()
T0818 003:389.594 - 0.458ms returns FALSE
T0818 003:389.600 JLINK_HasError()
T0818 003:391.136 JLINK_IsHalted()
T0818 003:391.626 - 0.490ms returns FALSE
T0818 003:391.632 JLINK_HasError()
T0818 003:393.134 JLINK_IsHalted()
T0818 003:393.608 - 0.473ms returns FALSE
T0818 003:393.613 JLINK_HasError()
T0818 003:395.134 JLINK_IsHalted()
T0818 003:395.606 - 0.472ms returns FALSE
T0818 003:395.612 JLINK_HasError()
T0818 003:397.134 JLINK_IsHalted()
T0818 003:397.604 - 0.470ms returns FALSE
T0818 003:397.610 JLINK_HasError()
T0818 003:399.134 JLINK_IsHalted()
T0818 003:399.606 - 0.471ms returns FALSE
T0818 003:399.611 JLINK_HasError()
T0818 003:401.134 JLINK_IsHalted()
T0818 003:401.605 - 0.470ms returns FALSE
T0818 003:401.611 JLINK_HasError()
T0818 003:403.134 JLINK_IsHalted()
T0818 003:403.608 - 0.474ms returns FALSE
T0818 003:403.614 JLINK_HasError()
T0818 003:405.134 JLINK_IsHalted()
T0818 003:405.609 - 0.474ms returns FALSE
T0818 003:405.614 JLINK_HasError()
T0818 003:407.137 JLINK_IsHalted()
T0818 003:407.650 - 0.513ms returns FALSE
T0818 003:407.656 JLINK_HasError()
T0818 003:409.134 JLINK_IsHalted()
T0818 003:409.606 - 0.471ms returns FALSE
T0818 003:409.612 JLINK_HasError()
T0818 003:411.280 JLINK_IsHalted()
T0818 003:411.855 - 0.574ms returns FALSE
T0818 003:411.862 JLINK_HasError()
T0818 003:413.134 JLINK_IsHalted()
T0818 003:413.608 - 0.473ms returns FALSE
T0818 003:413.614 JLINK_HasError()
T0818 003:415.135 JLINK_IsHalted()
T0818 003:415.606 - 0.471ms returns FALSE
T0818 003:415.612 JLINK_HasError()
T0818 003:417.135 JLINK_IsHalted()
T0818 003:417.606 - 0.471ms returns FALSE
T0818 003:417.612 JLINK_HasError()
T0818 003:419.137 JLINK_IsHalted()
T0818 003:419.655 - 0.517ms returns FALSE
T0818 003:419.660 JLINK_HasError()
T0818 003:421.134 JLINK_IsHalted()
T0818 003:421.610 - 0.475ms returns FALSE
T0818 003:421.616 JLINK_HasError()
T0818 003:423.136 JLINK_IsHalted()
T0818 003:423.612 - 0.476ms returns FALSE
T0818 003:423.618 JLINK_HasError()
T0818 003:425.134 JLINK_IsHalted()
T0818 003:425.606 - 0.471ms returns FALSE
T0818 003:425.611 JLINK_HasError()
T0818 003:427.134 JLINK_IsHalted()
T0818 003:429.411   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:429.883 - 2.748ms returns TRUE
T0818 003:429.889 JLINK_ReadReg(R15 (PC))
T0818 003:429.894 - 0.005ms returns 0x20000000
T0818 003:429.899 JLINK_ClrBPEx(BPHandle = 0x00000041)
T0818 003:429.902 - 0.003ms returns 0x00
T0818 003:429.907 JLINK_ReadReg(R0)
T0818 003:429.910 - 0.003ms returns 0x105A7188
T0818 003:431.697 JLINK_HasError()
T0818 003:431.709 JLINK_WriteReg(R0, 0x00000003)
T0818 003:431.714 - 0.005ms returns 0
T0818 003:431.719 JLINK_WriteReg(R1, 0x08000000)
T0818 003:431.722 - 0.003ms returns 0
T0818 003:431.726 JLINK_WriteReg(R2, 0x0000CB58)
T0818 003:431.730 - 0.003ms returns 0
T0818 003:431.734 JLINK_WriteReg(R3, 0x04C11DB7)
T0818 003:431.737 - 0.003ms returns 0
T0818 003:431.741 JLINK_WriteReg(R4, 0x00000000)
T0818 003:431.744 - 0.003ms returns 0
T0818 003:431.748 JLINK_WriteReg(R5, 0x00000000)
T0818 003:431.752 - 0.003ms returns 0
T0818 003:431.756 JLINK_WriteReg(R6, 0x00000000)
T0818 003:431.760 - 0.003ms returns 0
T0818 003:431.763 JLINK_WriteReg(R7, 0x00000000)
T0818 003:431.767 - 0.003ms returns 0
T0818 003:431.771 JLINK_WriteReg(R8, 0x00000000)
T0818 003:431.774 - 0.003ms returns 0
T0818 003:431.778 JLINK_WriteReg(R9, 0x20000180)
T0818 003:431.782 - 0.003ms returns 0
T0818 003:431.786 JLINK_WriteReg(R10, 0x00000000)
T0818 003:431.789 - 0.003ms returns 0
T0818 003:431.793 JLINK_WriteReg(R11, 0x00000000)
T0818 003:431.796 - 0.003ms returns 0
T0818 003:431.800 JLINK_WriteReg(R12, 0x00000000)
T0818 003:431.804 - 0.003ms returns 0
T0818 003:431.808 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:431.812 - 0.003ms returns 0
T0818 003:431.816 JLINK_WriteReg(R14, 0x20000001)
T0818 003:431.819 - 0.003ms returns 0
T0818 003:431.823 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 003:431.827 - 0.003ms returns 0
T0818 003:431.831 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:431.834 - 0.003ms returns 0
T0818 003:431.838 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:431.842 - 0.003ms returns 0
T0818 003:431.846 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:431.849 - 0.003ms returns 0
T0818 003:431.853 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:431.856 - 0.003ms returns 0
T0818 003:431.861 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:431.866 - 0.004ms returns 0x00000042
T0818 003:431.870 JLINK_Go()
T0818 003:431.879   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:434.617 - 2.747ms 
T0818 003:434.629 JLINK_IsHalted()
T0818 003:436.906   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:437.378 - 2.748ms returns TRUE
T0818 003:437.384 JLINK_ReadReg(R15 (PC))
T0818 003:437.388 - 0.004ms returns 0x20000000
T0818 003:437.393 JLINK_ClrBPEx(BPHandle = 0x00000042)
T0818 003:437.396 - 0.003ms returns 0x00
T0818 003:437.401 JLINK_ReadReg(R0)
T0818 003:437.407 - 0.005ms returns 0x00000000
T0818 003:490.074 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T0818 003:490.091   Data:  FE E7
T0818 003:490.106   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 003:490.695 - 0.621ms returns 0x2
T0818 003:490.708 JLINK_HasError()
T0818 003:500.970 JLINK_Close()
T0818 003:502.538   OnDisconnectTarget() start
T0818 003:502.555    J-Link Script File: Executing OnDisconnectTarget()
T0818 003:502.564   CPU_WriteMem(4 bytes @ 0xE0042004)
T0818 003:503.112   CPU_WriteMem(4 bytes @ 0xE0042008)
T0818 003:504.728   OnDisconnectTarget() end - Took 1.08ms
T0818 003:504.743   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:524.866 - 23.895ms
T0818 003:524.886   
T0818 003:524.890   Closed
