T0818 000:003.628   SEGGER J-Link V8.16 Log File
T0818 000:003.749   DLL Compiled: Feb 26 2025 12:07:26
T0818 000:003.757   Logging started @ 2025-08-01 16:45
T0818 000:003.760   Process: G:\keil\keil arm\UV4\UV4.exe
T0818 000:003.775 - 3.764ms 
T0818 000:003.783 JLINK_SetWarnOutHandler(...)
T0818 000:003.786 - 0.004ms 
T0818 000:003.794 JLINK_OpenEx(...)
T0818 000:007.369   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0818 000:008.780   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T0818 000:008.885   Decompressing FW timestamp took 85 us
T0818 000:016.298   Hardware: V9.60
T0818 000:016.311   S/N: 69655018
T0818 000:016.316   OEM: SEGGER
T0818 000:016.321   Feature(s): RDI, GD<PERSON>, <PERSON><PERSON><PERSON>, FlashB<PERSON>, JFlash
T0818 000:017.555   Bootloader: (FW returned invalid version)
T0818 000:018.929   TELNET listener socket opened on port 19021
T0818 000:018.990   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T0818 000:019.091   WEBSRV Webserver running on local port 19080
T0818 000:019.152   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
T0818 000:019.223   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
T0818 000:328.409   Failed to connect to J-Link GUI Server.
T0818 000:328.447 - 324.645ms returns "O.K."
T0818 000:328.466 JLINK_GetEmuCaps()
T0818 000:328.473 - 0.004ms returns 0xB9FF7BBF
T0818 000:328.483 JLINK_TIF_GetAvailable(...)
T0818 000:328.854 - 0.371ms 
T0818 000:328.868 JLINK_SetErrorOutHandler(...)
T0818 000:328.871 - 0.003ms 
T0818 000:328.892 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025elec\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
T0818 000:339.643 - 10.752ms returns 0x00
T0818 000:341.845 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
T0818 000:343.090   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T0818 000:343.108     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T0818 000:347.745   Device "STM32F429IG" selected.
T0818 000:348.013 - 6.145ms returns 0x00
T0818 000:348.025 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T0818 000:348.040   ERROR: Unknown command
T0818 000:348.046 - 0.015ms returns 0x01
T0818 000:348.051 JLINK_GetHardwareVersion()
T0818 000:348.055 - 0.004ms returns 96000
T0818 000:348.060 JLINK_GetDLLVersion()
T0818 000:348.063 - 0.003ms returns 81600
T0818 000:348.068 JLINK_GetOEMString(...)
T0818 000:348.073 JLINK_GetFirmwareString(...)
T0818 000:348.076 - 0.003ms 
T0818 000:351.615 JLINK_GetDLLVersion()
T0818 000:351.646 - 0.030ms returns 81600
T0818 000:351.651 JLINK_GetCompileDateTime()
T0818 000:351.654 - 0.003ms 
T0818 000:352.713 JLINK_GetFirmwareString(...)
T0818 000:352.728 - 0.015ms 
T0818 000:353.759 JLINK_GetHardwareVersion()
T0818 000:353.772 - 0.013ms returns 96000
T0818 000:354.792 JLINK_GetSN()
T0818 000:354.805 - 0.012ms returns 69655018
T0818 000:355.888 JLINK_GetOEMString(...)
T0818 000:358.181 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T0818 000:359.721 - 1.542ms returns 0x00
T0818 000:359.731 JLINK_HasError()
T0818 000:359.743 JLINK_SetSpeed(5000)
T0818 000:360.079 - 0.337ms 
T0818 000:360.086 JLINK_GetId()
T0818 000:362.014   InitTarget() start
T0818 000:362.049    J-Link Script File: Executing InitTarget()
T0818 000:363.669   SWD selected. Executing JTAG -> SWD switching sequence.
T0818 000:368.039   DAP initialized successfully.
T0818 000:380.355   InitTarget() end - Took 16.6ms
T0818 000:382.654   Found SW-DP with ID 0x2BA01477
T0818 000:387.496   DPIDR: 0x2BA01477
T0818 000:388.886   CoreSight SoC-400 or earlier
T0818 000:390.047   Scanning AP map to find all available APs
T0818 000:392.337   AP[1]: Stopped AP scan as end of AP map has been reached
T0818 000:393.519   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
T0818 000:394.628   Iterating through AP map to find AHB-AP to use
T0818 000:397.138   AP[0]: Core found
T0818 000:398.277   AP[0]: AHB-AP ROM base: 0xE00FF000
T0818 000:400.094   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T0818 000:401.362   Found Cortex-M4 r0p1, Little endian.
T0818 000:402.184   -- Max. mem block: 0x00010C40
T0818 000:402.939   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:403.440   CPU_ReadMem(4 bytes @ 0x********)
T0818 000:405.389   FPUnit: 6 code (BP) slots and 2 literal slots
T0818 000:405.410   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T0818 000:405.902   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:406.436   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:406.925   CPU_WriteMem(4 bytes @ 0xE0001000)
T0818 000:407.433   CPU_ReadMem(4 bytes @ 0xE000ED88)
T0818 000:407.928   CPU_WriteMem(4 bytes @ 0xE000ED88)
T0818 000:408.436   CPU_ReadMem(4 bytes @ 0xE000ED88)
T0818 000:408.927   CPU_WriteMem(4 bytes @ 0xE000ED88)
T0818 000:410.851   CoreSight components:
T0818 000:412.065   ROMTbl[0] @ E00FF000
T0818 000:412.083   CPU_ReadMem(64 bytes @ 0xE00FF000)
T0818 000:412.894   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T0818 000:414.812   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T0818 000:414.831   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T0818 000:416.620   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T0818 000:416.636   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T0818 000:418.469   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T0818 000:418.485   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T0818 000:420.289   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T0818 000:420.305   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T0818 000:422.244   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T0818 000:422.269   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T0818 000:424.179   [0][5]: ******** CID B105900D PID 000BB925 ETM
T0818 000:424.632 - 64.545ms returns 0x2BA01477
T0818 000:424.683 JLINK_GetDLLVersion()
T0818 000:424.688 - 0.004ms returns 81600
T0818 000:424.696 JLINK_CORE_GetFound()
T0818 000:424.700 - 0.004ms returns 0xE0000FF
T0818 000:424.705 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T0818 000:424.710   Value=0xE00FF000
T0818 000:424.715 - 0.010ms returns 0
T0818 000:425.855 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T0818 000:425.868   Value=0xE00FF000
T0818 000:425.873 - 0.018ms returns 0
T0818 000:425.878 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T0818 000:425.881   Value=0x********
T0818 000:425.886 - 0.008ms returns 0
T0818 000:425.891 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T0818 000:425.916   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T0818 000:426.464   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T0818 000:426.472 - 0.580ms returns 32 (0x20)
T0818 000:426.477 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T0818 000:426.480   Value=0x00000000
T0818 000:426.485 - 0.008ms returns 0
T0818 000:426.489 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T0818 000:426.493   Value=0x********
T0818 000:426.498 - 0.008ms returns 0
T0818 000:426.502 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T0818 000:426.505   Value=0x********
T0818 000:426.510 - 0.008ms returns 0
T0818 000:426.514 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T0818 000:426.517   Value=0xE0001000
T0818 000:426.522 - 0.008ms returns 0
T0818 000:426.526 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T0818 000:426.530   Value=0x********
T0818 000:426.534 - 0.008ms returns 0
T0818 000:426.539 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T0818 000:426.542   Value=0xE000E000
T0818 000:426.546 - 0.008ms returns 0
T0818 000:426.551 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T0818 000:426.554   Value=0xE000EDF0
T0818 000:426.559 - 0.008ms returns 0
T0818 000:426.563 JLINK_GetDebugInfo(0x01 = Unknown)
T0818 000:426.567   Value=0x00000001
T0818 000:426.571 - 0.008ms returns 0
T0818 000:426.576 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T0818 000:426.581   CPU_ReadMem(4 bytes @ 0xE000ED00)
T0818 000:427.074   Data:  41 C2 0F 41
T0818 000:427.082   Debug reg: CPUID
T0818 000:427.087 - 0.511ms returns 1 (0x1)
T0818 000:427.092 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T0818 000:427.100   Value=0x00000000
T0818 000:427.105 - 0.013ms returns 0
T0818 000:427.109 JLINK_HasError()
T0818 000:427.114 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T0818 000:427.118 - 0.003ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T0818 000:427.122 JLINK_Reset()
T0818 000:427.128   JLINK_GetResetTypeDesc
T0818 000:427.132   - 0.003ms 
T0818 000:428.390   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T0818 000:428.418   CPU is running
T0818 000:428.426   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T0818 000:428.929   CPU is running
T0818 000:428.936   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:430.663   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T0818 000:432.741   Reset: Reset device via AIRCR.SYSRESETREQ.
T0818 000:432.758   CPU is running
T0818 000:432.766   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T0818 000:487.019   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:487.585   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:490.458   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:496.405   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:499.241   CPU_WriteMem(4 bytes @ 0x********)
T0818 000:499.742   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T0818 000:500.287   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:500.791 - 73.668ms 
T0818 000:500.840 JLINK_Halt()
T0818 000:500.845 - 0.004ms returns 0x00
T0818 000:500.851 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T0818 000:500.860   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T0818 000:501.301   Data:  03 00 03 00
T0818 000:501.307   Debug reg: DHCSR
T0818 000:501.312 - 0.461ms returns 1 (0x1)
T0818 000:501.318 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T0818 000:501.321   Debug reg: DHCSR
T0818 000:501.572   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T0818 000:502.065 - 0.746ms returns 0 (0x00000000)
T0818 000:502.072 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T0818 000:502.076   Debug reg: DEMCR
T0818 000:502.082   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T0818 000:502.503 - 0.430ms returns 0 (0x00000000)
T0818 000:507.239 JLINK_GetHWStatus(...)
T0818 000:507.607 - 0.366ms returns 0
T0818 000:510.778 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T0818 000:510.806 - 0.027ms returns 0x06
T0818 000:510.810 JLINK_GetNumBPUnits(Type = 0xF0)
T0818 000:510.814 - 0.003ms returns 0x2000
T0818 000:510.819 JLINK_GetNumWPUnits()
T0818 000:510.822 - 0.003ms returns 4
T0818 000:514.151 JLINK_GetSpeed()
T0818 000:514.179 - 0.027ms returns 4000
T0818 000:516.307 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T0818 000:516.333   CPU_ReadMem(4 bytes @ 0xE000E004)
T0818 000:516.816   Data:  02 00 00 00
T0818 000:516.833 - 0.526ms returns 1 (0x1)
T0818 000:516.840 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T0818 000:516.848   CPU_ReadMem(4 bytes @ 0xE000E004)
T0818 000:517.348   Data:  02 00 00 00
T0818 000:517.362 - 0.521ms returns 1 (0x1)
T0818 000:517.369 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T0818 000:517.373   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T0818 000:517.384   CPU_WriteMem(28 bytes @ 0xE0001000)
T0818 000:517.907 - 0.538ms returns 0x1C
T0818 000:517.917 JLINK_Halt()
T0818 000:517.920 - 0.004ms returns 0x00
T0818 000:517.924 JLINK_IsHalted()
T0818 000:517.929 - 0.004ms returns TRUE
T0818 000:519.886 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 000:519.900   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 000:520.125   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 000:521.981 - 2.095ms returns 0x184
T0818 000:522.029 JLINK_HasError()
T0818 000:522.035 JLINK_WriteReg(R0, 0x08000000)
T0818 000:522.041 - 0.006ms returns 0
T0818 000:522.046 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 000:522.049 - 0.003ms returns 0
T0818 000:522.053 JLINK_WriteReg(R2, 0x00000001)
T0818 000:522.057 - 0.003ms returns 0
T0818 000:522.061 JLINK_WriteReg(R3, 0x00000000)
T0818 000:522.064 - 0.003ms returns 0
T0818 000:522.068 JLINK_WriteReg(R4, 0x00000000)
T0818 000:522.072 - 0.003ms returns 0
T0818 000:522.076 JLINK_WriteReg(R5, 0x00000000)
T0818 000:522.083 - 0.007ms returns 0
T0818 000:522.088 JLINK_WriteReg(R6, 0x00000000)
T0818 000:522.092 - 0.003ms returns 0
T0818 000:522.096 JLINK_WriteReg(R7, 0x00000000)
T0818 000:522.099 - 0.003ms returns 0
T0818 000:522.115 JLINK_WriteReg(R8, 0x00000000)
T0818 000:522.118 - 0.014ms returns 0
T0818 000:522.122 JLINK_WriteReg(R9, 0x20000180)
T0818 000:522.126 - 0.003ms returns 0
T0818 000:522.130 JLINK_WriteReg(R10, 0x00000000)
T0818 000:522.133 - 0.003ms returns 0
T0818 000:522.137 JLINK_WriteReg(R11, 0x00000000)
T0818 000:522.140 - 0.003ms returns 0
T0818 000:522.145 JLINK_WriteReg(R12, 0x00000000)
T0818 000:522.148 - 0.003ms returns 0
T0818 000:522.152 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:522.156 - 0.003ms returns 0
T0818 000:522.160 JLINK_WriteReg(R14, 0x20000001)
T0818 000:522.163 - 0.003ms returns 0
T0818 000:522.175 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 000:522.178 - 0.010ms returns 0
T0818 000:522.182 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:522.186 - 0.003ms returns 0
T0818 000:522.190 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:522.193 - 0.003ms returns 0
T0818 000:522.197 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:522.200 - 0.003ms returns 0
T0818 000:522.204 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:522.208 - 0.003ms returns 0
T0818 000:522.212 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:522.220   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:522.723 - 0.510ms returns 0x00000001
T0818 000:522.730 JLINK_Go()
T0818 000:522.735   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 000:523.221   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:523.689   CPU_WriteMem(4 bytes @ 0xE0002008)
T0818 000:523.700   CPU_WriteMem(4 bytes @ 0xE000200C)
T0818 000:523.705   CPU_WriteMem(4 bytes @ 0xE0002010)
T0818 000:523.710   CPU_WriteMem(4 bytes @ 0xE0002014)
T0818 000:523.715   CPU_WriteMem(4 bytes @ 0xE0002018)
T0818 000:523.720   CPU_WriteMem(4 bytes @ 0xE000201C)
T0818 000:524.916   CPU_WriteMem(4 bytes @ 0xE0001004)
T0818 000:529.145   Memory map 'after startup completion point' is active
T0818 000:529.166 - 6.434ms 
T0818 000:529.179 JLINK_IsHalted()
T0818 000:531.523   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:532.064 - 2.884ms returns TRUE
T0818 000:532.085 JLINK_ReadReg(R15 (PC))
T0818 000:532.091 - 0.006ms returns 0x20000000
T0818 000:532.130 JLINK_ClrBPEx(BPHandle = 0x00000001)
T0818 000:532.136 - 0.005ms returns 0x00
T0818 000:532.140 JLINK_ReadReg(R0)
T0818 000:532.144 - 0.003ms returns 0x00000000
T0818 000:532.409 JLINK_HasError()
T0818 000:532.417 JLINK_WriteReg(R0, 0x08000000)
T0818 000:532.422 - 0.004ms returns 0
T0818 000:532.426 JLINK_WriteReg(R1, 0x00004000)
T0818 000:532.429 - 0.003ms returns 0
T0818 000:532.434 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:532.437 - 0.003ms returns 0
T0818 000:532.442 JLINK_WriteReg(R3, 0x00000000)
T0818 000:532.445 - 0.003ms returns 0
T0818 000:532.449 JLINK_WriteReg(R4, 0x00000000)
T0818 000:532.452 - 0.003ms returns 0
T0818 000:532.456 JLINK_WriteReg(R5, 0x00000000)
T0818 000:532.460 - 0.003ms returns 0
T0818 000:532.465 JLINK_WriteReg(R6, 0x00000000)
T0818 000:532.468 - 0.003ms returns 0
T0818 000:532.472 JLINK_WriteReg(R7, 0x00000000)
T0818 000:532.476 - 0.003ms returns 0
T0818 000:532.480 JLINK_WriteReg(R8, 0x00000000)
T0818 000:532.483 - 0.003ms returns 0
T0818 000:532.487 JLINK_WriteReg(R9, 0x20000180)
T0818 000:532.490 - 0.003ms returns 0
T0818 000:532.494 JLINK_WriteReg(R10, 0x00000000)
T0818 000:532.498 - 0.003ms returns 0
T0818 000:532.502 JLINK_WriteReg(R11, 0x00000000)
T0818 000:532.505 - 0.003ms returns 0
T0818 000:532.509 JLINK_WriteReg(R12, 0x00000000)
T0818 000:532.512 - 0.003ms returns 0
T0818 000:532.517 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:532.520 - 0.003ms returns 0
T0818 000:532.524 JLINK_WriteReg(R14, 0x20000001)
T0818 000:532.528 - 0.003ms returns 0
T0818 000:532.532 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 000:532.536 - 0.003ms returns 0
T0818 000:532.540 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:532.543 - 0.003ms returns 0
T0818 000:532.590 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:532.596 - 0.005ms returns 0
T0818 000:532.600 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:532.604 - 0.003ms returns 0
T0818 000:532.608 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:532.611 - 0.003ms returns 0
T0818 000:532.616 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:532.620 - 0.004ms returns 0x00000002
T0818 000:532.624 JLINK_Go()
T0818 000:532.633   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:535.431 - 2.806ms 
T0818 000:535.446 JLINK_IsHalted()
T0818 000:537.627   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:538.105 - 2.658ms returns TRUE
T0818 000:538.113 JLINK_ReadReg(R15 (PC))
T0818 000:538.117 - 0.004ms returns 0x20000000
T0818 000:538.122 JLINK_ClrBPEx(BPHandle = 0x00000002)
T0818 000:538.125 - 0.004ms returns 0x00
T0818 000:538.130 JLINK_ReadReg(R0)
T0818 000:538.134 - 0.004ms returns 0x00000001
T0818 000:538.139 JLINK_HasError()
T0818 000:538.143 JLINK_WriteReg(R0, 0x08000000)
T0818 000:538.147 - 0.003ms returns 0
T0818 000:538.151 JLINK_WriteReg(R1, 0x00004000)
T0818 000:538.155 - 0.003ms returns 0
T0818 000:538.159 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:538.162 - 0.003ms returns 0
T0818 000:538.166 JLINK_WriteReg(R3, 0x00000000)
T0818 000:538.169 - 0.003ms returns 0
T0818 000:538.173 JLINK_WriteReg(R4, 0x00000000)
T0818 000:538.177 - 0.003ms returns 0
T0818 000:538.181 JLINK_WriteReg(R5, 0x00000000)
T0818 000:538.184 - 0.003ms returns 0
T0818 000:538.188 JLINK_WriteReg(R6, 0x00000000)
T0818 000:538.191 - 0.003ms returns 0
T0818 000:538.195 JLINK_WriteReg(R7, 0x00000000)
T0818 000:538.199 - 0.003ms returns 0
T0818 000:538.203 JLINK_WriteReg(R8, 0x00000000)
T0818 000:538.206 - 0.003ms returns 0
T0818 000:538.210 JLINK_WriteReg(R9, 0x20000180)
T0818 000:538.214 - 0.003ms returns 0
T0818 000:538.218 JLINK_WriteReg(R10, 0x00000000)
T0818 000:538.221 - 0.003ms returns 0
T0818 000:538.225 JLINK_WriteReg(R11, 0x00000000)
T0818 000:538.229 - 0.003ms returns 0
T0818 000:538.233 JLINK_WriteReg(R12, 0x00000000)
T0818 000:538.236 - 0.003ms returns 0
T0818 000:538.240 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:538.244 - 0.003ms returns 0
T0818 000:538.248 JLINK_WriteReg(R14, 0x20000001)
T0818 000:538.252 - 0.003ms returns 0
T0818 000:538.256 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 000:538.259 - 0.003ms returns 0
T0818 000:538.263 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:538.267 - 0.003ms returns 0
T0818 000:538.271 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:538.274 - 0.003ms returns 0
T0818 000:538.278 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:538.281 - 0.003ms returns 0
T0818 000:538.285 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:538.289 - 0.003ms returns 0
T0818 000:538.293 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:538.297 - 0.003ms returns 0x00000003
T0818 000:538.301 JLINK_Go()
T0818 000:538.308   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:541.056 - 2.755ms 
T0818 000:541.068 JLINK_IsHalted()
T0818 000:541.604 - 0.536ms returns FALSE
T0818 000:541.612 JLINK_HasError()
T0818 000:551.413 JLINK_IsHalted()
T0818 000:551.907 - 0.494ms returns FALSE
T0818 000:551.915 JLINK_HasError()
T0818 000:553.404 JLINK_IsHalted()
T0818 000:553.949 - 0.544ms returns FALSE
T0818 000:553.955 JLINK_HasError()
T0818 000:555.404 JLINK_IsHalted()
T0818 000:555.888 - 0.484ms returns FALSE
T0818 000:555.894 JLINK_HasError()
T0818 000:557.464 JLINK_IsHalted()
T0818 000:558.001 - 0.537ms returns FALSE
T0818 000:558.007 JLINK_HasError()
T0818 000:559.409 JLINK_IsHalted()
T0818 000:559.896 - 0.486ms returns FALSE
T0818 000:559.917 JLINK_HasError()
T0818 000:561.406 JLINK_IsHalted()
T0818 000:561.890 - 0.483ms returns FALSE
T0818 000:561.899 JLINK_HasError()
T0818 000:563.406 JLINK_IsHalted()
T0818 000:563.853 - 0.447ms returns FALSE
T0818 000:563.863 JLINK_HasError()
T0818 000:565.404 JLINK_IsHalted()
T0818 000:565.854 - 0.449ms returns FALSE
T0818 000:565.862 JLINK_HasError()
T0818 000:567.406 JLINK_IsHalted()
T0818 000:567.876 - 0.470ms returns FALSE
T0818 000:567.935 JLINK_HasError()
T0818 000:569.405 JLINK_IsHalted()
T0818 000:569.890 - 0.485ms returns FALSE
T0818 000:569.896 JLINK_HasError()
T0818 000:571.407 JLINK_IsHalted()
T0818 000:571.846 - 0.439ms returns FALSE
T0818 000:571.853 JLINK_HasError()
T0818 000:573.403 JLINK_IsHalted()
T0818 000:573.891 - 0.487ms returns FALSE
T0818 000:573.898 JLINK_HasError()
T0818 000:575.406 JLINK_IsHalted()
T0818 000:575.852 - 0.446ms returns FALSE
T0818 000:575.859 JLINK_HasError()
T0818 000:577.403 JLINK_IsHalted()
T0818 000:577.909 - 0.505ms returns FALSE
T0818 000:577.926 JLINK_HasError()
T0818 000:579.405 JLINK_IsHalted()
T0818 000:579.870 - 0.464ms returns FALSE
T0818 000:579.877 JLINK_HasError()
T0818 000:581.405 JLINK_IsHalted()
T0818 000:581.853 - 0.448ms returns FALSE
T0818 000:581.862 JLINK_HasError()
T0818 000:583.408 JLINK_IsHalted()
T0818 000:583.936 - 0.527ms returns FALSE
T0818 000:583.949 JLINK_HasError()
T0818 000:585.403 JLINK_IsHalted()
T0818 000:585.895 - 0.491ms returns FALSE
T0818 000:585.902 JLINK_HasError()
T0818 000:587.407 JLINK_IsHalted()
T0818 000:587.873 - 0.465ms returns FALSE
T0818 000:587.891 JLINK_HasError()
T0818 000:589.411 JLINK_IsHalted()
T0818 000:589.894 - 0.483ms returns FALSE
T0818 000:589.902 JLINK_HasError()
T0818 000:591.408 JLINK_IsHalted()
T0818 000:591.852 - 0.443ms returns FALSE
T0818 000:591.863 JLINK_HasError()
T0818 000:593.406 JLINK_IsHalted()
T0818 000:593.936 - 0.529ms returns FALSE
T0818 000:593.948 JLINK_HasError()
T0818 000:595.403 JLINK_IsHalted()
T0818 000:595.860 - 0.456ms returns FALSE
T0818 000:595.867 JLINK_HasError()
T0818 000:597.407 JLINK_IsHalted()
T0818 000:597.856 - 0.448ms returns FALSE
T0818 000:597.863 JLINK_HasError()
T0818 000:599.402 JLINK_IsHalted()
T0818 000:599.850 - 0.448ms returns FALSE
T0818 000:599.855 JLINK_HasError()
T0818 000:601.402 JLINK_IsHalted()
T0818 000:601.852 - 0.450ms returns FALSE
T0818 000:601.859 JLINK_HasError()
T0818 000:603.401 JLINK_IsHalted()
T0818 000:603.888 - 0.486ms returns FALSE
T0818 000:603.894 JLINK_HasError()
T0818 000:605.400 JLINK_IsHalted()
T0818 000:605.875 - 0.475ms returns FALSE
T0818 000:605.881 JLINK_HasError()
T0818 000:607.400 JLINK_IsHalted()
T0818 000:607.911 - 0.511ms returns FALSE
T0818 000:607.917 JLINK_HasError()
T0818 000:609.401 JLINK_IsHalted()
T0818 000:609.838 - 0.437ms returns FALSE
T0818 000:609.849 JLINK_HasError()
T0818 000:611.400 JLINK_IsHalted()
T0818 000:611.848 - 0.448ms returns FALSE
T0818 000:611.854 JLINK_HasError()
T0818 000:613.400 JLINK_IsHalted()
T0818 000:613.909 - 0.508ms returns FALSE
T0818 000:613.914 JLINK_HasError()
T0818 000:615.400 JLINK_IsHalted()
T0818 000:615.887 - 0.487ms returns FALSE
T0818 000:615.893 JLINK_HasError()
T0818 000:617.400 JLINK_IsHalted()
T0818 000:617.869 - 0.468ms returns FALSE
T0818 000:617.874 JLINK_HasError()
T0818 000:619.400 JLINK_IsHalted()
T0818 000:619.865 - 0.465ms returns FALSE
T0818 000:619.871 JLINK_HasError()
T0818 000:621.405 JLINK_IsHalted()
T0818 000:621.893 - 0.488ms returns FALSE
T0818 000:621.900 JLINK_HasError()
T0818 000:623.404 JLINK_IsHalted()
T0818 000:623.887 - 0.482ms returns FALSE
T0818 000:623.892 JLINK_HasError()
T0818 000:625.401 JLINK_IsHalted()
T0818 000:625.888 - 0.487ms returns FALSE
T0818 000:625.894 JLINK_HasError()
T0818 000:629.403 JLINK_IsHalted()
T0818 000:629.907 - 0.503ms returns FALSE
T0818 000:629.913 JLINK_HasError()
T0818 000:632.402 JLINK_IsHalted()
T0818 000:632.888 - 0.486ms returns FALSE
T0818 000:632.895 JLINK_HasError()
T0818 000:634.402 JLINK_IsHalted()
T0818 000:634.887 - 0.485ms returns FALSE
T0818 000:634.897 JLINK_HasError()
T0818 000:636.400 JLINK_IsHalted()
T0818 000:636.886 - 0.485ms returns FALSE
T0818 000:636.892 JLINK_HasError()
T0818 000:638.400 JLINK_IsHalted()
T0818 000:638.886 - 0.486ms returns FALSE
T0818 000:638.891 JLINK_HasError()
T0818 000:640.405 JLINK_IsHalted()
T0818 000:640.854 - 0.448ms returns FALSE
T0818 000:640.867 JLINK_HasError()
T0818 000:643.418 JLINK_IsHalted()
T0818 000:643.914 - 0.495ms returns FALSE
T0818 000:643.926 JLINK_HasError()
T0818 000:646.402 JLINK_IsHalted()
T0818 000:646.903 - 0.500ms returns FALSE
T0818 000:646.909 JLINK_HasError()
T0818 000:648.400 JLINK_IsHalted()
T0818 000:648.887 - 0.486ms returns FALSE
T0818 000:648.893 JLINK_HasError()
T0818 000:650.414 JLINK_IsHalted()
T0818 000:650.939 - 0.525ms returns FALSE
T0818 000:650.945 JLINK_HasError()
T0818 000:653.401 JLINK_IsHalted()
T0818 000:653.903 - 0.501ms returns FALSE
T0818 000:653.908 JLINK_HasError()
T0818 000:655.400 JLINK_IsHalted()
T0818 000:655.947 - 0.546ms returns FALSE
T0818 000:655.954 JLINK_HasError()
T0818 000:657.400 JLINK_IsHalted()
T0818 000:657.885 - 0.484ms returns FALSE
T0818 000:657.890 JLINK_HasError()
T0818 000:659.400 JLINK_IsHalted()
T0818 000:659.886 - 0.486ms returns FALSE
T0818 000:659.892 JLINK_HasError()
T0818 000:661.401 JLINK_IsHalted()
T0818 000:661.888 - 0.487ms returns FALSE
T0818 000:661.894 JLINK_HasError()
T0818 000:663.402 JLINK_IsHalted()
T0818 000:663.893 - 0.491ms returns FALSE
T0818 000:663.900 JLINK_HasError()
T0818 000:665.400 JLINK_IsHalted()
T0818 000:665.865 - 0.465ms returns FALSE
T0818 000:665.870 JLINK_HasError()
T0818 000:667.400 JLINK_IsHalted()
T0818 000:667.886 - 0.485ms returns FALSE
T0818 000:667.891 JLINK_HasError()
T0818 000:669.400 JLINK_IsHalted()
T0818 000:669.887 - 0.487ms returns FALSE
T0818 000:669.893 JLINK_HasError()
T0818 000:671.402 JLINK_IsHalted()
T0818 000:671.986 - 0.583ms returns FALSE
T0818 000:671.996 JLINK_HasError()
T0818 000:674.401 JLINK_IsHalted()
T0818 000:674.903 - 0.502ms returns FALSE
T0818 000:674.909 JLINK_HasError()
T0818 000:676.400 JLINK_IsHalted()
T0818 000:676.888 - 0.487ms returns FALSE
T0818 000:676.893 JLINK_HasError()
T0818 000:678.400 JLINK_IsHalted()
T0818 000:678.847 - 0.446ms returns FALSE
T0818 000:678.852 JLINK_HasError()
T0818 000:680.401 JLINK_IsHalted()
T0818 000:680.892 - 0.491ms returns FALSE
T0818 000:680.898 JLINK_HasError()
T0818 000:682.400 JLINK_IsHalted()
T0818 000:682.909 - 0.509ms returns FALSE
T0818 000:682.914 JLINK_HasError()
T0818 000:684.400 JLINK_IsHalted()
T0818 000:684.887 - 0.487ms returns FALSE
T0818 000:684.893 JLINK_HasError()
T0818 000:686.401 JLINK_IsHalted()
T0818 000:686.887 - 0.485ms returns FALSE
T0818 000:686.893 JLINK_HasError()
T0818 000:688.402 JLINK_IsHalted()
T0818 000:688.904 - 0.501ms returns FALSE
T0818 000:688.911 JLINK_HasError()
T0818 000:690.401 JLINK_IsHalted()
T0818 000:690.894 - 0.492ms returns FALSE
T0818 000:690.899 JLINK_HasError()
T0818 000:692.400 JLINK_IsHalted()
T0818 000:692.880 - 0.479ms returns FALSE
T0818 000:692.889 JLINK_HasError()
T0818 000:694.404 JLINK_IsHalted()
T0818 000:694.886 - 0.482ms returns FALSE
T0818 000:694.892 JLINK_HasError()
T0818 000:696.400 JLINK_IsHalted()
T0818 000:696.870 - 0.470ms returns FALSE
T0818 000:696.876 JLINK_HasError()
T0818 000:698.400 JLINK_IsHalted()
T0818 000:698.865 - 0.464ms returns FALSE
T0818 000:698.870 JLINK_HasError()
T0818 000:700.404 JLINK_IsHalted()
T0818 000:700.947 - 0.542ms returns FALSE
T0818 000:700.955 JLINK_HasError()
T0818 000:703.403 JLINK_IsHalted()
T0818 000:704.018 - 0.615ms returns FALSE
T0818 000:704.030 JLINK_HasError()
T0818 000:705.400 JLINK_IsHalted()
T0818 000:705.887 - 0.486ms returns FALSE
T0818 000:705.893 JLINK_HasError()
T0818 000:707.400 JLINK_IsHalted()
T0818 000:707.893 - 0.493ms returns FALSE
T0818 000:707.899 JLINK_HasError()
T0818 000:709.400 JLINK_IsHalted()
T0818 000:709.863 - 0.463ms returns FALSE
T0818 000:709.868 JLINK_HasError()
T0818 000:711.403 JLINK_IsHalted()
T0818 000:711.887 - 0.483ms returns FALSE
T0818 000:711.893 JLINK_HasError()
T0818 000:713.421 JLINK_IsHalted()
T0818 000:713.956 - 0.535ms returns FALSE
T0818 000:713.963 JLINK_HasError()
T0818 000:715.404 JLINK_IsHalted()
T0818 000:715.893 - 0.489ms returns FALSE
T0818 000:715.899 JLINK_HasError()
T0818 000:717.400 JLINK_IsHalted()
T0818 000:717.865 - 0.464ms returns FALSE
T0818 000:717.870 JLINK_HasError()
T0818 000:719.400 JLINK_IsHalted()
T0818 000:719.969 - 0.568ms returns FALSE
T0818 000:719.980 JLINK_HasError()
T0818 000:721.403 JLINK_IsHalted()
T0818 000:721.887 - 0.484ms returns FALSE
T0818 000:721.893 JLINK_HasError()
T0818 000:723.400 JLINK_IsHalted()
T0818 000:723.912 - 0.512ms returns FALSE
T0818 000:723.918 JLINK_HasError()
T0818 000:725.441 JLINK_IsHalted()
T0818 000:725.975 - 0.533ms returns FALSE
T0818 000:725.982 JLINK_HasError()
T0818 000:728.403 JLINK_IsHalted()
T0818 000:728.844 - 0.440ms returns FALSE
T0818 000:728.850 JLINK_HasError()
T0818 000:730.405 JLINK_IsHalted()
T0818 000:730.946 - 0.540ms returns FALSE
T0818 000:730.954 JLINK_HasError()
T0818 000:732.400 JLINK_IsHalted()
T0818 000:732.846 - 0.445ms returns FALSE
T0818 000:732.852 JLINK_HasError()
T0818 000:734.400 JLINK_IsHalted()
T0818 000:734.865 - 0.465ms returns FALSE
T0818 000:734.870 JLINK_HasError()
T0818 000:736.278 JLINK_IsHalted()
T0818 000:736.798 - 0.520ms returns FALSE
T0818 000:736.809 JLINK_HasError()
T0818 000:739.280 JLINK_IsHalted()
T0818 000:739.718 - 0.438ms returns FALSE
T0818 000:739.725 JLINK_HasError()
T0818 000:741.281 JLINK_IsHalted()
T0818 000:741.799 - 0.517ms returns FALSE
T0818 000:741.805 JLINK_HasError()
T0818 000:743.278 JLINK_IsHalted()
T0818 000:743.850 - 0.572ms returns FALSE
T0818 000:743.856 JLINK_HasError()
T0818 000:745.278 JLINK_IsHalted()
T0818 000:745.761 - 0.483ms returns FALSE
T0818 000:745.767 JLINK_HasError()
T0818 000:747.277 JLINK_IsHalted()
T0818 000:747.760 - 0.482ms returns FALSE
T0818 000:747.765 JLINK_HasError()
T0818 000:749.278 JLINK_IsHalted()
T0818 000:749.724 - 0.446ms returns FALSE
T0818 000:749.729 JLINK_HasError()
T0818 000:751.283 JLINK_IsHalted()
T0818 000:751.847 - 0.563ms returns FALSE
T0818 000:751.857 JLINK_HasError()
T0818 000:754.280 JLINK_IsHalted()
T0818 000:754.751 - 0.470ms returns FALSE
T0818 000:754.758 JLINK_HasError()
T0818 000:756.281 JLINK_IsHalted()
T0818 000:756.742 - 0.461ms returns FALSE
T0818 000:756.748 JLINK_HasError()
T0818 000:758.279 JLINK_IsHalted()
T0818 000:758.719 - 0.439ms returns FALSE
T0818 000:758.725 JLINK_HasError()
T0818 000:760.379 JLINK_IsHalted()
T0818 000:760.927 - 0.547ms returns FALSE
T0818 000:760.936 JLINK_HasError()
T0818 000:762.279 JLINK_IsHalted()
T0818 000:762.761 - 0.482ms returns FALSE
T0818 000:762.767 JLINK_HasError()
T0818 000:764.278 JLINK_IsHalted()
T0818 000:764.761 - 0.483ms returns FALSE
T0818 000:764.767 JLINK_HasError()
T0818 000:766.278 JLINK_IsHalted()
T0818 000:766.745 - 0.466ms returns FALSE
T0818 000:766.753 JLINK_HasError()
T0818 000:769.279 JLINK_IsHalted()
T0818 000:769.800 - 0.520ms returns FALSE
T0818 000:769.806 JLINK_HasError()
T0818 000:771.279 JLINK_IsHalted()
T0818 000:771.742 - 0.463ms returns FALSE
T0818 000:771.747 JLINK_HasError()
T0818 000:773.277 JLINK_IsHalted()
T0818 000:773.723 - 0.445ms returns FALSE
T0818 000:773.729 JLINK_HasError()
T0818 000:775.277 JLINK_IsHalted()
T0818 000:775.785 - 0.508ms returns FALSE
T0818 000:775.791 JLINK_HasError()
T0818 000:777.278 JLINK_IsHalted()
T0818 000:777.760 - 0.482ms returns FALSE
T0818 000:777.765 JLINK_HasError()
T0818 000:779.283 JLINK_IsHalted()
T0818 000:779.750 - 0.467ms returns FALSE
T0818 000:779.757 JLINK_HasError()
T0818 000:781.278 JLINK_IsHalted()
T0818 000:781.749 - 0.469ms returns FALSE
T0818 000:781.754 JLINK_HasError()
T0818 000:783.282 JLINK_IsHalted()
T0818 000:783.830 - 0.548ms returns FALSE
T0818 000:783.840 JLINK_HasError()
T0818 000:785.279 JLINK_IsHalted()
T0818 000:785.783 - 0.504ms returns FALSE
T0818 000:785.789 JLINK_HasError()
T0818 000:787.278 JLINK_IsHalted()
T0818 000:787.786 - 0.508ms returns FALSE
T0818 000:787.792 JLINK_HasError()
T0818 000:789.280 JLINK_IsHalted()
T0818 000:789.743 - 0.463ms returns FALSE
T0818 000:789.753 JLINK_HasError()
T0818 000:791.281 JLINK_IsHalted()
T0818 000:791.762 - 0.480ms returns FALSE
T0818 000:791.768 JLINK_HasError()
T0818 000:793.278 JLINK_IsHalted()
T0818 000:793.726 - 0.448ms returns FALSE
T0818 000:793.737 JLINK_HasError()
T0818 000:795.278 JLINK_IsHalted()
T0818 000:795.783 - 0.505ms returns FALSE
T0818 000:795.789 JLINK_HasError()
T0818 000:797.278 JLINK_IsHalted()
T0818 000:797.799 - 0.521ms returns FALSE
T0818 000:797.805 JLINK_HasError()
T0818 000:799.284 JLINK_IsHalted()
T0818 000:799.706 - 0.422ms returns FALSE
T0818 000:799.712 JLINK_HasError()
T0818 000:801.283 JLINK_IsHalted()
T0818 000:801.752 - 0.468ms returns FALSE
T0818 000:801.761 JLINK_HasError()
T0818 000:803.290 JLINK_IsHalted()
T0818 000:803.747 - 0.457ms returns FALSE
T0818 000:803.753 JLINK_HasError()
T0818 000:805.277 JLINK_IsHalted()
T0818 000:805.783 - 0.505ms returns FALSE
T0818 000:805.789 JLINK_HasError()
T0818 000:807.278 JLINK_IsHalted()
T0818 000:807.744 - 0.466ms returns FALSE
T0818 000:807.749 JLINK_HasError()
T0818 000:809.277 JLINK_IsHalted()
T0818 000:809.762 - 0.484ms returns FALSE
T0818 000:809.767 JLINK_HasError()
T0818 000:811.279 JLINK_IsHalted()
T0818 000:811.741 - 0.462ms returns FALSE
T0818 000:811.747 JLINK_HasError()
T0818 000:813.279 JLINK_IsHalted()
T0818 000:814.020 - 0.740ms returns FALSE
T0818 000:814.031 JLINK_HasError()
T0818 000:816.285 JLINK_IsHalted()
T0818 000:816.746 - 0.460ms returns FALSE
T0818 000:816.755 JLINK_HasError()
T0818 000:818.285 JLINK_IsHalted()
T0818 000:818.699 - 0.414ms returns FALSE
T0818 000:818.707 JLINK_HasError()
T0818 000:820.398 JLINK_IsHalted()
T0818 000:821.517 - 1.118ms returns FALSE
T0818 000:821.527 JLINK_HasError()
T0818 000:823.464 JLINK_IsHalted()
T0818 000:824.038 - 0.574ms returns FALSE
T0818 000:824.044 JLINK_HasError()
T0818 000:825.278 JLINK_IsHalted()
T0818 000:825.761 - 0.483ms returns FALSE
T0818 000:825.767 JLINK_HasError()
T0818 000:827.278 JLINK_IsHalted()
T0818 000:827.783 - 0.504ms returns FALSE
T0818 000:827.790 JLINK_HasError()
T0818 000:829.277 JLINK_IsHalted()
T0818 000:829.838 - 0.560ms returns FALSE
T0818 000:829.847 JLINK_HasError()
T0818 000:831.280 JLINK_IsHalted()
T0818 000:831.699 - 0.418ms returns FALSE
T0818 000:831.705 JLINK_HasError()
T0818 000:833.279 JLINK_IsHalted()
T0818 000:833.747 - 0.468ms returns FALSE
T0818 000:833.753 JLINK_HasError()
T0818 000:835.278 JLINK_IsHalted()
T0818 000:835.740 - 0.462ms returns FALSE
T0818 000:835.745 JLINK_HasError()
T0818 000:837.278 JLINK_IsHalted()
T0818 000:837.723 - 0.445ms returns FALSE
T0818 000:837.728 JLINK_HasError()
T0818 000:839.280 JLINK_IsHalted()
T0818 000:839.784 - 0.503ms returns FALSE
T0818 000:839.791 JLINK_HasError()
T0818 000:841.278 JLINK_IsHalted()
T0818 000:841.747 - 0.468ms returns FALSE
T0818 000:841.752 JLINK_HasError()
T0818 000:843.284 JLINK_IsHalted()
T0818 000:843.751 - 0.467ms returns FALSE
T0818 000:843.757 JLINK_HasError()
T0818 000:845.278 JLINK_IsHalted()
T0818 000:845.787 - 0.509ms returns FALSE
T0818 000:845.794 JLINK_HasError()
T0818 000:847.285 JLINK_IsHalted()
T0818 000:847.800 - 0.514ms returns FALSE
T0818 000:847.807 JLINK_HasError()
T0818 000:849.281 JLINK_IsHalted()
T0818 000:849.783 - 0.501ms returns FALSE
T0818 000:849.792 JLINK_HasError()
T0818 000:851.283 JLINK_IsHalted()
T0818 000:851.794 - 0.510ms returns FALSE
T0818 000:851.803 JLINK_HasError()
T0818 000:853.285 JLINK_IsHalted()
T0818 000:853.764 - 0.478ms returns FALSE
T0818 000:853.770 JLINK_HasError()
T0818 000:855.278 JLINK_IsHalted()
T0818 000:855.739 - 0.461ms returns FALSE
T0818 000:855.745 JLINK_HasError()
T0818 000:857.279 JLINK_IsHalted()
T0818 000:857.695 - 0.416ms returns FALSE
T0818 000:857.701 JLINK_HasError()
T0818 000:859.283 JLINK_IsHalted()
T0818 000:859.789 - 0.506ms returns FALSE
T0818 000:859.796 JLINK_HasError()
T0818 000:861.277 JLINK_IsHalted()
T0818 000:861.794 - 0.516ms returns FALSE
T0818 000:861.805 JLINK_HasError()
T0818 000:864.280 JLINK_IsHalted()
T0818 000:864.722 - 0.441ms returns FALSE
T0818 000:864.735 JLINK_HasError()
T0818 000:866.283 JLINK_IsHalted()
T0818 000:866.762 - 0.479ms returns FALSE
T0818 000:866.773 JLINK_HasError()
T0818 000:868.278 JLINK_IsHalted()
T0818 000:868.746 - 0.467ms returns FALSE
T0818 000:868.751 JLINK_HasError()
T0818 000:870.289 JLINK_IsHalted()
T0818 000:870.762 - 0.472ms returns FALSE
T0818 000:870.768 JLINK_HasError()
T0818 000:872.278 JLINK_IsHalted()
T0818 000:872.740 - 0.461ms returns FALSE
T0818 000:872.745 JLINK_HasError()
T0818 000:874.284 JLINK_IsHalted()
T0818 000:874.798 - 0.513ms returns FALSE
T0818 000:874.805 JLINK_HasError()
T0818 000:876.277 JLINK_IsHalted()
T0818 000:876.760 - 0.482ms returns FALSE
T0818 000:876.766 JLINK_HasError()
T0818 000:878.282 JLINK_IsHalted()
T0818 000:878.789 - 0.506ms returns FALSE
T0818 000:878.794 JLINK_HasError()
T0818 000:880.286 JLINK_IsHalted()
T0818 000:880.740 - 0.453ms returns FALSE
T0818 000:880.746 JLINK_HasError()
T0818 000:882.278 JLINK_IsHalted()
T0818 000:882.725 - 0.447ms returns FALSE
T0818 000:882.730 JLINK_HasError()
T0818 000:884.278 JLINK_IsHalted()
T0818 000:884.768 - 0.489ms returns FALSE
T0818 000:884.773 JLINK_HasError()
T0818 000:886.278 JLINK_IsHalted()
T0818 000:886.760 - 0.482ms returns FALSE
T0818 000:886.766 JLINK_HasError()
T0818 000:888.278 JLINK_IsHalted()
T0818 000:888.761 - 0.483ms returns FALSE
T0818 000:888.767 JLINK_HasError()
T0818 000:890.286 JLINK_IsHalted()
T0818 000:890.743 - 0.457ms returns FALSE
T0818 000:890.749 JLINK_HasError()
T0818 000:892.279 JLINK_IsHalted()
T0818 000:892.722 - 0.443ms returns FALSE
T0818 000:892.731 JLINK_HasError()
T0818 000:894.280 JLINK_IsHalted()
T0818 000:894.801 - 0.520ms returns FALSE
T0818 000:894.807 JLINK_HasError()
T0818 000:896.278 JLINK_IsHalted()
T0818 000:896.763 - 0.485ms returns FALSE
T0818 000:896.769 JLINK_HasError()
T0818 000:898.278 JLINK_IsHalted()
T0818 000:898.762 - 0.484ms returns FALSE
T0818 000:898.768 JLINK_HasError()
T0818 000:900.282 JLINK_IsHalted()
T0818 000:900.741 - 0.458ms returns FALSE
T0818 000:900.748 JLINK_HasError()
T0818 000:902.278 JLINK_IsHalted()
T0818 000:902.761 - 0.482ms returns FALSE
T0818 000:902.767 JLINK_HasError()
T0818 000:904.278 JLINK_IsHalted()
T0818 000:904.764 - 0.485ms returns FALSE
T0818 000:904.769 JLINK_HasError()
T0818 000:906.278 JLINK_IsHalted()
T0818 000:906.763 - 0.485ms returns FALSE
T0818 000:906.768 JLINK_HasError()
T0818 000:908.277 JLINK_IsHalted()
T0818 000:908.761 - 0.483ms returns FALSE
T0818 000:908.767 JLINK_HasError()
T0818 000:910.288 JLINK_IsHalted()
T0818 000:910.750 - 0.462ms returns FALSE
T0818 000:910.756 JLINK_HasError()
T0818 000:912.278 JLINK_IsHalted()
T0818 000:912.723 - 0.445ms returns FALSE
T0818 000:912.729 JLINK_HasError()
T0818 000:914.277 JLINK_IsHalted()
T0818 000:914.767 - 0.490ms returns FALSE
T0818 000:914.773 JLINK_HasError()
T0818 000:916.277 JLINK_IsHalted()
T0818 000:916.761 - 0.483ms returns FALSE
T0818 000:916.766 JLINK_HasError()
T0818 000:918.280 JLINK_IsHalted()
T0818 000:918.707 - 0.427ms returns FALSE
T0818 000:918.714 JLINK_HasError()
T0818 000:920.367 JLINK_IsHalted()
T0818 000:920.949 - 0.581ms returns FALSE
T0818 000:920.958 JLINK_HasError()
T0818 000:922.278 JLINK_IsHalted()
T0818 000:922.725 - 0.446ms returns FALSE
T0818 000:922.730 JLINK_HasError()
T0818 000:924.278 JLINK_IsHalted()
T0818 000:924.798 - 0.519ms returns FALSE
T0818 000:924.803 JLINK_HasError()
T0818 000:926.283 JLINK_IsHalted()
T0818 000:926.729 - 0.446ms returns FALSE
T0818 000:926.735 JLINK_HasError()
T0818 000:928.278 JLINK_IsHalted()
T0818 000:928.761 - 0.483ms returns FALSE
T0818 000:928.767 JLINK_HasError()
T0818 000:930.380 JLINK_IsHalted()
T0818 000:930.911 - 0.531ms returns FALSE
T0818 000:930.920 JLINK_HasError()
T0818 000:932.278 JLINK_IsHalted()
T0818 000:932.782 - 0.503ms returns FALSE
T0818 000:932.788 JLINK_HasError()
T0818 000:934.278 JLINK_IsHalted()
T0818 000:934.768 - 0.490ms returns FALSE
T0818 000:934.773 JLINK_HasError()
T0818 000:936.280 JLINK_IsHalted()
T0818 000:936.761 - 0.480ms returns FALSE
T0818 000:936.767 JLINK_HasError()
T0818 000:938.277 JLINK_IsHalted()
T0818 000:938.760 - 0.482ms returns FALSE
T0818 000:938.769 JLINK_HasError()
T0818 000:940.373 JLINK_IsHalted()
T0818 000:941.036 - 0.662ms returns FALSE
T0818 000:941.046 JLINK_HasError()
T0818 000:942.284 JLINK_IsHalted()
T0818 000:942.762 - 0.478ms returns FALSE
T0818 000:942.771 JLINK_HasError()
T0818 000:944.465 JLINK_IsHalted()
T0818 000:944.952 - 0.486ms returns FALSE
T0818 000:944.957 JLINK_HasError()
T0818 000:946.280 JLINK_IsHalted()
T0818 000:946.761 - 0.480ms returns FALSE
T0818 000:946.767 JLINK_HasError()
T0818 000:948.278 JLINK_IsHalted()
T0818 000:948.762 - 0.483ms returns FALSE
T0818 000:948.767 JLINK_HasError()
T0818 000:950.371 JLINK_IsHalted()
T0818 000:950.904 - 0.533ms returns FALSE
T0818 000:950.913 JLINK_HasError()
T0818 000:952.278 JLINK_IsHalted()
T0818 000:954.536   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:954.999 - 2.720ms returns TRUE
T0818 000:955.005 JLINK_ReadReg(R15 (PC))
T0818 000:955.010 - 0.004ms returns 0x20000000
T0818 000:955.015 JLINK_ClrBPEx(BPHandle = 0x00000003)
T0818 000:955.019 - 0.004ms returns 0x00
T0818 000:955.023 JLINK_ReadReg(R0)
T0818 000:955.027 - 0.003ms returns 0x00000000
T0818 000:955.389 JLINK_HasError()
T0818 000:955.398 JLINK_WriteReg(R0, 0x08004000)
T0818 000:955.403 - 0.004ms returns 0
T0818 000:955.407 JLINK_WriteReg(R1, 0x00004000)
T0818 000:955.411 - 0.003ms returns 0
T0818 000:955.415 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:955.418 - 0.003ms returns 0
T0818 000:955.422 JLINK_WriteReg(R3, 0x00000000)
T0818 000:955.426 - 0.003ms returns 0
T0818 000:955.430 JLINK_WriteReg(R4, 0x00000000)
T0818 000:955.433 - 0.003ms returns 0
T0818 000:955.437 JLINK_WriteReg(R5, 0x00000000)
T0818 000:955.441 - 0.003ms returns 0
T0818 000:955.445 JLINK_WriteReg(R6, 0x00000000)
T0818 000:955.448 - 0.003ms returns 0
T0818 000:955.452 JLINK_WriteReg(R7, 0x00000000)
T0818 000:955.456 - 0.003ms returns 0
T0818 000:955.460 JLINK_WriteReg(R8, 0x00000000)
T0818 000:955.463 - 0.003ms returns 0
T0818 000:955.467 JLINK_WriteReg(R9, 0x20000180)
T0818 000:955.470 - 0.003ms returns 0
T0818 000:955.474 JLINK_WriteReg(R10, 0x00000000)
T0818 000:955.478 - 0.003ms returns 0
T0818 000:955.482 JLINK_WriteReg(R11, 0x00000000)
T0818 000:955.485 - 0.003ms returns 0
T0818 000:955.489 JLINK_WriteReg(R12, 0x00000000)
T0818 000:955.493 - 0.003ms returns 0
T0818 000:955.497 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:955.501 - 0.003ms returns 0
T0818 000:955.505 JLINK_WriteReg(R14, 0x20000001)
T0818 000:955.508 - 0.003ms returns 0
T0818 000:955.512 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 000:955.516 - 0.003ms returns 0
T0818 000:955.520 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:955.523 - 0.003ms returns 0
T0818 000:955.527 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:955.531 - 0.003ms returns 0
T0818 000:955.535 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:955.538 - 0.003ms returns 0
T0818 000:955.542 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:955.545 - 0.003ms returns 0
T0818 000:955.550 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:955.554 - 0.004ms returns 0x00000004
T0818 000:955.558 JLINK_Go()
T0818 000:955.566   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:958.277 - 2.718ms 
T0818 000:958.287 JLINK_IsHalted()
T0818 000:960.593   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 000:961.752 - 3.464ms returns TRUE
T0818 000:961.765 JLINK_ReadReg(R15 (PC))
T0818 000:961.770 - 0.005ms returns 0x20000000
T0818 000:961.774 JLINK_ClrBPEx(BPHandle = 0x00000004)
T0818 000:961.778 - 0.003ms returns 0x00
T0818 000:961.783 JLINK_ReadReg(R0)
T0818 000:961.786 - 0.003ms returns 0x00000001
T0818 000:961.790 JLINK_HasError()
T0818 000:961.795 JLINK_WriteReg(R0, 0x08004000)
T0818 000:961.799 - 0.003ms returns 0
T0818 000:961.803 JLINK_WriteReg(R1, 0x00004000)
T0818 000:961.807 - 0.003ms returns 0
T0818 000:961.811 JLINK_WriteReg(R2, 0x000000FF)
T0818 000:961.814 - 0.003ms returns 0
T0818 000:961.818 JLINK_WriteReg(R3, 0x00000000)
T0818 000:961.822 - 0.003ms returns 0
T0818 000:961.826 JLINK_WriteReg(R4, 0x00000000)
T0818 000:961.829 - 0.003ms returns 0
T0818 000:961.835 JLINK_WriteReg(R5, 0x00000000)
T0818 000:961.840 - 0.004ms returns 0
T0818 000:961.844 JLINK_WriteReg(R6, 0x00000000)
T0818 000:961.847 - 0.003ms returns 0
T0818 000:961.851 JLINK_WriteReg(R7, 0x00000000)
T0818 000:961.854 - 0.003ms returns 0
T0818 000:961.859 JLINK_WriteReg(R8, 0x00000000)
T0818 000:961.862 - 0.003ms returns 0
T0818 000:961.867 JLINK_WriteReg(R9, 0x20000180)
T0818 000:961.870 - 0.003ms returns 0
T0818 000:961.874 JLINK_WriteReg(R10, 0x00000000)
T0818 000:961.877 - 0.003ms returns 0
T0818 000:961.881 JLINK_WriteReg(R11, 0x00000000)
T0818 000:961.885 - 0.003ms returns 0
T0818 000:961.889 JLINK_WriteReg(R12, 0x00000000)
T0818 000:961.892 - 0.003ms returns 0
T0818 000:961.896 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 000:961.900 - 0.003ms returns 0
T0818 000:961.904 JLINK_WriteReg(R14, 0x20000001)
T0818 000:961.907 - 0.003ms returns 0
T0818 000:961.911 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 000:961.915 - 0.003ms returns 0
T0818 000:961.919 JLINK_WriteReg(XPSR, 0x01000000)
T0818 000:961.922 - 0.003ms returns 0
T0818 000:961.926 JLINK_WriteReg(MSP, 0x20001000)
T0818 000:961.929 - 0.003ms returns 0
T0818 000:961.933 JLINK_WriteReg(PSP, 0x20001000)
T0818 000:961.937 - 0.003ms returns 0
T0818 000:961.941 JLINK_WriteReg(CFBP, 0x00000000)
T0818 000:961.944 - 0.003ms returns 0
T0818 000:961.949 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 000:961.952 - 0.004ms returns 0x00000005
T0818 000:961.956 JLINK_Go()
T0818 000:961.964   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 000:964.798 - 2.841ms 
T0818 000:964.805 JLINK_IsHalted()
T0818 000:965.282 - 0.477ms returns FALSE
T0818 000:965.288 JLINK_HasError()
T0818 000:967.279 JLINK_IsHalted()
T0818 000:967.790 - 0.511ms returns FALSE
T0818 000:967.797 JLINK_HasError()
T0818 000:969.278 JLINK_IsHalted()
T0818 000:969.761 - 0.483ms returns FALSE
T0818 000:969.767 JLINK_HasError()
T0818 000:971.281 JLINK_IsHalted()
T0818 000:971.798 - 0.516ms returns FALSE
T0818 000:971.806 JLINK_HasError()
T0818 000:974.288 JLINK_IsHalted()
T0818 000:974.860 - 0.572ms returns FALSE
T0818 000:974.873 JLINK_HasError()
T0818 000:977.285 JLINK_IsHalted()
T0818 000:977.787 - 0.501ms returns FALSE
T0818 000:977.796 JLINK_HasError()
T0818 000:980.385 JLINK_IsHalted()
T0818 000:980.980 - 0.594ms returns FALSE
T0818 000:980.991 JLINK_HasError()
T0818 000:982.281 JLINK_IsHalted()
T0818 000:982.754 - 0.473ms returns FALSE
T0818 000:982.761 JLINK_HasError()
T0818 000:984.284 JLINK_IsHalted()
T0818 000:984.748 - 0.464ms returns FALSE
T0818 000:984.757 JLINK_HasError()
T0818 000:987.285 JLINK_IsHalted()
T0818 000:987.753 - 0.467ms returns FALSE
T0818 000:987.760 JLINK_HasError()
T0818 000:989.279 JLINK_IsHalted()
T0818 000:989.786 - 0.506ms returns FALSE
T0818 000:989.794 JLINK_HasError()
T0818 000:991.283 JLINK_IsHalted()
T0818 000:991.752 - 0.469ms returns FALSE
T0818 000:991.759 JLINK_HasError()
T0818 000:993.284 JLINK_IsHalted()
T0818 000:993.764 - 0.479ms returns FALSE
T0818 000:993.769 JLINK_HasError()
T0818 000:995.278 JLINK_IsHalted()
T0818 000:995.742 - 0.463ms returns FALSE
T0818 000:995.747 JLINK_HasError()
T0818 000:997.278 JLINK_IsHalted()
T0818 000:997.743 - 0.465ms returns FALSE
T0818 000:997.749 JLINK_HasError()
T0818 001:000.284 JLINK_IsHalted()
T0818 001:000.844 - 0.559ms returns FALSE
T0818 001:000.852 JLINK_HasError()
T0818 001:002.278 JLINK_IsHalted()
T0818 001:002.763 - 0.484ms returns FALSE
T0818 001:002.768 JLINK_HasError()
T0818 001:004.277 JLINK_IsHalted()
T0818 001:004.723 - 0.445ms returns FALSE
T0818 001:004.728 JLINK_HasError()
T0818 001:006.283 JLINK_IsHalted()
T0818 001:006.802 - 0.518ms returns FALSE
T0818 001:006.815 JLINK_HasError()
T0818 001:009.285 JLINK_IsHalted()
T0818 001:009.747 - 0.462ms returns FALSE
T0818 001:009.754 JLINK_HasError()
T0818 001:011.281 JLINK_IsHalted()
T0818 001:011.812 - 0.530ms returns FALSE
T0818 001:011.824 JLINK_HasError()
T0818 001:013.284 JLINK_IsHalted()
T0818 001:013.751 - 0.467ms returns FALSE
T0818 001:013.758 JLINK_HasError()
T0818 001:015.281 JLINK_IsHalted()
T0818 001:015.800 - 0.518ms returns FALSE
T0818 001:015.806 JLINK_HasError()
T0818 001:017.278 JLINK_IsHalted()
T0818 001:017.760 - 0.482ms returns FALSE
T0818 001:017.766 JLINK_HasError()
T0818 001:019.277 JLINK_IsHalted()
T0818 001:019.744 - 0.467ms returns FALSE
T0818 001:019.750 JLINK_HasError()
T0818 001:021.278 JLINK_IsHalted()
T0818 001:021.785 - 0.506ms returns FALSE
T0818 001:021.792 JLINK_HasError()
T0818 001:023.278 JLINK_IsHalted()
T0818 001:023.688 - 0.409ms returns FALSE
T0818 001:023.693 JLINK_HasError()
T0818 001:025.277 JLINK_IsHalted()
T0818 001:025.725 - 0.447ms returns FALSE
T0818 001:025.730 JLINK_HasError()
T0818 001:027.278 JLINK_IsHalted()
T0818 001:027.743 - 0.465ms returns FALSE
T0818 001:027.748 JLINK_HasError()
T0818 001:030.292 JLINK_IsHalted()
T0818 001:030.834 - 0.541ms returns FALSE
T0818 001:030.842 JLINK_HasError()
T0818 001:032.278 JLINK_IsHalted()
T0818 001:032.797 - 0.519ms returns FALSE
T0818 001:032.803 JLINK_HasError()
T0818 001:034.277 JLINK_IsHalted()
T0818 001:034.760 - 0.483ms returns FALSE
T0818 001:034.766 JLINK_HasError()
T0818 001:036.277 JLINK_IsHalted()
T0818 001:036.763 - 0.485ms returns FALSE
T0818 001:036.768 JLINK_HasError()
T0818 001:038.283 JLINK_IsHalted()
T0818 001:038.741 - 0.457ms returns FALSE
T0818 001:038.747 JLINK_HasError()
T0818 001:040.374 JLINK_IsHalted()
T0818 001:040.947 - 0.573ms returns FALSE
T0818 001:040.957 JLINK_HasError()
T0818 001:042.278 JLINK_IsHalted()
T0818 001:042.797 - 0.518ms returns FALSE
T0818 001:042.802 JLINK_HasError()
T0818 001:044.278 JLINK_IsHalted()
T0818 001:044.761 - 0.483ms returns FALSE
T0818 001:044.767 JLINK_HasError()
T0818 001:046.278 JLINK_IsHalted()
T0818 001:046.762 - 0.483ms returns FALSE
T0818 001:046.771 JLINK_HasError()
T0818 001:048.278 JLINK_IsHalted()
T0818 001:048.723 - 0.445ms returns FALSE
T0818 001:048.728 JLINK_HasError()
T0818 001:050.361 JLINK_IsHalted()
T0818 001:050.890 - 0.528ms returns FALSE
T0818 001:050.898 JLINK_HasError()
T0818 001:052.278 JLINK_IsHalted()
T0818 001:052.783 - 0.504ms returns FALSE
T0818 001:052.789 JLINK_HasError()
T0818 001:054.504 JLINK_IsHalted()
T0818 001:057.607 - 3.103ms returns FALSE
T0818 001:057.618 JLINK_HasError()
T0818 001:059.289 JLINK_IsHalted()
T0818 001:059.749 - 0.460ms returns FALSE
T0818 001:059.762 JLINK_HasError()
T0818 001:061.287 JLINK_IsHalted()
T0818 001:062.489 - 1.201ms returns FALSE
T0818 001:062.502 JLINK_HasError()
T0818 001:064.282 JLINK_IsHalted()
T0818 001:064.706 - 0.424ms returns FALSE
T0818 001:064.714 JLINK_HasError()
T0818 001:066.278 JLINK_IsHalted()
T0818 001:067.095 - 0.817ms returns FALSE
T0818 001:067.102 JLINK_HasError()
T0818 001:068.281 JLINK_IsHalted()
T0818 001:068.799 - 0.518ms returns FALSE
T0818 001:068.805 JLINK_HasError()
T0818 001:070.110 JLINK_IsHalted()
T0818 001:070.622 - 0.511ms returns FALSE
T0818 001:070.632 JLINK_HasError()
T0818 001:072.598 JLINK_IsHalted()
T0818 001:073.050 - 0.452ms returns FALSE
T0818 001:073.056 JLINK_HasError()
T0818 001:074.593 JLINK_IsHalted()
T0818 001:075.075 - 0.481ms returns FALSE
T0818 001:075.081 JLINK_HasError()
T0818 001:076.593 JLINK_IsHalted()
T0818 001:077.071 - 0.477ms returns FALSE
T0818 001:077.076 JLINK_HasError()
T0818 001:078.593 JLINK_IsHalted()
T0818 001:079.069 - 0.475ms returns FALSE
T0818 001:079.074 JLINK_HasError()
T0818 001:080.594 JLINK_IsHalted()
T0818 001:081.075 - 0.479ms returns FALSE
T0818 001:081.080 JLINK_HasError()
T0818 001:082.600 JLINK_IsHalted()
T0818 001:083.092 - 0.491ms returns FALSE
T0818 001:083.098 JLINK_HasError()
T0818 001:084.601 JLINK_IsHalted()
T0818 001:085.042 - 0.441ms returns FALSE
T0818 001:085.053 JLINK_HasError()
T0818 001:087.604 JLINK_IsHalted()
T0818 001:088.062 - 0.457ms returns FALSE
T0818 001:088.070 JLINK_HasError()
T0818 001:089.601 JLINK_IsHalted()
T0818 001:090.093 - 0.492ms returns FALSE
T0818 001:090.100 JLINK_HasError()
T0818 001:091.594 JLINK_IsHalted()
T0818 001:092.080 - 0.485ms returns FALSE
T0818 001:092.092 JLINK_HasError()
T0818 001:093.603 JLINK_IsHalted()
T0818 001:094.063 - 0.460ms returns FALSE
T0818 001:094.069 JLINK_HasError()
T0818 001:095.593 JLINK_IsHalted()
T0818 001:096.058 - 0.464ms returns FALSE
T0818 001:096.063 JLINK_HasError()
T0818 001:097.596 JLINK_IsHalted()
T0818 001:098.071 - 0.474ms returns FALSE
T0818 001:098.080 JLINK_HasError()
T0818 001:099.593 JLINK_IsHalted()
T0818 001:100.073 - 0.479ms returns FALSE
T0818 001:100.079 JLINK_HasError()
T0818 001:101.594 JLINK_IsHalted()
T0818 001:102.081 - 0.486ms returns FALSE
T0818 001:102.087 JLINK_HasError()
T0818 001:103.593 JLINK_IsHalted()
T0818 001:104.070 - 0.476ms returns FALSE
T0818 001:104.075 JLINK_HasError()
T0818 001:105.593 JLINK_IsHalted()
T0818 001:106.072 - 0.478ms returns FALSE
T0818 001:106.077 JLINK_HasError()
T0818 001:107.593 JLINK_IsHalted()
T0818 001:108.070 - 0.476ms returns FALSE
T0818 001:108.075 JLINK_HasError()
T0818 001:109.595 JLINK_IsHalted()
T0818 001:110.074 - 0.478ms returns FALSE
T0818 001:110.079 JLINK_HasError()
T0818 001:111.594 JLINK_IsHalted()
T0818 001:112.018 - 0.423ms returns FALSE
T0818 001:112.024 JLINK_HasError()
T0818 001:113.594 JLINK_IsHalted()
T0818 001:114.070 - 0.476ms returns FALSE
T0818 001:114.076 JLINK_HasError()
T0818 001:115.593 JLINK_IsHalted()
T0818 001:116.071 - 0.477ms returns FALSE
T0818 001:116.076 JLINK_HasError()
T0818 001:117.598 JLINK_IsHalted()
T0818 001:118.094 - 0.495ms returns FALSE
T0818 001:118.100 JLINK_HasError()
T0818 001:119.594 JLINK_IsHalted()
T0818 001:120.076 - 0.482ms returns FALSE
T0818 001:120.081 JLINK_HasError()
T0818 001:121.594 JLINK_IsHalted()
T0818 001:122.080 - 0.486ms returns FALSE
T0818 001:122.086 JLINK_HasError()
T0818 001:123.595 JLINK_IsHalted()
T0818 001:124.070 - 0.475ms returns FALSE
T0818 001:124.079 JLINK_HasError()
T0818 001:125.593 JLINK_IsHalted()
T0818 001:126.070 - 0.476ms returns FALSE
T0818 001:126.075 JLINK_HasError()
T0818 001:127.593 JLINK_IsHalted()
T0818 001:128.070 - 0.477ms returns FALSE
T0818 001:128.076 JLINK_HasError()
T0818 001:129.593 JLINK_IsHalted()
T0818 001:130.074 - 0.480ms returns FALSE
T0818 001:130.079 JLINK_HasError()
T0818 001:131.594 JLINK_IsHalted()
T0818 001:132.080 - 0.485ms returns FALSE
T0818 001:132.086 JLINK_HasError()
T0818 001:133.594 JLINK_IsHalted()
T0818 001:134.079 - 0.485ms returns FALSE
T0818 001:134.085 JLINK_HasError()
T0818 001:137.596 JLINK_IsHalted()
T0818 001:138.060 - 0.463ms returns FALSE
T0818 001:138.074 JLINK_HasError()
T0818 001:139.594 JLINK_IsHalted()
T0818 001:140.083 - 0.489ms returns FALSE
T0818 001:140.089 JLINK_HasError()
T0818 001:141.599 JLINK_IsHalted()
T0818 001:142.050 - 0.450ms returns FALSE
T0818 001:142.056 JLINK_HasError()
T0818 001:143.594 JLINK_IsHalted()
T0818 001:144.077 - 0.483ms returns FALSE
T0818 001:144.083 JLINK_HasError()
T0818 001:145.593 JLINK_IsHalted()
T0818 001:146.078 - 0.484ms returns FALSE
T0818 001:146.083 JLINK_HasError()
T0818 001:147.593 JLINK_IsHalted()
T0818 001:148.063 - 0.469ms returns FALSE
T0818 001:148.069 JLINK_HasError()
T0818 001:149.418 JLINK_IsHalted()
T0818 001:149.935 - 0.516ms returns FALSE
T0818 001:149.941 JLINK_HasError()
T0818 001:151.419 JLINK_IsHalted()
T0818 001:151.889 - 0.470ms returns FALSE
T0818 001:151.895 JLINK_HasError()
T0818 001:153.417 JLINK_IsHalted()
T0818 001:153.903 - 0.485ms returns FALSE
T0818 001:153.908 JLINK_HasError()
T0818 001:155.417 JLINK_IsHalted()
T0818 001:155.893 - 0.476ms returns FALSE
T0818 001:155.899 JLINK_HasError()
T0818 001:157.417 JLINK_IsHalted()
T0818 001:157.892 - 0.474ms returns FALSE
T0818 001:157.898 JLINK_HasError()
T0818 001:159.417 JLINK_IsHalted()
T0818 001:159.908 - 0.490ms returns FALSE
T0818 001:159.913 JLINK_HasError()
T0818 001:161.418 JLINK_IsHalted()
T0818 001:161.887 - 0.469ms returns FALSE
T0818 001:161.893 JLINK_HasError()
T0818 001:163.419 JLINK_IsHalted()
T0818 001:163.934 - 0.513ms returns FALSE
T0818 001:163.940 JLINK_HasError()
T0818 001:165.429 JLINK_IsHalted()
T0818 001:165.888 - 0.458ms returns FALSE
T0818 001:165.895 JLINK_HasError()
T0818 001:167.427 JLINK_IsHalted()
T0818 001:167.969 - 0.542ms returns FALSE
T0818 001:167.976 JLINK_HasError()
T0818 001:169.418 JLINK_IsHalted()
T0818 001:169.892 - 0.474ms returns FALSE
T0818 001:169.898 JLINK_HasError()
T0818 001:171.418 JLINK_IsHalted()
T0818 001:171.888 - 0.469ms returns FALSE
T0818 001:171.894 JLINK_HasError()
T0818 001:173.417 JLINK_IsHalted()
T0818 001:173.903 - 0.485ms returns FALSE
T0818 001:173.908 JLINK_HasError()
T0818 001:175.423 JLINK_IsHalted()
T0818 001:175.893 - 0.469ms returns FALSE
T0818 001:175.899 JLINK_HasError()
T0818 001:177.417 JLINK_IsHalted()
T0818 001:177.903 - 0.486ms returns FALSE
T0818 001:177.909 JLINK_HasError()
T0818 001:179.417 JLINK_IsHalted()
T0818 001:179.922 - 0.504ms returns FALSE
T0818 001:179.927 JLINK_HasError()
T0818 001:181.419 JLINK_IsHalted()
T0818 001:181.887 - 0.467ms returns FALSE
T0818 001:181.893 JLINK_HasError()
T0818 001:183.417 JLINK_IsHalted()
T0818 001:183.865 - 0.447ms returns FALSE
T0818 001:183.870 JLINK_HasError()
T0818 001:185.417 JLINK_IsHalted()
T0818 001:185.887 - 0.469ms returns FALSE
T0818 001:185.893 JLINK_HasError()
T0818 001:187.417 JLINK_IsHalted()
T0818 001:187.939 - 0.521ms returns FALSE
T0818 001:187.950 JLINK_HasError()
T0818 001:189.766 JLINK_IsHalted()
T0818 001:190.497 - 0.731ms returns FALSE
T0818 001:190.509 JLINK_HasError()
T0818 001:192.418 JLINK_IsHalted()
T0818 001:192.893 - 0.475ms returns FALSE
T0818 001:192.899 JLINK_HasError()
T0818 001:194.423 JLINK_IsHalted()
T0818 001:194.852 - 0.428ms returns FALSE
T0818 001:194.862 JLINK_HasError()
T0818 001:196.418 JLINK_IsHalted()
T0818 001:196.969 - 0.550ms returns FALSE
T0818 001:196.980 JLINK_HasError()
T0818 001:199.422 JLINK_IsHalted()
T0818 001:199.876 - 0.454ms returns FALSE
T0818 001:199.885 JLINK_HasError()
T0818 001:201.420 JLINK_IsHalted()
T0818 001:201.910 - 0.489ms returns FALSE
T0818 001:201.916 JLINK_HasError()
T0818 001:203.640 JLINK_IsHalted()
T0818 001:204.172 - 0.531ms returns FALSE
T0818 001:204.177 JLINK_HasError()
T0818 001:205.417 JLINK_IsHalted()
T0818 001:205.893 - 0.475ms returns FALSE
T0818 001:205.899 JLINK_HasError()
T0818 001:207.420 JLINK_IsHalted()
T0818 001:207.921 - 0.501ms returns FALSE
T0818 001:207.927 JLINK_HasError()
T0818 001:209.465 JLINK_IsHalted()
T0818 001:210.048 - 0.582ms returns FALSE
T0818 001:210.055 JLINK_HasError()
T0818 001:212.422 JLINK_IsHalted()
T0818 001:213.001 - 0.579ms returns FALSE
T0818 001:213.012 JLINK_HasError()
T0818 001:214.418 JLINK_IsHalted()
T0818 001:214.886 - 0.468ms returns FALSE
T0818 001:214.892 JLINK_HasError()
T0818 001:216.417 JLINK_IsHalted()
T0818 001:216.903 - 0.485ms returns FALSE
T0818 001:216.908 JLINK_HasError()
T0818 001:218.418 JLINK_IsHalted()
T0818 001:218.891 - 0.472ms returns FALSE
T0818 001:218.897 JLINK_HasError()
T0818 001:220.423 JLINK_IsHalted()
T0818 001:220.925 - 0.502ms returns FALSE
T0818 001:220.933 JLINK_HasError()
T0818 001:222.417 JLINK_IsHalted()
T0818 001:222.921 - 0.504ms returns FALSE
T0818 001:222.927 JLINK_HasError()
T0818 001:224.417 JLINK_IsHalted()
T0818 001:224.885 - 0.468ms returns FALSE
T0818 001:224.891 JLINK_HasError()
T0818 001:226.417 JLINK_IsHalted()
T0818 001:226.945 - 0.527ms returns FALSE
T0818 001:226.950 JLINK_HasError()
T0818 001:228.421 JLINK_IsHalted()
T0818 001:228.926 - 0.504ms returns FALSE
T0818 001:228.937 JLINK_HasError()
T0818 001:230.421 JLINK_IsHalted()
T0818 001:230.934 - 0.512ms returns FALSE
T0818 001:230.941 JLINK_HasError()
T0818 001:232.419 JLINK_IsHalted()
T0818 001:232.924 - 0.504ms returns FALSE
T0818 001:232.930 JLINK_HasError()
T0818 001:235.425 JLINK_IsHalted()
T0818 001:235.978 - 0.552ms returns FALSE
T0818 001:235.985 JLINK_HasError()
T0818 001:237.418 JLINK_IsHalted()
T0818 001:237.910 - 0.491ms returns FALSE
T0818 001:237.916 JLINK_HasError()
T0818 001:239.419 JLINK_IsHalted()
T0818 001:239.885 - 0.466ms returns FALSE
T0818 001:239.894 JLINK_HasError()
T0818 001:241.420 JLINK_IsHalted()
T0818 001:241.933 - 0.513ms returns FALSE
T0818 001:241.939 JLINK_HasError()
T0818 001:243.417 JLINK_IsHalted()
T0818 001:243.893 - 0.475ms returns FALSE
T0818 001:243.899 JLINK_HasError()
T0818 001:245.421 JLINK_IsHalted()
T0818 001:245.934 - 0.513ms returns FALSE
T0818 001:245.940 JLINK_HasError()
T0818 001:247.475 JLINK_IsHalted()
T0818 001:247.949 - 0.473ms returns FALSE
T0818 001:247.958 JLINK_HasError()
T0818 001:250.424 JLINK_IsHalted()
T0818 001:251.020 - 0.595ms returns FALSE
T0818 001:251.031 JLINK_HasError()
T0818 001:252.418 JLINK_IsHalted()
T0818 001:252.894 - 0.476ms returns FALSE
T0818 001:252.900 JLINK_HasError()
T0818 001:254.417 JLINK_IsHalted()
T0818 001:254.893 - 0.475ms returns FALSE
T0818 001:254.899 JLINK_HasError()
T0818 001:256.417 JLINK_IsHalted()
T0818 001:256.892 - 0.475ms returns FALSE
T0818 001:256.898 JLINK_HasError()
T0818 001:258.417 JLINK_IsHalted()
T0818 001:258.856 - 0.438ms returns FALSE
T0818 001:258.861 JLINK_HasError()
T0818 001:260.424 JLINK_IsHalted()
T0818 001:261.062 - 0.637ms returns FALSE
T0818 001:261.072 JLINK_HasError()
T0818 001:262.419 JLINK_IsHalted()
T0818 001:262.955 - 0.535ms returns FALSE
T0818 001:262.961 JLINK_HasError()
T0818 001:264.417 JLINK_IsHalted()
T0818 001:264.892 - 0.474ms returns FALSE
T0818 001:264.898 JLINK_HasError()
T0818 001:266.417 JLINK_IsHalted()
T0818 001:266.901 - 0.484ms returns FALSE
T0818 001:266.907 JLINK_HasError()
T0818 001:268.420 JLINK_IsHalted()
T0818 001:268.934 - 0.513ms returns FALSE
T0818 001:268.940 JLINK_HasError()
T0818 001:271.421 JLINK_IsHalted()
T0818 001:271.870 - 0.448ms returns FALSE
T0818 001:271.877 JLINK_HasError()
T0818 001:273.417 JLINK_IsHalted()
T0818 001:273.903 - 0.485ms returns FALSE
T0818 001:273.908 JLINK_HasError()
T0818 001:275.417 JLINK_IsHalted()
T0818 001:275.872 - 0.454ms returns FALSE
T0818 001:275.877 JLINK_HasError()
T0818 001:277.421 JLINK_IsHalted()
T0818 001:277.850 - 0.428ms returns FALSE
T0818 001:277.857 JLINK_HasError()
T0818 001:279.417 JLINK_IsHalted()
T0818 001:279.903 - 0.485ms returns FALSE
T0818 001:279.908 JLINK_HasError()
T0818 001:281.440 JLINK_IsHalted()
T0818 001:281.991 - 0.551ms returns FALSE
T0818 001:281.998 JLINK_HasError()
T0818 001:284.420 JLINK_IsHalted()
T0818 001:284.878 - 0.457ms returns FALSE
T0818 001:284.884 JLINK_HasError()
T0818 001:286.417 JLINK_IsHalted()
T0818 001:286.893 - 0.475ms returns FALSE
T0818 001:286.899 JLINK_HasError()
T0818 001:288.417 JLINK_IsHalted()
T0818 001:289.878 - 1.460ms returns FALSE
T0818 001:289.884 JLINK_HasError()
T0818 001:291.419 JLINK_IsHalted()
T0818 001:291.971 - 0.551ms returns FALSE
T0818 001:291.981 JLINK_HasError()
T0818 001:293.418 JLINK_IsHalted()
T0818 001:293.894 - 0.475ms returns FALSE
T0818 001:293.899 JLINK_HasError()
T0818 001:295.417 JLINK_IsHalted()
T0818 001:295.902 - 0.485ms returns FALSE
T0818 001:295.908 JLINK_HasError()
T0818 001:297.417 JLINK_IsHalted()
T0818 001:297.921 - 0.504ms returns FALSE
T0818 001:297.927 JLINK_HasError()
T0818 001:299.417 JLINK_IsHalted()
T0818 001:299.874 - 0.456ms returns FALSE
T0818 001:299.880 JLINK_HasError()
T0818 001:301.421 JLINK_IsHalted()
T0818 001:301.903 - 0.482ms returns FALSE
T0818 001:301.912 JLINK_HasError()
T0818 001:303.552 JLINK_IsHalted()
T0818 001:304.101 - 0.548ms returns FALSE
T0818 001:304.107 JLINK_HasError()
T0818 001:305.417 JLINK_IsHalted()
T0818 001:305.894 - 0.476ms returns FALSE
T0818 001:305.899 JLINK_HasError()
T0818 001:307.424 JLINK_IsHalted()
T0818 001:307.869 - 0.444ms returns FALSE
T0818 001:307.876 JLINK_HasError()
T0818 001:309.420 JLINK_IsHalted()
T0818 001:309.847 - 0.427ms returns FALSE
T0818 001:309.854 JLINK_HasError()
T0818 001:311.419 JLINK_IsHalted()
T0818 001:311.903 - 0.483ms returns FALSE
T0818 001:311.909 JLINK_HasError()
T0818 001:313.418 JLINK_IsHalted()
T0818 001:313.923 - 0.505ms returns FALSE
T0818 001:313.930 JLINK_HasError()
T0818 001:315.417 JLINK_IsHalted()
T0818 001:315.931 - 0.513ms returns FALSE
T0818 001:315.938 JLINK_HasError()
T0818 001:317.417 JLINK_IsHalted()
T0818 001:317.892 - 0.474ms returns FALSE
T0818 001:317.898 JLINK_HasError()
T0818 001:319.417 JLINK_IsHalted()
T0818 001:319.864 - 0.447ms returns FALSE
T0818 001:319.870 JLINK_HasError()
T0818 001:321.419 JLINK_IsHalted()
T0818 001:321.903 - 0.484ms returns FALSE
T0818 001:321.909 JLINK_HasError()
T0818 001:323.490 JLINK_IsHalted()
T0818 001:324.073 - 0.582ms returns FALSE
T0818 001:324.081 JLINK_HasError()
T0818 001:325.417 JLINK_IsHalted()
T0818 001:325.873 - 0.455ms returns FALSE
T0818 001:325.878 JLINK_HasError()
T0818 001:327.418 JLINK_IsHalted()
T0818 001:327.864 - 0.446ms returns FALSE
T0818 001:327.872 JLINK_HasError()
T0818 001:329.417 JLINK_IsHalted()
T0818 001:329.864 - 0.446ms returns FALSE
T0818 001:329.869 JLINK_HasError()
T0818 001:331.419 JLINK_IsHalted()
T0818 001:331.894 - 0.474ms returns FALSE
T0818 001:331.900 JLINK_HasError()
T0818 001:333.417 JLINK_IsHalted()
T0818 001:333.892 - 0.475ms returns FALSE
T0818 001:333.898 JLINK_HasError()
T0818 001:335.417 JLINK_IsHalted()
T0818 001:335.893 - 0.475ms returns FALSE
T0818 001:335.898 JLINK_HasError()
T0818 001:337.417 JLINK_IsHalted()
T0818 001:337.935 - 0.517ms returns FALSE
T0818 001:337.940 JLINK_HasError()
T0818 001:339.425 JLINK_IsHalted()
T0818 001:339.869 - 0.444ms returns FALSE
T0818 001:339.876 JLINK_HasError()
T0818 001:341.424 JLINK_IsHalted()
T0818 001:341.978 - 0.553ms returns FALSE
T0818 001:341.984 JLINK_HasError()
T0818 001:343.514 JLINK_IsHalted()
T0818 001:344.091 - 0.576ms returns FALSE
T0818 001:344.097 JLINK_HasError()
T0818 001:345.417 JLINK_IsHalted()
T0818 001:345.893 - 0.475ms returns FALSE
T0818 001:345.898 JLINK_HasError()
T0818 001:347.417 JLINK_IsHalted()
T0818 001:347.862 - 0.445ms returns FALSE
T0818 001:347.868 JLINK_HasError()
T0818 001:349.418 JLINK_IsHalted()
T0818 001:349.892 - 0.473ms returns FALSE
T0818 001:349.898 JLINK_HasError()
T0818 001:351.420 JLINK_IsHalted()
T0818 001:353.739   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:354.219 - 2.799ms returns TRUE
T0818 001:354.229 JLINK_ReadReg(R15 (PC))
T0818 001:354.235 - 0.006ms returns 0x20000000
T0818 001:354.241 JLINK_ClrBPEx(BPHandle = 0x00000005)
T0818 001:354.245 - 0.004ms returns 0x00
T0818 001:354.258 JLINK_ReadReg(R0)
T0818 001:354.263 - 0.004ms returns 0x00000000
T0818 001:354.599 JLINK_HasError()
T0818 001:354.608 JLINK_WriteReg(R0, 0x08008000)
T0818 001:354.613 - 0.004ms returns 0
T0818 001:354.617 JLINK_WriteReg(R1, 0x00004000)
T0818 001:354.621 - 0.003ms returns 0
T0818 001:354.625 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:354.628 - 0.003ms returns 0
T0818 001:354.632 JLINK_WriteReg(R3, 0x00000000)
T0818 001:354.635 - 0.003ms returns 0
T0818 001:354.639 JLINK_WriteReg(R4, 0x00000000)
T0818 001:354.643 - 0.003ms returns 0
T0818 001:354.647 JLINK_WriteReg(R5, 0x00000000)
T0818 001:354.651 - 0.003ms returns 0
T0818 001:354.654 JLINK_WriteReg(R6, 0x00000000)
T0818 001:354.658 - 0.003ms returns 0
T0818 001:354.662 JLINK_WriteReg(R7, 0x00000000)
T0818 001:354.665 - 0.003ms returns 0
T0818 001:354.669 JLINK_WriteReg(R8, 0x00000000)
T0818 001:354.673 - 0.003ms returns 0
T0818 001:354.677 JLINK_WriteReg(R9, 0x20000180)
T0818 001:354.680 - 0.003ms returns 0
T0818 001:354.684 JLINK_WriteReg(R10, 0x00000000)
T0818 001:354.688 - 0.003ms returns 0
T0818 001:354.692 JLINK_WriteReg(R11, 0x00000000)
T0818 001:354.701 - 0.009ms returns 0
T0818 001:354.705 JLINK_WriteReg(R12, 0x00000000)
T0818 001:354.709 - 0.003ms returns 0
T0818 001:354.713 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:354.716 - 0.003ms returns 0
T0818 001:354.721 JLINK_WriteReg(R14, 0x20000001)
T0818 001:354.724 - 0.003ms returns 0
T0818 001:354.728 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 001:354.731 - 0.003ms returns 0
T0818 001:354.736 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:354.739 - 0.003ms returns 0
T0818 001:354.743 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:354.747 - 0.003ms returns 0
T0818 001:354.754 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:354.758 - 0.004ms returns 0
T0818 001:354.762 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:354.766 - 0.003ms returns 0
T0818 001:354.770 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:354.775 - 0.004ms returns 0x00000006
T0818 001:354.779 JLINK_Go()
T0818 001:354.788   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:357.512 - 2.732ms 
T0818 001:357.524 JLINK_IsHalted()
T0818 001:359.783   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:360.369 - 2.844ms returns TRUE
T0818 001:360.377 JLINK_ReadReg(R15 (PC))
T0818 001:360.382 - 0.004ms returns 0x20000000
T0818 001:360.386 JLINK_ClrBPEx(BPHandle = 0x00000006)
T0818 001:360.390 - 0.003ms returns 0x00
T0818 001:360.394 JLINK_ReadReg(R0)
T0818 001:360.397 - 0.003ms returns 0x00000001
T0818 001:360.402 JLINK_HasError()
T0818 001:360.406 JLINK_WriteReg(R0, 0x08008000)
T0818 001:360.410 - 0.003ms returns 0
T0818 001:360.416 JLINK_WriteReg(R1, 0x00004000)
T0818 001:360.419 - 0.003ms returns 0
T0818 001:360.423 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:360.427 - 0.003ms returns 0
T0818 001:360.431 JLINK_WriteReg(R3, 0x00000000)
T0818 001:360.435 - 0.003ms returns 0
T0818 001:360.439 JLINK_WriteReg(R4, 0x00000000)
T0818 001:360.442 - 0.003ms returns 0
T0818 001:360.446 JLINK_WriteReg(R5, 0x00000000)
T0818 001:360.449 - 0.003ms returns 0
T0818 001:360.453 JLINK_WriteReg(R6, 0x00000000)
T0818 001:360.457 - 0.003ms returns 0
T0818 001:360.461 JLINK_WriteReg(R7, 0x00000000)
T0818 001:360.464 - 0.003ms returns 0
T0818 001:360.468 JLINK_WriteReg(R8, 0x00000000)
T0818 001:360.472 - 0.003ms returns 0
T0818 001:360.476 JLINK_WriteReg(R9, 0x20000180)
T0818 001:360.479 - 0.003ms returns 0
T0818 001:360.483 JLINK_WriteReg(R10, 0x00000000)
T0818 001:360.486 - 0.003ms returns 0
T0818 001:360.490 JLINK_WriteReg(R11, 0x00000000)
T0818 001:360.494 - 0.003ms returns 0
T0818 001:360.498 JLINK_WriteReg(R12, 0x00000000)
T0818 001:360.501 - 0.003ms returns 0
T0818 001:360.505 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:360.509 - 0.003ms returns 0
T0818 001:360.513 JLINK_WriteReg(R14, 0x20000001)
T0818 001:360.516 - 0.003ms returns 0
T0818 001:360.520 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 001:360.524 - 0.003ms returns 0
T0818 001:360.528 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:360.531 - 0.003ms returns 0
T0818 001:360.535 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:360.549 - 0.013ms returns 0
T0818 001:360.553 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:360.557 - 0.003ms returns 0
T0818 001:360.561 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:360.564 - 0.003ms returns 0
T0818 001:360.569 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:360.572 - 0.004ms returns 0x00000007
T0818 001:360.576 JLINK_Go()
T0818 001:360.583   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:364.180 - 3.603ms 
T0818 001:364.199 JLINK_IsHalted()
T0818 001:364.640 - 0.441ms returns FALSE
T0818 001:364.648 JLINK_HasError()
T0818 001:366.418 JLINK_IsHalted()
T0818 001:366.892 - 0.473ms returns FALSE
T0818 001:366.903 JLINK_HasError()
T0818 001:368.418 JLINK_IsHalted()
T0818 001:368.909 - 0.490ms returns FALSE
T0818 001:368.915 JLINK_HasError()
T0818 001:370.424 JLINK_IsHalted()
T0818 001:371.062 - 0.637ms returns FALSE
T0818 001:371.072 JLINK_HasError()
T0818 001:372.419 JLINK_IsHalted()
T0818 001:372.909 - 0.490ms returns FALSE
T0818 001:372.915 JLINK_HasError()
T0818 001:374.417 JLINK_IsHalted()
T0818 001:374.936 - 0.518ms returns FALSE
T0818 001:374.941 JLINK_HasError()
T0818 001:376.417 JLINK_IsHalted()
T0818 001:376.893 - 0.475ms returns FALSE
T0818 001:376.900 JLINK_HasError()
T0818 001:378.417 JLINK_IsHalted()
T0818 001:378.902 - 0.485ms returns FALSE
T0818 001:378.908 JLINK_HasError()
T0818 001:380.422 JLINK_IsHalted()
T0818 001:380.966 - 0.544ms returns FALSE
T0818 001:380.975 JLINK_HasError()
T0818 001:382.418 JLINK_IsHalted()
T0818 001:382.923 - 0.504ms returns FALSE
T0818 001:382.929 JLINK_HasError()
T0818 001:384.417 JLINK_IsHalted()
T0818 001:384.887 - 0.469ms returns FALSE
T0818 001:384.896 JLINK_HasError()
T0818 001:386.417 JLINK_IsHalted()
T0818 001:387.102 - 0.684ms returns FALSE
T0818 001:387.112 JLINK_HasError()
T0818 001:388.419 JLINK_IsHalted()
T0818 001:390.835 - 2.415ms returns FALSE
T0818 001:390.849 JLINK_HasError()
T0818 001:392.426 JLINK_IsHalted()
T0818 001:392.892 - 0.465ms returns FALSE
T0818 001:392.904 JLINK_HasError()
T0818 001:394.417 JLINK_IsHalted()
T0818 001:394.872 - 0.454ms returns FALSE
T0818 001:394.878 JLINK_HasError()
T0818 001:396.417 JLINK_IsHalted()
T0818 001:396.909 - 0.491ms returns FALSE
T0818 001:396.915 JLINK_HasError()
T0818 001:398.417 JLINK_IsHalted()
T0818 001:398.923 - 0.505ms returns FALSE
T0818 001:398.929 JLINK_HasError()
T0818 001:400.423 JLINK_IsHalted()
T0818 001:400.970 - 0.546ms returns FALSE
T0818 001:400.977 JLINK_HasError()
T0818 001:402.417 JLINK_IsHalted()
T0818 001:402.890 - 0.472ms returns FALSE
T0818 001:402.898 JLINK_HasError()
T0818 001:405.420 JLINK_IsHalted()
T0818 001:405.867 - 0.446ms returns FALSE
T0818 001:405.874 JLINK_HasError()
T0818 001:407.417 JLINK_IsHalted()
T0818 001:407.902 - 0.484ms returns FALSE
T0818 001:407.908 JLINK_HasError()
T0818 001:409.417 JLINK_IsHalted()
T0818 001:409.923 - 0.505ms returns FALSE
T0818 001:409.928 JLINK_HasError()
T0818 001:411.420 JLINK_IsHalted()
T0818 001:411.889 - 0.468ms returns FALSE
T0818 001:411.895 JLINK_HasError()
T0818 001:413.420 JLINK_IsHalted()
T0818 001:413.903 - 0.482ms returns FALSE
T0818 001:413.909 JLINK_HasError()
T0818 001:415.419 JLINK_IsHalted()
T0818 001:415.895 - 0.475ms returns FALSE
T0818 001:415.900 JLINK_HasError()
T0818 001:418.420 JLINK_IsHalted()
T0818 001:418.913 - 0.492ms returns FALSE
T0818 001:418.919 JLINK_HasError()
T0818 001:420.419 JLINK_IsHalted()
T0818 001:420.924 - 0.504ms returns FALSE
T0818 001:420.930 JLINK_HasError()
T0818 001:422.417 JLINK_IsHalted()
T0818 001:422.893 - 0.476ms returns FALSE
T0818 001:422.899 JLINK_HasError()
T0818 001:424.417 JLINK_IsHalted()
T0818 001:424.902 - 0.484ms returns FALSE
T0818 001:424.907 JLINK_HasError()
T0818 001:426.417 JLINK_IsHalted()
T0818 001:426.892 - 0.474ms returns FALSE
T0818 001:426.897 JLINK_HasError()
T0818 001:429.419 JLINK_IsHalted()
T0818 001:429.903 - 0.484ms returns FALSE
T0818 001:429.909 JLINK_HasError()
T0818 001:431.418 JLINK_IsHalted()
T0818 001:431.894 - 0.475ms returns FALSE
T0818 001:431.900 JLINK_HasError()
T0818 001:433.419 JLINK_IsHalted()
T0818 001:433.904 - 0.485ms returns FALSE
T0818 001:433.910 JLINK_HasError()
T0818 001:435.417 JLINK_IsHalted()
T0818 001:435.873 - 0.455ms returns FALSE
T0818 001:435.879 JLINK_HasError()
T0818 001:437.417 JLINK_IsHalted()
T0818 001:437.866 - 0.448ms returns FALSE
T0818 001:437.871 JLINK_HasError()
T0818 001:439.419 JLINK_IsHalted()
T0818 001:439.904 - 0.484ms returns FALSE
T0818 001:439.912 JLINK_HasError()
T0818 001:442.422 JLINK_IsHalted()
T0818 001:442.945 - 0.523ms returns FALSE
T0818 001:442.956 JLINK_HasError()
T0818 001:444.418 JLINK_IsHalted()
T0818 001:444.887 - 0.469ms returns FALSE
T0818 001:444.893 JLINK_HasError()
T0818 001:446.423 JLINK_IsHalted()
T0818 001:446.909 - 0.486ms returns FALSE
T0818 001:446.916 JLINK_HasError()
T0818 001:448.417 JLINK_IsHalted()
T0818 001:448.906 - 0.488ms returns FALSE
T0818 001:448.914 JLINK_HasError()
T0818 001:450.442 JLINK_IsHalted()
T0818 001:450.952 - 0.509ms returns FALSE
T0818 001:450.958 JLINK_HasError()
T0818 001:452.440 JLINK_IsHalted()
T0818 001:452.909 - 0.468ms returns FALSE
T0818 001:452.915 JLINK_HasError()
T0818 001:454.418 JLINK_IsHalted()
T0818 001:454.888 - 0.470ms returns FALSE
T0818 001:454.894 JLINK_HasError()
T0818 001:456.417 JLINK_IsHalted()
T0818 001:456.894 - 0.476ms returns FALSE
T0818 001:456.900 JLINK_HasError()
T0818 001:458.418 JLINK_IsHalted()
T0818 001:458.909 - 0.491ms returns FALSE
T0818 001:458.915 JLINK_HasError()
T0818 001:460.418 JLINK_IsHalted()
T0818 001:460.888 - 0.469ms returns FALSE
T0818 001:460.893 JLINK_HasError()
T0818 001:462.420 JLINK_IsHalted()
T0818 001:462.925 - 0.504ms returns FALSE
T0818 001:462.932 JLINK_HasError()
T0818 001:465.423 JLINK_IsHalted()
T0818 001:465.905 - 0.481ms returns FALSE
T0818 001:465.911 JLINK_HasError()
T0818 001:467.419 JLINK_IsHalted()
T0818 001:467.902 - 0.482ms returns FALSE
T0818 001:467.912 JLINK_HasError()
T0818 001:469.418 JLINK_IsHalted()
T0818 001:469.888 - 0.470ms returns FALSE
T0818 001:469.893 JLINK_HasError()
T0818 001:471.418 JLINK_IsHalted()
T0818 001:471.898 - 0.480ms returns FALSE
T0818 001:471.905 JLINK_HasError()
T0818 001:473.417 JLINK_IsHalted()
T0818 001:473.909 - 0.492ms returns FALSE
T0818 001:473.915 JLINK_HasError()
T0818 001:475.417 JLINK_IsHalted()
T0818 001:475.893 - 0.475ms returns FALSE
T0818 001:475.899 JLINK_HasError()
T0818 001:477.417 JLINK_IsHalted()
T0818 001:477.901 - 0.484ms returns FALSE
T0818 001:477.907 JLINK_HasError()
T0818 001:479.417 JLINK_IsHalted()
T0818 001:479.887 - 0.469ms returns FALSE
T0818 001:479.893 JLINK_HasError()
T0818 001:481.420 JLINK_IsHalted()
T0818 001:481.903 - 0.483ms returns FALSE
T0818 001:481.909 JLINK_HasError()
T0818 001:483.421 JLINK_IsHalted()
T0818 001:483.907 - 0.486ms returns FALSE
T0818 001:483.913 JLINK_HasError()
T0818 001:485.417 JLINK_IsHalted()
T0818 001:485.893 - 0.475ms returns FALSE
T0818 001:485.898 JLINK_HasError()
T0818 001:487.423 JLINK_IsHalted()
T0818 001:487.924 - 0.501ms returns FALSE
T0818 001:487.930 JLINK_HasError()
T0818 001:489.753 JLINK_IsHalted()
T0818 001:490.475 - 0.722ms returns FALSE
T0818 001:490.486 JLINK_HasError()
T0818 001:492.420 JLINK_IsHalted()
T0818 001:492.933 - 0.513ms returns FALSE
T0818 001:492.942 JLINK_HasError()
T0818 001:495.420 JLINK_IsHalted()
T0818 001:495.866 - 0.446ms returns FALSE
T0818 001:495.873 JLINK_HasError()
T0818 001:497.425 JLINK_IsHalted()
T0818 001:497.967 - 0.542ms returns FALSE
T0818 001:497.974 JLINK_HasError()
T0818 001:499.417 JLINK_IsHalted()
T0818 001:499.902 - 0.484ms returns FALSE
T0818 001:499.908 JLINK_HasError()
T0818 001:501.420 JLINK_IsHalted()
T0818 001:501.888 - 0.467ms returns FALSE
T0818 001:501.893 JLINK_HasError()
T0818 001:503.417 JLINK_IsHalted()
T0818 001:504.044 - 0.626ms returns FALSE
T0818 001:504.057 JLINK_HasError()
T0818 001:505.417 JLINK_IsHalted()
T0818 001:505.864 - 0.447ms returns FALSE
T0818 001:505.870 JLINK_HasError()
T0818 001:507.417 JLINK_IsHalted()
T0818 001:507.903 - 0.485ms returns FALSE
T0818 001:507.908 JLINK_HasError()
T0818 001:509.417 JLINK_IsHalted()
T0818 001:509.921 - 0.504ms returns FALSE
T0818 001:509.927 JLINK_HasError()
T0818 001:511.420 JLINK_IsHalted()
T0818 001:511.888 - 0.468ms returns FALSE
T0818 001:511.894 JLINK_HasError()
T0818 001:513.423 JLINK_IsHalted()
T0818 001:513.903 - 0.480ms returns FALSE
T0818 001:513.909 JLINK_HasError()
T0818 001:515.419 JLINK_IsHalted()
T0818 001:515.936 - 0.516ms returns FALSE
T0818 001:515.943 JLINK_HasError()
T0818 001:517.418 JLINK_IsHalted()
T0818 001:517.924 - 0.506ms returns FALSE
T0818 001:517.930 JLINK_HasError()
T0818 001:519.418 JLINK_IsHalted()
T0818 001:519.892 - 0.473ms returns FALSE
T0818 001:519.900 JLINK_HasError()
T0818 001:521.420 JLINK_IsHalted()
T0818 001:521.866 - 0.445ms returns FALSE
T0818 001:521.872 JLINK_HasError()
T0818 001:523.598 JLINK_IsHalted()
T0818 001:524.181 - 0.582ms returns FALSE
T0818 001:524.187 JLINK_HasError()
T0818 001:525.417 JLINK_IsHalted()
T0818 001:525.864 - 0.447ms returns FALSE
T0818 001:525.870 JLINK_HasError()
T0818 001:527.417 JLINK_IsHalted()
T0818 001:527.903 - 0.485ms returns FALSE
T0818 001:527.908 JLINK_HasError()
T0818 001:529.425 JLINK_IsHalted()
T0818 001:529.887 - 0.461ms returns FALSE
T0818 001:529.894 JLINK_HasError()
T0818 001:531.421 JLINK_IsHalted()
T0818 001:531.912 - 0.491ms returns FALSE
T0818 001:531.919 JLINK_HasError()
T0818 001:533.419 JLINK_IsHalted()
T0818 001:533.888 - 0.469ms returns FALSE
T0818 001:533.894 JLINK_HasError()
T0818 001:535.418 JLINK_IsHalted()
T0818 001:535.934 - 0.516ms returns FALSE
T0818 001:535.943 JLINK_HasError()
T0818 001:537.418 JLINK_IsHalted()
T0818 001:537.895 - 0.477ms returns FALSE
T0818 001:537.902 JLINK_HasError()
T0818 001:539.418 JLINK_IsHalted()
T0818 001:539.894 - 0.475ms returns FALSE
T0818 001:539.900 JLINK_HasError()
T0818 001:541.427 JLINK_IsHalted()
T0818 001:542.433 - 1.006ms returns FALSE
T0818 001:542.445 JLINK_HasError()
T0818 001:544.421 JLINK_IsHalted()
T0818 001:544.891 - 0.469ms returns FALSE
T0818 001:544.905 JLINK_HasError()
T0818 001:546.418 JLINK_IsHalted()
T0818 001:546.892 - 0.474ms returns FALSE
T0818 001:546.898 JLINK_HasError()
T0818 001:548.419 JLINK_IsHalted()
T0818 001:548.888 - 0.469ms returns FALSE
T0818 001:548.894 JLINK_HasError()
T0818 001:550.420 JLINK_IsHalted()
T0818 001:550.931 - 0.511ms returns FALSE
T0818 001:550.937 JLINK_HasError()
T0818 001:552.418 JLINK_IsHalted()
T0818 001:552.893 - 0.475ms returns FALSE
T0818 001:552.899 JLINK_HasError()
T0818 001:554.417 JLINK_IsHalted()
T0818 001:554.908 - 0.490ms returns FALSE
T0818 001:554.913 JLINK_HasError()
T0818 001:556.417 JLINK_IsHalted()
T0818 001:556.887 - 0.470ms returns FALSE
T0818 001:556.893 JLINK_HasError()
T0818 001:558.420 JLINK_IsHalted()
T0818 001:558.887 - 0.466ms returns FALSE
T0818 001:558.894 JLINK_HasError()
T0818 001:560.418 JLINK_IsHalted()
T0818 001:561.005 - 0.586ms returns FALSE
T0818 001:561.015 JLINK_HasError()
T0818 001:563.421 JLINK_IsHalted()
T0818 001:563.926 - 0.504ms returns FALSE
T0818 001:563.933 JLINK_HasError()
T0818 001:565.417 JLINK_IsHalted()
T0818 001:565.885 - 0.467ms returns FALSE
T0818 001:565.891 JLINK_HasError()
T0818 001:567.417 JLINK_IsHalted()
T0818 001:567.888 - 0.471ms returns FALSE
T0818 001:567.894 JLINK_HasError()
T0818 001:569.419 JLINK_IsHalted()
T0818 001:569.895 - 0.475ms returns FALSE
T0818 001:569.901 JLINK_HasError()
T0818 001:571.418 JLINK_IsHalted()
T0818 001:571.934 - 0.515ms returns FALSE
T0818 001:571.940 JLINK_HasError()
T0818 001:573.417 JLINK_IsHalted()
T0818 001:573.903 - 0.485ms returns FALSE
T0818 001:573.908 JLINK_HasError()
T0818 001:575.417 JLINK_IsHalted()
T0818 001:575.929 - 0.511ms returns FALSE
T0818 001:575.939 JLINK_HasError()
T0818 001:577.419 JLINK_IsHalted()
T0818 001:577.932 - 0.513ms returns FALSE
T0818 001:577.938 JLINK_HasError()
T0818 001:579.417 JLINK_IsHalted()
T0818 001:579.892 - 0.475ms returns FALSE
T0818 001:579.898 JLINK_HasError()
T0818 001:581.418 JLINK_IsHalted()
T0818 001:581.894 - 0.475ms returns FALSE
T0818 001:581.900 JLINK_HasError()
T0818 001:583.417 JLINK_IsHalted()
T0818 001:583.908 - 0.490ms returns FALSE
T0818 001:583.913 JLINK_HasError()
T0818 001:585.417 JLINK_IsHalted()
T0818 001:585.887 - 0.470ms returns FALSE
T0818 001:585.893 JLINK_HasError()
T0818 001:587.417 JLINK_IsHalted()
T0818 001:587.887 - 0.470ms returns FALSE
T0818 001:587.893 JLINK_HasError()
T0818 001:589.420 JLINK_IsHalted()
T0818 001:589.904 - 0.484ms returns FALSE
T0818 001:589.911 JLINK_HasError()
T0818 001:591.418 JLINK_IsHalted()
T0818 001:591.892 - 0.474ms returns FALSE
T0818 001:591.902 JLINK_HasError()
T0818 001:593.419 JLINK_IsHalted()
T0818 001:593.878 - 0.458ms returns FALSE
T0818 001:593.884 JLINK_HasError()
T0818 001:595.418 JLINK_IsHalted()
T0818 001:595.902 - 0.483ms returns FALSE
T0818 001:595.910 JLINK_HasError()
T0818 001:597.417 JLINK_IsHalted()
T0818 001:597.873 - 0.455ms returns FALSE
T0818 001:597.878 JLINK_HasError()
T0818 001:599.417 JLINK_IsHalted()
T0818 001:599.902 - 0.485ms returns FALSE
T0818 001:599.908 JLINK_HasError()
T0818 001:601.420 JLINK_IsHalted()
T0818 001:601.946 - 0.526ms returns FALSE
T0818 001:601.952 JLINK_HasError()
T0818 001:603.422 JLINK_IsHalted()
T0818 001:604.218 - 0.796ms returns FALSE
T0818 001:604.225 JLINK_HasError()
T0818 001:605.417 JLINK_IsHalted()
T0818 001:605.893 - 0.476ms returns FALSE
T0818 001:605.899 JLINK_HasError()
T0818 001:607.473 JLINK_IsHalted()
T0818 001:608.217 - 0.743ms returns FALSE
T0818 001:608.228 JLINK_HasError()
T0818 001:609.424 JLINK_IsHalted()
T0818 001:609.889 - 0.464ms returns FALSE
T0818 001:609.901 JLINK_HasError()
T0818 001:611.424 JLINK_IsHalted()
T0818 001:611.978 - 0.553ms returns FALSE
T0818 001:611.985 JLINK_HasError()
T0818 001:613.421 JLINK_IsHalted()
T0818 001:613.935 - 0.513ms returns FALSE
T0818 001:613.941 JLINK_HasError()
T0818 001:615.422 JLINK_IsHalted()
T0818 001:615.888 - 0.465ms returns FALSE
T0818 001:615.895 JLINK_HasError()
T0818 001:618.427 JLINK_IsHalted()
T0818 001:618.912 - 0.484ms returns FALSE
T0818 001:618.919 JLINK_HasError()
T0818 001:620.428 JLINK_IsHalted()
T0818 001:620.876 - 0.448ms returns FALSE
T0818 001:620.888 JLINK_HasError()
T0818 001:622.421 JLINK_IsHalted()
T0818 001:622.936 - 0.514ms returns FALSE
T0818 001:622.947 JLINK_HasError()
T0818 001:624.424 JLINK_IsHalted()
T0818 001:624.893 - 0.470ms returns FALSE
T0818 001:624.903 JLINK_HasError()
T0818 001:628.429 JLINK_IsHalted()
T0818 001:628.929 - 0.500ms returns FALSE
T0818 001:628.938 JLINK_HasError()
T0818 001:630.421 JLINK_IsHalted()
T0818 001:630.879 - 0.457ms returns FALSE
T0818 001:630.887 JLINK_HasError()
T0818 001:632.426 JLINK_IsHalted()
T0818 001:632.899 - 0.473ms returns FALSE
T0818 001:632.914 JLINK_HasError()
T0818 001:634.437 JLINK_IsHalted()
T0818 001:634.907 - 0.469ms returns FALSE
T0818 001:634.914 JLINK_HasError()
T0818 001:636.423 JLINK_IsHalted()
T0818 001:636.882 - 0.458ms returns FALSE
T0818 001:636.892 JLINK_HasError()
T0818 001:638.422 JLINK_IsHalted()
T0818 001:638.917 - 0.495ms returns FALSE
T0818 001:638.925 JLINK_HasError()
T0818 001:640.421 JLINK_IsHalted()
T0818 001:640.858 - 0.436ms returns FALSE
T0818 001:640.865 JLINK_HasError()
T0818 001:642.420 JLINK_IsHalted()
T0818 001:642.897 - 0.477ms returns FALSE
T0818 001:642.904 JLINK_HasError()
T0818 001:644.421 JLINK_IsHalted()
T0818 001:644.891 - 0.469ms returns FALSE
T0818 001:644.898 JLINK_HasError()
T0818 001:646.420 JLINK_IsHalted()
T0818 001:646.932 - 0.512ms returns FALSE
T0818 001:646.939 JLINK_HasError()
T0818 001:648.424 JLINK_IsHalted()
T0818 001:648.890 - 0.466ms returns FALSE
T0818 001:648.897 JLINK_HasError()
T0818 001:650.422 JLINK_IsHalted()
T0818 001:650.917 - 0.495ms returns FALSE
T0818 001:650.924 JLINK_HasError()
T0818 001:652.423 JLINK_IsHalted()
T0818 001:652.906 - 0.483ms returns FALSE
T0818 001:652.912 JLINK_HasError()
T0818 001:654.424 JLINK_IsHalted()
T0818 001:654.904 - 0.480ms returns FALSE
T0818 001:654.910 JLINK_HasError()
T0818 001:656.427 JLINK_IsHalted()
T0818 001:656.928 - 0.501ms returns FALSE
T0818 001:656.936 JLINK_HasError()
T0818 001:658.423 JLINK_IsHalted()
T0818 001:658.927 - 0.503ms returns FALSE
T0818 001:658.935 JLINK_HasError()
T0818 001:660.424 JLINK_IsHalted()
T0818 001:660.851 - 0.427ms returns FALSE
T0818 001:660.859 JLINK_HasError()
T0818 001:662.425 JLINK_IsHalted()
T0818 001:662.962 - 0.537ms returns FALSE
T0818 001:662.968 JLINK_HasError()
T0818 001:664.420 JLINK_IsHalted()
T0818 001:664.915 - 0.495ms returns FALSE
T0818 001:664.924 JLINK_HasError()
T0818 001:666.425 JLINK_IsHalted()
T0818 001:666.889 - 0.463ms returns FALSE
T0818 001:666.897 JLINK_HasError()
T0818 001:668.420 JLINK_IsHalted()
T0818 001:668.958 - 0.538ms returns FALSE
T0818 001:668.965 JLINK_HasError()
T0818 001:670.422 JLINK_IsHalted()
T0818 001:670.873 - 0.451ms returns FALSE
T0818 001:670.880 JLINK_HasError()
T0818 001:672.419 JLINK_IsHalted()
T0818 001:672.908 - 0.488ms returns FALSE
T0818 001:672.915 JLINK_HasError()
T0818 001:674.425 JLINK_IsHalted()
T0818 001:674.875 - 0.450ms returns FALSE
T0818 001:674.881 JLINK_HasError()
T0818 001:676.420 JLINK_IsHalted()
T0818 001:676.939 - 0.518ms returns FALSE
T0818 001:676.946 JLINK_HasError()
T0818 001:678.421 JLINK_IsHalted()
T0818 001:678.904 - 0.483ms returns FALSE
T0818 001:678.910 JLINK_HasError()
T0818 001:680.420 JLINK_IsHalted()
T0818 001:680.906 - 0.485ms returns FALSE
T0818 001:680.913 JLINK_HasError()
T0818 001:682.425 JLINK_IsHalted()
T0818 001:682.878 - 0.452ms returns FALSE
T0818 001:682.884 JLINK_HasError()
T0818 001:684.419 JLINK_IsHalted()
T0818 001:684.956 - 0.536ms returns FALSE
T0818 001:684.962 JLINK_HasError()
T0818 001:686.423 JLINK_IsHalted()
T0818 001:686.856 - 0.433ms returns FALSE
T0818 001:686.863 JLINK_HasError()
T0818 001:688.427 JLINK_IsHalted()
T0818 001:688.890 - 0.463ms returns FALSE
T0818 001:688.898 JLINK_HasError()
T0818 001:690.422 JLINK_IsHalted()
T0818 001:690.888 - 0.466ms returns FALSE
T0818 001:690.898 JLINK_HasError()
T0818 001:692.422 JLINK_IsHalted()
T0818 001:692.896 - 0.473ms returns FALSE
T0818 001:692.903 JLINK_HasError()
T0818 001:694.422 JLINK_IsHalted()
T0818 001:694.926 - 0.503ms returns FALSE
T0818 001:694.932 JLINK_HasError()
T0818 001:696.425 JLINK_IsHalted()
T0818 001:696.925 - 0.500ms returns FALSE
T0818 001:696.937 JLINK_HasError()
T0818 001:698.421 JLINK_IsHalted()
T0818 001:698.868 - 0.447ms returns FALSE
T0818 001:698.874 JLINK_HasError()
T0818 001:700.422 JLINK_IsHalted()
T0818 001:700.904 - 0.481ms returns FALSE
T0818 001:700.910 JLINK_HasError()
T0818 001:702.421 JLINK_IsHalted()
T0818 001:702.923 - 0.500ms returns FALSE
T0818 001:702.941 JLINK_HasError()
T0818 001:704.429 JLINK_IsHalted()
T0818 001:704.876 - 0.446ms returns FALSE
T0818 001:704.883 JLINK_HasError()
T0818 001:706.423 JLINK_IsHalted()
T0818 001:706.900 - 0.476ms returns FALSE
T0818 001:706.909 JLINK_HasError()
T0818 001:708.426 JLINK_IsHalted()
T0818 001:708.857 - 0.431ms returns FALSE
T0818 001:708.864 JLINK_HasError()
T0818 001:710.422 JLINK_IsHalted()
T0818 001:710.925 - 0.502ms returns FALSE
T0818 001:710.932 JLINK_HasError()
T0818 001:712.426 JLINK_IsHalted()
T0818 001:712.903 - 0.477ms returns FALSE
T0818 001:712.919 JLINK_HasError()
T0818 001:714.424 JLINK_IsHalted()
T0818 001:714.878 - 0.454ms returns FALSE
T0818 001:714.897 JLINK_HasError()
T0818 001:716.423 JLINK_IsHalted()
T0818 001:716.872 - 0.449ms returns FALSE
T0818 001:716.882 JLINK_HasError()
T0818 001:718.422 JLINK_IsHalted()
T0818 001:718.904 - 0.482ms returns FALSE
T0818 001:718.911 JLINK_HasError()
T0818 001:720.426 JLINK_IsHalted()
T0818 001:720.905 - 0.478ms returns FALSE
T0818 001:720.913 JLINK_HasError()
T0818 001:722.423 JLINK_IsHalted()
T0818 001:722.878 - 0.454ms returns FALSE
T0818 001:722.886 JLINK_HasError()
T0818 001:724.424 JLINK_IsHalted()
T0818 001:724.877 - 0.452ms returns FALSE
T0818 001:724.884 JLINK_HasError()
T0818 001:726.423 JLINK_IsHalted()
T0818 001:726.897 - 0.473ms returns FALSE
T0818 001:726.904 JLINK_HasError()
T0818 001:728.422 JLINK_IsHalted()
T0818 001:728.938 - 0.516ms returns FALSE
T0818 001:728.944 JLINK_HasError()
T0818 001:730.421 JLINK_IsHalted()
T0818 001:730.927 - 0.505ms returns FALSE
T0818 001:730.933 JLINK_HasError()
T0818 001:732.420 JLINK_IsHalted()
T0818 001:732.893 - 0.473ms returns FALSE
T0818 001:732.899 JLINK_HasError()
T0818 001:734.423 JLINK_IsHalted()
T0818 001:734.903 - 0.480ms returns FALSE
T0818 001:734.908 JLINK_HasError()
T0818 001:736.083 JLINK_IsHalted()
T0818 001:736.614 - 0.531ms returns FALSE
T0818 001:736.621 JLINK_HasError()
T0818 001:739.090 JLINK_IsHalted()
T0818 001:739.607 - 0.517ms returns FALSE
T0818 001:739.614 JLINK_HasError()
T0818 001:741.086 JLINK_IsHalted()
T0818 001:741.619 - 0.533ms returns FALSE
T0818 001:741.626 JLINK_HasError()
T0818 001:743.088 JLINK_IsHalted()
T0818 001:743.595 - 0.507ms returns FALSE
T0818 001:743.601 JLINK_HasError()
T0818 001:745.084 JLINK_IsHalted()
T0818 001:745.596 - 0.511ms returns FALSE
T0818 001:745.602 JLINK_HasError()
T0818 001:747.089 JLINK_IsHalted()
T0818 001:747.574 - 0.484ms returns FALSE
T0818 001:747.586 JLINK_HasError()
T0818 001:749.085 JLINK_IsHalted()
T0818 001:749.596 - 0.511ms returns FALSE
T0818 001:749.602 JLINK_HasError()
T0818 001:751.089 JLINK_IsHalted()
T0818 001:751.597 - 0.507ms returns FALSE
T0818 001:751.606 JLINK_HasError()
T0818 001:753.086 JLINK_IsHalted()
T0818 001:753.607 - 0.521ms returns FALSE
T0818 001:753.615 JLINK_HasError()
T0818 001:755.088 JLINK_IsHalted()
T0818 001:755.602 - 0.513ms returns FALSE
T0818 001:755.616 JLINK_HasError()
T0818 001:757.084 JLINK_IsHalted()
T0818 001:757.557 - 0.472ms returns FALSE
T0818 001:757.563 JLINK_HasError()
T0818 001:759.087 JLINK_IsHalted()
T0818 001:759.560 - 0.472ms returns FALSE
T0818 001:759.567 JLINK_HasError()
T0818 001:761.098 JLINK_IsHalted()
T0818 001:761.577 - 0.478ms returns FALSE
T0818 001:761.590 JLINK_HasError()
T0818 001:763.087 JLINK_IsHalted()
T0818 001:763.597 - 0.509ms returns FALSE
T0818 001:763.608 JLINK_HasError()
T0818 001:765.088 JLINK_IsHalted()
T0818 001:765.595 - 0.507ms returns FALSE
T0818 001:765.603 JLINK_HasError()
T0818 001:767.097 JLINK_IsHalted()
T0818 001:767.668 - 0.570ms returns FALSE
T0818 001:767.680 JLINK_HasError()
T0818 001:769.086 JLINK_IsHalted()
T0818 001:769.608 - 0.522ms returns FALSE
T0818 001:769.615 JLINK_HasError()
T0818 001:771.089 JLINK_IsHalted()
T0818 001:771.608 - 0.519ms returns FALSE
T0818 001:771.615 JLINK_HasError()
T0818 001:773.088 JLINK_IsHalted()
T0818 001:773.604 - 0.516ms returns FALSE
T0818 001:773.655 JLINK_HasError()
T0818 001:775.086 JLINK_IsHalted()
T0818 001:775.601 - 0.515ms returns FALSE
T0818 001:775.608 JLINK_HasError()
T0818 001:777.087 JLINK_IsHalted()
T0818 001:779.450   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:779.891 - 2.803ms returns TRUE
T0818 001:779.899 JLINK_ReadReg(R15 (PC))
T0818 001:779.905 - 0.005ms returns 0x20000000
T0818 001:779.910 JLINK_ClrBPEx(BPHandle = 0x00000007)
T0818 001:779.913 - 0.003ms returns 0x00
T0818 001:779.918 JLINK_ReadReg(R0)
T0818 001:779.921 - 0.003ms returns 0x00000000
T0818 001:780.320 JLINK_HasError()
T0818 001:780.332 JLINK_WriteReg(R0, 0x0800C000)
T0818 001:780.338 - 0.005ms returns 0
T0818 001:780.342 JLINK_WriteReg(R1, 0x00004000)
T0818 001:780.345 - 0.003ms returns 0
T0818 001:780.349 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:780.353 - 0.003ms returns 0
T0818 001:780.357 JLINK_WriteReg(R3, 0x00000000)
T0818 001:780.360 - 0.003ms returns 0
T0818 001:780.364 JLINK_WriteReg(R4, 0x00000000)
T0818 001:780.367 - 0.003ms returns 0
T0818 001:780.371 JLINK_WriteReg(R5, 0x00000000)
T0818 001:780.375 - 0.003ms returns 0
T0818 001:780.379 JLINK_WriteReg(R6, 0x00000000)
T0818 001:780.382 - 0.003ms returns 0
T0818 001:780.386 JLINK_WriteReg(R7, 0x00000000)
T0818 001:780.390 - 0.003ms returns 0
T0818 001:780.394 JLINK_WriteReg(R8, 0x00000000)
T0818 001:780.397 - 0.003ms returns 0
T0818 001:780.402 JLINK_WriteReg(R9, 0x20000180)
T0818 001:780.405 - 0.003ms returns 0
T0818 001:780.409 JLINK_WriteReg(R10, 0x00000000)
T0818 001:780.412 - 0.003ms returns 0
T0818 001:780.416 JLINK_WriteReg(R11, 0x00000000)
T0818 001:780.420 - 0.003ms returns 0
T0818 001:780.424 JLINK_WriteReg(R12, 0x00000000)
T0818 001:780.427 - 0.003ms returns 0
T0818 001:780.432 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:780.435 - 0.004ms returns 0
T0818 001:780.439 JLINK_WriteReg(R14, 0x20000001)
T0818 001:780.443 - 0.003ms returns 0
T0818 001:780.447 JLINK_WriteReg(R15 (PC), 0x20000020)
T0818 001:780.450 - 0.003ms returns 0
T0818 001:780.455 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:780.458 - 0.004ms returns 0
T0818 001:780.462 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:780.466 - 0.003ms returns 0
T0818 001:780.470 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:780.473 - 0.003ms returns 0
T0818 001:780.477 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:780.481 - 0.003ms returns 0
T0818 001:780.486 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:780.490 - 0.004ms returns 0x00000008
T0818 001:780.494 JLINK_Go()
T0818 001:780.504   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:783.213 - 2.718ms 
T0818 001:783.225 JLINK_IsHalted()
T0818 001:785.523   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 001:786.017 - 2.792ms returns TRUE
T0818 001:786.025 JLINK_ReadReg(R15 (PC))
T0818 001:786.030 - 0.005ms returns 0x20000000
T0818 001:786.034 JLINK_ClrBPEx(BPHandle = 0x00000008)
T0818 001:786.038 - 0.004ms returns 0x00
T0818 001:786.042 JLINK_ReadReg(R0)
T0818 001:786.046 - 0.003ms returns 0x00000001
T0818 001:786.055 JLINK_HasError()
T0818 001:786.062 JLINK_WriteReg(R0, 0x0800C000)
T0818 001:786.066 - 0.003ms returns 0
T0818 001:786.070 JLINK_WriteReg(R1, 0x00004000)
T0818 001:786.074 - 0.003ms returns 0
T0818 001:786.078 JLINK_WriteReg(R2, 0x000000FF)
T0818 001:786.081 - 0.003ms returns 0
T0818 001:786.085 JLINK_WriteReg(R3, 0x00000000)
T0818 001:786.088 - 0.003ms returns 0
T0818 001:786.092 JLINK_WriteReg(R4, 0x00000000)
T0818 001:786.097 - 0.004ms returns 0
T0818 001:786.101 JLINK_WriteReg(R5, 0x00000000)
T0818 001:786.104 - 0.003ms returns 0
T0818 001:786.108 JLINK_WriteReg(R6, 0x00000000)
T0818 001:786.111 - 0.003ms returns 0
T0818 001:786.115 JLINK_WriteReg(R7, 0x00000000)
T0818 001:786.119 - 0.003ms returns 0
T0818 001:786.123 JLINK_WriteReg(R8, 0x00000000)
T0818 001:786.126 - 0.003ms returns 0
T0818 001:786.130 JLINK_WriteReg(R9, 0x20000180)
T0818 001:786.134 - 0.003ms returns 0
T0818 001:786.138 JLINK_WriteReg(R10, 0x00000000)
T0818 001:786.141 - 0.003ms returns 0
T0818 001:786.145 JLINK_WriteReg(R11, 0x00000000)
T0818 001:786.148 - 0.003ms returns 0
T0818 001:786.152 JLINK_WriteReg(R12, 0x00000000)
T0818 001:786.156 - 0.003ms returns 0
T0818 001:786.160 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 001:786.164 - 0.003ms returns 0
T0818 001:786.168 JLINK_WriteReg(R14, 0x20000001)
T0818 001:786.171 - 0.003ms returns 0
T0818 001:786.175 JLINK_WriteReg(R15 (PC), 0x200000C0)
T0818 001:786.178 - 0.003ms returns 0
T0818 001:786.182 JLINK_WriteReg(XPSR, 0x01000000)
T0818 001:786.186 - 0.003ms returns 0
T0818 001:786.190 JLINK_WriteReg(MSP, 0x20001000)
T0818 001:786.194 - 0.003ms returns 0
T0818 001:786.198 JLINK_WriteReg(PSP, 0x20001000)
T0818 001:786.202 - 0.003ms returns 0
T0818 001:786.206 JLINK_WriteReg(CFBP, 0x00000000)
T0818 001:786.209 - 0.003ms returns 0
T0818 001:786.213 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 001:786.217 - 0.004ms returns 0x00000009
T0818 001:786.221 JLINK_Go()
T0818 001:786.228   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 001:788.961 - 2.739ms 
T0818 001:788.969 JLINK_IsHalted()
T0818 001:789.474 - 0.504ms returns FALSE
T0818 001:789.492 JLINK_HasError()
T0818 001:791.095 JLINK_IsHalted()
T0818 001:791.566 - 0.471ms returns FALSE
T0818 001:791.577 JLINK_HasError()
T0818 001:793.091 JLINK_IsHalted()
T0818 001:793.565 - 0.474ms returns FALSE
T0818 001:793.574 JLINK_HasError()
T0818 001:795.085 JLINK_IsHalted()
T0818 001:795.595 - 0.510ms returns FALSE
T0818 001:795.601 JLINK_HasError()
T0818 001:797.088 JLINK_IsHalted()
T0818 001:797.577 - 0.488ms returns FALSE
T0818 001:797.595 JLINK_HasError()
T0818 001:799.089 JLINK_IsHalted()
T0818 001:799.573 - 0.484ms returns FALSE
T0818 001:799.582 JLINK_HasError()
T0818 001:801.089 JLINK_IsHalted()
T0818 001:801.621 - 0.532ms returns FALSE
T0818 001:801.630 JLINK_HasError()
T0818 001:803.088 JLINK_IsHalted()
T0818 001:803.565 - 0.476ms returns FALSE
T0818 001:803.572 JLINK_HasError()
T0818 001:805.091 JLINK_IsHalted()
T0818 001:805.561 - 0.470ms returns FALSE
T0818 001:805.568 JLINK_HasError()
T0818 001:807.086 JLINK_IsHalted()
T0818 001:807.608 - 0.520ms returns FALSE
T0818 001:807.617 JLINK_HasError()
T0818 001:809.086 JLINK_IsHalted()
T0818 001:809.601 - 0.515ms returns FALSE
T0818 001:809.610 JLINK_HasError()
T0818 001:811.088 JLINK_IsHalted()
T0818 001:811.575 - 0.487ms returns FALSE
T0818 001:811.581 JLINK_HasError()
T0818 001:813.094 JLINK_IsHalted()
T0818 001:813.606 - 0.512ms returns FALSE
T0818 001:813.615 JLINK_HasError()
T0818 001:815.089 JLINK_IsHalted()
T0818 001:815.614 - 0.524ms returns FALSE
T0818 001:815.624 JLINK_HasError()
T0818 001:817.086 JLINK_IsHalted()
T0818 001:817.604 - 0.516ms returns FALSE
T0818 001:817.611 JLINK_HasError()
T0818 001:819.091 JLINK_IsHalted()
T0818 001:819.574 - 0.482ms returns FALSE
T0818 001:819.580 JLINK_HasError()
T0818 001:821.085 JLINK_IsHalted()
T0818 001:821.576 - 0.490ms returns FALSE
T0818 001:821.582 JLINK_HasError()
T0818 001:823.087 JLINK_IsHalted()
T0818 001:823.606 - 0.519ms returns FALSE
T0818 001:823.618 JLINK_HasError()
T0818 001:825.084 JLINK_IsHalted()
T0818 001:825.595 - 0.510ms returns FALSE
T0818 001:825.602 JLINK_HasError()
T0818 001:827.088 JLINK_IsHalted()
T0818 001:827.607 - 0.519ms returns FALSE
T0818 001:827.614 JLINK_HasError()
T0818 001:829.085 JLINK_IsHalted()
T0818 001:829.560 - 0.474ms returns FALSE
T0818 001:829.566 JLINK_HasError()
T0818 001:831.087 JLINK_IsHalted()
T0818 001:831.565 - 0.477ms returns FALSE
T0818 001:831.573 JLINK_HasError()
T0818 001:833.084 JLINK_IsHalted()
T0818 001:833.571 - 0.486ms returns FALSE
T0818 001:833.577 JLINK_HasError()
T0818 001:835.094 JLINK_IsHalted()
T0818 001:835.606 - 0.511ms returns FALSE
T0818 001:835.613 JLINK_HasError()
T0818 001:837.087 JLINK_IsHalted()
T0818 001:837.604 - 0.516ms returns FALSE
T0818 001:837.614 JLINK_HasError()
T0818 001:839.086 JLINK_IsHalted()
T0818 001:839.600 - 0.513ms returns FALSE
T0818 001:839.607 JLINK_HasError()
T0818 001:841.089 JLINK_IsHalted()
T0818 001:841.620 - 0.531ms returns FALSE
T0818 001:841.628 JLINK_HasError()
T0818 001:843.087 JLINK_IsHalted()
T0818 001:843.600 - 0.513ms returns FALSE
T0818 001:843.608 JLINK_HasError()
T0818 001:846.088 JLINK_IsHalted()
T0818 001:846.568 - 0.479ms returns FALSE
T0818 001:846.575 JLINK_HasError()
T0818 001:848.086 JLINK_IsHalted()
T0818 001:848.566 - 0.479ms returns FALSE
T0818 001:848.573 JLINK_HasError()
T0818 001:850.089 JLINK_IsHalted()
T0818 001:850.578 - 0.488ms returns FALSE
T0818 001:850.627 JLINK_HasError()
T0818 001:852.087 JLINK_IsHalted()
T0818 001:852.598 - 0.510ms returns FALSE
T0818 001:852.604 JLINK_HasError()
T0818 001:854.087 JLINK_IsHalted()
T0818 001:854.601 - 0.513ms returns FALSE
T0818 001:854.607 JLINK_HasError()
T0818 001:856.098 JLINK_IsHalted()
T0818 001:856.608 - 0.510ms returns FALSE
T0818 001:856.615 JLINK_HasError()
T0818 001:858.088 JLINK_IsHalted()
T0818 001:858.623 - 0.535ms returns FALSE
T0818 001:858.639 JLINK_HasError()
T0818 001:860.090 JLINK_IsHalted()
T0818 001:860.602 - 0.512ms returns FALSE
T0818 001:860.610 JLINK_HasError()
T0818 001:862.090 JLINK_IsHalted()
T0818 001:862.622 - 0.532ms returns FALSE
T0818 001:862.628 JLINK_HasError()
T0818 001:864.088 JLINK_IsHalted()
T0818 001:864.577 - 0.488ms returns FALSE
T0818 001:864.583 JLINK_HasError()
T0818 001:866.088 JLINK_IsHalted()
T0818 001:866.573 - 0.485ms returns FALSE
T0818 001:866.579 JLINK_HasError()
T0818 001:868.087 JLINK_IsHalted()
T0818 001:868.603 - 0.516ms returns FALSE
T0818 001:868.610 JLINK_HasError()
T0818 001:870.089 JLINK_IsHalted()
T0818 001:870.565 - 0.475ms returns FALSE
T0818 001:870.572 JLINK_HasError()
T0818 001:872.086 JLINK_IsHalted()
T0818 001:872.596 - 0.510ms returns FALSE
T0818 001:872.604 JLINK_HasError()
T0818 001:874.094 JLINK_IsHalted()
T0818 001:874.606 - 0.511ms returns FALSE
T0818 001:874.614 JLINK_HasError()
T0818 001:876.086 JLINK_IsHalted()
T0818 001:876.561 - 0.475ms returns FALSE
T0818 001:876.569 JLINK_HasError()
T0818 001:878.090 JLINK_IsHalted()
T0818 001:878.576 - 0.486ms returns FALSE
T0818 001:878.583 JLINK_HasError()
T0818 001:880.086 JLINK_IsHalted()
T0818 001:880.603 - 0.517ms returns FALSE
T0818 001:880.612 JLINK_HasError()
T0818 001:882.085 JLINK_IsHalted()
T0818 001:882.604 - 0.519ms returns FALSE
T0818 001:882.611 JLINK_HasError()
T0818 001:884.086 JLINK_IsHalted()
T0818 001:884.605 - 0.519ms returns FALSE
T0818 001:884.612 JLINK_HasError()
T0818 001:886.086 JLINK_IsHalted()
T0818 001:886.574 - 0.487ms returns FALSE
T0818 001:886.581 JLINK_HasError()
T0818 001:888.088 JLINK_IsHalted()
T0818 001:888.579 - 0.491ms returns FALSE
T0818 001:888.589 JLINK_HasError()
T0818 001:890.086 JLINK_IsHalted()
T0818 001:890.597 - 0.510ms returns FALSE
T0818 001:890.606 JLINK_HasError()
T0818 001:892.090 JLINK_IsHalted()
T0818 001:892.576 - 0.485ms returns FALSE
T0818 001:892.584 JLINK_HasError()
T0818 001:894.084 JLINK_IsHalted()
T0818 001:894.573 - 0.488ms returns FALSE
T0818 001:894.578 JLINK_HasError()
T0818 001:896.086 JLINK_IsHalted()
T0818 001:896.597 - 0.511ms returns FALSE
T0818 001:896.604 JLINK_HasError()
T0818 001:898.099 JLINK_IsHalted()
T0818 001:898.610 - 0.511ms returns FALSE
T0818 001:898.617 JLINK_HasError()
T0818 001:900.086 JLINK_IsHalted()
T0818 001:900.599 - 0.512ms returns FALSE
T0818 001:900.610 JLINK_HasError()
T0818 001:902.090 JLINK_IsHalted()
T0818 001:902.597 - 0.507ms returns FALSE
T0818 001:902.604 JLINK_HasError()
T0818 001:904.086 JLINK_IsHalted()
T0818 001:904.577 - 0.491ms returns FALSE
T0818 001:904.583 JLINK_HasError()
T0818 001:906.089 JLINK_IsHalted()
T0818 001:906.575 - 0.486ms returns FALSE
T0818 001:906.581 JLINK_HasError()
T0818 001:908.084 JLINK_IsHalted()
T0818 001:908.561 - 0.476ms returns FALSE
T0818 001:908.567 JLINK_HasError()
T0818 001:910.087 JLINK_IsHalted()
T0818 001:910.786 - 0.698ms returns FALSE
T0818 001:910.795 JLINK_HasError()
T0818 001:912.084 JLINK_IsHalted()
T0818 001:912.596 - 0.511ms returns FALSE
T0818 001:912.602 JLINK_HasError()
T0818 001:914.088 JLINK_IsHalted()
T0818 001:914.574 - 0.485ms returns FALSE
T0818 001:914.580 JLINK_HasError()
T0818 001:916.085 JLINK_IsHalted()
T0818 001:916.574 - 0.489ms returns FALSE
T0818 001:916.580 JLINK_HasError()
T0818 001:918.088 JLINK_IsHalted()
T0818 001:918.560 - 0.471ms returns FALSE
T0818 001:918.569 JLINK_HasError()
T0818 001:920.088 JLINK_IsHalted()
T0818 001:920.849 - 0.760ms returns FALSE
T0818 001:920.860 JLINK_HasError()
T0818 001:922.088 JLINK_IsHalted()
T0818 001:922.599 - 0.510ms returns FALSE
T0818 001:922.607 JLINK_HasError()
T0818 001:924.091 JLINK_IsHalted()
T0818 001:924.606 - 0.515ms returns FALSE
T0818 001:924.614 JLINK_HasError()
T0818 001:926.090 JLINK_IsHalted()
T0818 001:926.610 - 0.519ms returns FALSE
T0818 001:926.617 JLINK_HasError()
T0818 001:928.085 JLINK_IsHalted()
T0818 001:928.603 - 0.517ms returns FALSE
T0818 001:928.612 JLINK_HasError()
T0818 001:930.100 JLINK_IsHalted()
T0818 001:930.607 - 0.507ms returns FALSE
T0818 001:930.618 JLINK_HasError()
T0818 001:932.086 JLINK_IsHalted()
T0818 001:932.603 - 0.516ms returns FALSE
T0818 001:932.611 JLINK_HasError()
T0818 001:934.095 JLINK_IsHalted()
T0818 001:934.578 - 0.483ms returns FALSE
T0818 001:934.593 JLINK_HasError()
T0818 001:936.091 JLINK_IsHalted()
T0818 001:936.606 - 0.513ms returns FALSE
T0818 001:936.614 JLINK_HasError()
T0818 001:938.088 JLINK_IsHalted()
T0818 001:938.565 - 0.476ms returns FALSE
T0818 001:938.574 JLINK_HasError()
T0818 001:940.089 JLINK_IsHalted()
T0818 001:940.647 - 0.557ms returns FALSE
T0818 001:940.653 JLINK_HasError()
T0818 001:942.091 JLINK_IsHalted()
T0818 001:942.607 - 0.515ms returns FALSE
T0818 001:942.614 JLINK_HasError()
T0818 001:944.088 JLINK_IsHalted()
T0818 001:944.518 - 0.429ms returns FALSE
T0818 001:944.527 JLINK_HasError()
T0818 001:946.085 JLINK_IsHalted()
T0818 001:946.610 - 0.524ms returns FALSE
T0818 001:946.617 JLINK_HasError()
T0818 001:948.088 JLINK_IsHalted()
T0818 001:948.606 - 0.518ms returns FALSE
T0818 001:948.613 JLINK_HasError()
T0818 001:950.085 JLINK_IsHalted()
T0818 001:950.603 - 0.517ms returns FALSE
T0818 001:950.611 JLINK_HasError()
T0818 001:953.089 JLINK_IsHalted()
T0818 001:953.608 - 0.518ms returns FALSE
T0818 001:953.615 JLINK_HasError()
T0818 001:955.086 JLINK_IsHalted()
T0818 001:955.606 - 0.519ms returns FALSE
T0818 001:955.612 JLINK_HasError()
T0818 001:957.089 JLINK_IsHalted()
T0818 001:957.574 - 0.485ms returns FALSE
T0818 001:957.580 JLINK_HasError()
T0818 001:959.085 JLINK_IsHalted()
T0818 001:959.596 - 0.510ms returns FALSE
T0818 001:959.602 JLINK_HasError()
T0818 001:961.087 JLINK_IsHalted()
T0818 001:961.574 - 0.487ms returns FALSE
T0818 001:961.580 JLINK_HasError()
T0818 001:963.084 JLINK_IsHalted()
T0818 001:963.516 - 0.432ms returns FALSE
T0818 001:963.522 JLINK_HasError()
T0818 001:965.089 JLINK_IsHalted()
T0818 001:965.670 - 0.579ms returns FALSE
T0818 001:965.686 JLINK_HasError()
T0818 001:968.096 JLINK_IsHalted()
T0818 001:968.576 - 0.481ms returns FALSE
T0818 001:968.583 JLINK_HasError()
T0818 001:970.085 JLINK_IsHalted()
T0818 001:970.596 - 0.510ms returns FALSE
T0818 001:970.602 JLINK_HasError()
T0818 001:972.087 JLINK_IsHalted()
T0818 001:972.608 - 0.520ms returns FALSE
T0818 001:972.614 JLINK_HasError()
T0818 001:974.086 JLINK_IsHalted()
T0818 001:974.604 - 0.517ms returns FALSE
T0818 001:974.610 JLINK_HasError()
T0818 001:976.086 JLINK_IsHalted()
T0818 001:976.602 - 0.516ms returns FALSE
T0818 001:976.608 JLINK_HasError()
T0818 001:978.087 JLINK_IsHalted()
T0818 001:978.560 - 0.473ms returns FALSE
T0818 001:978.567 JLINK_HasError()
T0818 001:980.088 JLINK_IsHalted()
T0818 001:980.606 - 0.517ms returns FALSE
T0818 001:980.614 JLINK_HasError()
T0818 001:982.086 JLINK_IsHalted()
T0818 001:982.609 - 0.522ms returns FALSE
T0818 001:982.615 JLINK_HasError()
T0818 001:984.085 JLINK_IsHalted()
T0818 001:984.605 - 0.519ms returns FALSE
T0818 001:984.611 JLINK_HasError()
T0818 001:986.089 JLINK_IsHalted()
T0818 001:986.563 - 0.474ms returns FALSE
T0818 001:986.569 JLINK_HasError()
T0818 001:988.084 JLINK_IsHalted()
T0818 001:988.576 - 0.491ms returns FALSE
T0818 001:988.582 JLINK_HasError()
T0818 001:990.091 JLINK_IsHalted()
T0818 001:990.752 - 0.661ms returns FALSE
T0818 001:990.759 JLINK_HasError()
T0818 001:992.084 JLINK_IsHalted()
T0818 001:992.597 - 0.512ms returns FALSE
T0818 001:992.607 JLINK_HasError()
T0818 001:994.089 JLINK_IsHalted()
T0818 001:994.597 - 0.508ms returns FALSE
T0818 001:994.605 JLINK_HasError()
T0818 001:996.085 JLINK_IsHalted()
T0818 001:996.603 - 0.518ms returns FALSE
T0818 001:996.609 JLINK_HasError()
T0818 001:998.088 JLINK_IsHalted()
T0818 001:998.561 - 0.473ms returns FALSE
T0818 001:998.568 JLINK_HasError()
T0818 002:000.086 JLINK_IsHalted()
T0818 002:000.604 - 0.518ms returns FALSE
T0818 002:000.614 JLINK_HasError()
T0818 002:002.088 JLINK_IsHalted()
T0818 002:002.610 - 0.522ms returns FALSE
T0818 002:002.616 JLINK_HasError()
T0818 002:004.094 JLINK_IsHalted()
T0818 002:004.577 - 0.483ms returns FALSE
T0818 002:004.584 JLINK_HasError()
T0818 002:006.090 JLINK_IsHalted()
T0818 002:006.577 - 0.486ms returns FALSE
T0818 002:006.586 JLINK_HasError()
T0818 002:008.086 JLINK_IsHalted()
T0818 002:008.594 - 0.508ms returns FALSE
T0818 002:008.601 JLINK_HasError()
T0818 002:010.087 JLINK_IsHalted()
T0818 002:010.606 - 0.518ms returns FALSE
T0818 002:010.614 JLINK_HasError()
T0818 002:012.086 JLINK_IsHalted()
T0818 002:012.604 - 0.517ms returns FALSE
T0818 002:012.612 JLINK_HasError()
T0818 002:014.090 JLINK_IsHalted()
T0818 002:014.575 - 0.485ms returns FALSE
T0818 002:014.582 JLINK_HasError()
T0818 002:016.088 JLINK_IsHalted()
T0818 002:016.558 - 0.470ms returns FALSE
T0818 002:016.567 JLINK_HasError()
T0818 002:018.088 JLINK_IsHalted()
T0818 002:018.575 - 0.487ms returns FALSE
T0818 002:018.581 JLINK_HasError()
T0818 002:020.086 JLINK_IsHalted()
T0818 002:020.844 - 0.757ms returns FALSE
T0818 002:020.854 JLINK_HasError()
T0818 002:022.085 JLINK_IsHalted()
T0818 002:022.574 - 0.488ms returns FALSE
T0818 002:022.580 JLINK_HasError()
T0818 002:024.086 JLINK_IsHalted()
T0818 002:024.574 - 0.488ms returns FALSE
T0818 002:024.580 JLINK_HasError()
T0818 002:026.084 JLINK_IsHalted()
T0818 002:026.574 - 0.489ms returns FALSE
T0818 002:026.580 JLINK_HasError()
T0818 002:028.086 JLINK_IsHalted()
T0818 002:028.565 - 0.478ms returns FALSE
T0818 002:028.572 JLINK_HasError()
T0818 002:030.086 JLINK_IsHalted()
T0818 002:030.845 - 0.759ms returns FALSE
T0818 002:030.855 JLINK_HasError()
T0818 002:032.086 JLINK_IsHalted()
T0818 002:032.602 - 0.515ms returns FALSE
T0818 002:032.608 JLINK_HasError()
T0818 002:034.085 JLINK_IsHalted()
T0818 002:034.608 - 0.522ms returns FALSE
T0818 002:034.614 JLINK_HasError()
T0818 002:036.099 JLINK_IsHalted()
T0818 002:036.601 - 0.502ms returns FALSE
T0818 002:036.608 JLINK_HasError()
T0818 002:038.088 JLINK_IsHalted()
T0818 002:038.604 - 0.516ms returns FALSE
T0818 002:038.613 JLINK_HasError()
T0818 002:040.089 JLINK_IsHalted()
T0818 002:040.664 - 0.574ms returns FALSE
T0818 002:040.683 JLINK_HasError()
T0818 002:042.087 JLINK_IsHalted()
T0818 002:042.608 - 0.521ms returns FALSE
T0818 002:042.615 JLINK_HasError()
T0818 002:044.086 JLINK_IsHalted()
T0818 002:044.607 - 0.521ms returns FALSE
T0818 002:044.615 JLINK_HasError()
T0818 002:046.084 JLINK_IsHalted()
T0818 002:046.573 - 0.489ms returns FALSE
T0818 002:046.580 JLINK_HasError()
T0818 002:048.086 JLINK_IsHalted()
T0818 002:048.605 - 0.518ms returns FALSE
T0818 002:048.612 JLINK_HasError()
T0818 002:050.093 JLINK_IsHalted()
T0818 002:050.561 - 0.467ms returns FALSE
T0818 002:050.568 JLINK_HasError()
T0818 002:052.086 JLINK_IsHalted()
T0818 002:052.603 - 0.516ms returns FALSE
T0818 002:052.609 JLINK_HasError()
T0818 002:054.085 JLINK_IsHalted()
T0818 002:054.604 - 0.518ms returns FALSE
T0818 002:054.610 JLINK_HasError()
T0818 002:056.085 JLINK_IsHalted()
T0818 002:056.574 - 0.488ms returns FALSE
T0818 002:056.580 JLINK_HasError()
T0818 002:058.087 JLINK_IsHalted()
T0818 002:058.606 - 0.519ms returns FALSE
T0818 002:058.613 JLINK_HasError()
T0818 002:060.089 JLINK_IsHalted()
T0818 002:060.565 - 0.475ms returns FALSE
T0818 002:060.573 JLINK_HasError()
T0818 002:063.089 JLINK_IsHalted()
T0818 002:063.564 - 0.474ms returns FALSE
T0818 002:063.570 JLINK_HasError()
T0818 002:065.085 JLINK_IsHalted()
T0818 002:065.605 - 0.520ms returns FALSE
T0818 002:065.611 JLINK_HasError()
T0818 002:067.084 JLINK_IsHalted()
T0818 002:067.595 - 0.510ms returns FALSE
T0818 002:067.602 JLINK_HasError()
T0818 002:069.085 JLINK_IsHalted()
T0818 002:069.575 - 0.490ms returns FALSE
T0818 002:069.581 JLINK_HasError()
T0818 002:071.085 JLINK_IsHalted()
T0818 002:071.602 - 0.516ms returns FALSE
T0818 002:071.608 JLINK_HasError()
T0818 002:073.087 JLINK_IsHalted()
T0818 002:073.560 - 0.473ms returns FALSE
T0818 002:073.567 JLINK_HasError()
T0818 002:075.086 JLINK_IsHalted()
T0818 002:075.569 - 0.482ms returns FALSE
T0818 002:075.588 JLINK_HasError()
T0818 002:077.097 JLINK_IsHalted()
T0818 002:077.611 - 0.513ms returns FALSE
T0818 002:077.619 JLINK_HasError()
T0818 002:079.091 JLINK_IsHalted()
T0818 002:079.657 - 0.565ms returns FALSE
T0818 002:079.664 JLINK_HasError()
T0818 002:081.095 JLINK_IsHalted()
T0818 002:081.578 - 0.482ms returns FALSE
T0818 002:081.588 JLINK_HasError()
T0818 002:083.093 JLINK_IsHalted()
T0818 002:083.564 - 0.470ms returns FALSE
T0818 002:083.571 JLINK_HasError()
T0818 002:085.086 JLINK_IsHalted()
T0818 002:085.597 - 0.511ms returns FALSE
T0818 002:085.606 JLINK_HasError()
T0818 002:087.089 JLINK_IsHalted()
T0818 002:087.574 - 0.485ms returns FALSE
T0818 002:087.582 JLINK_HasError()
T0818 002:089.094 JLINK_IsHalted()
T0818 002:089.662 - 0.567ms returns FALSE
T0818 002:089.676 JLINK_HasError()
T0818 002:091.091 JLINK_IsHalted()
T0818 002:091.580 - 0.487ms returns FALSE
T0818 002:091.591 JLINK_HasError()
T0818 002:093.087 JLINK_IsHalted()
T0818 002:093.596 - 0.508ms returns FALSE
T0818 002:093.606 JLINK_HasError()
T0818 002:095.089 JLINK_IsHalted()
T0818 002:095.566 - 0.476ms returns FALSE
T0818 002:095.573 JLINK_HasError()
T0818 002:097.086 JLINK_IsHalted()
T0818 002:097.601 - 0.515ms returns FALSE
T0818 002:097.607 JLINK_HasError()
T0818 002:099.087 JLINK_IsHalted()
T0818 002:099.605 - 0.518ms returns FALSE
T0818 002:099.611 JLINK_HasError()
T0818 002:101.085 JLINK_IsHalted()
T0818 002:101.560 - 0.475ms returns FALSE
T0818 002:101.566 JLINK_HasError()
T0818 002:103.088 JLINK_IsHalted()
T0818 002:103.575 - 0.486ms returns FALSE
T0818 002:103.581 JLINK_HasError()
T0818 002:105.084 JLINK_IsHalted()
T0818 002:105.595 - 0.511ms returns FALSE
T0818 002:105.602 JLINK_HasError()
T0818 002:107.087 JLINK_IsHalted()
T0818 002:107.568 - 0.481ms returns FALSE
T0818 002:107.575 JLINK_HasError()
T0818 002:109.087 JLINK_IsHalted()
T0818 002:109.604 - 0.516ms returns FALSE
T0818 002:109.611 JLINK_HasError()
T0818 002:111.088 JLINK_IsHalted()
T0818 002:111.562 - 0.474ms returns FALSE
T0818 002:111.570 JLINK_HasError()
T0818 002:113.085 JLINK_IsHalted()
T0818 002:113.606 - 0.520ms returns FALSE
T0818 002:113.612 JLINK_HasError()
T0818 002:115.088 JLINK_IsHalted()
T0818 002:115.605 - 0.516ms returns FALSE
T0818 002:115.611 JLINK_HasError()
T0818 002:117.085 JLINK_IsHalted()
T0818 002:117.600 - 0.515ms returns FALSE
T0818 002:117.605 JLINK_HasError()
T0818 002:119.082 JLINK_IsHalted()
T0818 002:119.573 - 0.490ms returns FALSE
T0818 002:119.578 JLINK_HasError()
T0818 002:121.090 JLINK_IsHalted()
T0818 002:121.574 - 0.483ms returns FALSE
T0818 002:121.581 JLINK_HasError()
T0818 002:123.082 JLINK_IsHalted()
T0818 002:123.597 - 0.514ms returns FALSE
T0818 002:123.606 JLINK_HasError()
T0818 002:127.084 JLINK_IsHalted()
T0818 002:127.574 - 0.490ms returns FALSE
T0818 002:127.581 JLINK_HasError()
T0818 002:129.082 JLINK_IsHalted()
T0818 002:129.572 - 0.489ms returns FALSE
T0818 002:129.578 JLINK_HasError()
T0818 002:131.083 JLINK_IsHalted()
T0818 002:131.603 - 0.519ms returns FALSE
T0818 002:131.609 JLINK_HasError()
T0818 002:133.083 JLINK_IsHalted()
T0818 002:133.594 - 0.510ms returns FALSE
T0818 002:133.599 JLINK_HasError()
T0818 002:135.082 JLINK_IsHalted()
T0818 002:135.562 - 0.479ms returns FALSE
T0818 002:135.567 JLINK_HasError()
T0818 002:137.085 JLINK_IsHalted()
T0818 002:137.605 - 0.519ms returns FALSE
T0818 002:137.612 JLINK_HasError()
T0818 002:139.085 JLINK_IsHalted()
T0818 002:139.606 - 0.521ms returns FALSE
T0818 002:139.618 JLINK_HasError()
T0818 002:140.998 JLINK_IsHalted()
T0818 002:141.468 - 0.470ms returns FALSE
T0818 002:141.474 JLINK_HasError()
T0818 002:142.996 JLINK_IsHalted()
T0818 002:143.471 - 0.475ms returns FALSE
T0818 002:143.477 JLINK_HasError()
T0818 002:145.000 JLINK_IsHalted()
T0818 002:145.469 - 0.469ms returns FALSE
T0818 002:145.475 JLINK_HasError()
T0818 002:146.996 JLINK_IsHalted()
T0818 002:147.466 - 0.470ms returns FALSE
T0818 002:147.472 JLINK_HasError()
T0818 002:148.995 JLINK_IsHalted()
T0818 002:149.454 - 0.458ms returns FALSE
T0818 002:149.459 JLINK_HasError()
T0818 002:150.996 JLINK_IsHalted()
T0818 002:151.459 - 0.463ms returns FALSE
T0818 002:151.466 JLINK_HasError()
T0818 002:152.995 JLINK_IsHalted()
T0818 002:153.501 - 0.505ms returns FALSE
T0818 002:153.506 JLINK_HasError()
T0818 002:154.995 JLINK_IsHalted()
T0818 002:155.479 - 0.483ms returns FALSE
T0818 002:155.493 JLINK_HasError()
T0818 002:157.003 JLINK_IsHalted()
T0818 002:157.575 - 0.571ms returns FALSE
T0818 002:157.581 JLINK_HasError()
T0818 002:158.996 JLINK_IsHalted()
T0818 002:159.474 - 0.478ms returns FALSE
T0818 002:159.479 JLINK_HasError()
T0818 002:160.997 JLINK_IsHalted()
T0818 002:161.468 - 0.470ms returns FALSE
T0818 002:161.474 JLINK_HasError()
T0818 002:163.003 JLINK_IsHalted()
T0818 002:163.561 - 0.558ms returns FALSE
T0818 002:163.568 JLINK_HasError()
T0818 002:164.996 JLINK_IsHalted()
T0818 002:165.468 - 0.471ms returns FALSE
T0818 002:165.473 JLINK_HasError()
T0818 002:166.996 JLINK_IsHalted()
T0818 002:167.468 - 0.471ms returns FALSE
T0818 002:167.473 JLINK_HasError()
T0818 002:168.995 JLINK_IsHalted()
T0818 002:169.473 - 0.477ms returns FALSE
T0818 002:169.478 JLINK_HasError()
T0818 002:170.997 JLINK_IsHalted()
T0818 002:171.469 - 0.471ms returns FALSE
T0818 002:171.476 JLINK_HasError()
T0818 002:174.001 JLINK_IsHalted()
T0818 002:174.455 - 0.453ms returns FALSE
T0818 002:174.466 JLINK_HasError()
T0818 002:175.995 JLINK_IsHalted()
T0818 002:176.466 - 0.470ms returns FALSE
T0818 002:176.471 JLINK_HasError()
T0818 002:177.997 JLINK_IsHalted()
T0818 002:178.469 - 0.472ms returns FALSE
T0818 002:178.475 JLINK_HasError()
T0818 002:179.995 JLINK_IsHalted()
T0818 002:180.663 - 0.666ms returns FALSE
T0818 002:180.673 JLINK_HasError()
T0818 002:181.996 JLINK_IsHalted()
T0818 002:182.466 - 0.470ms returns FALSE
T0818 002:182.472 JLINK_HasError()
T0818 002:183.997 JLINK_IsHalted()
T0818 002:184.513 - 0.515ms returns FALSE
T0818 002:184.530 JLINK_HasError()
T0818 002:185.996 JLINK_IsHalted()
T0818 002:186.453 - 0.457ms returns FALSE
T0818 002:186.462 JLINK_HasError()
T0818 002:187.998 JLINK_IsHalted()
T0818 002:188.514 - 0.516ms returns FALSE
T0818 002:188.520 JLINK_HasError()
T0818 002:190.607 JLINK_IsHalted()
T0818 002:191.438 - 0.830ms returns FALSE
T0818 002:191.447 JLINK_HasError()
T0818 002:192.998 JLINK_IsHalted()
T0818 002:193.468 - 0.470ms returns FALSE
T0818 002:193.475 JLINK_HasError()
T0818 002:194.996 JLINK_IsHalted()
T0818 002:195.468 - 0.471ms returns FALSE
T0818 002:195.474 JLINK_HasError()
T0818 002:196.996 JLINK_IsHalted()
T0818 002:197.467 - 0.470ms returns FALSE
T0818 002:197.473 JLINK_HasError()
T0818 002:198.996 JLINK_IsHalted()
T0818 002:199.467 - 0.471ms returns FALSE
T0818 002:199.473 JLINK_HasError()
T0818 002:201.001 JLINK_IsHalted()
T0818 002:201.429 - 0.428ms returns FALSE
T0818 002:201.436 JLINK_HasError()
T0818 002:203.214 JLINK_IsHalted()
T0818 002:205.502   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:205.970 - 2.756ms returns TRUE
T0818 002:205.977 JLINK_ReadReg(R15 (PC))
T0818 002:205.982 - 0.005ms returns 0x20000000
T0818 002:205.986 JLINK_ClrBPEx(BPHandle = 0x00000009)
T0818 002:205.990 - 0.003ms returns 0x00
T0818 002:205.996 JLINK_ReadReg(R0)
T0818 002:206.000 - 0.004ms returns 0x00000000
T0818 002:206.250 JLINK_HasError()
T0818 002:206.258 JLINK_WriteReg(R0, 0x00000001)
T0818 002:206.263 - 0.004ms returns 0
T0818 002:206.267 JLINK_WriteReg(R1, 0x00004000)
T0818 002:206.271 - 0.003ms returns 0
T0818 002:206.275 JLINK_WriteReg(R2, 0x000000FF)
T0818 002:206.278 - 0.003ms returns 0
T0818 002:206.282 JLINK_WriteReg(R3, 0x00000000)
T0818 002:206.286 - 0.003ms returns 0
T0818 002:206.290 JLINK_WriteReg(R4, 0x00000000)
T0818 002:206.293 - 0.003ms returns 0
T0818 002:206.297 JLINK_WriteReg(R5, 0x00000000)
T0818 002:206.301 - 0.003ms returns 0
T0818 002:206.305 JLINK_WriteReg(R6, 0x00000000)
T0818 002:206.308 - 0.003ms returns 0
T0818 002:206.312 JLINK_WriteReg(R7, 0x00000000)
T0818 002:206.316 - 0.003ms returns 0
T0818 002:206.320 JLINK_WriteReg(R8, 0x00000000)
T0818 002:206.323 - 0.003ms returns 0
T0818 002:206.327 JLINK_WriteReg(R9, 0x20000180)
T0818 002:206.331 - 0.003ms returns 0
T0818 002:206.335 JLINK_WriteReg(R10, 0x00000000)
T0818 002:206.338 - 0.003ms returns 0
T0818 002:206.342 JLINK_WriteReg(R11, 0x00000000)
T0818 002:206.346 - 0.003ms returns 0
T0818 002:206.350 JLINK_WriteReg(R12, 0x00000000)
T0818 002:206.353 - 0.003ms returns 0
T0818 002:206.357 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:206.361 - 0.003ms returns 0
T0818 002:206.365 JLINK_WriteReg(R14, 0x20000001)
T0818 002:206.369 - 0.003ms returns 0
T0818 002:206.373 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 002:206.376 - 0.003ms returns 0
T0818 002:206.380 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:206.384 - 0.003ms returns 0
T0818 002:206.388 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:206.391 - 0.003ms returns 0
T0818 002:206.395 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:206.398 - 0.003ms returns 0
T0818 002:206.402 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:206.406 - 0.003ms returns 0
T0818 002:206.410 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:206.420 - 0.009ms returns 0x0000000A
T0818 002:206.424 JLINK_Go()
T0818 002:206.432   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:209.243 - 2.819ms 
T0818 002:209.257 JLINK_IsHalted()
T0818 002:211.550   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:212.083 - 2.825ms returns TRUE
T0818 002:212.093 JLINK_ReadReg(R15 (PC))
T0818 002:212.098 - 0.005ms returns 0x20000000
T0818 002:212.124 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T0818 002:212.130 - 0.005ms returns 0x00
T0818 002:212.134 JLINK_ReadReg(R0)
T0818 002:212.138 - 0.003ms returns 0x00000000
T0818 002:265.924 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 002:265.937   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 002:265.953   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 002:268.441 - 2.517ms returns 0x184
T0818 002:268.481 JLINK_HasError()
T0818 002:268.487 JLINK_WriteReg(R0, 0x08000000)
T0818 002:268.493 - 0.006ms returns 0
T0818 002:268.501 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 002:268.506 - 0.005ms returns 0
T0818 002:268.511 JLINK_WriteReg(R2, 0x00000002)
T0818 002:268.514 - 0.003ms returns 0
T0818 002:268.518 JLINK_WriteReg(R3, 0x00000000)
T0818 002:268.522 - 0.003ms returns 0
T0818 002:268.527 JLINK_WriteReg(R4, 0x00000000)
T0818 002:268.530 - 0.003ms returns 0
T0818 002:268.535 JLINK_WriteReg(R5, 0x00000000)
T0818 002:268.538 - 0.003ms returns 0
T0818 002:268.543 JLINK_WriteReg(R6, 0x00000000)
T0818 002:268.546 - 0.003ms returns 0
T0818 002:268.551 JLINK_WriteReg(R7, 0x00000000)
T0818 002:268.554 - 0.003ms returns 0
T0818 002:268.558 JLINK_WriteReg(R8, 0x00000000)
T0818 002:268.562 - 0.003ms returns 0
T0818 002:268.566 JLINK_WriteReg(R9, 0x20000180)
T0818 002:268.570 - 0.003ms returns 0
T0818 002:268.574 JLINK_WriteReg(R10, 0x00000000)
T0818 002:268.578 - 0.003ms returns 0
T0818 002:268.582 JLINK_WriteReg(R11, 0x00000000)
T0818 002:268.586 - 0.003ms returns 0
T0818 002:268.590 JLINK_WriteReg(R12, 0x00000000)
T0818 002:268.593 - 0.003ms returns 0
T0818 002:268.597 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:268.602 - 0.004ms returns 0
T0818 002:268.606 JLINK_WriteReg(R14, 0x20000001)
T0818 002:268.610 - 0.003ms returns 0
T0818 002:268.614 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 002:268.617 - 0.003ms returns 0
T0818 002:268.622 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:268.625 - 0.003ms returns 0
T0818 002:268.629 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:268.633 - 0.003ms returns 0
T0818 002:268.637 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:268.641 - 0.003ms returns 0
T0818 002:268.645 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:268.649 - 0.003ms returns 0
T0818 002:268.653 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:268.660   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:269.192 - 0.538ms returns 0x0000000B
T0818 002:269.203 JLINK_Go()
T0818 002:269.210   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 002:269.723   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:272.653 - 3.449ms 
T0818 002:272.663 JLINK_IsHalted()
T0818 002:274.954   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:275.424 - 2.760ms returns TRUE
T0818 002:275.429 JLINK_ReadReg(R15 (PC))
T0818 002:275.434 - 0.004ms returns 0x20000000
T0818 002:275.438 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T0818 002:275.442 - 0.003ms returns 0x00
T0818 002:275.446 JLINK_ReadReg(R0)
T0818 002:275.450 - 0.003ms returns 0x00000000
T0818 002:275.676 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:275.683   Data:  80 17 00 20 C1 01 00 08 2D 28 00 08 11 25 00 08 ...
T0818 002:275.696   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:278.332 - 2.656ms returns 0x27C
T0818 002:278.338 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:278.342   Data:  4F F0 00 0B 23 F0 00 43 50 EA 01 04 5E D0 52 EA ...
T0818 002:278.350   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:280.250 - 1.911ms returns 0x184
T0818 002:280.259 JLINK_HasError()
T0818 002:280.266 JLINK_WriteReg(R0, 0x08000000)
T0818 002:280.271 - 0.005ms returns 0
T0818 002:280.276 JLINK_WriteReg(R1, 0x00000400)
T0818 002:280.281 - 0.005ms returns 0
T0818 002:280.287 JLINK_WriteReg(R2, 0x20000184)
T0818 002:280.291 - 0.004ms returns 0
T0818 002:280.296 JLINK_WriteReg(R3, 0x00000000)
T0818 002:280.300 - 0.004ms returns 0
T0818 002:280.305 JLINK_WriteReg(R4, 0x00000000)
T0818 002:280.310 - 0.004ms returns 0
T0818 002:280.315 JLINK_WriteReg(R5, 0x00000000)
T0818 002:280.319 - 0.004ms returns 0
T0818 002:280.324 JLINK_WriteReg(R6, 0x00000000)
T0818 002:280.328 - 0.004ms returns 0
T0818 002:280.334 JLINK_WriteReg(R7, 0x00000000)
T0818 002:280.338 - 0.004ms returns 0
T0818 002:280.343 JLINK_WriteReg(R8, 0x00000000)
T0818 002:280.347 - 0.004ms returns 0
T0818 002:280.352 JLINK_WriteReg(R9, 0x20000180)
T0818 002:280.356 - 0.004ms returns 0
T0818 002:280.361 JLINK_WriteReg(R10, 0x00000000)
T0818 002:280.366 - 0.004ms returns 0
T0818 002:280.371 JLINK_WriteReg(R11, 0x00000000)
T0818 002:280.375 - 0.004ms returns 0
T0818 002:280.380 JLINK_WriteReg(R12, 0x00000000)
T0818 002:280.420 - 0.039ms returns 0
T0818 002:280.425 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:280.429 - 0.003ms returns 0
T0818 002:280.433 JLINK_WriteReg(R14, 0x20000001)
T0818 002:280.436 - 0.003ms returns 0
T0818 002:280.441 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:280.456 - 0.015ms returns 0
T0818 002:280.460 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:280.464 - 0.003ms returns 0
T0818 002:280.468 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:280.471 - 0.003ms returns 0
T0818 002:280.475 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:280.478 - 0.003ms returns 0
T0818 002:280.483 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:280.486 - 0.003ms returns 0
T0818 002:280.490 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:280.494 - 0.004ms returns 0x0000000C
T0818 002:280.498 JLINK_Go()
T0818 002:280.506   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:283.435 - 2.936ms 
T0818 002:283.445 JLINK_IsHalted()
T0818 002:283.925 - 0.480ms returns FALSE
T0818 002:283.931 JLINK_HasError()
T0818 002:286.997 JLINK_IsHalted()
T0818 002:287.512 - 0.515ms returns FALSE
T0818 002:287.519 JLINK_HasError()
T0818 002:288.996 JLINK_IsHalted()
T0818 002:291.234   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:291.752 - 2.756ms returns TRUE
T0818 002:291.759 JLINK_ReadReg(R15 (PC))
T0818 002:291.763 - 0.004ms returns 0x20000000
T0818 002:291.768 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T0818 002:291.772 - 0.003ms returns 0x00
T0818 002:291.776 JLINK_ReadReg(R0)
T0818 002:291.780 - 0.003ms returns 0x00000000
T0818 002:292.042 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:292.048   Data:  19 46 70 47 10 B5 41 00 0C D0 C0 F3 C7 51 96 29 ...
T0818 002:292.058   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:294.676 - 2.634ms returns 0x27C
T0818 002:294.686 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:294.690   Data:  C1 F3 0A 56 40 F4 80 15 43 F4 80 13 A7 EB 06 08 ...
T0818 002:294.696   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:296.556 - 1.869ms returns 0x184
T0818 002:296.564 JLINK_HasError()
T0818 002:296.594 JLINK_WriteReg(R0, 0x08000400)
T0818 002:296.599 - 0.005ms returns 0
T0818 002:296.604 JLINK_WriteReg(R1, 0x00000400)
T0818 002:296.607 - 0.003ms returns 0
T0818 002:296.611 JLINK_WriteReg(R2, 0x20000184)
T0818 002:296.615 - 0.003ms returns 0
T0818 002:296.619 JLINK_WriteReg(R3, 0x00000000)
T0818 002:296.622 - 0.003ms returns 0
T0818 002:296.626 JLINK_WriteReg(R4, 0x00000000)
T0818 002:296.630 - 0.003ms returns 0
T0818 002:296.634 JLINK_WriteReg(R5, 0x00000000)
T0818 002:296.637 - 0.003ms returns 0
T0818 002:296.641 JLINK_WriteReg(R6, 0x00000000)
T0818 002:296.644 - 0.003ms returns 0
T0818 002:296.648 JLINK_WriteReg(R7, 0x00000000)
T0818 002:296.652 - 0.003ms returns 0
T0818 002:296.656 JLINK_WriteReg(R8, 0x00000000)
T0818 002:296.659 - 0.003ms returns 0
T0818 002:296.663 JLINK_WriteReg(R9, 0x20000180)
T0818 002:296.666 - 0.003ms returns 0
T0818 002:296.671 JLINK_WriteReg(R10, 0x00000000)
T0818 002:296.674 - 0.003ms returns 0
T0818 002:296.678 JLINK_WriteReg(R11, 0x00000000)
T0818 002:296.682 - 0.003ms returns 0
T0818 002:296.686 JLINK_WriteReg(R12, 0x00000000)
T0818 002:296.689 - 0.003ms returns 0
T0818 002:296.693 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:296.697 - 0.003ms returns 0
T0818 002:296.701 JLINK_WriteReg(R14, 0x20000001)
T0818 002:296.704 - 0.003ms returns 0
T0818 002:296.708 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:296.712 - 0.003ms returns 0
T0818 002:296.716 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:296.719 - 0.003ms returns 0
T0818 002:296.723 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:296.727 - 0.003ms returns 0
T0818 002:296.731 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:296.734 - 0.003ms returns 0
T0818 002:296.738 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:296.742 - 0.003ms returns 0
T0818 002:296.746 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:296.750 - 0.004ms returns 0x0000000D
T0818 002:296.754 JLINK_Go()
T0818 002:296.761   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:299.653 - 2.898ms 
T0818 002:299.668 JLINK_IsHalted()
T0818 002:300.162 - 0.494ms returns FALSE
T0818 002:300.168 JLINK_HasError()
T0818 002:301.999 JLINK_IsHalted()
T0818 002:302.455 - 0.456ms returns FALSE
T0818 002:302.462 JLINK_HasError()
T0818 002:303.998 JLINK_IsHalted()
T0818 002:306.286   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:306.753 - 2.754ms returns TRUE
T0818 002:306.759 JLINK_ReadReg(R15 (PC))
T0818 002:306.764 - 0.004ms returns 0x20000000
T0818 002:306.768 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T0818 002:306.772 - 0.003ms returns 0x00
T0818 002:306.776 JLINK_ReadReg(R0)
T0818 002:306.780 - 0.003ms returns 0x00000000
T0818 002:307.063 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:307.070   Data:  70 47 49 B1 CC F1 20 04 21 FA 04 F4 11 FA 0C F1 ...
T0818 002:307.079   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:309.698 - 2.634ms returns 0x27C
T0818 002:309.704 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:309.708   Data:  00 F0 A8 F8 30 46 00 F0 A5 F8 48 46 4F F4 00 51 ...
T0818 002:309.714   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:311.561 - 1.856ms returns 0x184
T0818 002:311.573 JLINK_HasError()
T0818 002:311.578 JLINK_WriteReg(R0, 0x08000800)
T0818 002:311.582 - 0.004ms returns 0
T0818 002:311.587 JLINK_WriteReg(R1, 0x00000400)
T0818 002:311.590 - 0.003ms returns 0
T0818 002:311.594 JLINK_WriteReg(R2, 0x20000184)
T0818 002:311.598 - 0.003ms returns 0
T0818 002:311.602 JLINK_WriteReg(R3, 0x00000000)
T0818 002:311.605 - 0.003ms returns 0
T0818 002:311.610 JLINK_WriteReg(R4, 0x00000000)
T0818 002:311.613 - 0.003ms returns 0
T0818 002:311.617 JLINK_WriteReg(R5, 0x00000000)
T0818 002:311.621 - 0.003ms returns 0
T0818 002:311.625 JLINK_WriteReg(R6, 0x00000000)
T0818 002:311.628 - 0.003ms returns 0
T0818 002:311.632 JLINK_WriteReg(R7, 0x00000000)
T0818 002:311.636 - 0.003ms returns 0
T0818 002:311.640 JLINK_WriteReg(R8, 0x00000000)
T0818 002:311.643 - 0.003ms returns 0
T0818 002:311.648 JLINK_WriteReg(R9, 0x20000180)
T0818 002:311.651 - 0.003ms returns 0
T0818 002:311.655 JLINK_WriteReg(R10, 0x00000000)
T0818 002:311.658 - 0.003ms returns 0
T0818 002:311.662 JLINK_WriteReg(R11, 0x00000000)
T0818 002:311.666 - 0.003ms returns 0
T0818 002:311.670 JLINK_WriteReg(R12, 0x00000000)
T0818 002:311.673 - 0.003ms returns 0
T0818 002:311.677 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:311.681 - 0.003ms returns 0
T0818 002:311.685 JLINK_WriteReg(R14, 0x20000001)
T0818 002:311.688 - 0.003ms returns 0
T0818 002:311.692 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:311.696 - 0.003ms returns 0
T0818 002:311.700 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:311.703 - 0.003ms returns 0
T0818 002:311.707 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:311.711 - 0.003ms returns 0
T0818 002:311.715 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:311.718 - 0.003ms returns 0
T0818 002:311.722 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:311.726 - 0.003ms returns 0
T0818 002:311.730 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:311.734 - 0.004ms returns 0x0000000E
T0818 002:311.738 JLINK_Go()
T0818 002:311.746   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:314.662 - 2.923ms 
T0818 002:314.672 JLINK_IsHalted()
T0818 002:315.144 - 0.471ms returns FALSE
T0818 002:315.149 JLINK_HasError()
T0818 002:316.996 JLINK_IsHalted()
T0818 002:317.454 - 0.457ms returns FALSE
T0818 002:317.459 JLINK_HasError()
T0818 002:318.995 JLINK_IsHalted()
T0818 002:321.287   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:321.750 - 2.754ms returns TRUE
T0818 002:321.760 JLINK_ReadReg(R15 (PC))
T0818 002:321.764 - 0.004ms returns 0x20000000
T0818 002:321.795 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T0818 002:321.800 - 0.005ms returns 0x00
T0818 002:321.805 JLINK_ReadReg(R0)
T0818 002:321.808 - 0.003ms returns 0x00000000
T0818 002:322.064 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:322.071   Data:  C5 F3 80 12 30 46 4F F4 80 51 00 F0 35 FC 20 46 ...
T0818 002:322.079   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:324.697 - 2.632ms returns 0x27C
T0818 002:324.735 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:324.739   Data:  83 56 4F F4 B4 77 9F ED 2B CA 20 EE 0A 0A BC EE ...
T0818 002:324.746   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:326.584 - 1.849ms returns 0x184
T0818 002:326.590 JLINK_HasError()
T0818 002:326.595 JLINK_WriteReg(R0, 0x08000C00)
T0818 002:326.599 - 0.004ms returns 0
T0818 002:326.603 JLINK_WriteReg(R1, 0x00000400)
T0818 002:326.607 - 0.003ms returns 0
T0818 002:326.611 JLINK_WriteReg(R2, 0x20000184)
T0818 002:326.614 - 0.003ms returns 0
T0818 002:326.618 JLINK_WriteReg(R3, 0x00000000)
T0818 002:326.621 - 0.003ms returns 0
T0818 002:326.626 JLINK_WriteReg(R4, 0x00000000)
T0818 002:326.629 - 0.003ms returns 0
T0818 002:326.633 JLINK_WriteReg(R5, 0x00000000)
T0818 002:326.636 - 0.003ms returns 0
T0818 002:326.640 JLINK_WriteReg(R6, 0x00000000)
T0818 002:326.644 - 0.003ms returns 0
T0818 002:326.648 JLINK_WriteReg(R7, 0x00000000)
T0818 002:326.652 - 0.003ms returns 0
T0818 002:326.656 JLINK_WriteReg(R8, 0x00000000)
T0818 002:326.659 - 0.003ms returns 0
T0818 002:326.663 JLINK_WriteReg(R9, 0x20000180)
T0818 002:326.666 - 0.003ms returns 0
T0818 002:326.670 JLINK_WriteReg(R10, 0x00000000)
T0818 002:326.674 - 0.003ms returns 0
T0818 002:326.678 JLINK_WriteReg(R11, 0x00000000)
T0818 002:326.681 - 0.003ms returns 0
T0818 002:326.685 JLINK_WriteReg(R12, 0x00000000)
T0818 002:326.688 - 0.003ms returns 0
T0818 002:326.692 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:326.696 - 0.003ms returns 0
T0818 002:326.700 JLINK_WriteReg(R14, 0x20000001)
T0818 002:326.703 - 0.003ms returns 0
T0818 002:326.707 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:326.711 - 0.003ms returns 0
T0818 002:326.715 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:326.718 - 0.003ms returns 0
T0818 002:326.722 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:326.726 - 0.003ms returns 0
T0818 002:326.730 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:326.733 - 0.003ms returns 0
T0818 002:326.737 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:326.741 - 0.003ms returns 0
T0818 002:326.745 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:326.749 - 0.004ms returns 0x0000000F
T0818 002:326.753 JLINK_Go()
T0818 002:326.759   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:329.831 - 3.077ms 
T0818 002:329.843 JLINK_IsHalted()
T0818 002:330.754 - 0.911ms returns FALSE
T0818 002:330.766 JLINK_HasError()
T0818 002:332.003 JLINK_IsHalted()
T0818 002:332.469 - 0.466ms returns FALSE
T0818 002:332.475 JLINK_HasError()
T0818 002:333.996 JLINK_IsHalted()
T0818 002:336.269   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:336.749 - 2.753ms returns TRUE
T0818 002:336.755 JLINK_ReadReg(R15 (PC))
T0818 002:336.760 - 0.004ms returns 0x20000000
T0818 002:336.764 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T0818 002:336.768 - 0.004ms returns 0x00
T0818 002:336.772 JLINK_ReadReg(R0)
T0818 002:336.776 - 0.003ms returns 0x00000000
T0818 002:337.076 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:337.083   Data:  FE E7 00 00 B3 F5 80 4F 03 46 18 D1 B0 B5 53 F8 ...
T0818 002:337.092   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:339.677 - 2.601ms returns 0x27C
T0818 002:339.683 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:339.687   Data:  00 F0 B0 60 01 20 84 F8 35 00 00 20 00 21 84 F8 ...
T0818 002:339.694   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:341.567 - 1.883ms returns 0x184
T0818 002:341.579 JLINK_HasError()
T0818 002:341.584 JLINK_WriteReg(R0, 0x08001000)
T0818 002:341.589 - 0.005ms returns 0
T0818 002:341.593 JLINK_WriteReg(R1, 0x00000400)
T0818 002:341.596 - 0.003ms returns 0
T0818 002:341.600 JLINK_WriteReg(R2, 0x20000184)
T0818 002:341.604 - 0.003ms returns 0
T0818 002:341.608 JLINK_WriteReg(R3, 0x00000000)
T0818 002:341.611 - 0.003ms returns 0
T0818 002:341.615 JLINK_WriteReg(R4, 0x00000000)
T0818 002:341.619 - 0.003ms returns 0
T0818 002:341.622 JLINK_WriteReg(R5, 0x00000000)
T0818 002:341.626 - 0.003ms returns 0
T0818 002:341.630 JLINK_WriteReg(R6, 0x00000000)
T0818 002:341.634 - 0.003ms returns 0
T0818 002:341.664 JLINK_WriteReg(R7, 0x00000000)
T0818 002:341.667 - 0.003ms returns 0
T0818 002:341.671 JLINK_WriteReg(R8, 0x00000000)
T0818 002:341.675 - 0.003ms returns 0
T0818 002:341.679 JLINK_WriteReg(R9, 0x20000180)
T0818 002:341.682 - 0.003ms returns 0
T0818 002:341.686 JLINK_WriteReg(R10, 0x00000000)
T0818 002:341.690 - 0.003ms returns 0
T0818 002:341.694 JLINK_WriteReg(R11, 0x00000000)
T0818 002:341.697 - 0.003ms returns 0
T0818 002:341.701 JLINK_WriteReg(R12, 0x00000000)
T0818 002:341.705 - 0.003ms returns 0
T0818 002:341.709 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:341.712 - 0.003ms returns 0
T0818 002:341.716 JLINK_WriteReg(R14, 0x20000001)
T0818 002:341.720 - 0.003ms returns 0
T0818 002:341.724 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:341.727 - 0.003ms returns 0
T0818 002:341.731 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:341.735 - 0.003ms returns 0
T0818 002:341.739 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:341.742 - 0.003ms returns 0
T0818 002:341.746 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:341.750 - 0.003ms returns 0
T0818 002:341.754 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:341.757 - 0.003ms returns 0
T0818 002:341.762 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:341.766 - 0.004ms returns 0x00000010
T0818 002:341.770 JLINK_Go()
T0818 002:341.777   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:345.206 - 3.435ms 
T0818 002:345.218 JLINK_IsHalted()
T0818 002:345.701 - 0.483ms returns FALSE
T0818 002:345.707 JLINK_HasError()
T0818 002:346.997 JLINK_IsHalted()
T0818 002:347.472 - 0.474ms returns FALSE
T0818 002:347.480 JLINK_HasError()
T0818 002:348.996 JLINK_IsHalted()
T0818 002:349.500 - 0.504ms returns FALSE
T0818 002:349.506 JLINK_HasError()
T0818 002:351.000 JLINK_IsHalted()
T0818 002:353.314   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:353.799 - 2.799ms returns TRUE
T0818 002:353.805 JLINK_ReadReg(R15 (PC))
T0818 002:353.810 - 0.004ms returns 0x20000000
T0818 002:353.814 JLINK_ClrBPEx(BPHandle = 0x00000010)
T0818 002:353.818 - 0.003ms returns 0x00
T0818 002:353.822 JLINK_ReadReg(R0)
T0818 002:353.826 - 0.003ms returns 0x00000000
T0818 002:354.134 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:354.142   Data:  52 F8 0C 30 07 F0 0C 06 0F 25 B5 40 AB 43 01 9D ...
T0818 002:354.151   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:356.696 - 2.562ms returns 0x27C
T0818 002:356.704 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:356.707   Data:  00 20 02 B0 70 BD 00 00 70 47 00 00 00 28 04 BF ...
T0818 002:356.714   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:358.584 - 1.880ms returns 0x184
T0818 002:358.596 JLINK_HasError()
T0818 002:358.601 JLINK_WriteReg(R0, 0x08001400)
T0818 002:358.606 - 0.005ms returns 0
T0818 002:358.610 JLINK_WriteReg(R1, 0x00000400)
T0818 002:358.614 - 0.003ms returns 0
T0818 002:358.618 JLINK_WriteReg(R2, 0x20000184)
T0818 002:358.621 - 0.003ms returns 0
T0818 002:358.625 JLINK_WriteReg(R3, 0x00000000)
T0818 002:358.628 - 0.003ms returns 0
T0818 002:358.632 JLINK_WriteReg(R4, 0x00000000)
T0818 002:358.636 - 0.003ms returns 0
T0818 002:358.640 JLINK_WriteReg(R5, 0x00000000)
T0818 002:358.643 - 0.003ms returns 0
T0818 002:358.648 JLINK_WriteReg(R6, 0x00000000)
T0818 002:358.651 - 0.003ms returns 0
T0818 002:358.655 JLINK_WriteReg(R7, 0x00000000)
T0818 002:358.663 - 0.007ms returns 0
T0818 002:358.667 JLINK_WriteReg(R8, 0x00000000)
T0818 002:358.670 - 0.003ms returns 0
T0818 002:358.674 JLINK_WriteReg(R9, 0x20000180)
T0818 002:358.678 - 0.003ms returns 0
T0818 002:358.682 JLINK_WriteReg(R10, 0x00000000)
T0818 002:358.685 - 0.003ms returns 0
T0818 002:358.689 JLINK_WriteReg(R11, 0x00000000)
T0818 002:358.693 - 0.003ms returns 0
T0818 002:358.697 JLINK_WriteReg(R12, 0x00000000)
T0818 002:358.700 - 0.003ms returns 0
T0818 002:358.704 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:358.708 - 0.003ms returns 0
T0818 002:358.712 JLINK_WriteReg(R14, 0x20000001)
T0818 002:358.716 - 0.003ms returns 0
T0818 002:358.720 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:358.746 - 0.026ms returns 0
T0818 002:358.752 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:358.756 - 0.003ms returns 0
T0818 002:358.760 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:358.763 - 0.003ms returns 0
T0818 002:358.767 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:358.770 - 0.003ms returns 0
T0818 002:358.774 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:358.778 - 0.003ms returns 0
T0818 002:358.782 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:358.786 - 0.004ms returns 0x00000011
T0818 002:358.790 JLINK_Go()
T0818 002:358.798   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:361.790 - 2.998ms 
T0818 002:361.804 JLINK_IsHalted()
T0818 002:362.283 - 0.478ms returns FALSE
T0818 002:362.289 JLINK_HasError()
T0818 002:363.999 JLINK_IsHalted()
T0818 002:364.424 - 0.424ms returns FALSE
T0818 002:364.430 JLINK_HasError()
T0818 002:366.006 JLINK_IsHalted()
T0818 002:368.506   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:368.955 - 2.949ms returns TRUE
T0818 002:368.965 JLINK_ReadReg(R15 (PC))
T0818 002:368.971 - 0.006ms returns 0x20000000
T0818 002:368.976 JLINK_ClrBPEx(BPHandle = 0x00000011)
T0818 002:368.980 - 0.003ms returns 0x00
T0818 002:368.984 JLINK_ReadReg(R0)
T0818 002:368.988 - 0.003ms returns 0x00000000
T0818 002:369.320 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:369.327   Data:  C4 F2 02 01 00 68 09 68 43 F6 C8 22 C1 F3 82 21 ...
T0818 002:369.337   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:371.935 - 2.614ms returns 0x27C
T0818 002:371.948 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:371.952   Data:  38 68 C0 05 26 D4 38 68 40 F4 80 70 38 60 FF F7 ...
T0818 002:371.961   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:373.789 - 1.840ms returns 0x184
T0818 002:373.802 JLINK_HasError()
T0818 002:373.809 JLINK_WriteReg(R0, 0x08001800)
T0818 002:373.813 - 0.004ms returns 0
T0818 002:373.819 JLINK_WriteReg(R1, 0x00000400)
T0818 002:373.822 - 0.003ms returns 0
T0818 002:373.828 JLINK_WriteReg(R2, 0x20000184)
T0818 002:373.832 - 0.003ms returns 0
T0818 002:373.837 JLINK_WriteReg(R3, 0x00000000)
T0818 002:373.841 - 0.003ms returns 0
T0818 002:373.846 JLINK_WriteReg(R4, 0x00000000)
T0818 002:373.850 - 0.003ms returns 0
T0818 002:373.856 JLINK_WriteReg(R5, 0x00000000)
T0818 002:373.860 - 0.003ms returns 0
T0818 002:373.865 JLINK_WriteReg(R6, 0x00000000)
T0818 002:373.869 - 0.003ms returns 0
T0818 002:373.874 JLINK_WriteReg(R7, 0x00000000)
T0818 002:373.878 - 0.003ms returns 0
T0818 002:373.884 JLINK_WriteReg(R8, 0x00000000)
T0818 002:373.890 - 0.006ms returns 0
T0818 002:373.896 JLINK_WriteReg(R9, 0x20000180)
T0818 002:373.900 - 0.003ms returns 0
T0818 002:373.905 JLINK_WriteReg(R10, 0x00000000)
T0818 002:373.909 - 0.003ms returns 0
T0818 002:373.914 JLINK_WriteReg(R11, 0x00000000)
T0818 002:373.918 - 0.003ms returns 0
T0818 002:373.924 JLINK_WriteReg(R12, 0x00000000)
T0818 002:373.927 - 0.003ms returns 0
T0818 002:373.933 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:373.937 - 0.004ms returns 0
T0818 002:373.942 JLINK_WriteReg(R14, 0x20000001)
T0818 002:373.946 - 0.003ms returns 0
T0818 002:373.952 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:373.955 - 0.003ms returns 0
T0818 002:373.961 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:373.964 - 0.003ms returns 0
T0818 002:373.970 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:373.974 - 0.003ms returns 0
T0818 002:373.979 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:373.983 - 0.003ms returns 0
T0818 002:373.988 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:373.992 - 0.003ms returns 0
T0818 002:373.998 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:374.003 - 0.004ms returns 0x00000012
T0818 002:374.007 JLINK_Go()
T0818 002:374.015   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:376.749 - 2.742ms 
T0818 002:376.761 JLINK_IsHalted()
T0818 002:377.228 - 0.467ms returns FALSE
T0818 002:377.236 JLINK_HasError()
T0818 002:378.996 JLINK_IsHalted()
T0818 002:379.468 - 0.472ms returns FALSE
T0818 002:379.474 JLINK_HasError()
T0818 002:380.997 JLINK_IsHalted()
T0818 002:383.431   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:383.875 - 2.878ms returns TRUE
T0818 002:383.881 JLINK_ReadReg(R15 (PC))
T0818 002:383.886 - 0.004ms returns 0x20000000
T0818 002:383.890 JLINK_ClrBPEx(BPHandle = 0x00000012)
T0818 002:383.894 - 0.004ms returns 0x00
T0818 002:383.898 JLINK_ReadReg(R0)
T0818 002:383.902 - 0.003ms returns 0x00000000
T0818 002:384.214 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:384.220   Data:  05 60 08 43 70 60 01 20 38 66 FF F7 3B FC 04 46 ...
T0818 002:384.228   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:386.830 - 2.616ms returns 0x27C
T0818 002:386.837 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:386.840   Data:  21 F4 80 71 42 E8 03 13 00 2B F5 D1 01 68 51 E8 ...
T0818 002:386.847   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:388.765 - 1.928ms returns 0x184
T0818 002:388.771 JLINK_HasError()
T0818 002:388.776 JLINK_WriteReg(R0, 0x08001C00)
T0818 002:388.780 - 0.004ms returns 0
T0818 002:388.784 JLINK_WriteReg(R1, 0x00000400)
T0818 002:388.788 - 0.003ms returns 0
T0818 002:388.792 JLINK_WriteReg(R2, 0x20000184)
T0818 002:388.795 - 0.003ms returns 0
T0818 002:388.799 JLINK_WriteReg(R3, 0x00000000)
T0818 002:388.803 - 0.003ms returns 0
T0818 002:388.807 JLINK_WriteReg(R4, 0x00000000)
T0818 002:388.810 - 0.003ms returns 0
T0818 002:388.814 JLINK_WriteReg(R5, 0x00000000)
T0818 002:388.817 - 0.003ms returns 0
T0818 002:388.821 JLINK_WriteReg(R6, 0x00000000)
T0818 002:388.825 - 0.003ms returns 0
T0818 002:388.829 JLINK_WriteReg(R7, 0x00000000)
T0818 002:388.832 - 0.003ms returns 0
T0818 002:388.836 JLINK_WriteReg(R8, 0x00000000)
T0818 002:388.840 - 0.003ms returns 0
T0818 002:388.844 JLINK_WriteReg(R9, 0x20000180)
T0818 002:388.847 - 0.003ms returns 0
T0818 002:388.851 JLINK_WriteReg(R10, 0x00000000)
T0818 002:388.854 - 0.003ms returns 0
T0818 002:388.861 JLINK_WriteReg(R11, 0x00000000)
T0818 002:388.865 - 0.003ms returns 0
T0818 002:388.869 JLINK_WriteReg(R12, 0x00000000)
T0818 002:388.872 - 0.003ms returns 0
T0818 002:388.876 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:388.880 - 0.003ms returns 0
T0818 002:388.884 JLINK_WriteReg(R14, 0x20000001)
T0818 002:388.887 - 0.003ms returns 0
T0818 002:388.891 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:388.895 - 0.003ms returns 0
T0818 002:388.899 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:388.902 - 0.003ms returns 0
T0818 002:388.906 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:388.910 - 0.003ms returns 0
T0818 002:388.914 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:388.917 - 0.003ms returns 0
T0818 002:388.921 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:388.924 - 0.003ms returns 0
T0818 002:388.928 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:388.932 - 0.004ms returns 0x00000013
T0818 002:388.936 JLINK_Go()
T0818 002:388.943   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:391.667 - 2.730ms 
T0818 002:391.679 JLINK_IsHalted()
T0818 002:392.144 - 0.465ms returns FALSE
T0818 002:392.150 JLINK_HasError()
T0818 002:393.998 JLINK_IsHalted()
T0818 002:394.473 - 0.475ms returns FALSE
T0818 002:394.479 JLINK_HasError()
T0818 002:395.995 JLINK_IsHalted()
T0818 002:398.315   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:398.800 - 2.804ms returns TRUE
T0818 002:398.806 JLINK_ReadReg(R15 (PC))
T0818 002:398.810 - 0.004ms returns 0x20000000
T0818 002:398.815 JLINK_ClrBPEx(BPHandle = 0x00000013)
T0818 002:398.818 - 0.004ms returns 0x00
T0818 002:398.823 JLINK_ReadReg(R0)
T0818 002:398.826 - 0.003ms returns 0x00000000
T0818 002:399.124 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:399.131   Data:  43 E8 03 25 00 2D F5 D1 02 68 52 E8 05 2F 03 68 ...
T0818 002:399.140   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:401.724 - 2.600ms returns 0x27C
T0818 002:401.737 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:401.740   Data:  00 21 0C F8 0E 10 00 E0 00 21 C1 80 04 E0 00 BF ...
T0818 002:401.749   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:403.618 - 1.881ms returns 0x184
T0818 002:403.629 JLINK_HasError()
T0818 002:403.663 JLINK_WriteReg(R0, 0x08002000)
T0818 002:403.668 - 0.004ms returns 0
T0818 002:404.194 JLINK_WriteReg(R1, 0x00000400)
T0818 002:404.199 - 0.005ms returns 0
T0818 002:404.204 JLINK_WriteReg(R2, 0x20000184)
T0818 002:404.207 - 0.003ms returns 0
T0818 002:404.211 JLINK_WriteReg(R3, 0x00000000)
T0818 002:404.214 - 0.003ms returns 0
T0818 002:404.218 JLINK_WriteReg(R4, 0x00000000)
T0818 002:404.222 - 0.003ms returns 0
T0818 002:404.226 JLINK_WriteReg(R5, 0x00000000)
T0818 002:404.229 - 0.003ms returns 0
T0818 002:404.233 JLINK_WriteReg(R6, 0x00000000)
T0818 002:404.236 - 0.003ms returns 0
T0818 002:404.240 JLINK_WriteReg(R7, 0x00000000)
T0818 002:404.244 - 0.003ms returns 0
T0818 002:404.248 JLINK_WriteReg(R8, 0x00000000)
T0818 002:404.251 - 0.003ms returns 0
T0818 002:404.255 JLINK_WriteReg(R9, 0x20000180)
T0818 002:404.258 - 0.003ms returns 0
T0818 002:404.264 JLINK_WriteReg(R10, 0x00000000)
T0818 002:404.268 - 0.004ms returns 0
T0818 002:404.272 JLINK_WriteReg(R11, 0x00000000)
T0818 002:404.276 - 0.003ms returns 0
T0818 002:404.280 JLINK_WriteReg(R12, 0x00000000)
T0818 002:404.283 - 0.003ms returns 0
T0818 002:404.287 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:404.291 - 0.003ms returns 0
T0818 002:404.295 JLINK_WriteReg(R14, 0x20000001)
T0818 002:404.298 - 0.003ms returns 0
T0818 002:404.302 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:404.306 - 0.003ms returns 0
T0818 002:404.310 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:404.313 - 0.003ms returns 0
T0818 002:404.317 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:404.320 - 0.003ms returns 0
T0818 002:404.324 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:404.328 - 0.003ms returns 0
T0818 002:404.332 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:404.335 - 0.003ms returns 0
T0818 002:404.339 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:404.344 - 0.004ms returns 0x00000014
T0818 002:404.348 JLINK_Go()
T0818 002:404.355   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:407.081 - 2.733ms 
T0818 002:407.089 JLINK_IsHalted()
T0818 002:407.564 - 0.474ms returns FALSE
T0818 002:407.569 JLINK_HasError()
T0818 002:408.996 JLINK_IsHalted()
T0818 002:409.467 - 0.471ms returns FALSE
T0818 002:409.472 JLINK_HasError()
T0818 002:410.996 JLINK_IsHalted()
T0818 002:413.272   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:413.725 - 2.728ms returns TRUE
T0818 002:413.731 JLINK_ReadReg(R15 (PC))
T0818 002:413.735 - 0.004ms returns 0x20000000
T0818 002:413.739 JLINK_ClrBPEx(BPHandle = 0x00000014)
T0818 002:413.743 - 0.003ms returns 0x00
T0818 002:413.747 JLINK_ReadReg(R0)
T0818 002:413.751 - 0.003ms returns 0x00000000
T0818 002:414.053 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:414.060   Data:  09 F1 01 05 0A E0 00 BF 97 B1 17 F8 01 0B 21 68 ...
T0818 002:414.068   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:416.676 - 2.623ms returns 0x27C
T0818 002:416.682 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:416.686   Data:  CD F8 04 90 01 68 41 F0 08 01 01 60 01 68 01 F0 ...
T0818 002:416.693   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:418.561 - 1.878ms returns 0x184
T0818 002:418.574 JLINK_HasError()
T0818 002:418.580 JLINK_WriteReg(R0, 0x08002400)
T0818 002:418.585 - 0.005ms returns 0
T0818 002:418.590 JLINK_WriteReg(R1, 0x00000400)
T0818 002:418.593 - 0.003ms returns 0
T0818 002:418.597 JLINK_WriteReg(R2, 0x20000184)
T0818 002:418.600 - 0.003ms returns 0
T0818 002:418.604 JLINK_WriteReg(R3, 0x00000000)
T0818 002:418.608 - 0.003ms returns 0
T0818 002:418.612 JLINK_WriteReg(R4, 0x00000000)
T0818 002:418.615 - 0.003ms returns 0
T0818 002:418.619 JLINK_WriteReg(R5, 0x00000000)
T0818 002:418.623 - 0.003ms returns 0
T0818 002:418.627 JLINK_WriteReg(R6, 0x00000000)
T0818 002:418.630 - 0.003ms returns 0
T0818 002:418.634 JLINK_WriteReg(R7, 0x00000000)
T0818 002:418.637 - 0.003ms returns 0
T0818 002:418.641 JLINK_WriteReg(R8, 0x00000000)
T0818 002:418.645 - 0.003ms returns 0
T0818 002:418.649 JLINK_WriteReg(R9, 0x20000180)
T0818 002:418.652 - 0.003ms returns 0
T0818 002:418.678 JLINK_WriteReg(R10, 0x00000000)
T0818 002:418.682 - 0.003ms returns 0
T0818 002:418.686 JLINK_WriteReg(R11, 0x00000000)
T0818 002:418.690 - 0.003ms returns 0
T0818 002:418.694 JLINK_WriteReg(R12, 0x00000000)
T0818 002:418.697 - 0.003ms returns 0
T0818 002:418.701 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:418.705 - 0.003ms returns 0
T0818 002:418.709 JLINK_WriteReg(R14, 0x20000001)
T0818 002:418.712 - 0.003ms returns 0
T0818 002:418.716 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:418.720 - 0.003ms returns 0
T0818 002:418.724 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:418.727 - 0.003ms returns 0
T0818 002:418.731 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:418.735 - 0.003ms returns 0
T0818 002:418.739 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:418.742 - 0.003ms returns 0
T0818 002:418.746 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:418.749 - 0.003ms returns 0
T0818 002:418.754 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:418.758 - 0.004ms returns 0x00000015
T0818 002:418.762 JLINK_Go()
T0818 002:418.770   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:421.669 - 2.906ms 
T0818 002:421.682 JLINK_IsHalted()
T0818 002:422.157 - 0.474ms returns FALSE
T0818 002:422.166 JLINK_HasError()
T0818 002:423.998 JLINK_IsHalted()
T0818 002:424.512 - 0.514ms returns FALSE
T0818 002:424.519 JLINK_HasError()
T0818 002:425.996 JLINK_IsHalted()
T0818 002:428.286   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:428.800 - 2.803ms returns TRUE
T0818 002:428.806 JLINK_ReadReg(R15 (PC))
T0818 002:428.811 - 0.004ms returns 0x20000000
T0818 002:428.815 JLINK_ClrBPEx(BPHandle = 0x00000015)
T0818 002:428.819 - 0.004ms returns 0x00
T0818 002:428.823 JLINK_ReadReg(R0)
T0818 002:428.827 - 0.003ms returns 0x00000000
T0818 002:429.109 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:429.117   Data:  4F F0 0C 0C 4F F4 E1 32 80 E8 0E 00 C0 E9 03 33 ...
T0818 002:429.126   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:431.710 - 2.601ms returns 0x27C
T0818 002:431.726 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:431.730   Data:  10 80 72 B6 FE E7 00 BF FE F7 B0 FE 12 B0 10 BD ...
T0818 002:431.739   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:433.559 - 1.832ms returns 0x184
T0818 002:433.572 JLINK_HasError()
T0818 002:433.578 JLINK_WriteReg(R0, 0x08002800)
T0818 002:433.583 - 0.004ms returns 0
T0818 002:433.589 JLINK_WriteReg(R1, 0x00000400)
T0818 002:433.593 - 0.004ms returns 0
T0818 002:433.599 JLINK_WriteReg(R2, 0x20000184)
T0818 002:433.602 - 0.003ms returns 0
T0818 002:433.608 JLINK_WriteReg(R3, 0x00000000)
T0818 002:433.611 - 0.003ms returns 0
T0818 002:433.617 JLINK_WriteReg(R4, 0x00000000)
T0818 002:433.620 - 0.003ms returns 0
T0818 002:433.626 JLINK_WriteReg(R5, 0x00000000)
T0818 002:433.630 - 0.003ms returns 0
T0818 002:433.635 JLINK_WriteReg(R6, 0x00000000)
T0818 002:433.639 - 0.003ms returns 0
T0818 002:433.644 JLINK_WriteReg(R7, 0x00000000)
T0818 002:433.648 - 0.003ms returns 0
T0818 002:433.653 JLINK_WriteReg(R8, 0x00000000)
T0818 002:433.657 - 0.003ms returns 0
T0818 002:433.662 JLINK_WriteReg(R9, 0x20000180)
T0818 002:433.666 - 0.003ms returns 0
T0818 002:433.672 JLINK_WriteReg(R10, 0x00000000)
T0818 002:433.675 - 0.003ms returns 0
T0818 002:433.681 JLINK_WriteReg(R11, 0x00000000)
T0818 002:433.684 - 0.003ms returns 0
T0818 002:433.690 JLINK_WriteReg(R12, 0x00000000)
T0818 002:433.693 - 0.003ms returns 0
T0818 002:433.699 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:433.703 - 0.004ms returns 0
T0818 002:433.709 JLINK_WriteReg(R14, 0x20000001)
T0818 002:433.712 - 0.003ms returns 0
T0818 002:433.718 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:433.722 - 0.003ms returns 0
T0818 002:433.727 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:433.731 - 0.003ms returns 0
T0818 002:433.736 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:433.740 - 0.003ms returns 0
T0818 002:433.746 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:433.749 - 0.003ms returns 0
T0818 002:433.755 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:433.788 - 0.032ms returns 0
T0818 002:433.796 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:433.800 - 0.004ms returns 0x00000016
T0818 002:433.805 JLINK_Go()
T0818 002:433.813   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:436.470 - 2.664ms 
T0818 002:436.492 JLINK_IsHalted()
T0818 002:437.005 - 0.512ms returns FALSE
T0818 002:437.013 JLINK_HasError()
T0818 002:438.998 JLINK_IsHalted()
T0818 002:439.467 - 0.469ms returns FALSE
T0818 002:439.474 JLINK_HasError()
T0818 002:441.000 JLINK_IsHalted()
T0818 002:443.432   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:443.918 - 2.917ms returns TRUE
T0818 002:443.930 JLINK_ReadReg(R15 (PC))
T0818 002:443.936 - 0.005ms returns 0x20000000
T0818 002:443.940 JLINK_ClrBPEx(BPHandle = 0x00000016)
T0818 002:443.944 - 0.004ms returns 0x00
T0818 002:443.948 JLINK_ReadReg(R0)
T0818 002:443.952 - 0.003ms returns 0x00000000
T0818 002:444.242 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:444.249   Data:  11 10 00 EB 02 10 C1 F3 42 11 18 E0 9A 00 9B 0F ...
T0818 002:444.258   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:446.833 - 2.590ms returns 0x27C
T0818 002:446.839 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:446.843   Data:  01 20 4F F4 CA 71 00 22 00 23 FE F7 63 F8 FD F7 ...
T0818 002:446.850   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:448.764 - 1.924ms returns 0x184
T0818 002:448.770 JLINK_HasError()
T0818 002:448.774 JLINK_WriteReg(R0, 0x08002C00)
T0818 002:448.779 - 0.004ms returns 0
T0818 002:448.783 JLINK_WriteReg(R1, 0x00000400)
T0818 002:448.786 - 0.003ms returns 0
T0818 002:448.790 JLINK_WriteReg(R2, 0x20000184)
T0818 002:448.794 - 0.003ms returns 0
T0818 002:448.798 JLINK_WriteReg(R3, 0x00000000)
T0818 002:448.801 - 0.003ms returns 0
T0818 002:448.806 JLINK_WriteReg(R4, 0x00000000)
T0818 002:448.809 - 0.003ms returns 0
T0818 002:448.813 JLINK_WriteReg(R5, 0x00000000)
T0818 002:448.816 - 0.003ms returns 0
T0818 002:448.820 JLINK_WriteReg(R6, 0x00000000)
T0818 002:448.823 - 0.003ms returns 0
T0818 002:448.827 JLINK_WriteReg(R7, 0x00000000)
T0818 002:448.831 - 0.003ms returns 0
T0818 002:448.835 JLINK_WriteReg(R8, 0x00000000)
T0818 002:448.838 - 0.003ms returns 0
T0818 002:448.842 JLINK_WriteReg(R9, 0x20000180)
T0818 002:448.845 - 0.003ms returns 0
T0818 002:448.850 JLINK_WriteReg(R10, 0x00000000)
T0818 002:448.853 - 0.003ms returns 0
T0818 002:448.857 JLINK_WriteReg(R11, 0x00000000)
T0818 002:448.860 - 0.003ms returns 0
T0818 002:448.864 JLINK_WriteReg(R12, 0x00000000)
T0818 002:448.868 - 0.003ms returns 0
T0818 002:448.872 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:448.875 - 0.003ms returns 0
T0818 002:448.879 JLINK_WriteReg(R14, 0x20000001)
T0818 002:448.882 - 0.003ms returns 0
T0818 002:448.887 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:448.890 - 0.003ms returns 0
T0818 002:448.894 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:448.898 - 0.003ms returns 0
T0818 002:448.902 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:448.905 - 0.003ms returns 0
T0818 002:448.909 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:448.912 - 0.003ms returns 0
T0818 002:448.916 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:448.920 - 0.003ms returns 0
T0818 002:448.924 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:448.928 - 0.004ms returns 0x00000017
T0818 002:448.932 JLINK_Go()
T0818 002:448.939   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:451.668 - 2.735ms 
T0818 002:451.682 JLINK_IsHalted()
T0818 002:452.144 - 0.462ms returns FALSE
T0818 002:452.150 JLINK_HasError()
T0818 002:453.996 JLINK_IsHalted()
T0818 002:454.464 - 0.468ms returns FALSE
T0818 002:454.470 JLINK_HasError()
T0818 002:455.996 JLINK_IsHalted()
T0818 002:458.363   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:458.853 - 2.856ms returns TRUE
T0818 002:458.859 JLINK_ReadReg(R15 (PC))
T0818 002:458.864 - 0.004ms returns 0x20000000
T0818 002:458.868 JLINK_ClrBPEx(BPHandle = 0x00000017)
T0818 002:458.872 - 0.003ms returns 0x00
T0818 002:458.876 JLINK_ReadReg(R0)
T0818 002:458.879 - 0.003ms returns 0x00000000
T0818 002:459.188 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:459.196   Data:  00 01 20 46 01 22 FF F7 E7 F8 FD F7 79 FE FD F7 ...
T0818 002:459.205   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:461.816 - 2.628ms returns 0x27C
T0818 002:461.828 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:461.832   Data:  FC F7 F5 FF 80 46 89 46 32 46 3B 46 10 46 19 46 ...
T0818 002:461.841   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:463.696 - 1.867ms returns 0x184
T0818 002:463.706 JLINK_HasError()
T0818 002:463.735 JLINK_WriteReg(R0, 0x08003000)
T0818 002:463.741 - 0.005ms returns 0
T0818 002:463.745 JLINK_WriteReg(R1, 0x00000400)
T0818 002:463.749 - 0.003ms returns 0
T0818 002:463.753 JLINK_WriteReg(R2, 0x20000184)
T0818 002:463.756 - 0.003ms returns 0
T0818 002:463.760 JLINK_WriteReg(R3, 0x00000000)
T0818 002:463.764 - 0.003ms returns 0
T0818 002:463.768 JLINK_WriteReg(R4, 0x00000000)
T0818 002:463.771 - 0.003ms returns 0
T0818 002:463.775 JLINK_WriteReg(R5, 0x00000000)
T0818 002:463.779 - 0.003ms returns 0
T0818 002:463.783 JLINK_WriteReg(R6, 0x00000000)
T0818 002:463.786 - 0.003ms returns 0
T0818 002:463.790 JLINK_WriteReg(R7, 0x00000000)
T0818 002:463.794 - 0.003ms returns 0
T0818 002:463.798 JLINK_WriteReg(R8, 0x00000000)
T0818 002:463.801 - 0.003ms returns 0
T0818 002:463.805 JLINK_WriteReg(R9, 0x20000180)
T0818 002:463.808 - 0.003ms returns 0
T0818 002:463.812 JLINK_WriteReg(R10, 0x00000000)
T0818 002:463.816 - 0.003ms returns 0
T0818 002:463.820 JLINK_WriteReg(R11, 0x00000000)
T0818 002:463.823 - 0.003ms returns 0
T0818 002:463.827 JLINK_WriteReg(R12, 0x00000000)
T0818 002:463.831 - 0.003ms returns 0
T0818 002:463.835 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:463.838 - 0.003ms returns 0
T0818 002:463.842 JLINK_WriteReg(R14, 0x20000001)
T0818 002:463.846 - 0.003ms returns 0
T0818 002:463.850 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:463.853 - 0.003ms returns 0
T0818 002:463.858 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:463.861 - 0.003ms returns 0
T0818 002:463.865 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:463.875 - 0.009ms returns 0
T0818 002:463.879 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:463.882 - 0.003ms returns 0
T0818 002:463.886 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:463.890 - 0.003ms returns 0
T0818 002:463.894 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:463.898 - 0.004ms returns 0x00000018
T0818 002:463.902 JLINK_Go()
T0818 002:463.909   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:466.661 - 2.759ms 
T0818 002:466.667 JLINK_IsHalted()
T0818 002:467.145 - 0.477ms returns FALSE
T0818 002:467.159 JLINK_HasError()
T0818 002:468.999 JLINK_IsHalted()
T0818 002:469.425 - 0.426ms returns FALSE
T0818 002:469.431 JLINK_HasError()
T0818 002:470.997 JLINK_IsHalted()
T0818 002:473.317   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:473.800 - 2.802ms returns TRUE
T0818 002:473.806 JLINK_ReadReg(R15 (PC))
T0818 002:473.810 - 0.004ms returns 0x20000000
T0818 002:473.815 JLINK_ClrBPEx(BPHandle = 0x00000018)
T0818 002:473.818 - 0.003ms returns 0x00
T0818 002:473.823 JLINK_ReadReg(R0)
T0818 002:473.826 - 0.003ms returns 0x00000000
T0818 002:474.084 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:474.090   Data:  30 03 09 2B 09 D8 07 EB 87 03 02 EB 43 03 C7 18 ...
T0818 002:474.098   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:476.721 - 2.637ms returns 0x27C
T0818 002:476.727 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:476.731   Data:  00 9B 83 F0 08 03 53 EA 0A 03 0A D1 5C EA 01 02 ...
T0818 002:476.737   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:478.605 - 1.877ms returns 0x184
T0818 002:478.618 JLINK_HasError()
T0818 002:478.623 JLINK_WriteReg(R0, 0x08003400)
T0818 002:478.628 - 0.005ms returns 0
T0818 002:478.632 JLINK_WriteReg(R1, 0x00000400)
T0818 002:478.636 - 0.003ms returns 0
T0818 002:478.640 JLINK_WriteReg(R2, 0x20000184)
T0818 002:478.643 - 0.003ms returns 0
T0818 002:478.647 JLINK_WriteReg(R3, 0x00000000)
T0818 002:478.651 - 0.003ms returns 0
T0818 002:478.657 JLINK_WriteReg(R4, 0x00000000)
T0818 002:478.662 - 0.004ms returns 0
T0818 002:478.666 JLINK_WriteReg(R5, 0x00000000)
T0818 002:478.670 - 0.003ms returns 0
T0818 002:478.674 JLINK_WriteReg(R6, 0x00000000)
T0818 002:478.677 - 0.003ms returns 0
T0818 002:478.681 JLINK_WriteReg(R7, 0x00000000)
T0818 002:478.684 - 0.003ms returns 0
T0818 002:478.688 JLINK_WriteReg(R8, 0x00000000)
T0818 002:478.692 - 0.003ms returns 0
T0818 002:478.696 JLINK_WriteReg(R9, 0x20000180)
T0818 002:478.699 - 0.003ms returns 0
T0818 002:478.703 JLINK_WriteReg(R10, 0x00000000)
T0818 002:478.707 - 0.003ms returns 0
T0818 002:478.711 JLINK_WriteReg(R11, 0x00000000)
T0818 002:478.714 - 0.003ms returns 0
T0818 002:478.718 JLINK_WriteReg(R12, 0x00000000)
T0818 002:478.721 - 0.003ms returns 0
T0818 002:478.725 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:478.729 - 0.003ms returns 0
T0818 002:478.733 JLINK_WriteReg(R14, 0x20000001)
T0818 002:478.736 - 0.003ms returns 0
T0818 002:478.740 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:478.744 - 0.003ms returns 0
T0818 002:478.748 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:478.752 - 0.003ms returns 0
T0818 002:478.756 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:478.759 - 0.003ms returns 0
T0818 002:478.763 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:478.766 - 0.003ms returns 0
T0818 002:478.770 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:478.774 - 0.003ms returns 0
T0818 002:478.778 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:478.782 - 0.004ms returns 0x00000019
T0818 002:478.786 JLINK_Go()
T0818 002:478.794   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:481.756 - 2.969ms 
T0818 002:481.768 JLINK_IsHalted()
T0818 002:482.240 - 0.472ms returns FALSE
T0818 002:482.246 JLINK_HasError()
T0818 002:484.001 JLINK_IsHalted()
T0818 002:484.460 - 0.458ms returns FALSE
T0818 002:484.467 JLINK_HasError()
T0818 002:485.997 JLINK_IsHalted()
T0818 002:488.318   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:488.831 - 2.833ms returns TRUE
T0818 002:488.840 JLINK_ReadReg(R15 (PC))
T0818 002:488.845 - 0.004ms returns 0x20000000
T0818 002:488.849 JLINK_ClrBPEx(BPHandle = 0x00000019)
T0818 002:488.853 - 0.003ms returns 0x00
T0818 002:488.857 JLINK_ReadReg(R0)
T0818 002:488.861 - 0.003ms returns 0x00000000
T0818 002:489.125 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:489.132   Data:  65 28 0C D0 06 DC 45 28 09 D0 46 28 1D D0 47 28 ...
T0818 002:489.141   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:491.833 - 2.707ms returns 0x27C
T0818 002:491.953 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:491.957   Data:  1E 46 90 46 C8 03 01 D5 30 27 00 E0 20 27 88 04 ...
T0818 002:491.966   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:493.811 - 1.858ms returns 0x184
T0818 002:493.822 JLINK_HasError()
T0818 002:493.828 JLINK_WriteReg(R0, 0x08003800)
T0818 002:493.833 - 0.004ms returns 0
T0818 002:493.839 JLINK_WriteReg(R1, 0x00000400)
T0818 002:493.842 - 0.003ms returns 0
T0818 002:493.848 JLINK_WriteReg(R2, 0x20000184)
T0818 002:493.852 - 0.003ms returns 0
T0818 002:493.857 JLINK_WriteReg(R3, 0x00000000)
T0818 002:493.861 - 0.003ms returns 0
T0818 002:493.866 JLINK_WriteReg(R4, 0x00000000)
T0818 002:493.870 - 0.003ms returns 0
T0818 002:493.876 JLINK_WriteReg(R5, 0x00000000)
T0818 002:493.879 - 0.003ms returns 0
T0818 002:493.886 JLINK_WriteReg(R6, 0x00000000)
T0818 002:493.890 - 0.003ms returns 0
T0818 002:493.896 JLINK_WriteReg(R7, 0x00000000)
T0818 002:493.899 - 0.003ms returns 0
T0818 002:493.905 JLINK_WriteReg(R8, 0x00000000)
T0818 002:493.909 - 0.003ms returns 0
T0818 002:493.914 JLINK_WriteReg(R9, 0x20000180)
T0818 002:493.918 - 0.003ms returns 0
T0818 002:493.923 JLINK_WriteReg(R10, 0x00000000)
T0818 002:493.927 - 0.003ms returns 0
T0818 002:493.933 JLINK_WriteReg(R11, 0x00000000)
T0818 002:493.936 - 0.003ms returns 0
T0818 002:493.942 JLINK_WriteReg(R12, 0x00000000)
T0818 002:493.945 - 0.003ms returns 0
T0818 002:493.951 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:493.955 - 0.004ms returns 0
T0818 002:493.963 JLINK_WriteReg(R14, 0x20000001)
T0818 002:493.968 - 0.004ms returns 0
T0818 002:493.973 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:493.977 - 0.003ms returns 0
T0818 002:493.983 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:493.986 - 0.003ms returns 0
T0818 002:493.992 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:493.996 - 0.003ms returns 0
T0818 002:494.001 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:494.005 - 0.003ms returns 0
T0818 002:494.010 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:494.014 - 0.003ms returns 0
T0818 002:494.020 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:494.024 - 0.004ms returns 0x0000001A
T0818 002:494.030 JLINK_Go()
T0818 002:494.037   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:496.842 - 2.812ms 
T0818 002:496.850 JLINK_IsHalted()
T0818 002:497.313 - 0.462ms returns FALSE
T0818 002:497.321 JLINK_HasError()
T0818 002:498.996 JLINK_IsHalted()
T0818 002:499.467 - 0.471ms returns FALSE
T0818 002:499.482 JLINK_HasError()
T0818 002:501.998 JLINK_IsHalted()
T0818 002:504.296   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:504.746 - 2.748ms returns TRUE
T0818 002:504.752 JLINK_ReadReg(R15 (PC))
T0818 002:504.756 - 0.004ms returns 0x20000000
T0818 002:504.761 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T0818 002:504.764 - 0.003ms returns 0x00
T0818 002:504.769 JLINK_ReadReg(R0)
T0818 002:504.772 - 0.003ms returns 0x00000000
T0818 002:505.051 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:505.058   Data:  88 01 88 05 88 03 88 07 48 00 48 04 48 02 48 06 ...
T0818 002:505.074   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:507.697 - 2.646ms returns 0x27C
T0818 002:507.703 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:507.707   Data:  9C 02 9C 06 9C 01 9C 05 9C 03 9C 07 5C 00 5C 04 ...
T0818 002:507.714   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:509.557 - 1.853ms returns 0x184
T0818 002:509.562 JLINK_HasError()
T0818 002:509.567 JLINK_WriteReg(R0, 0x08003C00)
T0818 002:509.571 - 0.004ms returns 0
T0818 002:509.575 JLINK_WriteReg(R1, 0x00000400)
T0818 002:509.579 - 0.003ms returns 0
T0818 002:509.583 JLINK_WriteReg(R2, 0x20000184)
T0818 002:509.586 - 0.003ms returns 0
T0818 002:509.590 JLINK_WriteReg(R3, 0x00000000)
T0818 002:509.594 - 0.003ms returns 0
T0818 002:509.598 JLINK_WriteReg(R4, 0x00000000)
T0818 002:509.601 - 0.003ms returns 0
T0818 002:509.605 JLINK_WriteReg(R5, 0x00000000)
T0818 002:509.608 - 0.003ms returns 0
T0818 002:509.612 JLINK_WriteReg(R6, 0x00000000)
T0818 002:509.616 - 0.003ms returns 0
T0818 002:509.620 JLINK_WriteReg(R7, 0x00000000)
T0818 002:509.623 - 0.003ms returns 0
T0818 002:509.627 JLINK_WriteReg(R8, 0x00000000)
T0818 002:509.631 - 0.003ms returns 0
T0818 002:509.635 JLINK_WriteReg(R9, 0x20000180)
T0818 002:509.638 - 0.003ms returns 0
T0818 002:509.642 JLINK_WriteReg(R10, 0x00000000)
T0818 002:509.646 - 0.003ms returns 0
T0818 002:509.650 JLINK_WriteReg(R11, 0x00000000)
T0818 002:509.653 - 0.003ms returns 0
T0818 002:509.657 JLINK_WriteReg(R12, 0x00000000)
T0818 002:509.660 - 0.003ms returns 0
T0818 002:509.664 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:509.668 - 0.003ms returns 0
T0818 002:509.672 JLINK_WriteReg(R14, 0x20000001)
T0818 002:509.676 - 0.003ms returns 0
T0818 002:509.680 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:509.683 - 0.003ms returns 0
T0818 002:509.687 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:509.690 - 0.003ms returns 0
T0818 002:509.694 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:509.698 - 0.003ms returns 0
T0818 002:509.702 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:509.705 - 0.003ms returns 0
T0818 002:509.709 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:509.713 - 0.003ms returns 0
T0818 002:509.717 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:509.721 - 0.004ms returns 0x0000001B
T0818 002:509.725 JLINK_Go()
T0818 002:509.731   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:512.540 - 2.815ms 
T0818 002:512.551 JLINK_IsHalted()
T0818 002:513.014 - 0.462ms returns FALSE
T0818 002:513.019 JLINK_HasError()
T0818 002:514.996 JLINK_IsHalted()
T0818 002:515.474 - 0.478ms returns FALSE
T0818 002:515.487 JLINK_HasError()
T0818 002:517.001 JLINK_IsHalted()
T0818 002:519.336   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:519.799 - 2.797ms returns TRUE
T0818 002:519.806 JLINK_ReadReg(R15 (PC))
T0818 002:519.811 - 0.005ms returns 0x20000000
T0818 002:519.815 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T0818 002:519.819 - 0.003ms returns 0x00
T0818 002:519.824 JLINK_ReadReg(R0)
T0818 002:519.827 - 0.003ms returns 0x00000000
T0818 002:520.198 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:520.205   Data:  8A 01 8A 05 8A 03 8A 07 4A 00 4A 04 4A 02 4A 06 ...
T0818 002:520.218   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:522.916 - 2.718ms returns 0x27C
T0818 002:522.940 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:522.944   Data:  9E 02 9E 06 9E 01 9E 05 9E 03 9E 07 5E 00 5E 04 ...
T0818 002:522.954   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:524.769 - 1.827ms returns 0x184
T0818 002:524.781 JLINK_HasError()
T0818 002:524.813 JLINK_WriteReg(R0, 0x08004000)
T0818 002:524.819 - 0.006ms returns 0
T0818 002:524.823 JLINK_WriteReg(R1, 0x00000400)
T0818 002:524.827 - 0.003ms returns 0
T0818 002:524.831 JLINK_WriteReg(R2, 0x20000184)
T0818 002:524.834 - 0.003ms returns 0
T0818 002:524.838 JLINK_WriteReg(R3, 0x00000000)
T0818 002:524.842 - 0.003ms returns 0
T0818 002:524.846 JLINK_WriteReg(R4, 0x00000000)
T0818 002:524.849 - 0.003ms returns 0
T0818 002:524.853 JLINK_WriteReg(R5, 0x00000000)
T0818 002:524.856 - 0.003ms returns 0
T0818 002:524.860 JLINK_WriteReg(R6, 0x00000000)
T0818 002:524.864 - 0.003ms returns 0
T0818 002:524.868 JLINK_WriteReg(R7, 0x00000000)
T0818 002:524.871 - 0.003ms returns 0
T0818 002:524.876 JLINK_WriteReg(R8, 0x00000000)
T0818 002:524.879 - 0.003ms returns 0
T0818 002:524.883 JLINK_WriteReg(R9, 0x20000180)
T0818 002:524.886 - 0.003ms returns 0
T0818 002:524.890 JLINK_WriteReg(R10, 0x00000000)
T0818 002:524.894 - 0.003ms returns 0
T0818 002:524.898 JLINK_WriteReg(R11, 0x00000000)
T0818 002:524.902 - 0.003ms returns 0
T0818 002:524.906 JLINK_WriteReg(R12, 0x00000000)
T0818 002:524.909 - 0.003ms returns 0
T0818 002:524.913 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:524.917 - 0.003ms returns 0
T0818 002:524.921 JLINK_WriteReg(R14, 0x20000001)
T0818 002:524.924 - 0.003ms returns 0
T0818 002:524.928 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:524.932 - 0.003ms returns 0
T0818 002:524.936 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:524.939 - 0.003ms returns 0
T0818 002:524.943 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:524.947 - 0.003ms returns 0
T0818 002:524.951 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:524.954 - 0.003ms returns 0
T0818 002:524.958 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:524.961 - 0.003ms returns 0
T0818 002:524.966 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:524.970 - 0.004ms returns 0x0000001C
T0818 002:524.974 JLINK_Go()
T0818 002:524.983   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:527.741 - 2.766ms 
T0818 002:527.747 JLINK_IsHalted()
T0818 002:528.225 - 0.477ms returns FALSE
T0818 002:528.231 JLINK_HasError()
T0818 002:529.996 JLINK_IsHalted()
T0818 002:530.460 - 0.464ms returns FALSE
T0818 002:530.467 JLINK_HasError()
T0818 002:531.996 JLINK_IsHalted()
T0818 002:534.286   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:534.742 - 2.745ms returns TRUE
T0818 002:534.748 JLINK_ReadReg(R15 (PC))
T0818 002:534.753 - 0.004ms returns 0x20000000
T0818 002:534.757 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T0818 002:534.761 - 0.003ms returns 0x00
T0818 002:534.765 JLINK_ReadReg(R0)
T0818 002:534.768 - 0.003ms returns 0x00000000
T0818 002:535.103 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:535.110   Data:  67 C7 3F 3F 70 D8 41 3F 01 E2 43 3F 03 E4 45 3F ...
T0818 002:535.119   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:537.742 - 2.639ms returns 0x27C
T0818 002:537.748 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:537.752   Data:  D4 7C AC 3E 12 8F A6 3E E5 9A A0 3E 86 A0 9A 3E ...
T0818 002:537.763   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:539.601 - 1.853ms returns 0x184
T0818 002:539.607 JLINK_HasError()
T0818 002:539.612 JLINK_WriteReg(R0, 0x08004400)
T0818 002:539.616 - 0.004ms returns 0
T0818 002:539.620 JLINK_WriteReg(R1, 0x00000400)
T0818 002:539.624 - 0.003ms returns 0
T0818 002:539.628 JLINK_WriteReg(R2, 0x20000184)
T0818 002:539.635 - 0.007ms returns 0
T0818 002:539.639 JLINK_WriteReg(R3, 0x00000000)
T0818 002:539.642 - 0.003ms returns 0
T0818 002:539.646 JLINK_WriteReg(R4, 0x00000000)
T0818 002:539.650 - 0.003ms returns 0
T0818 002:539.654 JLINK_WriteReg(R5, 0x00000000)
T0818 002:539.657 - 0.003ms returns 0
T0818 002:539.661 JLINK_WriteReg(R6, 0x00000000)
T0818 002:539.665 - 0.003ms returns 0
T0818 002:539.669 JLINK_WriteReg(R7, 0x00000000)
T0818 002:539.672 - 0.003ms returns 0
T0818 002:539.676 JLINK_WriteReg(R8, 0x00000000)
T0818 002:539.680 - 0.003ms returns 0
T0818 002:539.684 JLINK_WriteReg(R9, 0x20000180)
T0818 002:539.687 - 0.003ms returns 0
T0818 002:539.691 JLINK_WriteReg(R10, 0x00000000)
T0818 002:539.694 - 0.003ms returns 0
T0818 002:539.698 JLINK_WriteReg(R11, 0x00000000)
T0818 002:539.702 - 0.003ms returns 0
T0818 002:539.706 JLINK_WriteReg(R12, 0x00000000)
T0818 002:539.709 - 0.003ms returns 0
T0818 002:539.713 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:539.717 - 0.003ms returns 0
T0818 002:539.721 JLINK_WriteReg(R14, 0x20000001)
T0818 002:539.724 - 0.003ms returns 0
T0818 002:539.728 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:539.732 - 0.003ms returns 0
T0818 002:539.736 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:539.739 - 0.003ms returns 0
T0818 002:539.743 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:539.747 - 0.003ms returns 0
T0818 002:539.751 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:539.754 - 0.003ms returns 0
T0818 002:539.758 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:539.761 - 0.003ms returns 0
T0818 002:539.766 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:539.770 - 0.004ms returns 0x0000001D
T0818 002:539.774 JLINK_Go()
T0818 002:539.780   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:542.486 - 2.700ms 
T0818 002:542.494 JLINK_IsHalted()
T0818 002:542.968 - 0.472ms returns FALSE
T0818 002:542.974 JLINK_HasError()
T0818 002:544.996 JLINK_IsHalted()
T0818 002:545.470 - 0.474ms returns FALSE
T0818 002:545.476 JLINK_HasError()
T0818 002:546.996 JLINK_IsHalted()
T0818 002:549.317   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:549.786 - 2.789ms returns TRUE
T0818 002:549.792 JLINK_ReadReg(R15 (PC))
T0818 002:549.796 - 0.004ms returns 0x20000000
T0818 002:549.801 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T0818 002:549.804 - 0.003ms returns 0x00
T0818 002:549.808 JLINK_ReadReg(R0)
T0818 002:549.812 - 0.003ms returns 0x00000000
T0818 002:550.065 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:550.071   Data:  67 C7 3F BF 70 D8 41 BF 01 E2 43 BF 03 E4 45 BF ...
T0818 002:550.079   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:552.706 - 2.640ms returns 0x27C
T0818 002:552.724 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:552.728   Data:  D4 7C AC BE 12 8F A6 BE E5 9A A0 BE 86 A0 9A BE ...
T0818 002:552.737   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:554.585 - 1.861ms returns 0x184
T0818 002:554.591 JLINK_HasError()
T0818 002:554.596 JLINK_WriteReg(R0, 0x08004800)
T0818 002:554.600 - 0.004ms returns 0
T0818 002:554.604 JLINK_WriteReg(R1, 0x00000400)
T0818 002:554.608 - 0.003ms returns 0
T0818 002:554.612 JLINK_WriteReg(R2, 0x20000184)
T0818 002:554.615 - 0.003ms returns 0
T0818 002:554.619 JLINK_WriteReg(R3, 0x00000000)
T0818 002:554.623 - 0.003ms returns 0
T0818 002:554.627 JLINK_WriteReg(R4, 0x00000000)
T0818 002:554.630 - 0.003ms returns 0
T0818 002:554.634 JLINK_WriteReg(R5, 0x00000000)
T0818 002:554.637 - 0.003ms returns 0
T0818 002:554.641 JLINK_WriteReg(R6, 0x00000000)
T0818 002:554.645 - 0.003ms returns 0
T0818 002:554.649 JLINK_WriteReg(R7, 0x00000000)
T0818 002:554.652 - 0.003ms returns 0
T0818 002:554.656 JLINK_WriteReg(R8, 0x00000000)
T0818 002:554.662 - 0.006ms returns 0
T0818 002:554.667 JLINK_WriteReg(R9, 0x20000180)
T0818 002:554.671 - 0.003ms returns 0
T0818 002:554.675 JLINK_WriteReg(R10, 0x00000000)
T0818 002:554.678 - 0.003ms returns 0
T0818 002:554.682 JLINK_WriteReg(R11, 0x00000000)
T0818 002:554.686 - 0.003ms returns 0
T0818 002:554.690 JLINK_WriteReg(R12, 0x00000000)
T0818 002:554.693 - 0.003ms returns 0
T0818 002:554.697 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:554.701 - 0.003ms returns 0
T0818 002:554.705 JLINK_WriteReg(R14, 0x20000001)
T0818 002:554.708 - 0.003ms returns 0
T0818 002:554.713 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:554.716 - 0.003ms returns 0
T0818 002:554.720 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:554.724 - 0.003ms returns 0
T0818 002:554.728 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:554.731 - 0.003ms returns 0
T0818 002:554.735 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:554.738 - 0.003ms returns 0
T0818 002:554.742 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:554.746 - 0.003ms returns 0
T0818 002:554.750 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:554.754 - 0.004ms returns 0x0000001E
T0818 002:554.758 JLINK_Go()
T0818 002:554.766   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:557.430 - 2.671ms 
T0818 002:557.436 JLINK_IsHalted()
T0818 002:557.924 - 0.488ms returns FALSE
T0818 002:557.930 JLINK_HasError()
T0818 002:559.001 JLINK_IsHalted()
T0818 002:559.476 - 0.475ms returns FALSE
T0818 002:559.483 JLINK_HasError()
T0818 002:560.997 JLINK_IsHalted()
T0818 002:561.471 - 0.474ms returns FALSE
T0818 002:561.477 JLINK_HasError()
T0818 002:562.996 JLINK_IsHalted()
T0818 002:565.316   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:565.766 - 2.769ms returns TRUE
T0818 002:565.772 JLINK_ReadReg(R15 (PC))
T0818 002:565.776 - 0.004ms returns 0x20000000
T0818 002:565.780 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T0818 002:565.784 - 0.003ms returns 0x00
T0818 002:565.788 JLINK_ReadReg(R0)
T0818 002:565.792 - 0.003ms returns 0x00000000
T0818 002:566.068 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:566.075   Data:  07 E0 2F 3D 2A BF 7F 3F FC 26 36 3D 9E BA 7F 3F ...
T0818 002:566.084   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:568.701 - 2.632ms returns 0x27C
T0818 002:568.713 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:568.717   Data:  B0 7E 7C 3F FC DE 28 3E 08 6E 7C 3F 8D 6B 2A 3E ...
T0818 002:568.726   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:570.599 - 1.886ms returns 0x184
T0818 002:570.611 JLINK_HasError()
T0818 002:570.616 JLINK_WriteReg(R0, 0x08004C00)
T0818 002:570.622 - 0.005ms returns 0
T0818 002:570.626 JLINK_WriteReg(R1, 0x00000400)
T0818 002:570.629 - 0.003ms returns 0
T0818 002:570.634 JLINK_WriteReg(R2, 0x20000184)
T0818 002:570.637 - 0.003ms returns 0
T0818 002:570.641 JLINK_WriteReg(R3, 0x00000000)
T0818 002:570.644 - 0.003ms returns 0
T0818 002:570.648 JLINK_WriteReg(R4, 0x00000000)
T0818 002:570.652 - 0.003ms returns 0
T0818 002:570.656 JLINK_WriteReg(R5, 0x00000000)
T0818 002:570.659 - 0.003ms returns 0
T0818 002:570.663 JLINK_WriteReg(R6, 0x00000000)
T0818 002:570.667 - 0.003ms returns 0
T0818 002:570.670 JLINK_WriteReg(R7, 0x00000000)
T0818 002:570.674 - 0.003ms returns 0
T0818 002:570.678 JLINK_WriteReg(R8, 0x00000000)
T0818 002:570.681 - 0.003ms returns 0
T0818 002:570.685 JLINK_WriteReg(R9, 0x20000180)
T0818 002:570.689 - 0.003ms returns 0
T0818 002:570.693 JLINK_WriteReg(R10, 0x00000000)
T0818 002:570.696 - 0.003ms returns 0
T0818 002:570.700 JLINK_WriteReg(R11, 0x00000000)
T0818 002:570.704 - 0.003ms returns 0
T0818 002:570.708 JLINK_WriteReg(R12, 0x00000000)
T0818 002:570.711 - 0.003ms returns 0
T0818 002:570.715 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:570.719 - 0.003ms returns 0
T0818 002:570.723 JLINK_WriteReg(R14, 0x20000001)
T0818 002:570.774 - 0.051ms returns 0
T0818 002:570.778 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:570.782 - 0.003ms returns 0
T0818 002:570.786 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:570.790 - 0.003ms returns 0
T0818 002:570.796 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:570.800 - 0.004ms returns 0
T0818 002:570.804 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:570.808 - 0.003ms returns 0
T0818 002:570.812 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:570.815 - 0.003ms returns 0
T0818 002:570.820 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:570.824 - 0.004ms returns 0x0000001F
T0818 002:570.828 JLINK_Go()
T0818 002:570.835   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:574.104 - 3.275ms 
T0818 002:574.115 JLINK_IsHalted()
T0818 002:574.606 - 0.490ms returns FALSE
T0818 002:574.612 JLINK_HasError()
T0818 002:577.004 JLINK_IsHalted()
T0818 002:577.622 - 0.617ms returns FALSE
T0818 002:577.635 JLINK_HasError()
T0818 002:578.997 JLINK_IsHalted()
T0818 002:582.052   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:582.577 - 3.580ms returns TRUE
T0818 002:582.586 JLINK_ReadReg(R15 (PC))
T0818 002:582.591 - 0.004ms returns 0x20000000
T0818 002:582.596 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T0818 002:582.599 - 0.003ms returns 0x00
T0818 002:582.604 JLINK_ReadReg(R0)
T0818 002:582.607 - 0.003ms returns 0x00000000
T0818 002:582.873 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:582.879   Data:  51 B6 72 3E 93 9C 78 3F E8 3C 74 3E 86 84 78 3F ...
T0818 002:582.889   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:585.514 - 2.640ms returns 0x27C
T0818 002:585.520 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:585.523   Data:  30 68 6F 3F EC 54 B5 3E 83 44 6F 3F E6 10 B6 3E ...
T0818 002:585.530   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:587.424 - 1.904ms returns 0x184
T0818 002:587.429 JLINK_HasError()
T0818 002:587.434 JLINK_WriteReg(R0, 0x08005000)
T0818 002:587.438 - 0.004ms returns 0
T0818 002:587.442 JLINK_WriteReg(R1, 0x00000400)
T0818 002:587.446 - 0.003ms returns 0
T0818 002:587.450 JLINK_WriteReg(R2, 0x20000184)
T0818 002:587.453 - 0.003ms returns 0
T0818 002:587.457 JLINK_WriteReg(R3, 0x00000000)
T0818 002:587.460 - 0.003ms returns 0
T0818 002:587.465 JLINK_WriteReg(R4, 0x00000000)
T0818 002:587.468 - 0.003ms returns 0
T0818 002:587.472 JLINK_WriteReg(R5, 0x00000000)
T0818 002:587.475 - 0.003ms returns 0
T0818 002:587.479 JLINK_WriteReg(R6, 0x00000000)
T0818 002:587.483 - 0.003ms returns 0
T0818 002:587.487 JLINK_WriteReg(R7, 0x00000000)
T0818 002:587.490 - 0.003ms returns 0
T0818 002:587.494 JLINK_WriteReg(R8, 0x00000000)
T0818 002:587.498 - 0.003ms returns 0
T0818 002:587.502 JLINK_WriteReg(R9, 0x20000180)
T0818 002:587.509 - 0.007ms returns 0
T0818 002:587.513 JLINK_WriteReg(R10, 0x00000000)
T0818 002:587.516 - 0.003ms returns 0
T0818 002:587.520 JLINK_WriteReg(R11, 0x00000000)
T0818 002:587.524 - 0.003ms returns 0
T0818 002:587.528 JLINK_WriteReg(R12, 0x00000000)
T0818 002:587.531 - 0.003ms returns 0
T0818 002:587.535 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:587.539 - 0.003ms returns 0
T0818 002:587.543 JLINK_WriteReg(R14, 0x20000001)
T0818 002:587.546 - 0.003ms returns 0
T0818 002:587.550 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:587.554 - 0.003ms returns 0
T0818 002:587.558 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:587.562 - 0.004ms returns 0
T0818 002:587.566 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:587.569 - 0.003ms returns 0
T0818 002:587.573 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:587.577 - 0.003ms returns 0
T0818 002:587.581 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:587.584 - 0.003ms returns 0
T0818 002:587.589 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:587.593 - 0.004ms returns 0x00000020
T0818 002:587.597 JLINK_Go()
T0818 002:587.604   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:590.717 - 3.119ms 
T0818 002:590.729 JLINK_IsHalted()
T0818 002:591.476 - 0.745ms returns FALSE
T0818 002:591.484 JLINK_HasError()
T0818 002:592.996 JLINK_IsHalted()
T0818 002:593.472 - 0.475ms returns FALSE
T0818 002:593.477 JLINK_HasError()
T0818 002:594.997 JLINK_IsHalted()
T0818 002:597.251   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:597.726 - 2.728ms returns TRUE
T0818 002:597.732 JLINK_ReadReg(R15 (PC))
T0818 002:597.739 - 0.006ms returns 0x20000000
T0818 002:597.745 JLINK_ClrBPEx(BPHandle = 0x00000020)
T0818 002:597.749 - 0.004ms returns 0x00
T0818 002:597.753 JLINK_ReadReg(R0)
T0818 002:597.756 - 0.003ms returns 0x00000000
T0818 002:598.029 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:598.045   Data:  6B 10 D8 3E 2A EC 67 3F A3 C6 D8 3E 87 C1 67 3F ...
T0818 002:598.054   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:600.704 - 2.675ms returns 0x27C
T0818 002:600.719 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:600.724   Data:  6A 1E 59 3F 36 A1 07 3F 17 E9 58 3F 6F F6 07 3F ...
T0818 002:600.736   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:602.587 - 1.868ms returns 0x184
T0818 002:602.603 JLINK_HasError()
T0818 002:602.631 JLINK_WriteReg(R0, 0x08005400)
T0818 002:602.637 - 0.005ms returns 0
T0818 002:602.641 JLINK_WriteReg(R1, 0x00000400)
T0818 002:602.645 - 0.003ms returns 0
T0818 002:602.649 JLINK_WriteReg(R2, 0x20000184)
T0818 002:602.652 - 0.003ms returns 0
T0818 002:602.656 JLINK_WriteReg(R3, 0x00000000)
T0818 002:602.659 - 0.003ms returns 0
T0818 002:602.663 JLINK_WriteReg(R4, 0x00000000)
T0818 002:602.667 - 0.003ms returns 0
T0818 002:602.671 JLINK_WriteReg(R5, 0x00000000)
T0818 002:602.674 - 0.003ms returns 0
T0818 002:602.678 JLINK_WriteReg(R6, 0x00000000)
T0818 002:602.682 - 0.003ms returns 0
T0818 002:602.686 JLINK_WriteReg(R7, 0x00000000)
T0818 002:602.689 - 0.003ms returns 0
T0818 002:602.693 JLINK_WriteReg(R8, 0x00000000)
T0818 002:602.696 - 0.003ms returns 0
T0818 002:602.700 JLINK_WriteReg(R9, 0x20000180)
T0818 002:602.704 - 0.003ms returns 0
T0818 002:602.708 JLINK_WriteReg(R10, 0x00000000)
T0818 002:602.711 - 0.003ms returns 0
T0818 002:602.715 JLINK_WriteReg(R11, 0x00000000)
T0818 002:602.719 - 0.003ms returns 0
T0818 002:602.723 JLINK_WriteReg(R12, 0x00000000)
T0818 002:602.726 - 0.003ms returns 0
T0818 002:602.730 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:602.734 - 0.003ms returns 0
T0818 002:602.738 JLINK_WriteReg(R14, 0x20000001)
T0818 002:602.741 - 0.003ms returns 0
T0818 002:602.746 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:602.749 - 0.003ms returns 0
T0818 002:602.753 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:602.756 - 0.003ms returns 0
T0818 002:602.760 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:602.764 - 0.003ms returns 0
T0818 002:602.768 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:602.771 - 0.003ms returns 0
T0818 002:602.775 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:602.779 - 0.003ms returns 0
T0818 002:602.783 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:602.787 - 0.004ms returns 0x00000021
T0818 002:602.791 JLINK_Go()
T0818 002:602.799   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:605.474 - 2.683ms 
T0818 002:605.482 JLINK_IsHalted()
T0818 002:605.966 - 0.483ms returns FALSE
T0818 002:605.971 JLINK_HasError()
T0818 002:607.996 JLINK_IsHalted()
T0818 002:608.466 - 0.470ms returns FALSE
T0818 002:608.472 JLINK_HasError()
T0818 002:609.995 JLINK_IsHalted()
T0818 002:612.436   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:612.940 - 2.944ms returns TRUE
T0818 002:612.946 JLINK_ReadReg(R15 (PC))
T0818 002:612.951 - 0.004ms returns 0x20000000
T0818 002:612.955 JLINK_ClrBPEx(BPHandle = 0x00000021)
T0818 002:612.959 - 0.003ms returns 0x00
T0818 002:612.963 JLINK_ReadReg(R0)
T0818 002:612.966 - 0.003ms returns 0x00000000
T0818 002:613.227 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:613.234   Data:  07 3C 17 3F 1C 52 4E 3F 18 8D 17 3F 89 16 4E 3F ...
T0818 002:613.242   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:615.855 - 2.627ms returns 0x27C
T0818 002:615.861 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:615.865   Data:  A4 7C 3A 3F A5 61 2F 3F B7 37 3A 3F D3 AA 2F 3F ...
T0818 002:615.871   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:617.786 - 1.924ms returns 0x184
T0818 002:617.791 JLINK_HasError()
T0818 002:617.796 JLINK_WriteReg(R0, 0x08005800)
T0818 002:617.800 - 0.004ms returns 0
T0818 002:617.804 JLINK_WriteReg(R1, 0x00000400)
T0818 002:617.811 - 0.006ms returns 0
T0818 002:617.816 JLINK_WriteReg(R2, 0x20000184)
T0818 002:617.820 - 0.003ms returns 0
T0818 002:617.824 JLINK_WriteReg(R3, 0x00000000)
T0818 002:617.827 - 0.003ms returns 0
T0818 002:617.831 JLINK_WriteReg(R4, 0x00000000)
T0818 002:617.834 - 0.003ms returns 0
T0818 002:617.838 JLINK_WriteReg(R5, 0x00000000)
T0818 002:617.842 - 0.003ms returns 0
T0818 002:617.846 JLINK_WriteReg(R6, 0x00000000)
T0818 002:617.849 - 0.003ms returns 0
T0818 002:617.853 JLINK_WriteReg(R7, 0x00000000)
T0818 002:617.857 - 0.003ms returns 0
T0818 002:617.861 JLINK_WriteReg(R8, 0x00000000)
T0818 002:617.864 - 0.003ms returns 0
T0818 002:617.868 JLINK_WriteReg(R9, 0x20000180)
T0818 002:617.871 - 0.003ms returns 0
T0818 002:617.875 JLINK_WriteReg(R10, 0x00000000)
T0818 002:617.879 - 0.003ms returns 0
T0818 002:617.883 JLINK_WriteReg(R11, 0x00000000)
T0818 002:617.886 - 0.003ms returns 0
T0818 002:617.890 JLINK_WriteReg(R12, 0x00000000)
T0818 002:617.893 - 0.003ms returns 0
T0818 002:617.897 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:617.901 - 0.003ms returns 0
T0818 002:617.905 JLINK_WriteReg(R14, 0x20000001)
T0818 002:617.909 - 0.003ms returns 0
T0818 002:617.913 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:617.916 - 0.003ms returns 0
T0818 002:617.920 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:617.924 - 0.003ms returns 0
T0818 002:617.928 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:617.931 - 0.003ms returns 0
T0818 002:617.935 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:617.938 - 0.003ms returns 0
T0818 002:617.942 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:617.946 - 0.003ms returns 0
T0818 002:617.950 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:617.954 - 0.004ms returns 0x00000022
T0818 002:617.958 JLINK_Go()
T0818 002:617.964   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:620.732 - 2.773ms 
T0818 002:620.744 JLINK_IsHalted()
T0818 002:621.249 - 0.504ms returns FALSE
T0818 002:621.258 JLINK_HasError()
T0818 002:623.004 JLINK_IsHalted()
T0818 002:623.462 - 0.458ms returns FALSE
T0818 002:623.468 JLINK_HasError()
T0818 002:624.995 JLINK_IsHalted()
T0818 002:627.289   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:627.791 - 2.794ms returns TRUE
T0818 002:627.799 JLINK_ReadReg(R15 (PC))
T0818 002:627.804 - 0.004ms returns 0x20000000
T0818 002:627.808 JLINK_ClrBPEx(BPHandle = 0x00000022)
T0818 002:627.812 - 0.003ms returns 0x00
T0818 002:627.816 JLINK_ReadReg(R0)
T0818 002:627.820 - 0.003ms returns 0x00000000
T0818 002:628.087 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:628.094   Data:  03 A0 3C 3F 49 CA 2C 3F EC E3 3C 3F 0F 80 2C 3F ...
T0818 002:628.104   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:630.662 - 2.575ms returns 0x27C
T0818 002:630.677 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:630.680   Data:  39 B0 14 3F AF 64 50 3F 58 5E 14 3F 03 9F 50 3F ...
T0818 002:630.689   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:632.950 - 2.273ms returns 0x184
T0818 002:632.964 JLINK_HasError()
T0818 002:632.970 JLINK_WriteReg(R0, 0x08005C00)
T0818 002:632.975 - 0.005ms returns 0
T0818 002:632.979 JLINK_WriteReg(R1, 0x00000400)
T0818 002:632.982 - 0.003ms returns 0
T0818 002:632.986 JLINK_WriteReg(R2, 0x20000184)
T0818 002:632.991 - 0.004ms returns 0
T0818 002:632.996 JLINK_WriteReg(R3, 0x00000000)
T0818 002:632.999 - 0.003ms returns 0
T0818 002:633.003 JLINK_WriteReg(R4, 0x00000000)
T0818 002:633.007 - 0.003ms returns 0
T0818 002:633.011 JLINK_WriteReg(R5, 0x00000000)
T0818 002:633.014 - 0.003ms returns 0
T0818 002:633.018 JLINK_WriteReg(R6, 0x00000000)
T0818 002:633.022 - 0.003ms returns 0
T0818 002:633.026 JLINK_WriteReg(R7, 0x00000000)
T0818 002:633.029 - 0.003ms returns 0
T0818 002:633.033 JLINK_WriteReg(R8, 0x00000000)
T0818 002:633.036 - 0.003ms returns 0
T0818 002:633.040 JLINK_WriteReg(R9, 0x20000180)
T0818 002:633.044 - 0.003ms returns 0
T0818 002:633.048 JLINK_WriteReg(R10, 0x00000000)
T0818 002:633.051 - 0.003ms returns 0
T0818 002:633.055 JLINK_WriteReg(R11, 0x00000000)
T0818 002:633.061 - 0.005ms returns 0
T0818 002:633.066 JLINK_WriteReg(R12, 0x00000000)
T0818 002:633.069 - 0.003ms returns 0
T0818 002:633.073 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:633.077 - 0.003ms returns 0
T0818 002:633.081 JLINK_WriteReg(R14, 0x20000001)
T0818 002:633.084 - 0.003ms returns 0
T0818 002:633.089 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:633.092 - 0.003ms returns 0
T0818 002:633.096 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:633.099 - 0.003ms returns 0
T0818 002:633.103 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:633.107 - 0.003ms returns 0
T0818 002:633.111 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:633.114 - 0.003ms returns 0
T0818 002:633.118 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:633.122 - 0.003ms returns 0
T0818 002:633.126 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:633.130 - 0.004ms returns 0x00000023
T0818 002:633.134 JLINK_Go()
T0818 002:633.141   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:635.977 - 2.843ms 
T0818 002:635.985 JLINK_IsHalted()
T0818 002:636.470 - 0.484ms returns FALSE
T0818 002:636.476 JLINK_HasError()
T0818 002:639.002 JLINK_IsHalted()
T0818 002:639.474 - 0.472ms returns FALSE
T0818 002:639.481 JLINK_HasError()
T0818 002:641.005 JLINK_IsHalted()
T0818 002:643.683   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:644.195 - 3.189ms returns TRUE
T0818 002:644.205 JLINK_ReadReg(R15 (PC))
T0818 002:644.210 - 0.005ms returns 0x20000000
T0818 002:644.215 JLINK_ClrBPEx(BPHandle = 0x00000023)
T0818 002:644.219 - 0.003ms returns 0x00
T0818 002:644.223 JLINK_ReadReg(R0)
T0818 002:644.227 - 0.003ms returns 0x00000000
T0818 002:644.504 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:644.511   Data:  50 C4 5A 3F 91 9E 04 3F 75 F8 5A 3F 89 48 04 3F ...
T0818 002:644.520   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:647.058 - 2.554ms returns 0x27C
T0818 002:647.066 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:647.069   Data:  09 5A D2 3E 91 65 69 3F AA A2 D1 3E CC 8E 69 3F ...
T0818 002:647.076   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:648.967 - 1.901ms returns 0x184
T0818 002:648.972 JLINK_HasError()
T0818 002:648.980 JLINK_WriteReg(R0, 0x08006000)
T0818 002:648.985 - 0.004ms returns 0
T0818 002:648.989 JLINK_WriteReg(R1, 0x00000400)
T0818 002:648.994 - 0.004ms returns 0
T0818 002:648.998 JLINK_WriteReg(R2, 0x20000184)
T0818 002:649.001 - 0.003ms returns 0
T0818 002:649.006 JLINK_WriteReg(R3, 0x00000000)
T0818 002:649.009 - 0.003ms returns 0
T0818 002:649.013 JLINK_WriteReg(R4, 0x00000000)
T0818 002:649.016 - 0.003ms returns 0
T0818 002:649.020 JLINK_WriteReg(R5, 0x00000000)
T0818 002:649.024 - 0.003ms returns 0
T0818 002:649.028 JLINK_WriteReg(R6, 0x00000000)
T0818 002:649.031 - 0.003ms returns 0
T0818 002:649.035 JLINK_WriteReg(R7, 0x00000000)
T0818 002:649.039 - 0.003ms returns 0
T0818 002:649.043 JLINK_WriteReg(R8, 0x00000000)
T0818 002:649.046 - 0.003ms returns 0
T0818 002:649.050 JLINK_WriteReg(R9, 0x20000180)
T0818 002:649.054 - 0.003ms returns 0
T0818 002:649.058 JLINK_WriteReg(R10, 0x00000000)
T0818 002:649.061 - 0.003ms returns 0
T0818 002:649.065 JLINK_WriteReg(R11, 0x00000000)
T0818 002:649.069 - 0.003ms returns 0
T0818 002:649.073 JLINK_WriteReg(R12, 0x00000000)
T0818 002:649.076 - 0.003ms returns 0
T0818 002:649.080 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:649.084 - 0.003ms returns 0
T0818 002:649.088 JLINK_WriteReg(R14, 0x20000001)
T0818 002:649.091 - 0.003ms returns 0
T0818 002:649.095 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:649.099 - 0.003ms returns 0
T0818 002:649.103 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:649.107 - 0.003ms returns 0
T0818 002:649.111 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:649.114 - 0.003ms returns 0
T0818 002:649.118 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:649.122 - 0.003ms returns 0
T0818 002:649.126 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:649.129 - 0.003ms returns 0
T0818 002:649.133 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:649.137 - 0.004ms returns 0x00000024
T0818 002:649.144 JLINK_Go()
T0818 002:649.152   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:651.875 - 2.730ms 
T0818 002:651.886 JLINK_IsHalted()
T0818 002:652.351 - 0.464ms returns FALSE
T0818 002:652.357 JLINK_HasError()
T0818 002:653.996 JLINK_IsHalted()
T0818 002:654.500 - 0.504ms returns FALSE
T0818 002:654.506 JLINK_HasError()
T0818 002:655.995 JLINK_IsHalted()
T0818 002:658.271   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:658.742 - 2.746ms returns TRUE
T0818 002:658.748 JLINK_ReadReg(R15 (PC))
T0818 002:658.752 - 0.004ms returns 0x20000000
T0818 002:658.757 JLINK_ClrBPEx(BPHandle = 0x00000024)
T0818 002:658.760 - 0.003ms returns 0x00
T0818 002:658.765 JLINK_ReadReg(R0)
T0818 002:658.768 - 0.003ms returns 0x00000000
T0818 002:659.060 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:659.068   Data:  66 80 70 3F 49 B4 AE 3E C6 A2 70 3F 3C F7 AD 3E ...
T0818 002:659.077   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:661.781 - 2.720ms returns 0x27C
T0818 002:661.798 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:661.802   Data:  66 7C 66 3E 4E 6E 79 3F 86 F4 64 3E DC 84 79 3F ...
T0818 002:661.814   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:663.693 - 1.894ms returns 0x184
T0818 002:663.710 JLINK_HasError()
T0818 002:663.717 JLINK_WriteReg(R0, 0x08006400)
T0818 002:663.724 - 0.007ms returns 0
T0818 002:663.730 JLINK_WriteReg(R1, 0x00000400)
T0818 002:663.734 - 0.004ms returns 0
T0818 002:663.739 JLINK_WriteReg(R2, 0x20000184)
T0818 002:663.744 - 0.004ms returns 0
T0818 002:663.749 JLINK_WriteReg(R3, 0x00000000)
T0818 002:663.753 - 0.004ms returns 0
T0818 002:663.759 JLINK_WriteReg(R4, 0x00000000)
T0818 002:663.763 - 0.004ms returns 0
T0818 002:663.768 JLINK_WriteReg(R5, 0x00000000)
T0818 002:663.772 - 0.004ms returns 0
T0818 002:663.778 JLINK_WriteReg(R6, 0x00000000)
T0818 002:663.782 - 0.004ms returns 0
T0818 002:663.787 JLINK_WriteReg(R7, 0x00000000)
T0818 002:663.791 - 0.004ms returns 0
T0818 002:663.796 JLINK_WriteReg(R8, 0x00000000)
T0818 002:663.802 - 0.005ms returns 0
T0818 002:663.806 JLINK_WriteReg(R9, 0x20000180)
T0818 002:663.809 - 0.003ms returns 0
T0818 002:663.813 JLINK_WriteReg(R10, 0x00000000)
T0818 002:663.816 - 0.003ms returns 0
T0818 002:663.820 JLINK_WriteReg(R11, 0x00000000)
T0818 002:663.824 - 0.003ms returns 0
T0818 002:663.828 JLINK_WriteReg(R12, 0x00000000)
T0818 002:663.832 - 0.003ms returns 0
T0818 002:663.836 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:663.840 - 0.004ms returns 0
T0818 002:663.844 JLINK_WriteReg(R14, 0x20000001)
T0818 002:663.847 - 0.003ms returns 0
T0818 002:663.851 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:663.855 - 0.003ms returns 0
T0818 002:663.859 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:663.862 - 0.003ms returns 0
T0818 002:663.866 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:663.870 - 0.003ms returns 0
T0818 002:663.874 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:663.877 - 0.003ms returns 0
T0818 002:663.881 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:663.885 - 0.003ms returns 0
T0818 002:663.889 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:663.894 - 0.004ms returns 0x00000025
T0818 002:663.898 JLINK_Go()
T0818 002:663.906   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:666.696 - 2.798ms 
T0818 002:666.707 JLINK_IsHalted()
T0818 002:667.195 - 0.488ms returns FALSE
T0818 002:667.203 JLINK_HasError()
T0818 002:668.999 JLINK_IsHalted()
T0818 002:669.477 - 0.477ms returns FALSE
T0818 002:669.505 JLINK_HasError()
T0818 002:671.004 JLINK_IsHalted()
T0818 002:673.370   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:673.868 - 2.864ms returns TRUE
T0818 002:673.875 JLINK_ReadReg(R15 (PC))
T0818 002:673.880 - 0.005ms returns 0x20000000
T0818 002:673.884 JLINK_ClrBPEx(BPHandle = 0x00000025)
T0818 002:673.888 - 0.003ms returns 0x00
T0818 002:673.892 JLINK_ReadReg(R0)
T0818 002:673.896 - 0.003ms returns 0x00000000
T0818 002:674.409 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:674.421   Data:  73 FE 7C 3F 6B E9 1A 3E BC 0D 7D 3F E0 5B 19 3E ...
T0818 002:674.437   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:676.970 - 2.561ms returns 0x27C
T0818 002:676.981 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:676.985   Data:  B9 49 FB 3C 29 E1 7F 3F 2D BA EE 3C 2B E4 7F 3F ...
T0818 002:676.994   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:678.785 - 1.804ms returns 0x184
T0818 002:678.791 JLINK_HasError()
T0818 002:678.796 JLINK_WriteReg(R0, 0x08006800)
T0818 002:678.800 - 0.004ms returns 0
T0818 002:678.805 JLINK_WriteReg(R1, 0x00000400)
T0818 002:678.808 - 0.003ms returns 0
T0818 002:678.812 JLINK_WriteReg(R2, 0x20000184)
T0818 002:678.816 - 0.003ms returns 0
T0818 002:678.820 JLINK_WriteReg(R3, 0x00000000)
T0818 002:678.823 - 0.003ms returns 0
T0818 002:678.827 JLINK_WriteReg(R4, 0x00000000)
T0818 002:678.831 - 0.003ms returns 0
T0818 002:678.835 JLINK_WriteReg(R5, 0x00000000)
T0818 002:678.838 - 0.003ms returns 0
T0818 002:678.842 JLINK_WriteReg(R6, 0x00000000)
T0818 002:678.845 - 0.003ms returns 0
T0818 002:678.849 JLINK_WriteReg(R7, 0x00000000)
T0818 002:678.853 - 0.003ms returns 0
T0818 002:678.857 JLINK_WriteReg(R8, 0x00000000)
T0818 002:678.860 - 0.003ms returns 0
T0818 002:678.864 JLINK_WriteReg(R9, 0x20000180)
T0818 002:678.868 - 0.003ms returns 0
T0818 002:678.872 JLINK_WriteReg(R10, 0x00000000)
T0818 002:678.875 - 0.003ms returns 0
T0818 002:678.879 JLINK_WriteReg(R11, 0x00000000)
T0818 002:678.882 - 0.003ms returns 0
T0818 002:678.886 JLINK_WriteReg(R12, 0x00000000)
T0818 002:678.890 - 0.003ms returns 0
T0818 002:678.894 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:678.898 - 0.003ms returns 0
T0818 002:678.902 JLINK_WriteReg(R14, 0x20000001)
T0818 002:678.906 - 0.003ms returns 0
T0818 002:678.910 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:678.913 - 0.003ms returns 0
T0818 002:678.917 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:678.921 - 0.003ms returns 0
T0818 002:678.925 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:678.928 - 0.003ms returns 0
T0818 002:678.932 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:678.936 - 0.003ms returns 0
T0818 002:678.940 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:678.943 - 0.003ms returns 0
T0818 002:678.947 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:678.951 - 0.004ms returns 0x00000026
T0818 002:678.955 JLINK_Go()
T0818 002:678.962   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:681.664 - 2.708ms 
T0818 002:681.674 JLINK_IsHalted()
T0818 002:682.144 - 0.469ms returns FALSE
T0818 002:682.149 JLINK_HasError()
T0818 002:684.997 JLINK_IsHalted()
T0818 002:685.467 - 0.470ms returns FALSE
T0818 002:685.473 JLINK_HasError()
T0818 002:686.996 JLINK_IsHalted()
T0818 002:689.458   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:689.969 - 2.973ms returns TRUE
T0818 002:689.975 JLINK_ReadReg(R15 (PC))
T0818 002:689.979 - 0.004ms returns 0x20000000
T0818 002:689.984 JLINK_ClrBPEx(BPHandle = 0x00000026)
T0818 002:689.988 - 0.003ms returns 0x00
T0818 002:689.992 JLINK_ReadReg(R0)
T0818 002:689.995 - 0.003ms returns 0x00000000
T0818 002:690.356 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:690.367   Data:  8F C3 7F 3F FC 26 36 BD 2A BF 7F 3F D5 6D 3C BD ...
T0818 002:690.377   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:692.995 - 2.638ms returns 0x27C
T0818 002:693.035 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:693.039   Data:  FC DE 28 BE B0 7E 7C 3F 8D 6B 2A BE 08 6E 7C 3F ...
T0818 002:693.049   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:694.969 - 1.934ms returns 0x184
T0818 002:694.975 JLINK_HasError()
T0818 002:694.980 JLINK_WriteReg(R0, 0x08006C00)
T0818 002:694.984 - 0.004ms returns 0
T0818 002:694.988 JLINK_WriteReg(R1, 0x00000400)
T0818 002:694.992 - 0.003ms returns 0
T0818 002:694.996 JLINK_WriteReg(R2, 0x20000184)
T0818 002:694.999 - 0.003ms returns 0
T0818 002:695.003 JLINK_WriteReg(R3, 0x00000000)
T0818 002:695.007 - 0.003ms returns 0
T0818 002:695.011 JLINK_WriteReg(R4, 0x00000000)
T0818 002:695.014 - 0.003ms returns 0
T0818 002:695.018 JLINK_WriteReg(R5, 0x00000000)
T0818 002:695.022 - 0.003ms returns 0
T0818 002:695.030 JLINK_WriteReg(R6, 0x00000000)
T0818 002:695.033 - 0.003ms returns 0
T0818 002:695.037 JLINK_WriteReg(R7, 0x00000000)
T0818 002:695.041 - 0.003ms returns 0
T0818 002:695.045 JLINK_WriteReg(R8, 0x00000000)
T0818 002:695.048 - 0.003ms returns 0
T0818 002:695.052 JLINK_WriteReg(R9, 0x20000180)
T0818 002:695.055 - 0.003ms returns 0
T0818 002:695.059 JLINK_WriteReg(R10, 0x00000000)
T0818 002:695.063 - 0.003ms returns 0
T0818 002:695.067 JLINK_WriteReg(R11, 0x00000000)
T0818 002:695.070 - 0.003ms returns 0
T0818 002:695.074 JLINK_WriteReg(R12, 0x00000000)
T0818 002:695.077 - 0.003ms returns 0
T0818 002:695.082 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:695.085 - 0.003ms returns 0
T0818 002:695.089 JLINK_WriteReg(R14, 0x20000001)
T0818 002:695.093 - 0.003ms returns 0
T0818 002:695.097 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:695.100 - 0.003ms returns 0
T0818 002:695.104 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:695.108 - 0.003ms returns 0
T0818 002:695.112 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:695.115 - 0.003ms returns 0
T0818 002:695.119 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:695.122 - 0.003ms returns 0
T0818 002:695.127 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:695.130 - 0.003ms returns 0
T0818 002:695.134 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:695.138 - 0.004ms returns 0x00000027
T0818 002:695.142 JLINK_Go()
T0818 002:695.149   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:697.875 - 2.732ms 
T0818 002:697.881 JLINK_IsHalted()
T0818 002:698.344 - 0.463ms returns FALSE
T0818 002:698.349 JLINK_HasError()
T0818 002:701.001 JLINK_IsHalted()
T0818 002:701.458 - 0.457ms returns FALSE
T0818 002:701.465 JLINK_HasError()
T0818 002:703.997 JLINK_IsHalted()
T0818 002:706.318   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:706.800 - 2.802ms returns TRUE
T0818 002:706.807 JLINK_ReadReg(R15 (PC))
T0818 002:706.811 - 0.004ms returns 0x20000000
T0818 002:706.816 JLINK_ClrBPEx(BPHandle = 0x00000027)
T0818 002:706.820 - 0.003ms returns 0x00
T0818 002:706.824 JLINK_ReadReg(R0)
T0818 002:706.827 - 0.003ms returns 0x00000000
T0818 002:707.103 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:707.110   Data:  7B B4 78 3F E8 3C 74 BE 93 9C 78 3F 5A C3 75 BE ...
T0818 002:707.119   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:709.696 - 2.593ms returns 0x27C
T0818 002:709.703 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:709.707   Data:  EC 54 B5 BE 30 68 6F 3F E6 10 B6 BE 83 44 6F 3F ...
T0818 002:709.714   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:711.596 - 1.892ms returns 0x184
T0818 002:711.605 JLINK_HasError()
T0818 002:711.610 JLINK_WriteReg(R0, 0x08007000)
T0818 002:711.614 - 0.004ms returns 0
T0818 002:711.618 JLINK_WriteReg(R1, 0x00000400)
T0818 002:711.622 - 0.003ms returns 0
T0818 002:711.626 JLINK_WriteReg(R2, 0x20000184)
T0818 002:711.629 - 0.003ms returns 0
T0818 002:711.633 JLINK_WriteReg(R3, 0x00000000)
T0818 002:711.637 - 0.003ms returns 0
T0818 002:711.641 JLINK_WriteReg(R4, 0x00000000)
T0818 002:711.644 - 0.003ms returns 0
T0818 002:711.649 JLINK_WriteReg(R5, 0x00000000)
T0818 002:711.652 - 0.003ms returns 0
T0818 002:711.656 JLINK_WriteReg(R6, 0x00000000)
T0818 002:711.659 - 0.003ms returns 0
T0818 002:711.663 JLINK_WriteReg(R7, 0x00000000)
T0818 002:711.667 - 0.003ms returns 0
T0818 002:711.671 JLINK_WriteReg(R8, 0x00000000)
T0818 002:711.674 - 0.003ms returns 0
T0818 002:711.678 JLINK_WriteReg(R9, 0x20000180)
T0818 002:711.681 - 0.003ms returns 0
T0818 002:711.685 JLINK_WriteReg(R10, 0x00000000)
T0818 002:711.689 - 0.003ms returns 0
T0818 002:711.693 JLINK_WriteReg(R11, 0x00000000)
T0818 002:711.696 - 0.003ms returns 0
T0818 002:711.700 JLINK_WriteReg(R12, 0x00000000)
T0818 002:711.704 - 0.003ms returns 0
T0818 002:711.708 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:711.711 - 0.003ms returns 0
T0818 002:711.715 JLINK_WriteReg(R14, 0x20000001)
T0818 002:711.718 - 0.003ms returns 0
T0818 002:711.723 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:711.726 - 0.003ms returns 0
T0818 002:711.734 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:711.737 - 0.003ms returns 0
T0818 002:711.742 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:711.745 - 0.003ms returns 0
T0818 002:711.749 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:711.752 - 0.003ms returns 0
T0818 002:711.756 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:711.760 - 0.003ms returns 0
T0818 002:711.764 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:711.768 - 0.004ms returns 0x00000028
T0818 002:711.772 JLINK_Go()
T0818 002:711.779   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:714.551 - 2.778ms 
T0818 002:714.557 JLINK_IsHalted()
T0818 002:714.998 - 0.440ms returns FALSE
T0818 002:715.003 JLINK_HasError()
T0818 002:716.997 JLINK_IsHalted()
T0818 002:717.471 - 0.473ms returns FALSE
T0818 002:717.477 JLINK_HasError()
T0818 002:718.996 JLINK_IsHalted()
T0818 002:721.287   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:721.799 - 2.803ms returns TRUE
T0818 002:721.809 JLINK_ReadReg(R15 (PC))
T0818 002:721.814 - 0.004ms returns 0x20000000
T0818 002:721.818 JLINK_ClrBPEx(BPHandle = 0x00000028)
T0818 002:721.822 - 0.003ms returns 0x00
T0818 002:721.826 JLINK_ReadReg(R0)
T0818 002:721.830 - 0.003ms returns 0x00000000
T0818 002:722.140 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:722.148   Data:  A8 16 68 3F A3 C6 D8 BE 2A EC 67 3F B9 7C D9 BE ...
T0818 002:722.157   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:724.744 - 2.604ms returns 0x27C
T0818 002:724.756 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:724.760   Data:  36 A1 07 BF 6A 1E 59 3F 6F F6 07 BF 17 E9 58 3F ...
T0818 002:724.768   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:726.593 - 1.837ms returns 0x184
T0818 002:726.600 JLINK_HasError()
T0818 002:726.604 JLINK_WriteReg(R0, 0x08007400)
T0818 002:726.609 - 0.004ms returns 0
T0818 002:726.613 JLINK_WriteReg(R1, 0x00000400)
T0818 002:726.616 - 0.003ms returns 0
T0818 002:726.620 JLINK_WriteReg(R2, 0x20000184)
T0818 002:726.624 - 0.003ms returns 0
T0818 002:726.628 JLINK_WriteReg(R3, 0x00000000)
T0818 002:726.631 - 0.003ms returns 0
T0818 002:726.635 JLINK_WriteReg(R4, 0x00000000)
T0818 002:726.638 - 0.003ms returns 0
T0818 002:726.642 JLINK_WriteReg(R5, 0x00000000)
T0818 002:726.646 - 0.003ms returns 0
T0818 002:726.650 JLINK_WriteReg(R6, 0x00000000)
T0818 002:726.653 - 0.003ms returns 0
T0818 002:726.658 JLINK_WriteReg(R7, 0x00000000)
T0818 002:726.661 - 0.003ms returns 0
T0818 002:726.665 JLINK_WriteReg(R8, 0x00000000)
T0818 002:726.668 - 0.003ms returns 0
T0818 002:726.672 JLINK_WriteReg(R9, 0x20000180)
T0818 002:726.676 - 0.003ms returns 0
T0818 002:726.680 JLINK_WriteReg(R10, 0x00000000)
T0818 002:726.683 - 0.003ms returns 0
T0818 002:726.687 JLINK_WriteReg(R11, 0x00000000)
T0818 002:726.690 - 0.003ms returns 0
T0818 002:726.694 JLINK_WriteReg(R12, 0x00000000)
T0818 002:726.698 - 0.003ms returns 0
T0818 002:726.702 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:726.706 - 0.004ms returns 0
T0818 002:726.710 JLINK_WriteReg(R14, 0x20000001)
T0818 002:726.713 - 0.003ms returns 0
T0818 002:726.717 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:726.721 - 0.003ms returns 0
T0818 002:726.725 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:726.728 - 0.003ms returns 0
T0818 002:726.732 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:726.736 - 0.003ms returns 0
T0818 002:726.740 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:726.743 - 0.003ms returns 0
T0818 002:726.747 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:726.751 - 0.003ms returns 0
T0818 002:726.755 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:726.759 - 0.004ms returns 0x00000029
T0818 002:726.763 JLINK_Go()
T0818 002:726.770   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:729.558 - 2.795ms 
T0818 002:729.564 JLINK_IsHalted()
T0818 002:730.048 - 0.483ms returns FALSE
T0818 002:730.053 JLINK_HasError()
T0818 002:732.998 JLINK_IsHalted()
T0818 002:733.453 - 0.454ms returns FALSE
T0818 002:733.460 JLINK_HasError()
T0818 002:734.996 JLINK_IsHalted()
T0818 002:737.271   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:737.765 - 2.768ms returns TRUE
T0818 002:737.771 JLINK_ReadReg(R15 (PC))
T0818 002:737.775 - 0.004ms returns 0x20000000
T0818 002:737.779 JLINK_ClrBPEx(BPHandle = 0x00000029)
T0818 002:737.783 - 0.003ms returns 0x00
T0818 002:737.787 JLINK_ReadReg(R0)
T0818 002:737.791 - 0.003ms returns 0x00000000
T0818 002:738.077 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:738.084   Data:  90 8D 4E 3F 18 8D 17 BF 1C 52 4E 3F 12 DE 17 BF ...
T0818 002:738.092   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:740.680 - 2.603ms returns 0x27C
T0818 002:740.691 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:740.695   Data:  A5 61 2F BF A4 7C 3A 3F D3 AA 2F BF B7 37 3A 3F ...
T0818 002:740.703   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:742.560 - 1.868ms returns 0x184
T0818 002:742.572 JLINK_HasError()
T0818 002:742.577 JLINK_WriteReg(R0, 0x08007800)
T0818 002:742.582 - 0.005ms returns 0
T0818 002:742.586 JLINK_WriteReg(R1, 0x00000400)
T0818 002:742.590 - 0.003ms returns 0
T0818 002:742.594 JLINK_WriteReg(R2, 0x20000184)
T0818 002:742.597 - 0.003ms returns 0
T0818 002:742.601 JLINK_WriteReg(R3, 0x00000000)
T0818 002:742.605 - 0.003ms returns 0
T0818 002:742.609 JLINK_WriteReg(R4, 0x00000000)
T0818 002:742.612 - 0.003ms returns 0
T0818 002:742.616 JLINK_WriteReg(R5, 0x00000000)
T0818 002:742.620 - 0.003ms returns 0
T0818 002:742.624 JLINK_WriteReg(R6, 0x00000000)
T0818 002:742.627 - 0.003ms returns 0
T0818 002:742.631 JLINK_WriteReg(R7, 0x00000000)
T0818 002:742.634 - 0.003ms returns 0
T0818 002:742.638 JLINK_WriteReg(R8, 0x00000000)
T0818 002:742.642 - 0.003ms returns 0
T0818 002:742.646 JLINK_WriteReg(R9, 0x20000180)
T0818 002:742.649 - 0.003ms returns 0
T0818 002:742.653 JLINK_WriteReg(R10, 0x00000000)
T0818 002:742.657 - 0.003ms returns 0
T0818 002:742.661 JLINK_WriteReg(R11, 0x00000000)
T0818 002:742.664 - 0.003ms returns 0
T0818 002:742.668 JLINK_WriteReg(R12, 0x00000000)
T0818 002:742.672 - 0.003ms returns 0
T0818 002:742.676 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:742.679 - 0.003ms returns 0
T0818 002:742.683 JLINK_WriteReg(R14, 0x20000001)
T0818 002:742.687 - 0.003ms returns 0
T0818 002:742.691 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:742.694 - 0.003ms returns 0
T0818 002:742.698 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:742.702 - 0.003ms returns 0
T0818 002:742.706 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:742.709 - 0.003ms returns 0
T0818 002:742.713 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:742.716 - 0.003ms returns 0
T0818 002:742.720 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:742.724 - 0.003ms returns 0
T0818 002:742.728 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:742.732 - 0.004ms returns 0x0000002A
T0818 002:742.736 JLINK_Go()
T0818 002:742.744   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:745.468 - 2.731ms 
T0818 002:745.480 JLINK_IsHalted()
T0818 002:745.930 - 0.449ms returns FALSE
T0818 002:745.938 JLINK_HasError()
T0818 002:747.791 JLINK_IsHalted()
T0818 002:748.217 - 0.426ms returns FALSE
T0818 002:748.223 JLINK_HasError()
T0818 002:749.789 JLINK_IsHalted()
T0818 002:752.088   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:752.604 - 2.814ms returns TRUE
T0818 002:752.611 JLINK_ReadReg(R15 (PC))
T0818 002:752.615 - 0.004ms returns 0x20000000
T0818 002:752.620 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T0818 002:752.624 - 0.003ms returns 0x00
T0818 002:752.628 JLINK_ReadReg(R0)
T0818 002:752.631 - 0.003ms returns 0x00000000
T0818 002:752.894 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:752.901   Data:  69 14 2D 3F EC E3 3C BF 49 CA 2C 3F B8 27 3D BF ...
T0818 002:752.909   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:755.515 - 2.620ms returns 0x27C
T0818 002:755.523 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:755.527   Data:  AF 64 50 BF 39 B0 14 3F 03 9F 50 BF 58 5E 14 3F ...
T0818 002:755.535   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:757.431 - 1.907ms returns 0x184
T0818 002:757.436 JLINK_HasError()
T0818 002:757.441 JLINK_WriteReg(R0, 0x08007C00)
T0818 002:757.449 - 0.007ms returns 0
T0818 002:757.453 JLINK_WriteReg(R1, 0x00000400)
T0818 002:757.456 - 0.003ms returns 0
T0818 002:757.460 JLINK_WriteReg(R2, 0x20000184)
T0818 002:757.464 - 0.003ms returns 0
T0818 002:757.468 JLINK_WriteReg(R3, 0x00000000)
T0818 002:757.481 - 0.012ms returns 0
T0818 002:757.485 JLINK_WriteReg(R4, 0x00000000)
T0818 002:757.488 - 0.003ms returns 0
T0818 002:757.492 JLINK_WriteReg(R5, 0x00000000)
T0818 002:757.496 - 0.003ms returns 0
T0818 002:757.503 JLINK_WriteReg(R6, 0x00000000)
T0818 002:757.507 - 0.003ms returns 0
T0818 002:757.511 JLINK_WriteReg(R7, 0x00000000)
T0818 002:757.514 - 0.003ms returns 0
T0818 002:757.518 JLINK_WriteReg(R8, 0x00000000)
T0818 002:757.522 - 0.003ms returns 0
T0818 002:757.526 JLINK_WriteReg(R9, 0x20000180)
T0818 002:757.529 - 0.003ms returns 0
T0818 002:757.533 JLINK_WriteReg(R10, 0x00000000)
T0818 002:757.537 - 0.003ms returns 0
T0818 002:757.541 JLINK_WriteReg(R11, 0x00000000)
T0818 002:757.544 - 0.003ms returns 0
T0818 002:757.548 JLINK_WriteReg(R12, 0x00000000)
T0818 002:757.552 - 0.003ms returns 0
T0818 002:757.556 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:757.559 - 0.003ms returns 0
T0818 002:757.564 JLINK_WriteReg(R14, 0x20000001)
T0818 002:757.567 - 0.003ms returns 0
T0818 002:757.571 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:757.574 - 0.003ms returns 0
T0818 002:757.578 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:757.582 - 0.003ms returns 0
T0818 002:757.586 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:757.589 - 0.003ms returns 0
T0818 002:757.593 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:757.596 - 0.003ms returns 0
T0818 002:757.600 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:757.604 - 0.003ms returns 0
T0818 002:757.608 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:757.612 - 0.004ms returns 0x0000002B
T0818 002:757.616 JLINK_Go()
T0818 002:757.623   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:760.459 - 2.842ms 
T0818 002:760.469 JLINK_IsHalted()
T0818 002:760.960 - 0.490ms returns FALSE
T0818 002:760.972 JLINK_HasError()
T0818 002:762.792 JLINK_IsHalted()
T0818 002:763.300 - 0.508ms returns FALSE
T0818 002:763.306 JLINK_HasError()
T0818 002:764.789 JLINK_IsHalted()
T0818 002:767.159   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:767.640 - 2.850ms returns TRUE
T0818 002:767.646 JLINK_ReadReg(R15 (PC))
T0818 002:767.650 - 0.004ms returns 0x20000000
T0818 002:767.654 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T0818 002:767.658 - 0.003ms returns 0x00
T0818 002:767.662 JLINK_ReadReg(R0)
T0818 002:767.666 - 0.003ms returns 0x00000000
T0818 002:767.950 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:767.957   Data:  84 F4 04 3F 75 F8 5A BF 91 9E 04 3F 79 2C 5B BF ...
T0818 002:767.966   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:770.508 - 2.557ms returns 0x27C
T0818 002:770.519 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:770.523   Data:  91 65 69 BF 09 5A D2 3E CC 8E 69 BF AA A2 D1 3E ...
T0818 002:770.532   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:772.332 - 1.812ms returns 0x184
T0818 002:772.343 JLINK_HasError()
T0818 002:772.373 JLINK_WriteReg(R0, 0x08008000)
T0818 002:772.379 - 0.005ms returns 0
T0818 002:772.383 JLINK_WriteReg(R1, 0x00000400)
T0818 002:772.387 - 0.003ms returns 0
T0818 002:772.391 JLINK_WriteReg(R2, 0x20000184)
T0818 002:772.394 - 0.003ms returns 0
T0818 002:772.398 JLINK_WriteReg(R3, 0x00000000)
T0818 002:772.402 - 0.003ms returns 0
T0818 002:772.406 JLINK_WriteReg(R4, 0x00000000)
T0818 002:772.409 - 0.003ms returns 0
T0818 002:772.413 JLINK_WriteReg(R5, 0x00000000)
T0818 002:772.416 - 0.003ms returns 0
T0818 002:772.420 JLINK_WriteReg(R6, 0x00000000)
T0818 002:772.424 - 0.003ms returns 0
T0818 002:772.428 JLINK_WriteReg(R7, 0x00000000)
T0818 002:772.431 - 0.003ms returns 0
T0818 002:772.435 JLINK_WriteReg(R8, 0x00000000)
T0818 002:772.439 - 0.003ms returns 0
T0818 002:772.443 JLINK_WriteReg(R9, 0x20000180)
T0818 002:772.446 - 0.003ms returns 0
T0818 002:772.450 JLINK_WriteReg(R10, 0x00000000)
T0818 002:772.458 - 0.007ms returns 0
T0818 002:772.462 JLINK_WriteReg(R11, 0x00000000)
T0818 002:772.465 - 0.003ms returns 0
T0818 002:772.469 JLINK_WriteReg(R12, 0x00000000)
T0818 002:772.473 - 0.003ms returns 0
T0818 002:772.477 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:772.480 - 0.003ms returns 0
T0818 002:772.484 JLINK_WriteReg(R14, 0x20000001)
T0818 002:772.488 - 0.003ms returns 0
T0818 002:772.492 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:772.495 - 0.003ms returns 0
T0818 002:772.499 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:772.503 - 0.003ms returns 0
T0818 002:772.507 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:772.510 - 0.003ms returns 0
T0818 002:772.514 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:772.517 - 0.003ms returns 0
T0818 002:772.522 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:772.525 - 0.003ms returns 0
T0818 002:772.529 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:772.534 - 0.004ms returns 0x0000002C
T0818 002:772.538 JLINK_Go()
T0818 002:772.545   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:775.263 - 2.725ms 
T0818 002:775.269 JLINK_IsHalted()
T0818 002:775.726 - 0.456ms returns FALSE
T0818 002:775.731 JLINK_HasError()
T0818 002:776.790 JLINK_IsHalted()
T0818 002:777.264 - 0.474ms returns FALSE
T0818 002:777.270 JLINK_HasError()
T0818 002:778.789 JLINK_IsHalted()
T0818 002:779.248 - 0.459ms returns FALSE
T0818 002:779.254 JLINK_HasError()
T0818 002:780.791 JLINK_IsHalted()
T0818 002:783.111   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:783.618 - 2.827ms returns TRUE
T0818 002:783.625 JLINK_ReadReg(R15 (PC))
T0818 002:783.629 - 0.004ms returns 0x20000000
T0818 002:783.633 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T0818 002:783.637 - 0.004ms returns 0x00
T0818 002:783.641 JLINK_ReadReg(R0)
T0818 002:783.645 - 0.003ms returns 0x00000000
T0818 002:783.902 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:783.908   Data:  3A 71 AF 3E C6 A2 70 BF 49 B4 AE 3E 02 C5 70 BF ...
T0818 002:783.916   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:786.505 - 2.603ms returns 0x27C
T0818 002:786.516 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:786.520   Data:  4E 6E 79 BF 66 7C 66 3E DC 84 79 BF 86 F4 64 3E ...
T0818 002:786.529   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:788.433 - 1.916ms returns 0x184
T0818 002:788.439 JLINK_HasError()
T0818 002:788.444 JLINK_WriteReg(R0, 0x08008400)
T0818 002:788.448 - 0.004ms returns 0
T0818 002:788.452 JLINK_WriteReg(R1, 0x00000400)
T0818 002:788.456 - 0.003ms returns 0
T0818 002:788.460 JLINK_WriteReg(R2, 0x20000184)
T0818 002:788.463 - 0.003ms returns 0
T0818 002:788.467 JLINK_WriteReg(R3, 0x00000000)
T0818 002:788.471 - 0.003ms returns 0
T0818 002:788.475 JLINK_WriteReg(R4, 0x00000000)
T0818 002:788.478 - 0.003ms returns 0
T0818 002:788.482 JLINK_WriteReg(R5, 0x00000000)
T0818 002:788.485 - 0.003ms returns 0
T0818 002:788.489 JLINK_WriteReg(R6, 0x00000000)
T0818 002:788.492 - 0.003ms returns 0
T0818 002:788.497 JLINK_WriteReg(R7, 0x00000000)
T0818 002:788.500 - 0.003ms returns 0
T0818 002:788.504 JLINK_WriteReg(R8, 0x00000000)
T0818 002:788.507 - 0.003ms returns 0
T0818 002:788.511 JLINK_WriteReg(R9, 0x20000180)
T0818 002:788.515 - 0.003ms returns 0
T0818 002:788.518 JLINK_WriteReg(R10, 0x00000000)
T0818 002:788.522 - 0.003ms returns 0
T0818 002:788.526 JLINK_WriteReg(R11, 0x00000000)
T0818 002:788.530 - 0.003ms returns 0
T0818 002:788.533 JLINK_WriteReg(R12, 0x00000000)
T0818 002:788.537 - 0.003ms returns 0
T0818 002:788.541 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:788.544 - 0.003ms returns 0
T0818 002:788.548 JLINK_WriteReg(R14, 0x20000001)
T0818 002:788.552 - 0.003ms returns 0
T0818 002:788.556 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:788.559 - 0.003ms returns 0
T0818 002:788.563 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:788.567 - 0.003ms returns 0
T0818 002:788.571 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:788.574 - 0.003ms returns 0
T0818 002:788.578 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:788.581 - 0.003ms returns 0
T0818 002:788.588 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:788.592 - 0.003ms returns 0
T0818 002:788.596 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:788.600 - 0.004ms returns 0x0000002D
T0818 002:788.604 JLINK_Go()
T0818 002:788.610   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:791.558 - 2.954ms 
T0818 002:791.565 JLINK_IsHalted()
T0818 002:791.991 - 0.425ms returns FALSE
T0818 002:791.996 JLINK_HasError()
T0818 002:793.790 JLINK_IsHalted()
T0818 002:794.243 - 0.452ms returns FALSE
T0818 002:794.248 JLINK_HasError()
T0818 002:795.789 JLINK_IsHalted()
T0818 002:798.087   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:798.577 - 2.787ms returns TRUE
T0818 002:798.586 JLINK_ReadReg(R15 (PC))
T0818 002:798.590 - 0.004ms returns 0x20000000
T0818 002:798.616 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T0818 002:798.621 - 0.004ms returns 0x00
T0818 002:798.625 JLINK_ReadReg(R0)
T0818 002:798.629 - 0.003ms returns 0x00000000
T0818 002:798.924 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:798.931   Data:  DE 76 1C 3E BC 0D 7D BF 6B E9 1A 3E DD 1C 7D BF ...
T0818 002:798.939   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:801.517 - 2.592ms returns 0x27C
T0818 002:801.529 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:801.533   Data:  29 E1 7F BF B9 49 FB 3C 2B E4 7F BF 2D BA EE 3C ...
T0818 002:801.542   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:803.477 - 1.947ms returns 0x184
T0818 002:803.489 JLINK_HasError()
T0818 002:803.495 JLINK_WriteReg(R0, 0x08008800)
T0818 002:803.500 - 0.005ms returns 0
T0818 002:803.504 JLINK_WriteReg(R1, 0x00000400)
T0818 002:803.508 - 0.003ms returns 0
T0818 002:803.512 JLINK_WriteReg(R2, 0x20000184)
T0818 002:803.515 - 0.003ms returns 0
T0818 002:803.520 JLINK_WriteReg(R3, 0x00000000)
T0818 002:803.523 - 0.003ms returns 0
T0818 002:803.527 JLINK_WriteReg(R4, 0x00000000)
T0818 002:803.531 - 0.003ms returns 0
T0818 002:803.535 JLINK_WriteReg(R5, 0x00000000)
T0818 002:803.538 - 0.003ms returns 0
T0818 002:803.542 JLINK_WriteReg(R6, 0x00000000)
T0818 002:803.545 - 0.003ms returns 0
T0818 002:803.550 JLINK_WriteReg(R7, 0x00000000)
T0818 002:803.553 - 0.003ms returns 0
T0818 002:803.557 JLINK_WriteReg(R8, 0x00000000)
T0818 002:803.560 - 0.003ms returns 0
T0818 002:803.564 JLINK_WriteReg(R9, 0x20000180)
T0818 002:803.568 - 0.003ms returns 0
T0818 002:803.572 JLINK_WriteReg(R10, 0x00000000)
T0818 002:803.575 - 0.003ms returns 0
T0818 002:803.579 JLINK_WriteReg(R11, 0x00000000)
T0818 002:803.583 - 0.003ms returns 0
T0818 002:803.587 JLINK_WriteReg(R12, 0x00000000)
T0818 002:803.590 - 0.003ms returns 0
T0818 002:803.594 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:803.598 - 0.003ms returns 0
T0818 002:803.602 JLINK_WriteReg(R14, 0x20000001)
T0818 002:803.606 - 0.003ms returns 0
T0818 002:803.610 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:803.613 - 0.003ms returns 0
T0818 002:803.617 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:803.621 - 0.003ms returns 0
T0818 002:803.625 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:803.628 - 0.003ms returns 0
T0818 002:803.632 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:803.636 - 0.003ms returns 0
T0818 002:803.640 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:803.643 - 0.003ms returns 0
T0818 002:803.647 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:803.652 - 0.004ms returns 0x0000002E
T0818 002:803.656 JLINK_Go()
T0818 002:803.666   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:806.519 - 2.863ms 
T0818 002:806.526 JLINK_IsHalted()
T0818 002:807.012 - 0.486ms returns FALSE
T0818 002:807.019 JLINK_HasError()
T0818 002:808.792 JLINK_IsHalted()
T0818 002:809.286 - 0.493ms returns FALSE
T0818 002:809.293 JLINK_HasError()
T0818 002:810.801 JLINK_IsHalted()
T0818 002:813.112   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:813.575 - 2.774ms returns TRUE
T0818 002:813.582 JLINK_ReadReg(R15 (PC))
T0818 002:813.586 - 0.004ms returns 0x20000000
T0818 002:813.591 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T0818 002:813.595 - 0.003ms returns 0x00
T0818 002:813.601 JLINK_ReadReg(R0)
T0818 002:813.607 - 0.005ms returns 0x00000000
T0818 002:813.962 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:813.969   Data:  07 E0 2F BD 2A BF 7F BF FC 26 36 BD 9E BA 7F BF ...
T0818 002:813.978   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:816.512 - 2.550ms returns 0x27C
T0818 002:816.518 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:816.522   Data:  B0 7E 7C BF FC DE 28 BE 08 6E 7C BF 8D 6B 2A BE ...
T0818 002:816.528   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:818.440 - 1.921ms returns 0x184
T0818 002:818.452 JLINK_HasError()
T0818 002:818.457 JLINK_WriteReg(R0, 0x08008C00)
T0818 002:818.463 - 0.005ms returns 0
T0818 002:818.467 JLINK_WriteReg(R1, 0x00000400)
T0818 002:818.471 - 0.003ms returns 0
T0818 002:818.475 JLINK_WriteReg(R2, 0x20000184)
T0818 002:818.478 - 0.003ms returns 0
T0818 002:818.482 JLINK_WriteReg(R3, 0x00000000)
T0818 002:818.485 - 0.003ms returns 0
T0818 002:818.489 JLINK_WriteReg(R4, 0x00000000)
T0818 002:818.493 - 0.003ms returns 0
T0818 002:818.497 JLINK_WriteReg(R5, 0x00000000)
T0818 002:818.500 - 0.003ms returns 0
T0818 002:818.504 JLINK_WriteReg(R6, 0x00000000)
T0818 002:818.508 - 0.003ms returns 0
T0818 002:818.512 JLINK_WriteReg(R7, 0x00000000)
T0818 002:818.515 - 0.003ms returns 0
T0818 002:818.519 JLINK_WriteReg(R8, 0x00000000)
T0818 002:818.522 - 0.003ms returns 0
T0818 002:818.526 JLINK_WriteReg(R9, 0x20000180)
T0818 002:818.530 - 0.003ms returns 0
T0818 002:818.534 JLINK_WriteReg(R10, 0x00000000)
T0818 002:818.537 - 0.003ms returns 0
T0818 002:818.541 JLINK_WriteReg(R11, 0x00000000)
T0818 002:818.545 - 0.003ms returns 0
T0818 002:818.549 JLINK_WriteReg(R12, 0x00000000)
T0818 002:818.552 - 0.003ms returns 0
T0818 002:818.556 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:818.560 - 0.004ms returns 0
T0818 002:818.564 JLINK_WriteReg(R14, 0x20000001)
T0818 002:818.568 - 0.003ms returns 0
T0818 002:818.572 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:818.575 - 0.003ms returns 0
T0818 002:818.579 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:818.583 - 0.003ms returns 0
T0818 002:818.587 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:818.590 - 0.003ms returns 0
T0818 002:818.594 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:818.598 - 0.003ms returns 0
T0818 002:818.602 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:818.605 - 0.003ms returns 0
T0818 002:818.610 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:818.615 - 0.004ms returns 0x0000002F
T0818 002:818.619 JLINK_Go()
T0818 002:818.626   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:821.437 - 2.817ms 
T0818 002:821.450 JLINK_IsHalted()
T0818 002:821.924 - 0.474ms returns FALSE
T0818 002:821.930 JLINK_HasError()
T0818 002:823.790 JLINK_IsHalted()
T0818 002:824.278 - 0.488ms returns FALSE
T0818 002:824.284 JLINK_HasError()
T0818 002:825.790 JLINK_IsHalted()
T0818 002:828.088   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:828.573 - 2.783ms returns TRUE
T0818 002:828.579 JLINK_ReadReg(R15 (PC))
T0818 002:828.583 - 0.004ms returns 0x20000000
T0818 002:828.588 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T0818 002:828.591 - 0.003ms returns 0x00
T0818 002:828.595 JLINK_ReadReg(R0)
T0818 002:828.599 - 0.003ms returns 0x00000000
T0818 002:828.927 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:828.933   Data:  51 B6 72 BE 93 9C 78 BF E8 3C 74 BE 86 84 78 BF ...
T0818 002:828.942   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:831.517 - 2.590ms returns 0x27C
T0818 002:831.530 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:831.534   Data:  30 68 6F BF EC 54 B5 BE 83 44 6F BF E6 10 B6 BE ...
T0818 002:831.542   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:833.431 - 1.901ms returns 0x184
T0818 002:833.437 JLINK_HasError()
T0818 002:833.442 JLINK_WriteReg(R0, 0x08009000)
T0818 002:833.446 - 0.004ms returns 0
T0818 002:833.451 JLINK_WriteReg(R1, 0x00000400)
T0818 002:833.454 - 0.003ms returns 0
T0818 002:833.458 JLINK_WriteReg(R2, 0x20000184)
T0818 002:833.462 - 0.003ms returns 0
T0818 002:833.466 JLINK_WriteReg(R3, 0x00000000)
T0818 002:833.474 - 0.007ms returns 0
T0818 002:833.478 JLINK_WriteReg(R4, 0x00000000)
T0818 002:833.481 - 0.003ms returns 0
T0818 002:833.485 JLINK_WriteReg(R5, 0x00000000)
T0818 002:833.488 - 0.003ms returns 0
T0818 002:833.492 JLINK_WriteReg(R6, 0x00000000)
T0818 002:833.496 - 0.003ms returns 0
T0818 002:833.500 JLINK_WriteReg(R7, 0x00000000)
T0818 002:833.503 - 0.003ms returns 0
T0818 002:833.507 JLINK_WriteReg(R8, 0x00000000)
T0818 002:833.510 - 0.003ms returns 0
T0818 002:833.514 JLINK_WriteReg(R9, 0x20000180)
T0818 002:833.518 - 0.003ms returns 0
T0818 002:833.522 JLINK_WriteReg(R10, 0x00000000)
T0818 002:833.525 - 0.003ms returns 0
T0818 002:833.529 JLINK_WriteReg(R11, 0x00000000)
T0818 002:833.533 - 0.003ms returns 0
T0818 002:833.537 JLINK_WriteReg(R12, 0x00000000)
T0818 002:833.540 - 0.003ms returns 0
T0818 002:833.544 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:833.548 - 0.004ms returns 0
T0818 002:833.552 JLINK_WriteReg(R14, 0x20000001)
T0818 002:833.555 - 0.003ms returns 0
T0818 002:833.559 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:833.563 - 0.003ms returns 0
T0818 002:833.567 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:833.570 - 0.003ms returns 0
T0818 002:833.574 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:833.578 - 0.003ms returns 0
T0818 002:833.582 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:833.585 - 0.003ms returns 0
T0818 002:833.589 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:833.592 - 0.003ms returns 0
T0818 002:833.597 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:833.601 - 0.004ms returns 0x00000030
T0818 002:833.605 JLINK_Go()
T0818 002:833.612   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:836.425 - 2.819ms 
T0818 002:836.434 JLINK_IsHalted()
T0818 002:836.915 - 0.480ms returns FALSE
T0818 002:836.926 JLINK_HasError()
T0818 002:838.792 JLINK_IsHalted()
T0818 002:839.285 - 0.492ms returns FALSE
T0818 002:839.292 JLINK_HasError()
T0818 002:840.795 JLINK_IsHalted()
T0818 002:843.160   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:843.639 - 2.844ms returns TRUE
T0818 002:843.646 JLINK_ReadReg(R15 (PC))
T0818 002:843.650 - 0.004ms returns 0x20000000
T0818 002:843.655 JLINK_ClrBPEx(BPHandle = 0x00000030)
T0818 002:843.658 - 0.003ms returns 0x00
T0818 002:843.663 JLINK_ReadReg(R0)
T0818 002:843.666 - 0.003ms returns 0x00000000
T0818 002:843.949 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:843.957   Data:  6B 10 D8 BE 2A EC 67 BF A3 C6 D8 BE 87 C1 67 BF ...
T0818 002:843.966   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:846.500 - 2.550ms returns 0x27C
T0818 002:846.507 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:846.511   Data:  6A 1E 59 BF 36 A1 07 BF 17 E9 58 BF 6F F6 07 BF ...
T0818 002:846.519   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:848.424 - 1.917ms returns 0x184
T0818 002:848.430 JLINK_HasError()
T0818 002:848.435 JLINK_WriteReg(R0, 0x08009400)
T0818 002:848.439 - 0.004ms returns 0
T0818 002:848.444 JLINK_WriteReg(R1, 0x00000400)
T0818 002:848.447 - 0.003ms returns 0
T0818 002:848.451 JLINK_WriteReg(R2, 0x20000184)
T0818 002:848.454 - 0.003ms returns 0
T0818 002:848.458 JLINK_WriteReg(R3, 0x00000000)
T0818 002:848.462 - 0.003ms returns 0
T0818 002:848.466 JLINK_WriteReg(R4, 0x00000000)
T0818 002:848.470 - 0.003ms returns 0
T0818 002:848.474 JLINK_WriteReg(R5, 0x00000000)
T0818 002:848.477 - 0.003ms returns 0
T0818 002:848.481 JLINK_WriteReg(R6, 0x00000000)
T0818 002:848.484 - 0.003ms returns 0
T0818 002:848.488 JLINK_WriteReg(R7, 0x00000000)
T0818 002:848.492 - 0.003ms returns 0
T0818 002:848.496 JLINK_WriteReg(R8, 0x00000000)
T0818 002:848.499 - 0.003ms returns 0
T0818 002:848.503 JLINK_WriteReg(R9, 0x20000180)
T0818 002:848.506 - 0.003ms returns 0
T0818 002:848.510 JLINK_WriteReg(R10, 0x00000000)
T0818 002:848.514 - 0.003ms returns 0
T0818 002:848.518 JLINK_WriteReg(R11, 0x00000000)
T0818 002:848.521 - 0.003ms returns 0
T0818 002:848.525 JLINK_WriteReg(R12, 0x00000000)
T0818 002:848.529 - 0.003ms returns 0
T0818 002:848.533 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:848.540 - 0.006ms returns 0
T0818 002:848.544 JLINK_WriteReg(R14, 0x20000001)
T0818 002:848.547 - 0.003ms returns 0
T0818 002:848.551 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:848.555 - 0.003ms returns 0
T0818 002:848.559 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:848.562 - 0.003ms returns 0
T0818 002:848.566 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:848.570 - 0.003ms returns 0
T0818 002:848.574 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:848.577 - 0.003ms returns 0
T0818 002:848.581 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:848.584 - 0.003ms returns 0
T0818 002:848.589 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:848.593 - 0.004ms returns 0x00000031
T0818 002:848.597 JLINK_Go()
T0818 002:848.603   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:851.438 - 2.840ms 
T0818 002:851.455 JLINK_IsHalted()
T0818 002:851.892 - 0.437ms returns FALSE
T0818 002:851.903 JLINK_HasError()
T0818 002:853.792 JLINK_IsHalted()
T0818 002:854.262 - 0.469ms returns FALSE
T0818 002:854.269 JLINK_HasError()
T0818 002:855.794 JLINK_IsHalted()
T0818 002:858.134   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:858.618 - 2.824ms returns TRUE
T0818 002:858.624 JLINK_ReadReg(R15 (PC))
T0818 002:858.629 - 0.004ms returns 0x20000000
T0818 002:858.633 JLINK_ClrBPEx(BPHandle = 0x00000031)
T0818 002:858.637 - 0.003ms returns 0x00
T0818 002:858.641 JLINK_ReadReg(R0)
T0818 002:858.645 - 0.003ms returns 0x00000000
T0818 002:858.987 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:858.995   Data:  07 3C 17 BF 1C 52 4E BF 18 8D 17 BF 89 16 4E BF ...
T0818 002:859.005   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:861.608 - 2.620ms returns 0x27C
T0818 002:861.621 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:861.625   Data:  A4 7C 3A BF A5 61 2F BF B7 37 3A BF D3 AA 2F BF ...
T0818 002:861.634   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:863.512 - 1.892ms returns 0x184
T0818 002:863.519 JLINK_HasError()
T0818 002:863.524 JLINK_WriteReg(R0, 0x08009800)
T0818 002:863.528 - 0.004ms returns 0
T0818 002:863.532 JLINK_WriteReg(R1, 0x00000400)
T0818 002:863.536 - 0.003ms returns 0
T0818 002:863.540 JLINK_WriteReg(R2, 0x20000184)
T0818 002:863.544 - 0.003ms returns 0
T0818 002:863.548 JLINK_WriteReg(R3, 0x00000000)
T0818 002:863.552 - 0.003ms returns 0
T0818 002:863.556 JLINK_WriteReg(R4, 0x00000000)
T0818 002:863.559 - 0.003ms returns 0
T0818 002:863.563 JLINK_WriteReg(R5, 0x00000000)
T0818 002:863.566 - 0.003ms returns 0
T0818 002:863.570 JLINK_WriteReg(R6, 0x00000000)
T0818 002:863.574 - 0.003ms returns 0
T0818 002:863.578 JLINK_WriteReg(R7, 0x00000000)
T0818 002:863.581 - 0.003ms returns 0
T0818 002:863.585 JLINK_WriteReg(R8, 0x00000000)
T0818 002:863.589 - 0.003ms returns 0
T0818 002:863.593 JLINK_WriteReg(R9, 0x20000180)
T0818 002:863.596 - 0.003ms returns 0
T0818 002:863.600 JLINK_WriteReg(R10, 0x00000000)
T0818 002:863.603 - 0.003ms returns 0
T0818 002:863.608 JLINK_WriteReg(R11, 0x00000000)
T0818 002:863.611 - 0.003ms returns 0
T0818 002:863.615 JLINK_WriteReg(R12, 0x00000000)
T0818 002:863.618 - 0.003ms returns 0
T0818 002:863.622 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:863.626 - 0.003ms returns 0
T0818 002:863.630 JLINK_WriteReg(R14, 0x20000001)
T0818 002:863.633 - 0.003ms returns 0
T0818 002:863.638 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:863.641 - 0.003ms returns 0
T0818 002:863.645 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:863.649 - 0.003ms returns 0
T0818 002:863.653 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:863.656 - 0.003ms returns 0
T0818 002:863.660 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:863.663 - 0.003ms returns 0
T0818 002:863.668 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:863.671 - 0.003ms returns 0
T0818 002:863.675 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:863.679 - 0.004ms returns 0x00000032
T0818 002:863.683 JLINK_Go()
T0818 002:863.690   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:866.346 - 2.662ms 
T0818 002:866.355 JLINK_IsHalted()
T0818 002:866.811 - 0.455ms returns FALSE
T0818 002:866.818 JLINK_HasError()
T0818 002:868.790 JLINK_IsHalted()
T0818 002:869.278 - 0.488ms returns FALSE
T0818 002:869.284 JLINK_HasError()
T0818 002:870.791 JLINK_IsHalted()
T0818 002:873.062   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:873.603 - 2.811ms returns TRUE
T0818 002:873.609 JLINK_ReadReg(R15 (PC))
T0818 002:873.613 - 0.004ms returns 0x20000000
T0818 002:873.618 JLINK_ClrBPEx(BPHandle = 0x00000032)
T0818 002:873.621 - 0.003ms returns 0x00
T0818 002:873.625 JLINK_ReadReg(R0)
T0818 002:873.629 - 0.003ms returns 0x00000000
T0818 002:873.932 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:873.940   Data:  03 A0 3C BF 49 CA 2C BF EC E3 3C BF 0F 80 2C BF ...
T0818 002:873.949   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:876.557 - 2.624ms returns 0x27C
T0818 002:876.563 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:876.567   Data:  39 B0 14 BF AF 64 50 BF 58 5E 14 BF 03 9F 50 BF ...
T0818 002:876.573   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:878.432 - 1.869ms returns 0x184
T0818 002:878.442 JLINK_HasError()
T0818 002:878.474 JLINK_WriteReg(R0, 0x08009C00)
T0818 002:878.479 - 0.005ms returns 0
T0818 002:878.483 JLINK_WriteReg(R1, 0x00000400)
T0818 002:878.487 - 0.003ms returns 0
T0818 002:878.491 JLINK_WriteReg(R2, 0x20000184)
T0818 002:878.494 - 0.003ms returns 0
T0818 002:878.498 JLINK_WriteReg(R3, 0x00000000)
T0818 002:878.502 - 0.003ms returns 0
T0818 002:878.506 JLINK_WriteReg(R4, 0x00000000)
T0818 002:878.509 - 0.003ms returns 0
T0818 002:878.513 JLINK_WriteReg(R5, 0x00000000)
T0818 002:878.517 - 0.003ms returns 0
T0818 002:878.521 JLINK_WriteReg(R6, 0x00000000)
T0818 002:878.524 - 0.003ms returns 0
T0818 002:878.528 JLINK_WriteReg(R7, 0x00000000)
T0818 002:878.531 - 0.003ms returns 0
T0818 002:878.535 JLINK_WriteReg(R8, 0x00000000)
T0818 002:878.539 - 0.003ms returns 0
T0818 002:878.543 JLINK_WriteReg(R9, 0x20000180)
T0818 002:878.546 - 0.003ms returns 0
T0818 002:878.550 JLINK_WriteReg(R10, 0x00000000)
T0818 002:878.554 - 0.003ms returns 0
T0818 002:878.558 JLINK_WriteReg(R11, 0x00000000)
T0818 002:878.561 - 0.003ms returns 0
T0818 002:878.565 JLINK_WriteReg(R12, 0x00000000)
T0818 002:878.568 - 0.003ms returns 0
T0818 002:878.572 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:878.576 - 0.003ms returns 0
T0818 002:878.580 JLINK_WriteReg(R14, 0x20000001)
T0818 002:878.584 - 0.003ms returns 0
T0818 002:878.588 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:878.591 - 0.003ms returns 0
T0818 002:878.595 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:878.598 - 0.003ms returns 0
T0818 002:878.602 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:878.606 - 0.003ms returns 0
T0818 002:878.610 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:878.613 - 0.003ms returns 0
T0818 002:878.617 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:878.620 - 0.003ms returns 0
T0818 002:878.625 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:878.629 - 0.004ms returns 0x00000033
T0818 002:878.633 JLINK_Go()
T0818 002:878.640   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:881.454 - 2.820ms 
T0818 002:881.470 JLINK_IsHalted()
T0818 002:881.974 - 0.504ms returns FALSE
T0818 002:881.988 JLINK_HasError()
T0818 002:883.792 JLINK_IsHalted()
T0818 002:884.269 - 0.476ms returns FALSE
T0818 002:884.276 JLINK_HasError()
T0818 002:886.798 JLINK_IsHalted()
T0818 002:889.130   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:889.603 - 2.805ms returns TRUE
T0818 002:889.611 JLINK_ReadReg(R15 (PC))
T0818 002:889.618 - 0.006ms returns 0x20000000
T0818 002:889.623 JLINK_ClrBPEx(BPHandle = 0x00000033)
T0818 002:889.627 - 0.003ms returns 0x00
T0818 002:889.631 JLINK_ReadReg(R0)
T0818 002:889.635 - 0.003ms returns 0x00000000
T0818 002:890.008 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:890.015   Data:  50 C4 5A BF 91 9E 04 BF 75 F8 5A BF 89 48 04 BF ...
T0818 002:890.027   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:892.613 - 2.605ms returns 0x27C
T0818 002:892.632 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:892.640   Data:  09 5A D2 BE 91 65 69 BF AA A2 D1 BE CC 8E 69 BF ...
T0818 002:892.654   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:894.514 - 1.882ms returns 0x184
T0818 002:894.521 JLINK_HasError()
T0818 002:894.527 JLINK_WriteReg(R0, 0x0800A000)
T0818 002:894.531 - 0.005ms returns 0
T0818 002:894.535 JLINK_WriteReg(R1, 0x00000400)
T0818 002:894.539 - 0.003ms returns 0
T0818 002:894.543 JLINK_WriteReg(R2, 0x20000184)
T0818 002:894.546 - 0.003ms returns 0
T0818 002:894.550 JLINK_WriteReg(R3, 0x00000000)
T0818 002:894.554 - 0.003ms returns 0
T0818 002:894.558 JLINK_WriteReg(R4, 0x00000000)
T0818 002:894.561 - 0.003ms returns 0
T0818 002:894.566 JLINK_WriteReg(R5, 0x00000000)
T0818 002:894.570 - 0.005ms returns 0
T0818 002:894.574 JLINK_WriteReg(R6, 0x00000000)
T0818 002:894.578 - 0.003ms returns 0
T0818 002:894.582 JLINK_WriteReg(R7, 0x00000000)
T0818 002:894.585 - 0.003ms returns 0
T0818 002:894.589 JLINK_WriteReg(R8, 0x00000000)
T0818 002:894.593 - 0.003ms returns 0
T0818 002:894.597 JLINK_WriteReg(R9, 0x20000180)
T0818 002:894.600 - 0.003ms returns 0
T0818 002:894.604 JLINK_WriteReg(R10, 0x00000000)
T0818 002:894.608 - 0.003ms returns 0
T0818 002:894.612 JLINK_WriteReg(R11, 0x00000000)
T0818 002:894.615 - 0.003ms returns 0
T0818 002:894.619 JLINK_WriteReg(R12, 0x00000000)
T0818 002:894.622 - 0.003ms returns 0
T0818 002:894.627 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:894.630 - 0.003ms returns 0
T0818 002:894.634 JLINK_WriteReg(R14, 0x20000001)
T0818 002:894.638 - 0.003ms returns 0
T0818 002:894.642 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:894.645 - 0.003ms returns 0
T0818 002:894.649 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:894.653 - 0.003ms returns 0
T0818 002:894.657 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:894.660 - 0.003ms returns 0
T0818 002:894.664 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:894.668 - 0.003ms returns 0
T0818 002:894.672 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:894.675 - 0.003ms returns 0
T0818 002:894.680 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:894.684 - 0.004ms returns 0x00000034
T0818 002:894.688 JLINK_Go()
T0818 002:894.696   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:897.502 - 2.812ms 
T0818 002:897.515 JLINK_IsHalted()
T0818 002:898.000 - 0.485ms returns FALSE
T0818 002:898.006 JLINK_HasError()
T0818 002:900.793 JLINK_IsHalted()
T0818 002:901.287 - 0.494ms returns FALSE
T0818 002:901.294 JLINK_HasError()
T0818 002:902.792 JLINK_IsHalted()
T0818 002:905.093   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:905.594 - 2.801ms returns TRUE
T0818 002:905.604 JLINK_ReadReg(R15 (PC))
T0818 002:905.609 - 0.005ms returns 0x20000000
T0818 002:905.638 JLINK_ClrBPEx(BPHandle = 0x00000034)
T0818 002:905.643 - 0.005ms returns 0x00
T0818 002:905.648 JLINK_ReadReg(R0)
T0818 002:905.652 - 0.003ms returns 0x00000000
T0818 002:905.994 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:906.002   Data:  66 80 70 BF 49 B4 AE BE C6 A2 70 BF 3C F7 AD BE ...
T0818 002:906.011   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:908.499 - 2.505ms returns 0x27C
T0818 002:908.506 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:908.510   Data:  66 7C 66 BE 4E 6E 79 BF 86 F4 64 BE DC 84 79 BF ...
T0818 002:908.517   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:910.435 - 1.928ms returns 0x184
T0818 002:910.443 JLINK_HasError()
T0818 002:910.448 JLINK_WriteReg(R0, 0x0800A400)
T0818 002:910.453 - 0.004ms returns 0
T0818 002:910.457 JLINK_WriteReg(R1, 0x00000400)
T0818 002:910.461 - 0.003ms returns 0
T0818 002:910.465 JLINK_WriteReg(R2, 0x20000184)
T0818 002:910.469 - 0.003ms returns 0
T0818 002:910.473 JLINK_WriteReg(R3, 0x00000000)
T0818 002:910.476 - 0.003ms returns 0
T0818 002:910.480 JLINK_WriteReg(R4, 0x00000000)
T0818 002:910.484 - 0.003ms returns 0
T0818 002:910.488 JLINK_WriteReg(R5, 0x00000000)
T0818 002:910.491 - 0.003ms returns 0
T0818 002:910.495 JLINK_WriteReg(R6, 0x00000000)
T0818 002:910.499 - 0.003ms returns 0
T0818 002:910.503 JLINK_WriteReg(R7, 0x00000000)
T0818 002:910.568 - 0.065ms returns 0
T0818 002:910.573 JLINK_WriteReg(R8, 0x00000000)
T0818 002:910.577 - 0.003ms returns 0
T0818 002:910.581 JLINK_WriteReg(R9, 0x20000180)
T0818 002:910.584 - 0.003ms returns 0
T0818 002:910.588 JLINK_WriteReg(R10, 0x00000000)
T0818 002:910.592 - 0.003ms returns 0
T0818 002:910.596 JLINK_WriteReg(R11, 0x00000000)
T0818 002:910.610 - 0.014ms returns 0
T0818 002:910.614 JLINK_WriteReg(R12, 0x00000000)
T0818 002:910.618 - 0.003ms returns 0
T0818 002:910.622 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:910.626 - 0.004ms returns 0
T0818 002:910.630 JLINK_WriteReg(R14, 0x20000001)
T0818 002:910.633 - 0.003ms returns 0
T0818 002:910.638 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:910.641 - 0.003ms returns 0
T0818 002:910.645 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:910.648 - 0.003ms returns 0
T0818 002:910.652 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:910.656 - 0.003ms returns 0
T0818 002:910.660 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:910.663 - 0.003ms returns 0
T0818 002:910.668 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:910.671 - 0.003ms returns 0
T0818 002:910.676 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:910.680 - 0.004ms returns 0x00000035
T0818 002:910.684 JLINK_Go()
T0818 002:910.692   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:913.430 - 2.746ms 
T0818 002:913.439 JLINK_IsHalted()
T0818 002:913.924 - 0.484ms returns FALSE
T0818 002:913.930 JLINK_HasError()
T0818 002:916.793 JLINK_IsHalted()
T0818 002:917.243 - 0.450ms returns FALSE
T0818 002:917.250 JLINK_HasError()
T0818 002:918.790 JLINK_IsHalted()
T0818 002:921.137   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:921.603 - 2.813ms returns TRUE
T0818 002:921.609 JLINK_ReadReg(R15 (PC))
T0818 002:921.614 - 0.004ms returns 0x20000000
T0818 002:921.618 JLINK_ClrBPEx(BPHandle = 0x00000035)
T0818 002:921.622 - 0.003ms returns 0x00
T0818 002:921.626 JLINK_ReadReg(R0)
T0818 002:921.630 - 0.003ms returns 0x00000000
T0818 002:921.933 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:921.941   Data:  73 FE 7C BF 6B E9 1A BE BC 0D 7D BF E0 5B 19 BE ...
T0818 002:921.950   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:924.514 - 2.581ms returns 0x27C
T0818 002:924.521 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:924.524   Data:  B9 49 FB BC 29 E1 7F BF 2D BA EE BC 2B E4 7F BF ...
T0818 002:924.531   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:926.435 - 1.914ms returns 0x184
T0818 002:926.441 JLINK_HasError()
T0818 002:926.446 JLINK_WriteReg(R0, 0x0800A800)
T0818 002:926.450 - 0.004ms returns 0
T0818 002:926.454 JLINK_WriteReg(R1, 0x00000400)
T0818 002:926.457 - 0.003ms returns 0
T0818 002:926.461 JLINK_WriteReg(R2, 0x20000184)
T0818 002:926.465 - 0.003ms returns 0
T0818 002:926.469 JLINK_WriteReg(R3, 0x00000000)
T0818 002:926.473 - 0.004ms returns 0
T0818 002:926.477 JLINK_WriteReg(R4, 0x00000000)
T0818 002:926.480 - 0.003ms returns 0
T0818 002:926.484 JLINK_WriteReg(R5, 0x00000000)
T0818 002:926.488 - 0.003ms returns 0
T0818 002:926.492 JLINK_WriteReg(R6, 0x00000000)
T0818 002:926.495 - 0.003ms returns 0
T0818 002:926.499 JLINK_WriteReg(R7, 0x00000000)
T0818 002:926.503 - 0.003ms returns 0
T0818 002:926.507 JLINK_WriteReg(R8, 0x00000000)
T0818 002:926.510 - 0.003ms returns 0
T0818 002:926.514 JLINK_WriteReg(R9, 0x20000180)
T0818 002:926.517 - 0.003ms returns 0
T0818 002:926.521 JLINK_WriteReg(R10, 0x00000000)
T0818 002:926.525 - 0.003ms returns 0
T0818 002:926.529 JLINK_WriteReg(R11, 0x00000000)
T0818 002:926.532 - 0.003ms returns 0
T0818 002:926.536 JLINK_WriteReg(R12, 0x00000000)
T0818 002:926.540 - 0.003ms returns 0
T0818 002:926.544 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:926.548 - 0.003ms returns 0
T0818 002:926.552 JLINK_WriteReg(R14, 0x20000001)
T0818 002:926.555 - 0.003ms returns 0
T0818 002:926.559 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:926.562 - 0.003ms returns 0
T0818 002:926.567 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:926.570 - 0.003ms returns 0
T0818 002:926.574 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:926.581 - 0.007ms returns 0
T0818 002:926.585 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:926.589 - 0.003ms returns 0
T0818 002:926.593 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:926.596 - 0.003ms returns 0
T0818 002:926.600 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:926.604 - 0.004ms returns 0x00000036
T0818 002:926.609 JLINK_Go()
T0818 002:926.615   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:929.430 - 2.821ms 
T0818 002:929.439 JLINK_IsHalted()
T0818 002:929.924 - 0.485ms returns FALSE
T0818 002:929.930 JLINK_HasError()
T0818 002:932.791 JLINK_IsHalted()
T0818 002:933.288 - 0.496ms returns FALSE
T0818 002:933.294 JLINK_HasError()
T0818 002:934.789 JLINK_IsHalted()
T0818 002:937.090   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:937.594 - 2.804ms returns TRUE
T0818 002:937.600 JLINK_ReadReg(R15 (PC))
T0818 002:937.604 - 0.004ms returns 0x20000000
T0818 002:937.608 JLINK_ClrBPEx(BPHandle = 0x00000036)
T0818 002:937.612 - 0.003ms returns 0x00
T0818 002:937.616 JLINK_ReadReg(R0)
T0818 002:937.620 - 0.003ms returns 0x00000000
T0818 002:937.911 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:937.917   Data:  8F C3 7F BF FC 26 36 3D 2A BF 7F BF D5 6D 3C 3D ...
T0818 002:937.926   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:940.532 - 2.620ms returns 0x27C
T0818 002:940.539 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:940.543   Data:  FC DE 28 3E B0 7E 7C BF 8D 6B 2A 3E 08 6E 7C BF ...
T0818 002:940.551   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:942.434 - 1.894ms returns 0x184
T0818 002:942.447 JLINK_HasError()
T0818 002:942.452 JLINK_WriteReg(R0, 0x0800AC00)
T0818 002:942.458 - 0.006ms returns 0
T0818 002:942.462 JLINK_WriteReg(R1, 0x00000400)
T0818 002:942.466 - 0.003ms returns 0
T0818 002:942.470 JLINK_WriteReg(R2, 0x20000184)
T0818 002:942.474 - 0.003ms returns 0
T0818 002:942.478 JLINK_WriteReg(R3, 0x00000000)
T0818 002:942.481 - 0.003ms returns 0
T0818 002:942.486 JLINK_WriteReg(R4, 0x00000000)
T0818 002:942.489 - 0.003ms returns 0
T0818 002:942.494 JLINK_WriteReg(R5, 0x00000000)
T0818 002:942.497 - 0.003ms returns 0
T0818 002:942.501 JLINK_WriteReg(R6, 0x00000000)
T0818 002:942.504 - 0.003ms returns 0
T0818 002:942.508 JLINK_WriteReg(R7, 0x00000000)
T0818 002:942.512 - 0.003ms returns 0
T0818 002:942.516 JLINK_WriteReg(R8, 0x00000000)
T0818 002:942.519 - 0.003ms returns 0
T0818 002:942.523 JLINK_WriteReg(R9, 0x20000180)
T0818 002:942.526 - 0.003ms returns 0
T0818 002:942.530 JLINK_WriteReg(R10, 0x00000000)
T0818 002:942.534 - 0.003ms returns 0
T0818 002:942.538 JLINK_WriteReg(R11, 0x00000000)
T0818 002:942.541 - 0.003ms returns 0
T0818 002:942.545 JLINK_WriteReg(R12, 0x00000000)
T0818 002:942.549 - 0.003ms returns 0
T0818 002:942.553 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:942.557 - 0.004ms returns 0
T0818 002:942.561 JLINK_WriteReg(R14, 0x20000001)
T0818 002:942.565 - 0.003ms returns 0
T0818 002:942.569 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:942.572 - 0.003ms returns 0
T0818 002:942.576 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:942.580 - 0.003ms returns 0
T0818 002:942.584 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:942.587 - 0.003ms returns 0
T0818 002:942.591 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:942.595 - 0.003ms returns 0
T0818 002:942.599 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:942.602 - 0.003ms returns 0
T0818 002:942.606 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:942.611 - 0.004ms returns 0x00000037
T0818 002:942.615 JLINK_Go()
T0818 002:942.624   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:945.423 - 2.807ms 
T0818 002:945.431 JLINK_IsHalted()
T0818 002:945.937 - 0.505ms returns FALSE
T0818 002:945.943 JLINK_HasError()
T0818 002:949.795 JLINK_IsHalted()
T0818 002:952.098   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:952.608 - 2.812ms returns TRUE
T0818 002:952.616 JLINK_ReadReg(R15 (PC))
T0818 002:952.621 - 0.005ms returns 0x20000000
T0818 002:952.626 JLINK_ClrBPEx(BPHandle = 0x00000037)
T0818 002:952.630 - 0.004ms returns 0x00
T0818 002:952.638 JLINK_ReadReg(R0)
T0818 002:952.641 - 0.004ms returns 0x00000000
T0818 002:952.966 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:952.973   Data:  7B B4 78 BF E8 3C 74 3E 93 9C 78 BF 5A C3 75 3E ...
T0818 002:952.983   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:955.603 - 2.637ms returns 0x27C
T0818 002:955.609 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:955.613   Data:  EC 54 B5 3E 30 68 6F BF E6 10 B6 3E 83 44 6F BF ...
T0818 002:955.619   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:957.454 - 1.845ms returns 0x184
T0818 002:957.465 JLINK_HasError()
T0818 002:957.496 JLINK_WriteReg(R0, 0x0800B000)
T0818 002:957.501 - 0.005ms returns 0
T0818 002:957.506 JLINK_WriteReg(R1, 0x00000400)
T0818 002:957.509 - 0.003ms returns 0
T0818 002:957.513 JLINK_WriteReg(R2, 0x20000184)
T0818 002:957.517 - 0.003ms returns 0
T0818 002:957.521 JLINK_WriteReg(R3, 0x00000000)
T0818 002:957.524 - 0.003ms returns 0
T0818 002:957.529 JLINK_WriteReg(R4, 0x00000000)
T0818 002:957.532 - 0.003ms returns 0
T0818 002:957.536 JLINK_WriteReg(R5, 0x00000000)
T0818 002:957.539 - 0.003ms returns 0
T0818 002:957.543 JLINK_WriteReg(R6, 0x00000000)
T0818 002:957.547 - 0.003ms returns 0
T0818 002:957.551 JLINK_WriteReg(R7, 0x00000000)
T0818 002:957.554 - 0.003ms returns 0
T0818 002:957.558 JLINK_WriteReg(R8, 0x00000000)
T0818 002:957.562 - 0.003ms returns 0
T0818 002:957.566 JLINK_WriteReg(R9, 0x20000180)
T0818 002:957.569 - 0.003ms returns 0
T0818 002:957.573 JLINK_WriteReg(R10, 0x00000000)
T0818 002:957.576 - 0.003ms returns 0
T0818 002:957.580 JLINK_WriteReg(R11, 0x00000000)
T0818 002:957.584 - 0.003ms returns 0
T0818 002:957.588 JLINK_WriteReg(R12, 0x00000000)
T0818 002:957.591 - 0.003ms returns 0
T0818 002:957.595 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:957.599 - 0.003ms returns 0
T0818 002:957.603 JLINK_WriteReg(R14, 0x20000001)
T0818 002:957.606 - 0.003ms returns 0
T0818 002:957.611 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:957.614 - 0.003ms returns 0
T0818 002:957.618 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:957.622 - 0.003ms returns 0
T0818 002:957.626 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:957.629 - 0.003ms returns 0
T0818 002:957.634 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:957.637 - 0.003ms returns 0
T0818 002:957.641 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:957.644 - 0.003ms returns 0
T0818 002:957.649 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:957.653 - 0.004ms returns 0x00000038
T0818 002:957.658 JLINK_Go()
T0818 002:957.665   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:960.438 - 2.780ms 
T0818 002:960.450 JLINK_IsHalted()
T0818 002:960.933 - 0.482ms returns FALSE
T0818 002:960.946 JLINK_HasError()
T0818 002:963.792 JLINK_IsHalted()
T0818 002:964.270 - 0.477ms returns FALSE
T0818 002:964.276 JLINK_HasError()
T0818 002:965.790 JLINK_IsHalted()
T0818 002:968.106   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:968.575 - 2.785ms returns TRUE
T0818 002:968.581 JLINK_ReadReg(R15 (PC))
T0818 002:968.586 - 0.004ms returns 0x20000000
T0818 002:968.590 JLINK_ClrBPEx(BPHandle = 0x00000038)
T0818 002:968.594 - 0.004ms returns 0x00
T0818 002:968.599 JLINK_ReadReg(R0)
T0818 002:968.602 - 0.003ms returns 0x00000000
T0818 002:968.901 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:968.908   Data:  A8 16 68 BF A3 C6 D8 3E 2A EC 67 BF B9 7C D9 3E ...
T0818 002:968.917   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:971.498 - 2.597ms returns 0x27C
T0818 002:971.508 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:971.511   Data:  36 A1 07 3F 6A 1E 59 BF 6F F6 07 3F 17 E9 58 BF ...
T0818 002:971.520   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:973.433 - 1.925ms returns 0x184
T0818 002:973.439 JLINK_HasError()
T0818 002:973.444 JLINK_WriteReg(R0, 0x0800B400)
T0818 002:973.448 - 0.004ms returns 0
T0818 002:973.452 JLINK_WriteReg(R1, 0x00000400)
T0818 002:973.456 - 0.003ms returns 0
T0818 002:973.460 JLINK_WriteReg(R2, 0x20000184)
T0818 002:973.463 - 0.003ms returns 0
T0818 002:973.467 JLINK_WriteReg(R3, 0x00000000)
T0818 002:973.474 - 0.006ms returns 0
T0818 002:973.478 JLINK_WriteReg(R4, 0x00000000)
T0818 002:973.481 - 0.003ms returns 0
T0818 002:973.485 JLINK_WriteReg(R5, 0x00000000)
T0818 002:973.489 - 0.003ms returns 0
T0818 002:973.493 JLINK_WriteReg(R6, 0x00000000)
T0818 002:973.496 - 0.003ms returns 0
T0818 002:973.500 JLINK_WriteReg(R7, 0x00000000)
T0818 002:973.504 - 0.003ms returns 0
T0818 002:973.508 JLINK_WriteReg(R8, 0x00000000)
T0818 002:973.511 - 0.003ms returns 0
T0818 002:973.515 JLINK_WriteReg(R9, 0x20000180)
T0818 002:973.518 - 0.003ms returns 0
T0818 002:973.522 JLINK_WriteReg(R10, 0x00000000)
T0818 002:973.526 - 0.003ms returns 0
T0818 002:973.530 JLINK_WriteReg(R11, 0x00000000)
T0818 002:973.533 - 0.003ms returns 0
T0818 002:973.538 JLINK_WriteReg(R12, 0x00000000)
T0818 002:973.541 - 0.003ms returns 0
T0818 002:973.546 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:973.549 - 0.003ms returns 0
T0818 002:973.553 JLINK_WriteReg(R14, 0x20000001)
T0818 002:973.557 - 0.003ms returns 0
T0818 002:973.561 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:973.564 - 0.003ms returns 0
T0818 002:973.568 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:973.572 - 0.003ms returns 0
T0818 002:973.576 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:973.579 - 0.003ms returns 0
T0818 002:973.583 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:973.587 - 0.003ms returns 0
T0818 002:973.591 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:973.594 - 0.003ms returns 0
T0818 002:973.599 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:973.603 - 0.004ms returns 0x00000039
T0818 002:973.607 JLINK_Go()
T0818 002:973.614   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:976.457 - 2.849ms 
T0818 002:976.474 JLINK_IsHalted()
T0818 002:976.953 - 0.479ms returns FALSE
T0818 002:976.959 JLINK_HasError()
T0818 002:979.791 JLINK_IsHalted()
T0818 002:980.263 - 0.471ms returns FALSE
T0818 002:980.270 JLINK_HasError()
T0818 002:981.791 JLINK_IsHalted()
T0818 002:984.090   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 002:984.595 - 2.803ms returns TRUE
T0818 002:984.605 JLINK_ReadReg(R15 (PC))
T0818 002:984.609 - 0.004ms returns 0x20000000
T0818 002:984.640 JLINK_ClrBPEx(BPHandle = 0x00000039)
T0818 002:984.644 - 0.004ms returns 0x00
T0818 002:984.649 JLINK_ReadReg(R0)
T0818 002:984.653 - 0.003ms returns 0x00000000
T0818 002:985.125 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 002:985.138   Data:  90 8D 4E BF 18 8D 17 3F 1C 52 4E BF 12 DE 17 3F ...
T0818 002:985.149   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 002:987.758 - 2.633ms returns 0x27C
T0818 002:987.774 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 002:987.777   Data:  A5 61 2F 3F A4 7C 3A BF D3 AA 2F 3F B7 37 3A BF ...
T0818 002:987.787   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 002:989.631 - 1.857ms returns 0x184
T0818 002:989.637 JLINK_HasError()
T0818 002:989.642 JLINK_WriteReg(R0, 0x0800B800)
T0818 002:989.646 - 0.004ms returns 0
T0818 002:989.650 JLINK_WriteReg(R1, 0x00000400)
T0818 002:989.654 - 0.003ms returns 0
T0818 002:989.658 JLINK_WriteReg(R2, 0x20000184)
T0818 002:989.661 - 0.003ms returns 0
T0818 002:989.665 JLINK_WriteReg(R3, 0x00000000)
T0818 002:989.668 - 0.003ms returns 0
T0818 002:989.672 JLINK_WriteReg(R4, 0x00000000)
T0818 002:989.676 - 0.003ms returns 0
T0818 002:989.680 JLINK_WriteReg(R5, 0x00000000)
T0818 002:989.683 - 0.003ms returns 0
T0818 002:989.687 JLINK_WriteReg(R6, 0x00000000)
T0818 002:989.691 - 0.003ms returns 0
T0818 002:989.695 JLINK_WriteReg(R7, 0x00000000)
T0818 002:989.698 - 0.003ms returns 0
T0818 002:989.702 JLINK_WriteReg(R8, 0x00000000)
T0818 002:989.706 - 0.003ms returns 0
T0818 002:989.710 JLINK_WriteReg(R9, 0x20000180)
T0818 002:989.713 - 0.003ms returns 0
T0818 002:989.717 JLINK_WriteReg(R10, 0x00000000)
T0818 002:989.721 - 0.003ms returns 0
T0818 002:989.725 JLINK_WriteReg(R11, 0x00000000)
T0818 002:989.728 - 0.003ms returns 0
T0818 002:989.732 JLINK_WriteReg(R12, 0x00000000)
T0818 002:989.736 - 0.003ms returns 0
T0818 002:989.741 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 002:989.748 - 0.006ms returns 0
T0818 002:989.752 JLINK_WriteReg(R14, 0x20000001)
T0818 002:989.755 - 0.003ms returns 0
T0818 002:989.760 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 002:989.763 - 0.003ms returns 0
T0818 002:989.770 JLINK_WriteReg(XPSR, 0x01000000)
T0818 002:989.773 - 0.003ms returns 0
T0818 002:989.777 JLINK_WriteReg(MSP, 0x20001000)
T0818 002:989.781 - 0.003ms returns 0
T0818 002:989.785 JLINK_WriteReg(PSP, 0x20001000)
T0818 002:989.788 - 0.003ms returns 0
T0818 002:989.793 JLINK_WriteReg(CFBP, 0x00000000)
T0818 002:989.804 - 0.010ms returns 0
T0818 002:989.809 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 002:989.813 - 0.004ms returns 0x0000003A
T0818 002:989.817 JLINK_Go()
T0818 002:989.825   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 002:992.513 - 2.695ms 
T0818 002:992.520 JLINK_IsHalted()
T0818 002:993.049 - 0.528ms returns FALSE
T0818 002:993.054 JLINK_HasError()
T0818 002:995.790 JLINK_IsHalted()
T0818 002:996.279 - 0.488ms returns FALSE
T0818 002:996.285 JLINK_HasError()
T0818 002:997.791 JLINK_IsHalted()
T0818 003:000.088   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:000.576 - 2.784ms returns TRUE
T0818 003:000.583 JLINK_ReadReg(R15 (PC))
T0818 003:000.588 - 0.004ms returns 0x20000000
T0818 003:000.592 JLINK_ClrBPEx(BPHandle = 0x0000003A)
T0818 003:000.596 - 0.003ms returns 0x00
T0818 003:000.600 JLINK_ReadReg(R0)
T0818 003:000.604 - 0.003ms returns 0x00000000
T0818 003:000.861 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:000.867   Data:  69 14 2D BF EC E3 3C 3F 49 CA 2C BF B8 27 3D 3F ...
T0818 003:000.876   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:003.499 - 2.637ms returns 0x27C
T0818 003:003.505 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:003.508   Data:  AF 64 50 3F 39 B0 14 BF 03 9F 50 3F 58 5E 14 BF ...
T0818 003:003.515   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:005.344 - 1.839ms returns 0x184
T0818 003:005.349 JLINK_HasError()
T0818 003:005.354 JLINK_WriteReg(R0, 0x0800BC00)
T0818 003:005.358 - 0.004ms returns 0
T0818 003:005.362 JLINK_WriteReg(R1, 0x00000400)
T0818 003:005.366 - 0.003ms returns 0
T0818 003:005.370 JLINK_WriteReg(R2, 0x20000184)
T0818 003:005.373 - 0.003ms returns 0
T0818 003:005.377 JLINK_WriteReg(R3, 0x00000000)
T0818 003:005.381 - 0.003ms returns 0
T0818 003:005.385 JLINK_WriteReg(R4, 0x00000000)
T0818 003:005.388 - 0.003ms returns 0
T0818 003:005.392 JLINK_WriteReg(R5, 0x00000000)
T0818 003:005.395 - 0.003ms returns 0
T0818 003:005.400 JLINK_WriteReg(R6, 0x00000000)
T0818 003:005.403 - 0.003ms returns 0
T0818 003:005.407 JLINK_WriteReg(R7, 0x00000000)
T0818 003:005.412 - 0.004ms returns 0
T0818 003:005.417 JLINK_WriteReg(R8, 0x00000000)
T0818 003:005.421 - 0.003ms returns 0
T0818 003:005.425 JLINK_WriteReg(R9, 0x20000180)
T0818 003:005.428 - 0.003ms returns 0
T0818 003:005.432 JLINK_WriteReg(R10, 0x00000000)
T0818 003:005.436 - 0.003ms returns 0
T0818 003:005.440 JLINK_WriteReg(R11, 0x00000000)
T0818 003:005.443 - 0.003ms returns 0
T0818 003:005.447 JLINK_WriteReg(R12, 0x00000000)
T0818 003:005.450 - 0.003ms returns 0
T0818 003:005.455 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:005.458 - 0.003ms returns 0
T0818 003:005.462 JLINK_WriteReg(R14, 0x20000001)
T0818 003:005.466 - 0.003ms returns 0
T0818 003:005.470 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:005.473 - 0.003ms returns 0
T0818 003:005.477 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:005.481 - 0.003ms returns 0
T0818 003:005.485 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:005.488 - 0.003ms returns 0
T0818 003:005.492 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:005.495 - 0.003ms returns 0
T0818 003:005.499 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:005.503 - 0.003ms returns 0
T0818 003:005.507 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:005.511 - 0.004ms returns 0x0000003B
T0818 003:005.516 JLINK_Go()
T0818 003:005.522   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:008.322 - 2.805ms 
T0818 003:008.330 JLINK_IsHalted()
T0818 003:008.784 - 0.453ms returns FALSE
T0818 003:008.790 JLINK_HasError()
T0818 003:010.792 JLINK_IsHalted()
T0818 003:011.244 - 0.452ms returns FALSE
T0818 003:011.250 JLINK_HasError()
T0818 003:012.790 JLINK_IsHalted()
T0818 003:015.133   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:015.617 - 2.826ms returns TRUE
T0818 003:015.622 JLINK_ReadReg(R15 (PC))
T0818 003:015.627 - 0.004ms returns 0x20000000
T0818 003:015.631 JLINK_ClrBPEx(BPHandle = 0x0000003B)
T0818 003:015.635 - 0.003ms returns 0x00
T0818 003:015.639 JLINK_ReadReg(R0)
T0818 003:015.642 - 0.003ms returns 0x00000000
T0818 003:015.924 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:015.930   Data:  84 F4 04 BF 75 F8 5A 3F 91 9E 04 BF 79 2C 5B 3F ...
T0818 003:015.939   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:018.499 - 2.575ms returns 0x27C
T0818 003:018.505 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:018.509   Data:  91 65 69 3F 09 5A D2 BE CC 8E 69 3F AA A2 D1 BE ...
T0818 003:018.516   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:020.432 - 1.927ms returns 0x184
T0818 003:020.439 JLINK_HasError()
T0818 003:020.444 JLINK_WriteReg(R0, 0x0800C000)
T0818 003:020.448 - 0.004ms returns 0
T0818 003:020.453 JLINK_WriteReg(R1, 0x00000400)
T0818 003:020.456 - 0.003ms returns 0
T0818 003:020.460 JLINK_WriteReg(R2, 0x20000184)
T0818 003:020.464 - 0.003ms returns 0
T0818 003:020.468 JLINK_WriteReg(R3, 0x00000000)
T0818 003:020.471 - 0.003ms returns 0
T0818 003:020.475 JLINK_WriteReg(R4, 0x00000000)
T0818 003:020.478 - 0.003ms returns 0
T0818 003:020.482 JLINK_WriteReg(R5, 0x00000000)
T0818 003:020.486 - 0.003ms returns 0
T0818 003:020.490 JLINK_WriteReg(R6, 0x00000000)
T0818 003:020.493 - 0.003ms returns 0
T0818 003:020.497 JLINK_WriteReg(R7, 0x00000000)
T0818 003:020.501 - 0.003ms returns 0
T0818 003:020.505 JLINK_WriteReg(R8, 0x00000000)
T0818 003:020.508 - 0.003ms returns 0
T0818 003:020.512 JLINK_WriteReg(R9, 0x20000180)
T0818 003:020.515 - 0.003ms returns 0
T0818 003:020.520 JLINK_WriteReg(R10, 0x00000000)
T0818 003:020.523 - 0.003ms returns 0
T0818 003:020.527 JLINK_WriteReg(R11, 0x00000000)
T0818 003:020.530 - 0.003ms returns 0
T0818 003:020.534 JLINK_WriteReg(R12, 0x00000000)
T0818 003:020.538 - 0.003ms returns 0
T0818 003:020.542 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:020.546 - 0.003ms returns 0
T0818 003:020.550 JLINK_WriteReg(R14, 0x20000001)
T0818 003:020.553 - 0.003ms returns 0
T0818 003:020.557 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:020.560 - 0.003ms returns 0
T0818 003:020.564 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:020.568 - 0.003ms returns 0
T0818 003:020.572 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:020.575 - 0.003ms returns 0
T0818 003:020.579 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:020.583 - 0.003ms returns 0
T0818 003:020.598 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:020.602 - 0.003ms returns 0
T0818 003:020.607 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:020.610 - 0.004ms returns 0x0000003C
T0818 003:020.615 JLINK_Go()
T0818 003:020.622   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:023.360 - 2.744ms 
T0818 003:023.369 JLINK_IsHalted()
T0818 003:023.813 - 0.444ms returns FALSE
T0818 003:023.819 JLINK_HasError()
T0818 003:026.791 JLINK_IsHalted()
T0818 003:027.313 - 0.521ms returns FALSE
T0818 003:027.319 JLINK_HasError()
T0818 003:028.789 JLINK_IsHalted()
T0818 003:031.112   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:031.592 - 2.802ms returns TRUE
T0818 003:031.598 JLINK_ReadReg(R15 (PC))
T0818 003:031.602 - 0.004ms returns 0x20000000
T0818 003:031.607 JLINK_ClrBPEx(BPHandle = 0x0000003C)
T0818 003:031.610 - 0.003ms returns 0x00
T0818 003:031.615 JLINK_ReadReg(R0)
T0818 003:031.618 - 0.003ms returns 0x00000000
T0818 003:031.900 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:031.907   Data:  3A 71 AF BE C6 A2 70 3F 49 B4 AE BE 02 C5 70 3F ...
T0818 003:031.916   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:034.425 - 2.525ms returns 0x27C
T0818 003:034.431 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:034.437   Data:  4E 6E 79 3F 66 7C 66 BE DC 84 79 3F 86 F4 64 BE ...
T0818 003:034.444   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:036.286 - 1.854ms returns 0x184
T0818 003:036.297 JLINK_HasError()
T0818 003:036.836 JLINK_WriteReg(R0, 0x0800C400)
T0818 003:036.842 - 0.006ms returns 0
T0818 003:036.846 JLINK_WriteReg(R1, 0x00000400)
T0818 003:036.850 - 0.003ms returns 0
T0818 003:036.854 JLINK_WriteReg(R2, 0x20000184)
T0818 003:036.857 - 0.003ms returns 0
T0818 003:036.861 JLINK_WriteReg(R3, 0x00000000)
T0818 003:036.865 - 0.003ms returns 0
T0818 003:036.869 JLINK_WriteReg(R4, 0x00000000)
T0818 003:036.872 - 0.003ms returns 0
T0818 003:036.876 JLINK_WriteReg(R5, 0x00000000)
T0818 003:036.880 - 0.003ms returns 0
T0818 003:036.884 JLINK_WriteReg(R6, 0x00000000)
T0818 003:036.887 - 0.003ms returns 0
T0818 003:036.891 JLINK_WriteReg(R7, 0x00000000)
T0818 003:036.894 - 0.003ms returns 0
T0818 003:036.899 JLINK_WriteReg(R8, 0x00000000)
T0818 003:036.902 - 0.004ms returns 0
T0818 003:036.906 JLINK_WriteReg(R9, 0x20000180)
T0818 003:036.910 - 0.003ms returns 0
T0818 003:036.914 JLINK_WriteReg(R10, 0x00000000)
T0818 003:036.917 - 0.003ms returns 0
T0818 003:036.921 JLINK_WriteReg(R11, 0x00000000)
T0818 003:036.925 - 0.003ms returns 0
T0818 003:036.929 JLINK_WriteReg(R12, 0x00000000)
T0818 003:036.932 - 0.003ms returns 0
T0818 003:036.936 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:036.940 - 0.003ms returns 0
T0818 003:036.944 JLINK_WriteReg(R14, 0x20000001)
T0818 003:036.947 - 0.003ms returns 0
T0818 003:036.952 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:036.955 - 0.003ms returns 0
T0818 003:036.959 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:036.962 - 0.003ms returns 0
T0818 003:036.966 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:036.970 - 0.003ms returns 0
T0818 003:036.974 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:036.977 - 0.003ms returns 0
T0818 003:036.981 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:036.985 - 0.003ms returns 0
T0818 003:036.989 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:036.993 - 0.004ms returns 0x0000003D
T0818 003:036.997 JLINK_Go()
T0818 003:037.005   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:039.728 - 2.730ms 
T0818 003:039.734 JLINK_IsHalted()
T0818 003:040.191 - 0.457ms returns FALSE
T0818 003:040.198 JLINK_HasError()
T0818 003:042.791 JLINK_IsHalted()
T0818 003:043.238 - 0.447ms returns FALSE
T0818 003:043.245 JLINK_HasError()
T0818 003:044.790 JLINK_IsHalted()
T0818 003:047.090   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:047.594 - 2.804ms returns TRUE
T0818 003:047.600 JLINK_ReadReg(R15 (PC))
T0818 003:047.604 - 0.004ms returns 0x20000000
T0818 003:047.608 JLINK_ClrBPEx(BPHandle = 0x0000003D)
T0818 003:047.612 - 0.003ms returns 0x00
T0818 003:047.616 JLINK_ReadReg(R0)
T0818 003:047.620 - 0.003ms returns 0x00000000
T0818 003:047.975 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T0818 003:047.983   Data:  DE 76 1C BE BC 0D 7D 3F 6B E9 1A BE DD 1C 7D 3F ...
T0818 003:047.991   CPU_WriteMem(636 bytes @ 0x20000184)
T0818 003:050.524 - 2.542ms returns 0x27C
T0818 003:050.534 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T0818 003:050.538   Data:  29 E1 7F 3F B9 49 FB BC 2B E4 7F 3F 2D BA EE BC ...
T0818 003:050.546   CPU_WriteMem(388 bytes @ 0x20000400)
T0818 003:052.454 - 1.919ms returns 0x184
T0818 003:052.467 JLINK_HasError()
T0818 003:052.472 JLINK_WriteReg(R0, 0x0800C800)
T0818 003:052.477 - 0.005ms returns 0
T0818 003:052.482 JLINK_WriteReg(R1, 0x00000360)
T0818 003:052.485 - 0.003ms returns 0
T0818 003:052.489 JLINK_WriteReg(R2, 0x20000184)
T0818 003:052.493 - 0.003ms returns 0
T0818 003:052.497 JLINK_WriteReg(R3, 0x00000000)
T0818 003:052.500 - 0.003ms returns 0
T0818 003:052.504 JLINK_WriteReg(R4, 0x00000000)
T0818 003:052.507 - 0.003ms returns 0
T0818 003:052.511 JLINK_WriteReg(R5, 0x00000000)
T0818 003:052.515 - 0.003ms returns 0
T0818 003:052.519 JLINK_WriteReg(R6, 0x00000000)
T0818 003:052.522 - 0.003ms returns 0
T0818 003:052.526 JLINK_WriteReg(R7, 0x00000000)
T0818 003:052.532 - 0.005ms returns 0
T0818 003:052.536 JLINK_WriteReg(R8, 0x00000000)
T0818 003:052.539 - 0.003ms returns 0
T0818 003:052.543 JLINK_WriteReg(R9, 0x20000180)
T0818 003:052.546 - 0.003ms returns 0
T0818 003:052.550 JLINK_WriteReg(R10, 0x00000000)
T0818 003:052.554 - 0.003ms returns 0
T0818 003:052.558 JLINK_WriteReg(R11, 0x00000000)
T0818 003:052.561 - 0.003ms returns 0
T0818 003:052.565 JLINK_WriteReg(R12, 0x00000000)
T0818 003:052.569 - 0.003ms returns 0
T0818 003:052.573 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:052.577 - 0.003ms returns 0
T0818 003:052.581 JLINK_WriteReg(R14, 0x20000001)
T0818 003:052.584 - 0.003ms returns 0
T0818 003:052.588 JLINK_WriteReg(R15 (PC), 0x2000010C)
T0818 003:052.592 - 0.003ms returns 0
T0818 003:052.596 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:052.599 - 0.003ms returns 0
T0818 003:052.603 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:052.606 - 0.003ms returns 0
T0818 003:052.610 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:052.614 - 0.003ms returns 0
T0818 003:052.618 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:052.621 - 0.003ms returns 0
T0818 003:052.625 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:052.629 - 0.004ms returns 0x0000003E
T0818 003:052.634 JLINK_Go()
T0818 003:052.641   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:055.321 - 2.687ms 
T0818 003:055.327 JLINK_IsHalted()
T0818 003:055.799 - 0.471ms returns FALSE
T0818 003:055.804 JLINK_HasError()
T0818 003:058.792 JLINK_IsHalted()
T0818 003:061.063   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:061.602 - 2.810ms returns TRUE
T0818 003:061.608 JLINK_ReadReg(R15 (PC))
T0818 003:061.613 - 0.004ms returns 0x20000000
T0818 003:061.617 JLINK_ClrBPEx(BPHandle = 0x0000003E)
T0818 003:061.621 - 0.003ms returns 0x00
T0818 003:061.625 JLINK_ReadReg(R0)
T0818 003:061.628 - 0.003ms returns 0x00000000
T0818 003:061.633 JLINK_HasError()
T0818 003:061.637 JLINK_WriteReg(R0, 0x00000002)
T0818 003:061.641 - 0.003ms returns 0
T0818 003:061.645 JLINK_WriteReg(R1, 0x00000360)
T0818 003:061.648 - 0.003ms returns 0
T0818 003:061.652 JLINK_WriteReg(R2, 0x20000184)
T0818 003:061.656 - 0.003ms returns 0
T0818 003:061.660 JLINK_WriteReg(R3, 0x00000000)
T0818 003:061.663 - 0.003ms returns 0
T0818 003:061.667 JLINK_WriteReg(R4, 0x00000000)
T0818 003:061.670 - 0.003ms returns 0
T0818 003:061.674 JLINK_WriteReg(R5, 0x00000000)
T0818 003:061.678 - 0.003ms returns 0
T0818 003:061.682 JLINK_WriteReg(R6, 0x00000000)
T0818 003:061.685 - 0.003ms returns 0
T0818 003:061.689 JLINK_WriteReg(R7, 0x00000000)
T0818 003:061.692 - 0.003ms returns 0
T0818 003:061.696 JLINK_WriteReg(R8, 0x00000000)
T0818 003:061.700 - 0.003ms returns 0
T0818 003:061.704 JLINK_WriteReg(R9, 0x20000180)
T0818 003:061.707 - 0.003ms returns 0
T0818 003:061.711 JLINK_WriteReg(R10, 0x00000000)
T0818 003:061.715 - 0.003ms returns 0
T0818 003:061.719 JLINK_WriteReg(R11, 0x00000000)
T0818 003:061.722 - 0.003ms returns 0
T0818 003:061.726 JLINK_WriteReg(R12, 0x00000000)
T0818 003:061.729 - 0.003ms returns 0
T0818 003:061.734 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:061.737 - 0.003ms returns 0
T0818 003:061.741 JLINK_WriteReg(R14, 0x20000001)
T0818 003:061.744 - 0.003ms returns 0
T0818 003:061.749 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 003:061.752 - 0.003ms returns 0
T0818 003:061.756 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:061.759 - 0.003ms returns 0
T0818 003:061.763 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:061.767 - 0.003ms returns 0
T0818 003:061.771 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:061.774 - 0.003ms returns 0
T0818 003:061.778 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:061.781 - 0.003ms returns 0
T0818 003:061.786 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:061.790 - 0.004ms returns 0x0000003F
T0818 003:061.794 JLINK_Go()
T0818 003:061.823   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:064.515 - 2.721ms 
T0818 003:064.526 JLINK_IsHalted()
T0818 003:066.784   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:067.233 - 2.706ms returns TRUE
T0818 003:067.242 JLINK_ReadReg(R15 (PC))
T0818 003:067.252 - 0.009ms returns 0x20000000
T0818 003:067.256 JLINK_ClrBPEx(BPHandle = 0x0000003F)
T0818 003:067.260 - 0.003ms returns 0x00
T0818 003:067.264 JLINK_ReadReg(R0)
T0818 003:067.268 - 0.003ms returns 0x00000000
T0818 003:121.248 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T0818 003:121.263   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T0818 003:121.290   CPU_WriteMem(388 bytes @ 0x20000000)
T0818 003:123.119 - 1.871ms returns 0x184
T0818 003:123.136 JLINK_HasError()
T0818 003:123.142 JLINK_WriteReg(R0, 0x08000000)
T0818 003:123.147 - 0.004ms returns 0
T0818 003:123.151 JLINK_WriteReg(R1, 0x0ABA9500)
T0818 003:123.154 - 0.003ms returns 0
T0818 003:123.158 JLINK_WriteReg(R2, 0x00000003)
T0818 003:123.162 - 0.003ms returns 0
T0818 003:123.166 JLINK_WriteReg(R3, 0x00000000)
T0818 003:123.169 - 0.003ms returns 0
T0818 003:123.173 JLINK_WriteReg(R4, 0x00000000)
T0818 003:123.176 - 0.003ms returns 0
T0818 003:123.181 JLINK_WriteReg(R5, 0x00000000)
T0818 003:123.184 - 0.003ms returns 0
T0818 003:123.188 JLINK_WriteReg(R6, 0x00000000)
T0818 003:123.191 - 0.003ms returns 0
T0818 003:123.195 JLINK_WriteReg(R7, 0x00000000)
T0818 003:123.199 - 0.003ms returns 0
T0818 003:123.203 JLINK_WriteReg(R8, 0x00000000)
T0818 003:123.206 - 0.003ms returns 0
T0818 003:123.210 JLINK_WriteReg(R9, 0x20000180)
T0818 003:123.214 - 0.003ms returns 0
T0818 003:123.218 JLINK_WriteReg(R10, 0x00000000)
T0818 003:123.221 - 0.003ms returns 0
T0818 003:123.225 JLINK_WriteReg(R11, 0x00000000)
T0818 003:123.228 - 0.003ms returns 0
T0818 003:123.232 JLINK_WriteReg(R12, 0x00000000)
T0818 003:123.236 - 0.003ms returns 0
T0818 003:123.240 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:123.244 - 0.004ms returns 0
T0818 003:123.248 JLINK_WriteReg(R14, 0x20000001)
T0818 003:123.251 - 0.003ms returns 0
T0818 003:123.256 JLINK_WriteReg(R15 (PC), 0x20000054)
T0818 003:123.259 - 0.003ms returns 0
T0818 003:123.263 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:123.266 - 0.003ms returns 0
T0818 003:123.270 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:123.274 - 0.003ms returns 0
T0818 003:123.278 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:123.281 - 0.003ms returns 0
T0818 003:123.285 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:123.289 - 0.003ms returns 0
T0818 003:123.293 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:123.299   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:123.788 - 0.494ms returns 0x00000040
T0818 003:123.793 JLINK_Go()
T0818 003:123.798   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 003:124.326   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:127.036 - 3.242ms 
T0818 003:127.050 JLINK_IsHalted()
T0818 003:129.438   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:129.914 - 2.864ms returns TRUE
T0818 003:129.920 JLINK_ReadReg(R15 (PC))
T0818 003:129.924 - 0.004ms returns 0x20000000
T0818 003:129.929 JLINK_ClrBPEx(BPHandle = 0x00000040)
T0818 003:129.932 - 0.003ms returns 0x00
T0818 003:129.936 JLINK_ReadReg(R0)
T0818 003:129.940 - 0.003ms returns 0x00000000
T0818 003:129.944 JLINK_HasError()
T0818 003:129.949 JLINK_WriteReg(R0, 0xFFFFFFFF)
T0818 003:129.952 - 0.003ms returns 0
T0818 003:129.956 JLINK_WriteReg(R1, 0x08000000)
T0818 003:129.960 - 0.003ms returns 0
T0818 003:129.964 JLINK_WriteReg(R2, 0x0000CB60)
T0818 003:129.967 - 0.003ms returns 0
T0818 003:129.971 JLINK_WriteReg(R3, 0x04C11DB7)
T0818 003:129.975 - 0.003ms returns 0
T0818 003:129.978 JLINK_WriteReg(R4, 0x00000000)
T0818 003:129.982 - 0.003ms returns 0
T0818 003:129.986 JLINK_WriteReg(R5, 0x00000000)
T0818 003:129.989 - 0.003ms returns 0
T0818 003:129.993 JLINK_WriteReg(R6, 0x00000000)
T0818 003:129.996 - 0.003ms returns 0
T0818 003:130.001 JLINK_WriteReg(R7, 0x00000000)
T0818 003:130.007 - 0.006ms returns 0
T0818 003:130.012 JLINK_WriteReg(R8, 0x00000000)
T0818 003:130.015 - 0.003ms returns 0
T0818 003:130.019 JLINK_WriteReg(R9, 0x20000180)
T0818 003:130.022 - 0.003ms returns 0
T0818 003:130.026 JLINK_WriteReg(R10, 0x00000000)
T0818 003:130.030 - 0.003ms returns 0
T0818 003:130.037 JLINK_WriteReg(R11, 0x00000000)
T0818 003:130.040 - 0.003ms returns 0
T0818 003:130.044 JLINK_WriteReg(R12, 0x00000000)
T0818 003:130.048 - 0.003ms returns 0
T0818 003:130.052 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:130.055 - 0.003ms returns 0
T0818 003:130.059 JLINK_WriteReg(R14, 0x20000001)
T0818 003:130.062 - 0.003ms returns 0
T0818 003:130.066 JLINK_WriteReg(R15 (PC), 0x20000002)
T0818 003:130.070 - 0.003ms returns 0
T0818 003:130.074 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:130.077 - 0.003ms returns 0
T0818 003:130.081 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:130.085 - 0.003ms returns 0
T0818 003:130.089 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:130.092 - 0.003ms returns 0
T0818 003:130.096 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:130.100 - 0.003ms returns 0
T0818 003:130.104 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:130.108 - 0.003ms returns 0x00000041
T0818 003:130.112 JLINK_Go()
T0818 003:130.118   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:132.928 - 2.815ms 
T0818 003:132.946 JLINK_IsHalted()
T0818 003:133.426 - 0.480ms returns FALSE
T0818 003:133.432 JLINK_HasError()
T0818 003:136.795 JLINK_IsHalted()
T0818 003:137.255 - 0.459ms returns FALSE
T0818 003:137.261 JLINK_HasError()
T0818 003:138.799 JLINK_IsHalted()
T0818 003:139.302 - 0.502ms returns FALSE
T0818 003:139.308 JLINK_HasError()
T0818 003:140.795 JLINK_IsHalted()
T0818 003:141.243 - 0.447ms returns FALSE
T0818 003:141.251 JLINK_HasError()
T0818 003:142.797 JLINK_IsHalted()
T0818 003:143.289 - 0.491ms returns FALSE
T0818 003:143.302 JLINK_HasError()
T0818 003:144.792 JLINK_IsHalted()
T0818 003:145.262 - 0.469ms returns FALSE
T0818 003:145.269 JLINK_HasError()
T0818 003:146.804 JLINK_IsHalted()
T0818 003:147.268 - 0.464ms returns FALSE
T0818 003:147.277 JLINK_HasError()
T0818 003:148.812 JLINK_IsHalted()
T0818 003:149.264 - 0.452ms returns FALSE
T0818 003:149.272 JLINK_HasError()
T0818 003:150.811 JLINK_IsHalted()
T0818 003:151.336 - 0.524ms returns FALSE
T0818 003:151.349 JLINK_HasError()
T0818 003:152.809 JLINK_IsHalted()
T0818 003:153.352 - 0.543ms returns FALSE
T0818 003:153.362 JLINK_HasError()
T0818 003:154.809 JLINK_IsHalted()
T0818 003:155.335 - 0.526ms returns FALSE
T0818 003:155.344 JLINK_HasError()
T0818 003:156.812 JLINK_IsHalted()
T0818 003:157.333 - 0.519ms returns FALSE
T0818 003:157.339 JLINK_HasError()
T0818 003:158.807 JLINK_IsHalted()
T0818 003:159.300 - 0.492ms returns FALSE
T0818 003:159.306 JLINK_HasError()
T0818 003:160.810 JLINK_IsHalted()
T0818 003:161.347 - 0.536ms returns FALSE
T0818 003:161.354 JLINK_HasError()
T0818 003:162.811 JLINK_IsHalted()
T0818 003:163.328 - 0.516ms returns FALSE
T0818 003:163.338 JLINK_HasError()
T0818 003:165.817 JLINK_IsHalted()
T0818 003:166.302 - 0.485ms returns FALSE
T0818 003:166.309 JLINK_HasError()
T0818 003:167.815 JLINK_IsHalted()
T0818 003:168.349 - 0.534ms returns FALSE
T0818 003:168.363 JLINK_HasError()
T0818 003:169.813 JLINK_IsHalted()
T0818 003:170.269 - 0.456ms returns FALSE
T0818 003:170.280 JLINK_HasError()
T0818 003:171.809 JLINK_IsHalted()
T0818 003:172.257 - 0.448ms returns FALSE
T0818 003:172.264 JLINK_HasError()
T0818 003:173.810 JLINK_IsHalted()
T0818 003:174.244 - 0.434ms returns FALSE
T0818 003:174.250 JLINK_HasError()
T0818 003:175.806 JLINK_IsHalted()
T0818 003:176.299 - 0.492ms returns FALSE
T0818 003:176.305 JLINK_HasError()
T0818 003:177.810 JLINK_IsHalted()
T0818 003:178.332 - 0.521ms returns FALSE
T0818 003:178.340 JLINK_HasError()
T0818 003:180.812 JLINK_IsHalted()
T0818 003:181.288 - 0.476ms returns FALSE
T0818 003:181.296 JLINK_HasError()
T0818 003:182.811 JLINK_IsHalted()
T0818 003:183.242 - 0.431ms returns FALSE
T0818 003:183.248 JLINK_HasError()
T0818 003:185.812 JLINK_IsHalted()
T0818 003:186.305 - 0.492ms returns FALSE
T0818 003:186.312 JLINK_HasError()
T0818 003:187.808 JLINK_IsHalted()
T0818 003:188.300 - 0.491ms returns FALSE
T0818 003:188.306 JLINK_HasError()
T0818 003:190.811 JLINK_IsHalted()
T0818 003:191.274 - 0.462ms returns FALSE
T0818 003:191.280 JLINK_HasError()
T0818 003:192.808 JLINK_IsHalted()
T0818 003:193.246 - 0.438ms returns FALSE
T0818 003:193.252 JLINK_HasError()
T0818 003:194.808 JLINK_IsHalted()
T0818 003:195.287 - 0.478ms returns FALSE
T0818 003:195.293 JLINK_HasError()
T0818 003:196.809 JLINK_IsHalted()
T0818 003:197.289 - 0.480ms returns FALSE
T0818 003:197.296 JLINK_HasError()
T0818 003:198.812 JLINK_IsHalted()
T0818 003:199.228 - 0.416ms returns FALSE
T0818 003:199.236 JLINK_HasError()
T0818 003:201.813 JLINK_IsHalted()
T0818 003:202.349 - 0.536ms returns FALSE
T0818 003:202.356 JLINK_HasError()
T0818 003:203.818 JLINK_IsHalted()
T0818 003:204.333 - 0.515ms returns FALSE
T0818 003:204.340 JLINK_HasError()
T0818 003:205.808 JLINK_IsHalted()
T0818 003:206.333 - 0.524ms returns FALSE
T0818 003:206.345 JLINK_HasError()
T0818 003:207.813 JLINK_IsHalted()
T0818 003:208.266 - 0.452ms returns FALSE
T0818 003:208.273 JLINK_HasError()
T0818 003:210.816 JLINK_IsHalted()
T0818 003:211.304 - 0.487ms returns FALSE
T0818 003:211.314 JLINK_HasError()
T0818 003:212.809 JLINK_IsHalted()
T0818 003:213.285 - 0.476ms returns FALSE
T0818 003:213.292 JLINK_HasError()
T0818 003:214.811 JLINK_IsHalted()
T0818 003:215.264 - 0.453ms returns FALSE
T0818 003:215.273 JLINK_HasError()
T0818 003:216.813 JLINK_IsHalted()
T0818 003:217.254 - 0.440ms returns FALSE
T0818 003:217.261 JLINK_HasError()
T0818 003:219.812 JLINK_IsHalted()
T0818 003:220.278 - 0.466ms returns FALSE
T0818 003:220.294 JLINK_HasError()
T0818 003:221.815 JLINK_IsHalted()
T0818 003:222.317 - 0.502ms returns FALSE
T0818 003:222.324 JLINK_HasError()
T0818 003:223.808 JLINK_IsHalted()
T0818 003:224.249 - 0.441ms returns FALSE
T0818 003:224.258 JLINK_HasError()
T0818 003:225.812 JLINK_IsHalted()
T0818 003:226.309 - 0.497ms returns FALSE
T0818 003:226.330 JLINK_HasError()
T0818 003:227.810 JLINK_IsHalted()
T0818 003:228.303 - 0.492ms returns FALSE
T0818 003:228.312 JLINK_HasError()
T0818 003:229.811 JLINK_IsHalted()
T0818 003:230.276 - 0.465ms returns FALSE
T0818 003:230.298 JLINK_HasError()
T0818 003:231.808 JLINK_IsHalted()
T0818 003:232.352 - 0.543ms returns FALSE
T0818 003:232.363 JLINK_HasError()
T0818 003:233.813 JLINK_IsHalted()
T0818 003:234.306 - 0.492ms returns FALSE
T0818 003:234.314 JLINK_HasError()
T0818 003:235.811 JLINK_IsHalted()
T0818 003:236.300 - 0.489ms returns FALSE
T0818 003:236.307 JLINK_HasError()
T0818 003:238.814 JLINK_IsHalted()
T0818 003:239.282 - 0.468ms returns FALSE
T0818 003:239.290 JLINK_HasError()
T0818 003:240.811 JLINK_IsHalted()
T0818 003:241.334 - 0.522ms returns FALSE
T0818 003:241.340 JLINK_HasError()
T0818 003:242.814 JLINK_IsHalted()
T0818 003:243.310 - 0.495ms returns FALSE
T0818 003:243.316 JLINK_HasError()
T0818 003:244.809 JLINK_IsHalted()
T0818 003:245.288 - 0.479ms returns FALSE
T0818 003:245.295 JLINK_HasError()
T0818 003:246.809 JLINK_IsHalted()
T0818 003:247.244 - 0.435ms returns FALSE
T0818 003:247.250 JLINK_HasError()
T0818 003:248.809 JLINK_IsHalted()
T0818 003:249.300 - 0.491ms returns FALSE
T0818 003:249.307 JLINK_HasError()
T0818 003:250.809 JLINK_IsHalted()
T0818 003:251.286 - 0.477ms returns FALSE
T0818 003:251.293 JLINK_HasError()
T0818 003:252.806 JLINK_IsHalted()
T0818 003:253.299 - 0.492ms returns FALSE
T0818 003:253.305 JLINK_HasError()
T0818 003:254.808 JLINK_IsHalted()
T0818 003:255.301 - 0.493ms returns FALSE
T0818 003:255.308 JLINK_HasError()
T0818 003:256.811 JLINK_IsHalted()
T0818 003:257.261 - 0.450ms returns FALSE
T0818 003:257.267 JLINK_HasError()
T0818 003:258.808 JLINK_IsHalted()
T0818 003:259.244 - 0.435ms returns FALSE
T0818 003:259.250 JLINK_HasError()
T0818 003:260.810 JLINK_IsHalted()
T0818 003:261.331 - 0.521ms returns FALSE
T0818 003:261.337 JLINK_HasError()
T0818 003:262.811 JLINK_IsHalted()
T0818 003:263.330 - 0.519ms returns FALSE
T0818 003:263.337 JLINK_HasError()
T0818 003:264.819 JLINK_IsHalted()
T0818 003:265.333 - 0.514ms returns FALSE
T0818 003:265.341 JLINK_HasError()
T0818 003:266.810 JLINK_IsHalted()
T0818 003:267.289 - 0.479ms returns FALSE
T0818 003:267.298 JLINK_HasError()
T0818 003:268.810 JLINK_IsHalted()
T0818 003:269.303 - 0.492ms returns FALSE
T0818 003:269.310 JLINK_HasError()
T0818 003:270.811 JLINK_IsHalted()
T0818 003:271.431 - 0.619ms returns FALSE
T0818 003:271.443 JLINK_HasError()
T0818 003:272.807 JLINK_IsHalted()
T0818 003:273.290 - 0.482ms returns FALSE
T0818 003:273.296 JLINK_HasError()
T0818 003:274.814 JLINK_IsHalted()
T0818 003:275.322 - 0.508ms returns FALSE
T0818 003:275.329 JLINK_HasError()
T0818 003:277.818 JLINK_IsHalted()
T0818 003:278.313 - 0.495ms returns FALSE
T0818 003:278.321 JLINK_HasError()
T0818 003:279.809 JLINK_IsHalted()
T0818 003:280.290 - 0.480ms returns FALSE
T0818 003:280.297 JLINK_HasError()
T0818 003:281.808 JLINK_IsHalted()
T0818 003:282.245 - 0.436ms returns FALSE
T0818 003:282.251 JLINK_HasError()
T0818 003:283.810 JLINK_IsHalted()
T0818 003:284.287 - 0.477ms returns FALSE
T0818 003:284.293 JLINK_HasError()
T0818 003:285.816 JLINK_IsHalted()
T0818 003:286.304 - 0.487ms returns FALSE
T0818 003:286.311 JLINK_HasError()
T0818 003:287.809 JLINK_IsHalted()
T0818 003:288.288 - 0.478ms returns FALSE
T0818 003:288.298 JLINK_HasError()
T0818 003:289.810 JLINK_IsHalted()
T0818 003:290.342 - 0.532ms returns FALSE
T0818 003:290.357 JLINK_HasError()
T0818 003:291.810 JLINK_IsHalted()
T0818 003:292.349 - 0.539ms returns FALSE
T0818 003:292.356 JLINK_HasError()
T0818 003:293.811 JLINK_IsHalted()
T0818 003:294.264 - 0.453ms returns FALSE
T0818 003:294.276 JLINK_HasError()
T0818 003:295.809 JLINK_IsHalted()
T0818 003:296.288 - 0.479ms returns FALSE
T0818 003:296.296 JLINK_HasError()
T0818 003:297.808 JLINK_IsHalted()
T0818 003:298.301 - 0.493ms returns FALSE
T0818 003:298.308 JLINK_HasError()
T0818 003:299.809 JLINK_IsHalted()
T0818 003:300.297 - 0.487ms returns FALSE
T0818 003:300.318 JLINK_HasError()
T0818 003:301.811 JLINK_IsHalted()
T0818 003:302.267 - 0.455ms returns FALSE
T0818 003:302.276 JLINK_HasError()
T0818 003:303.812 JLINK_IsHalted()
T0818 003:304.348 - 0.536ms returns FALSE
T0818 003:304.358 JLINK_HasError()
T0818 003:305.810 JLINK_IsHalted()
T0818 003:306.254 - 0.443ms returns FALSE
T0818 003:306.263 JLINK_HasError()
T0818 003:307.807 JLINK_IsHalted()
T0818 003:308.302 - 0.495ms returns FALSE
T0818 003:308.308 JLINK_HasError()
T0818 003:309.809 JLINK_IsHalted()
T0818 003:310.256 - 0.446ms returns FALSE
T0818 003:310.266 JLINK_HasError()
T0818 003:311.810 JLINK_IsHalted()
T0818 003:312.280 - 0.469ms returns FALSE
T0818 003:312.287 JLINK_HasError()
T0818 003:313.809 JLINK_IsHalted()
T0818 003:314.281 - 0.471ms returns FALSE
T0818 003:314.287 JLINK_HasError()
T0818 003:316.811 JLINK_IsHalted()
T0818 003:317.348 - 0.536ms returns FALSE
T0818 003:317.355 JLINK_HasError()
T0818 003:318.812 JLINK_IsHalted()
T0818 003:319.243 - 0.431ms returns FALSE
T0818 003:319.250 JLINK_HasError()
T0818 003:320.810 JLINK_IsHalted()
T0818 003:321.286 - 0.476ms returns FALSE
T0818 003:321.292 JLINK_HasError()
T0818 003:322.807 JLINK_IsHalted()
T0818 003:323.252 - 0.444ms returns FALSE
T0818 003:323.258 JLINK_HasError()
T0818 003:324.808 JLINK_IsHalted()
T0818 003:325.287 - 0.478ms returns FALSE
T0818 003:325.293 JLINK_HasError()
T0818 003:326.810 JLINK_IsHalted()
T0818 003:327.288 - 0.477ms returns FALSE
T0818 003:327.294 JLINK_HasError()
T0818 003:328.806 JLINK_IsHalted()
T0818 003:329.286 - 0.480ms returns FALSE
T0818 003:329.292 JLINK_HasError()
T0818 003:330.811 JLINK_IsHalted()
T0818 003:331.348 - 0.536ms returns FALSE
T0818 003:331.356 JLINK_HasError()
T0818 003:332.808 JLINK_IsHalted()
T0818 003:333.291 - 0.483ms returns FALSE
T0818 003:333.298 JLINK_HasError()
T0818 003:335.815 JLINK_IsHalted()
T0818 003:336.277 - 0.461ms returns FALSE
T0818 003:336.284 JLINK_HasError()
T0818 003:337.809 JLINK_IsHalted()
T0818 003:338.273 - 0.463ms returns FALSE
T0818 003:338.280 JLINK_HasError()
T0818 003:339.810 JLINK_IsHalted()
T0818 003:340.314 - 0.503ms returns FALSE
T0818 003:340.331 JLINK_HasError()
T0818 003:341.808 JLINK_IsHalted()
T0818 003:342.244 - 0.436ms returns FALSE
T0818 003:342.250 JLINK_HasError()
T0818 003:343.816 JLINK_IsHalted()
T0818 003:344.301 - 0.484ms returns FALSE
T0818 003:344.307 JLINK_HasError()
T0818 003:345.808 JLINK_IsHalted()
T0818 003:346.300 - 0.491ms returns FALSE
T0818 003:346.305 JLINK_HasError()
T0818 003:347.808 JLINK_IsHalted()
T0818 003:348.332 - 0.524ms returns FALSE
T0818 003:348.339 JLINK_HasError()
T0818 003:349.807 JLINK_IsHalted()
T0818 003:350.265 - 0.457ms returns FALSE
T0818 003:350.274 JLINK_HasError()
T0818 003:351.810 JLINK_IsHalted()
T0818 003:352.244 - 0.433ms returns FALSE
T0818 003:352.250 JLINK_HasError()
T0818 003:353.808 JLINK_IsHalted()
T0818 003:354.360 - 0.551ms returns FALSE
T0818 003:354.371 JLINK_HasError()
T0818 003:355.809 JLINK_IsHalted()
T0818 003:356.286 - 0.477ms returns FALSE
T0818 003:356.293 JLINK_HasError()
T0818 003:357.812 JLINK_IsHalted()
T0818 003:358.288 - 0.476ms returns FALSE
T0818 003:358.295 JLINK_HasError()
T0818 003:359.818 JLINK_IsHalted()
T0818 003:360.310 - 0.492ms returns FALSE
T0818 003:360.316 JLINK_HasError()
T0818 003:361.812 JLINK_IsHalted()
T0818 003:362.345 - 0.533ms returns FALSE
T0818 003:362.353 JLINK_HasError()
T0818 003:363.810 JLINK_IsHalted()
T0818 003:364.302 - 0.491ms returns FALSE
T0818 003:364.310 JLINK_HasError()
T0818 003:365.810 JLINK_IsHalted()
T0818 003:366.330 - 0.520ms returns FALSE
T0818 003:366.336 JLINK_HasError()
T0818 003:367.806 JLINK_IsHalted()
T0818 003:368.300 - 0.493ms returns FALSE
T0818 003:368.306 JLINK_HasError()
T0818 003:369.824 JLINK_IsHalted()
T0818 003:370.314 - 0.489ms returns FALSE
T0818 003:370.350 JLINK_HasError()
T0818 003:371.816 JLINK_IsHalted()
T0818 003:372.250 - 0.432ms returns FALSE
T0818 003:372.262 JLINK_HasError()
T0818 003:373.814 JLINK_IsHalted()
T0818 003:374.269 - 0.454ms returns FALSE
T0818 003:374.278 JLINK_HasError()
T0818 003:375.815 JLINK_IsHalted()
T0818 003:376.264 - 0.449ms returns FALSE
T0818 003:376.272 JLINK_HasError()
T0818 003:377.817 JLINK_IsHalted()
T0818 003:378.257 - 0.440ms returns FALSE
T0818 003:378.271 JLINK_HasError()
T0818 003:379.807 JLINK_IsHalted()
T0818 003:380.335 - 0.527ms returns FALSE
T0818 003:380.345 JLINK_HasError()
T0818 003:381.810 JLINK_IsHalted()
T0818 003:382.233 - 0.422ms returns FALSE
T0818 003:382.242 JLINK_HasError()
T0818 003:383.812 JLINK_IsHalted()
T0818 003:384.305 - 0.492ms returns FALSE
T0818 003:384.313 JLINK_HasError()
T0818 003:386.814 JLINK_IsHalted()
T0818 003:387.303 - 0.489ms returns FALSE
T0818 003:387.310 JLINK_HasError()
T0818 003:388.806 JLINK_IsHalted()
T0818 003:389.325 - 0.519ms returns FALSE
T0818 003:389.332 JLINK_HasError()
T0818 003:390.808 JLINK_IsHalted()
T0818 003:391.303 - 0.495ms returns FALSE
T0818 003:391.310 JLINK_HasError()
T0818 003:392.808 JLINK_IsHalted()
T0818 003:393.242 - 0.434ms returns FALSE
T0818 003:393.248 JLINK_HasError()
T0818 003:394.810 JLINK_IsHalted()
T0818 003:395.290 - 0.480ms returns FALSE
T0818 003:395.296 JLINK_HasError()
T0818 003:396.807 JLINK_IsHalted()
T0818 003:397.264 - 0.456ms returns FALSE
T0818 003:397.269 JLINK_HasError()
T0818 003:398.808 JLINK_IsHalted()
T0818 003:399.362 - 0.552ms returns FALSE
T0818 003:399.377 JLINK_HasError()
T0818 003:400.814 JLINK_IsHalted()
T0818 003:401.324 - 0.509ms returns FALSE
T0818 003:401.330 JLINK_HasError()
T0818 003:402.816 JLINK_IsHalted()
T0818 003:403.327 - 0.510ms returns FALSE
T0818 003:403.338 JLINK_HasError()
T0818 003:404.810 JLINK_IsHalted()
T0818 003:405.242 - 0.432ms returns FALSE
T0818 003:405.249 JLINK_HasError()
T0818 003:406.809 JLINK_IsHalted()
T0818 003:407.302 - 0.492ms returns FALSE
T0818 003:407.310 JLINK_HasError()
T0818 003:408.811 JLINK_IsHalted()
T0818 003:409.256 - 0.444ms returns FALSE
T0818 003:409.262 JLINK_HasError()
T0818 003:410.808 JLINK_IsHalted()
T0818 003:411.272 - 0.464ms returns FALSE
T0818 003:411.279 JLINK_HasError()
T0818 003:412.810 JLINK_IsHalted()
T0818 003:413.285 - 0.474ms returns FALSE
T0818 003:413.291 JLINK_HasError()
T0818 003:414.808 JLINK_IsHalted()
T0818 003:415.302 - 0.493ms returns FALSE
T0818 003:415.310 JLINK_HasError()
T0818 003:416.811 JLINK_IsHalted()
T0818 003:417.300 - 0.489ms returns FALSE
T0818 003:417.308 JLINK_HasError()
T0818 003:418.807 JLINK_IsHalted()
T0818 003:421.176   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:421.700 - 2.892ms returns TRUE
T0818 003:421.708 JLINK_ReadReg(R15 (PC))
T0818 003:421.713 - 0.005ms returns 0x20000000
T0818 003:421.718 JLINK_ClrBPEx(BPHandle = 0x00000041)
T0818 003:421.722 - 0.003ms returns 0x00
T0818 003:421.726 JLINK_ReadReg(R0)
T0818 003:421.730 - 0.003ms returns 0x058DA8CB
T0818 003:423.355 JLINK_HasError()
T0818 003:423.370 JLINK_WriteReg(R0, 0x00000003)
T0818 003:423.375 - 0.005ms returns 0
T0818 003:423.379 JLINK_WriteReg(R1, 0x08000000)
T0818 003:423.383 - 0.003ms returns 0
T0818 003:423.387 JLINK_WriteReg(R2, 0x0000CB60)
T0818 003:423.390 - 0.003ms returns 0
T0818 003:423.394 JLINK_WriteReg(R3, 0x04C11DB7)
T0818 003:423.398 - 0.003ms returns 0
T0818 003:423.402 JLINK_WriteReg(R4, 0x00000000)
T0818 003:423.406 - 0.003ms returns 0
T0818 003:423.410 JLINK_WriteReg(R5, 0x00000000)
T0818 003:423.413 - 0.003ms returns 0
T0818 003:423.417 JLINK_WriteReg(R6, 0x00000000)
T0818 003:423.420 - 0.003ms returns 0
T0818 003:423.424 JLINK_WriteReg(R7, 0x00000000)
T0818 003:423.428 - 0.003ms returns 0
T0818 003:423.432 JLINK_WriteReg(R8, 0x00000000)
T0818 003:423.436 - 0.003ms returns 0
T0818 003:423.440 JLINK_WriteReg(R9, 0x20000180)
T0818 003:423.443 - 0.003ms returns 0
T0818 003:423.447 JLINK_WriteReg(R10, 0x00000000)
T0818 003:423.450 - 0.003ms returns 0
T0818 003:423.454 JLINK_WriteReg(R11, 0x00000000)
T0818 003:423.458 - 0.003ms returns 0
T0818 003:423.462 JLINK_WriteReg(R12, 0x00000000)
T0818 003:423.465 - 0.003ms returns 0
T0818 003:423.469 JLINK_WriteReg(R13 (SP), 0x20001000)
T0818 003:423.473 - 0.004ms returns 0
T0818 003:423.477 JLINK_WriteReg(R14, 0x20000001)
T0818 003:423.480 - 0.003ms returns 0
T0818 003:423.485 JLINK_WriteReg(R15 (PC), 0x20000086)
T0818 003:423.488 - 0.003ms returns 0
T0818 003:423.492 JLINK_WriteReg(XPSR, 0x01000000)
T0818 003:423.496 - 0.003ms returns 0
T0818 003:423.500 JLINK_WriteReg(MSP, 0x20001000)
T0818 003:423.503 - 0.003ms returns 0
T0818 003:423.507 JLINK_WriteReg(PSP, 0x20001000)
T0818 003:423.511 - 0.003ms returns 0
T0818 003:423.515 JLINK_WriteReg(CFBP, 0x00000000)
T0818 003:423.518 - 0.003ms returns 0
T0818 003:423.523 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T0818 003:423.528 - 0.005ms returns 0x00000042
T0818 003:423.532 JLINK_Go()
T0818 003:423.541   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:426.434 - 2.902ms 
T0818 003:426.452 JLINK_IsHalted()
T0818 003:428.763   CPU_ReadMem(2 bytes @ 0x20000000)
T0818 003:429.231 - 2.778ms returns TRUE
T0818 003:429.242 JLINK_ReadReg(R15 (PC))
T0818 003:429.247 - 0.004ms returns 0x20000000
T0818 003:429.288 JLINK_ClrBPEx(BPHandle = 0x00000042)
T0818 003:429.293 - 0.005ms returns 0x00
T0818 003:429.298 JLINK_ReadReg(R0)
T0818 003:429.301 - 0.003ms returns 0x00000000
T0818 003:482.093 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T0818 003:482.122   Data:  FE E7
T0818 003:482.140   CPU_WriteMem(2 bytes @ 0x20000000)
T0818 003:482.617 - 0.524ms returns 0x2
T0818 003:482.633 JLINK_HasError()
T0818 003:485.922 JLINK_Close()
T0818 003:487.731   OnDisconnectTarget() start
T0818 003:487.749    J-Link Script File: Executing OnDisconnectTarget()
T0818 003:487.761   CPU_WriteMem(4 bytes @ 0xE0042004)
T0818 003:488.181   CPU_WriteMem(4 bytes @ 0xE0042008)
T0818 003:489.861   OnDisconnectTarget() end - Took 929us
T0818 003:489.882   CPU_ReadMem(4 bytes @ 0xE0001000)
T0818 003:509.988 - 24.064ms
T0818 003:510.016   
T0818 003:510.033   Closed
