T7BD8 000:003.526   SEGGER J-Link V8.16 Log File
T7BD8 000:003.647   DLL Compiled: Feb 26 2025 12:07:26
T7BD8 000:003.656   Logging started @ 2025-08-01 11:10
T7BD8 000:003.660   Process: G:\keil\keil arm\UV4\UV4.exe
T7BD8 000:003.675 - 3.663ms 
T7BD8 000:003.683 JLINK_SetWarnOutHandler(...)
T7BD8 000:003.686 - 0.004ms 
T7BD8 000:003.693 JLINK_OpenEx(...)
T7BD8 000:007.263   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T7BD8 000:008.587   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T7BD8 000:008.706   Decompressing FW timestamp took 84 us
T7BD8 000:016.314   Hardware: V9.60
T7BD8 000:016.325   S/N: 69655018
T7BD8 000:016.330   OEM: SEGGER
T7BD8 000:016.335   Feature(s): RDI, GDB, FlashDL, FlashB<PERSON>, JFlash
T7BD8 000:017.632   Bootloader: (FW returned invalid version)
T7BD8 000:019.174   TELNET listener socket opened on port 19021
T7BD8 000:019.250   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T7BD8 000:019.371   WEBSRV Webserver running on local port 19080
T7BD8 000:019.436   Looking for J-Link GUI Server exe at: G:\keil\keil arm\ARM\Segger\JLinkGUIServer.exe
T7BD8 000:019.521   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V630d\\JLinkGUIServer.exe
T7BD8 000:325.274   Failed to connect to J-Link GUI Server.
T7BD8 000:325.309 - 321.607ms returns "O.K."
T7BD8 000:325.324 JLINK_GetEmuCaps()
T7BD8 000:325.330 - 0.004ms returns 0xB9FF7BBF
T7BD8 000:325.340 JLINK_TIF_GetAvailable(...)
T7BD8 000:325.801 - 0.461ms 
T7BD8 000:325.814 JLINK_SetErrorOutHandler(...)
T7BD8 000:325.818 - 0.004ms 
T7BD8 000:325.838 JLINK_ExecCommand("ProjectFile = "C:\Users\<USER>\Desktop\2025elec\zuolan_stm32\MDK-ARM\JLinkSettings.ini"", ...). 
T7BD8 000:336.543 - 10.706ms returns 0x00
T7BD8 000:338.290 JLINK_ExecCommand("Device = STM32F429IGTx", ...). 
T7BD8 000:339.677   Flash bank @ 0x90000000: SFL: Parsing sectorization info from ELF file
T7BD8 000:339.698     FlashDevice.SectorInfo[0]: .SectorSize = 0x00010000, .SectorStartAddr = 0x00000000
T7BD8 000:343.976   Device "STM32F429IG" selected.
T7BD8 000:344.252 - 5.939ms returns 0x00
T7BD8 000:344.263 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T7BD8 000:344.279   ERROR: Unknown command
T7BD8 000:344.284 - 0.015ms returns 0x01
T7BD8 000:344.289 JLINK_GetHardwareVersion()
T7BD8 000:344.294 - 0.005ms returns 96000
T7BD8 000:344.299 JLINK_GetDLLVersion()
T7BD8 000:344.302 - 0.003ms returns 81600
T7BD8 000:344.306 JLINK_GetOEMString(...)
T7BD8 000:344.311 JLINK_GetFirmwareString(...)
T7BD8 000:344.314 - 0.003ms 
T7BD8 000:347.445 JLINK_GetDLLVersion()
T7BD8 000:347.470 - 0.024ms returns 81600
T7BD8 000:347.475 JLINK_GetCompileDateTime()
T7BD8 000:347.479 - 0.003ms 
T7BD8 000:348.478 JLINK_GetFirmwareString(...)
T7BD8 000:348.493 - 0.015ms 
T7BD8 000:349.471 JLINK_GetHardwareVersion()
T7BD8 000:349.485 - 0.013ms returns 96000
T7BD8 000:350.416 JLINK_GetSN()
T7BD8 000:350.428 - 0.012ms returns 69655018
T7BD8 000:351.462 JLINK_GetOEMString(...)
T7BD8 000:353.337 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T7BD8 000:354.927 - 1.592ms returns 0x00
T7BD8 000:354.942 JLINK_HasError()
T7BD8 000:354.953 JLINK_SetSpeed(5000)
T7BD8 000:355.274 - 0.321ms 
T7BD8 000:355.281 JLINK_GetId()
T7BD8 000:356.820   InitTarget() start
T7BD8 000:356.854    J-Link Script File: Executing InitTarget()
T7BD8 000:358.383   SWD selected. Executing JTAG -> SWD switching sequence.
T7BD8 000:362.547   DAP initialized successfully.
T7BD8 000:373.569   InitTarget() end - Took 15.4ms
T7BD8 000:375.651   Found SW-DP with ID 0x2BA01477
T7BD8 000:380.388   DPIDR: 0x2BA01477
T7BD8 000:381.418   CoreSight SoC-400 or earlier
T7BD8 000:382.330   Scanning AP map to find all available APs
T7BD8 000:384.564   AP[1]: Stopped AP scan as end of AP map has been reached
T7BD8 000:385.523   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
T7BD8 000:386.494   Iterating through AP map to find AHB-AP to use
T7BD8 000:388.951   AP[0]: Core found
T7BD8 000:390.013   AP[0]: AHB-AP ROM base: 0xE00FF000
T7BD8 000:391.906   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T7BD8 000:393.095   Found Cortex-M4 r0p1, Little endian.
T7BD8 000:393.981   -- Max. mem block: 0x00010C40
T7BD8 000:394.823   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7BD8 000:395.317   CPU_ReadMem(4 bytes @ 0x********)
T7BD8 000:396.932   FPUnit: 6 code (BP) slots and 2 literal slots
T7BD8 000:396.951   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7BD8 000:397.443   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7BD8 000:397.989   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:398.513   CPU_WriteMem(4 bytes @ 0xE0001000)
T7BD8 000:399.030   CPU_ReadMem(4 bytes @ 0xE000ED88)
T7BD8 000:399.508   CPU_WriteMem(4 bytes @ 0xE000ED88)
T7BD8 000:400.027   CPU_ReadMem(4 bytes @ 0xE000ED88)
T7BD8 000:400.510   CPU_WriteMem(4 bytes @ 0xE000ED88)
T7BD8 000:402.296   CoreSight components:
T7BD8 000:403.418   ROMTbl[0] @ E00FF000
T7BD8 000:403.438   CPU_ReadMem(64 bytes @ 0xE00FF000)
T7BD8 000:404.225   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T7BD8 000:405.866   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T7BD8 000:405.886   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T7BD8 000:407.691   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T7BD8 000:407.721   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T7BD8 000:410.834   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T7BD8 000:410.865   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T7BD8 000:412.539   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T7BD8 000:412.559   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T7BD8 000:414.304   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T7BD8 000:414.322   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T7BD8 000:416.024   [0][5]: ******** CID B105900D PID 000BB925 ETM
T7BD8 000:416.687 - 61.405ms returns 0x2BA01477
T7BD8 000:416.707 JLINK_GetDLLVersion()
T7BD8 000:416.711 - 0.003ms returns 81600
T7BD8 000:416.749 JLINK_CORE_GetFound()
T7BD8 000:416.753 - 0.004ms returns 0xE0000FF
T7BD8 000:416.759 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T7BD8 000:416.765   Value=0xE00FF000
T7BD8 000:416.771 - 0.013ms returns 0
T7BD8 000:417.941 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T7BD8 000:417.961   Value=0xE00FF000
T7BD8 000:417.968 - 0.027ms returns 0
T7BD8 000:417.973 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T7BD8 000:417.976   Value=0x********
T7BD8 000:417.981 - 0.008ms returns 0
T7BD8 000:417.987 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T7BD8 000:418.022   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T7BD8 000:418.668   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T7BD8 000:418.682 - 0.695ms returns 32 (0x20)
T7BD8 000:418.689 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T7BD8 000:418.692   Value=0x00000000
T7BD8 000:418.697 - 0.008ms returns 0
T7BD8 000:418.701 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T7BD8 000:418.705   Value=0x********
T7BD8 000:418.710 - 0.008ms returns 0
T7BD8 000:418.714 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T7BD8 000:418.717   Value=0x********
T7BD8 000:418.722 - 0.008ms returns 0
T7BD8 000:418.726 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T7BD8 000:418.729   Value=0xE0001000
T7BD8 000:418.735 - 0.008ms returns 0
T7BD8 000:418.739 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T7BD8 000:418.742   Value=0x********
T7BD8 000:418.747 - 0.008ms returns 0
T7BD8 000:418.752 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T7BD8 000:418.755   Value=0xE000E000
T7BD8 000:418.759 - 0.008ms returns 0
T7BD8 000:418.764 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T7BD8 000:418.767   Value=0xE000EDF0
T7BD8 000:418.772 - 0.008ms returns 0
T7BD8 000:418.776 JLINK_GetDebugInfo(0x01 = Unknown)
T7BD8 000:418.780   Value=0x00000001
T7BD8 000:418.784 - 0.008ms returns 0
T7BD8 000:418.789 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T7BD8 000:418.795   CPU_ReadMem(4 bytes @ 0xE000ED00)
T7BD8 000:419.304   Data:  41 C2 0F 41
T7BD8 000:419.311   Debug reg: CPUID
T7BD8 000:419.316 - 0.527ms returns 1 (0x1)
T7BD8 000:419.322 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T7BD8 000:419.329   Value=0x00000000
T7BD8 000:419.334 - 0.012ms returns 0
T7BD8 000:419.339 JLINK_HasError()
T7BD8 000:419.345 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T7BD8 000:419.349 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T7BD8 000:419.353 JLINK_Reset()
T7BD8 000:419.359   JLINK_GetResetTypeDesc
T7BD8 000:419.363   - 0.003ms 
T7BD8 000:420.588   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T7BD8 000:420.627   CPU is running
T7BD8 000:420.636   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7BD8 000:421.152   CPU is running
T7BD8 000:421.159   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7BD8 000:422.737   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T7BD8 000:424.706   Reset: Reset device via AIRCR.SYSRESETREQ.
T7BD8 000:424.724   CPU is running
T7BD8 000:424.733   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T7BD8 000:478.489   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7BD8 000:479.022   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7BD8 000:481.972   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7BD8 000:489.295   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7BD8 000:492.141   CPU_WriteMem(4 bytes @ 0x********)
T7BD8 000:492.651   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T7BD8 000:493.119   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:493.588 - 74.234ms 
T7BD8 000:493.641 JLINK_Halt()
T7BD8 000:493.645 - 0.005ms returns 0x00
T7BD8 000:493.651 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T7BD8 000:493.659   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T7BD8 000:494.149   Data:  03 00 03 00
T7BD8 000:494.156   Debug reg: DHCSR
T7BD8 000:494.161 - 0.509ms returns 1 (0x1)
T7BD8 000:494.166 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T7BD8 000:494.169   Debug reg: DHCSR
T7BD8 000:494.434   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T7BD8 000:495.432 - 1.266ms returns 0 (0x00000000)
T7BD8 000:495.444 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T7BD8 000:495.449   Debug reg: DEMCR
T7BD8 000:495.457   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T7BD8 000:495.945 - 0.500ms returns 0 (0x00000000)
T7BD8 000:499.976 JLINK_GetHWStatus(...)
T7BD8 000:500.418 - 0.442ms returns 0
T7BD8 000:503.138 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T7BD8 000:503.165 - 0.026ms returns 0x06
T7BD8 000:503.170 JLINK_GetNumBPUnits(Type = 0xF0)
T7BD8 000:503.174 - 0.004ms returns 0x2000
T7BD8 000:503.178 JLINK_GetNumWPUnits()
T7BD8 000:503.182 - 0.003ms returns 4
T7BD8 000:505.681 JLINK_GetSpeed()
T7BD8 000:505.698 - 0.016ms returns 4000
T7BD8 000:507.504 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T7BD8 000:507.536   CPU_ReadMem(4 bytes @ 0xE000E004)
T7BD8 000:508.067   Data:  02 00 00 00
T7BD8 000:508.084 - 0.579ms returns 1 (0x1)
T7BD8 000:508.091 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T7BD8 000:508.098   CPU_ReadMem(4 bytes @ 0xE000E004)
T7BD8 000:508.567   Data:  02 00 00 00
T7BD8 000:508.573 - 0.482ms returns 1 (0x1)
T7BD8 000:508.579 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T7BD8 000:508.583   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T7BD8 000:508.594   CPU_WriteMem(28 bytes @ 0xE0001000)
T7BD8 000:509.158 - 0.578ms returns 0x1C
T7BD8 000:509.175 JLINK_Halt()
T7BD8 000:509.179 - 0.004ms returns 0x00
T7BD8 000:509.183 JLINK_IsHalted()
T7BD8 000:509.187 - 0.004ms returns TRUE
T7BD8 000:517.438 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T7BD8 000:517.454   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T7BD8 000:517.707   CPU_WriteMem(388 bytes @ 0x20000000)
T7BD8 000:519.631 - 2.192ms returns 0x184
T7BD8 000:519.691 JLINK_HasError()
T7BD8 000:519.698 JLINK_WriteReg(R0, 0x08000000)
T7BD8 000:519.704 - 0.006ms returns 0
T7BD8 000:519.709 JLINK_WriteReg(R1, 0x0ABA9500)
T7BD8 000:519.712 - 0.003ms returns 0
T7BD8 000:519.716 JLINK_WriteReg(R2, 0x00000001)
T7BD8 000:519.720 - 0.003ms returns 0
T7BD8 000:519.724 JLINK_WriteReg(R3, 0x00000000)
T7BD8 000:519.727 - 0.003ms returns 0
T7BD8 000:519.731 JLINK_WriteReg(R4, 0x00000000)
T7BD8 000:519.735 - 0.003ms returns 0
T7BD8 000:519.739 JLINK_WriteReg(R5, 0x00000000)
T7BD8 000:519.747 - 0.008ms returns 0
T7BD8 000:519.754 JLINK_WriteReg(R6, 0x00000000)
T7BD8 000:519.757 - 0.003ms returns 0
T7BD8 000:519.761 JLINK_WriteReg(R7, 0x00000000)
T7BD8 000:519.765 - 0.003ms returns 0
T7BD8 000:519.786 JLINK_WriteReg(R8, 0x00000000)
T7BD8 000:519.790 - 0.021ms returns 0
T7BD8 000:519.794 JLINK_WriteReg(R9, 0x20000180)
T7BD8 000:519.797 - 0.003ms returns 0
T7BD8 000:519.801 JLINK_WriteReg(R10, 0x00000000)
T7BD8 000:519.805 - 0.003ms returns 0
T7BD8 000:519.809 JLINK_WriteReg(R11, 0x00000000)
T7BD8 000:519.812 - 0.003ms returns 0
T7BD8 000:519.816 JLINK_WriteReg(R12, 0x00000000)
T7BD8 000:519.820 - 0.003ms returns 0
T7BD8 000:519.824 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 000:519.828 - 0.004ms returns 0
T7BD8 000:519.832 JLINK_WriteReg(R14, 0x20000001)
T7BD8 000:519.836 - 0.003ms returns 0
T7BD8 000:519.844 JLINK_WriteReg(R15 (PC), 0x20000054)
T7BD8 000:519.848 - 0.008ms returns 0
T7BD8 000:519.852 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 000:519.856 - 0.003ms returns 0
T7BD8 000:519.860 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 000:519.863 - 0.003ms returns 0
T7BD8 000:519.867 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 000:519.871 - 0.003ms returns 0
T7BD8 000:519.875 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 000:519.878 - 0.003ms returns 0
T7BD8 000:519.883 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 000:519.891   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 000:520.395 - 0.511ms returns 0x00000001
T7BD8 000:520.409 JLINK_Go()
T7BD8 000:520.415   CPU_WriteMem(2 bytes @ 0x20000000)
T7BD8 000:520.947   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:521.445   CPU_WriteMem(4 bytes @ 0xE0002008)
T7BD8 000:521.452   CPU_WriteMem(4 bytes @ 0xE000200C)
T7BD8 000:521.457   CPU_WriteMem(4 bytes @ 0xE0002010)
T7BD8 000:521.462   CPU_WriteMem(4 bytes @ 0xE0002014)
T7BD8 000:521.468   CPU_WriteMem(4 bytes @ 0xE0002018)
T7BD8 000:521.473   CPU_WriteMem(4 bytes @ 0xE000201C)
T7BD8 000:522.773   CPU_WriteMem(4 bytes @ 0xE0001004)
T7BD8 000:528.518   Memory map 'after startup completion point' is active
T7BD8 000:528.547 - 8.137ms 
T7BD8 000:528.556 JLINK_IsHalted()
T7BD8 000:530.884   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 000:531.388 - 2.831ms returns TRUE
T7BD8 000:531.396 JLINK_ReadReg(R15 (PC))
T7BD8 000:531.402 - 0.006ms returns 0x20000000
T7BD8 000:531.407 JLINK_ClrBPEx(BPHandle = 0x00000001)
T7BD8 000:531.410 - 0.004ms returns 0x00
T7BD8 000:531.415 JLINK_ReadReg(R0)
T7BD8 000:531.419 - 0.003ms returns 0x00000000
T7BD8 000:531.914 JLINK_HasError()
T7BD8 000:531.930 JLINK_WriteReg(R0, 0x08000000)
T7BD8 000:531.935 - 0.005ms returns 0
T7BD8 000:531.940 JLINK_WriteReg(R1, 0x00004000)
T7BD8 000:531.943 - 0.003ms returns 0
T7BD8 000:531.947 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 000:531.951 - 0.003ms returns 0
T7BD8 000:531.955 JLINK_WriteReg(R3, 0x00000000)
T7BD8 000:531.958 - 0.003ms returns 0
T7BD8 000:531.962 JLINK_WriteReg(R4, 0x00000000)
T7BD8 000:531.966 - 0.003ms returns 0
T7BD8 000:531.970 JLINK_WriteReg(R5, 0x00000000)
T7BD8 000:531.973 - 0.003ms returns 0
T7BD8 000:531.978 JLINK_WriteReg(R6, 0x00000000)
T7BD8 000:531.981 - 0.003ms returns 0
T7BD8 000:531.986 JLINK_WriteReg(R7, 0x00000000)
T7BD8 000:531.989 - 0.003ms returns 0
T7BD8 000:531.993 JLINK_WriteReg(R8, 0x00000000)
T7BD8 000:531.997 - 0.003ms returns 0
T7BD8 000:532.001 JLINK_WriteReg(R9, 0x20000180)
T7BD8 000:532.004 - 0.003ms returns 0
T7BD8 000:532.008 JLINK_WriteReg(R10, 0x00000000)
T7BD8 000:532.012 - 0.003ms returns 0
T7BD8 000:532.016 JLINK_WriteReg(R11, 0x00000000)
T7BD8 000:532.019 - 0.003ms returns 0
T7BD8 000:532.023 JLINK_WriteReg(R12, 0x00000000)
T7BD8 000:532.027 - 0.003ms returns 0
T7BD8 000:532.031 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 000:532.035 - 0.003ms returns 0
T7BD8 000:532.039 JLINK_WriteReg(R14, 0x20000001)
T7BD8 000:532.042 - 0.003ms returns 0
T7BD8 000:532.046 JLINK_WriteReg(R15 (PC), 0x20000020)
T7BD8 000:532.050 - 0.003ms returns 0
T7BD8 000:532.055 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 000:532.058 - 0.003ms returns 0
T7BD8 000:532.104 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 000:532.110 - 0.005ms returns 0
T7BD8 000:532.114 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 000:532.118 - 0.003ms returns 0
T7BD8 000:532.122 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 000:532.125 - 0.003ms returns 0
T7BD8 000:532.130 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 000:532.135 - 0.005ms returns 0x00000002
T7BD8 000:532.139 JLINK_Go()
T7BD8 000:532.149   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:534.841 - 2.702ms 
T7BD8 000:534.856 JLINK_IsHalted()
T7BD8 000:537.190   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 000:537.761 - 2.905ms returns TRUE
T7BD8 000:537.776 JLINK_ReadReg(R15 (PC))
T7BD8 000:537.781 - 0.005ms returns 0x20000000
T7BD8 000:537.786 JLINK_ClrBPEx(BPHandle = 0x00000002)
T7BD8 000:537.790 - 0.004ms returns 0x00
T7BD8 000:537.794 JLINK_ReadReg(R0)
T7BD8 000:537.798 - 0.003ms returns 0x00000001
T7BD8 000:537.803 JLINK_HasError()
T7BD8 000:537.808 JLINK_WriteReg(R0, 0x08000000)
T7BD8 000:537.812 - 0.003ms returns 0
T7BD8 000:537.816 JLINK_WriteReg(R1, 0x00004000)
T7BD8 000:537.820 - 0.003ms returns 0
T7BD8 000:537.824 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 000:537.827 - 0.003ms returns 0
T7BD8 000:537.831 JLINK_WriteReg(R3, 0x00000000)
T7BD8 000:537.834 - 0.003ms returns 0
T7BD8 000:537.839 JLINK_WriteReg(R4, 0x00000000)
T7BD8 000:537.842 - 0.003ms returns 0
T7BD8 000:537.846 JLINK_WriteReg(R5, 0x00000000)
T7BD8 000:537.850 - 0.003ms returns 0
T7BD8 000:537.854 JLINK_WriteReg(R6, 0x00000000)
T7BD8 000:537.857 - 0.003ms returns 0
T7BD8 000:537.861 JLINK_WriteReg(R7, 0x00000000)
T7BD8 000:537.864 - 0.003ms returns 0
T7BD8 000:537.868 JLINK_WriteReg(R8, 0x00000000)
T7BD8 000:537.872 - 0.003ms returns 0
T7BD8 000:537.876 JLINK_WriteReg(R9, 0x20000180)
T7BD8 000:537.879 - 0.003ms returns 0
T7BD8 000:537.883 JLINK_WriteReg(R10, 0x00000000)
T7BD8 000:537.887 - 0.003ms returns 0
T7BD8 000:537.891 JLINK_WriteReg(R11, 0x00000000)
T7BD8 000:537.894 - 0.003ms returns 0
T7BD8 000:537.898 JLINK_WriteReg(R12, 0x00000000)
T7BD8 000:537.910 - 0.012ms returns 0
T7BD8 000:537.914 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 000:537.918 - 0.003ms returns 0
T7BD8 000:537.922 JLINK_WriteReg(R14, 0x20000001)
T7BD8 000:537.926 - 0.003ms returns 0
T7BD8 000:537.930 JLINK_WriteReg(R15 (PC), 0x200000C0)
T7BD8 000:537.933 - 0.003ms returns 0
T7BD8 000:537.937 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 000:537.941 - 0.003ms returns 0
T7BD8 000:537.945 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 000:537.948 - 0.003ms returns 0
T7BD8 000:537.952 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 000:537.956 - 0.003ms returns 0
T7BD8 000:537.960 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 000:537.963 - 0.003ms returns 0
T7BD8 000:537.968 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 000:537.972 - 0.004ms returns 0x00000003
T7BD8 000:537.976 JLINK_Go()
T7BD8 000:537.984   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:540.754 - 2.777ms 
T7BD8 000:540.777 JLINK_IsHalted()
T7BD8 000:541.237 - 0.460ms returns FALSE
T7BD8 000:541.243 JLINK_HasError()
T7BD8 000:548.292 JLINK_IsHalted()
T7BD8 000:548.853 - 0.560ms returns FALSE
T7BD8 000:548.870 JLINK_HasError()
T7BD8 000:550.288 JLINK_IsHalted()
T7BD8 000:550.804 - 0.515ms returns FALSE
T7BD8 000:550.823 JLINK_HasError()
T7BD8 000:552.071 JLINK_IsHalted()
T7BD8 000:552.548 - 0.476ms returns FALSE
T7BD8 000:552.555 JLINK_HasError()
T7BD8 000:554.076 JLINK_IsHalted()
T7BD8 000:554.569 - 0.493ms returns FALSE
T7BD8 000:554.578 JLINK_HasError()
T7BD8 000:556.072 JLINK_IsHalted()
T7BD8 000:556.547 - 0.475ms returns FALSE
T7BD8 000:556.554 JLINK_HasError()
T7BD8 000:558.096 JLINK_IsHalted()
T7BD8 000:558.657 - 0.560ms returns FALSE
T7BD8 000:558.669 JLINK_HasError()
T7BD8 000:561.077 JLINK_IsHalted()
T7BD8 000:561.614 - 0.537ms returns FALSE
T7BD8 000:561.621 JLINK_HasError()
T7BD8 000:563.077 JLINK_IsHalted()
T7BD8 000:563.547 - 0.469ms returns FALSE
T7BD8 000:563.554 JLINK_HasError()
T7BD8 000:565.075 JLINK_IsHalted()
T7BD8 000:565.592 - 0.516ms returns FALSE
T7BD8 000:565.605 JLINK_HasError()
T7BD8 000:567.076 JLINK_IsHalted()
T7BD8 000:567.612 - 0.535ms returns FALSE
T7BD8 000:567.622 JLINK_HasError()
T7BD8 000:569.074 JLINK_IsHalted()
T7BD8 000:569.591 - 0.516ms returns FALSE
T7BD8 000:569.602 JLINK_HasError()
T7BD8 000:571.073 JLINK_IsHalted()
T7BD8 000:571.562 - 0.488ms returns FALSE
T7BD8 000:571.569 JLINK_HasError()
T7BD8 000:573.074 JLINK_IsHalted()
T7BD8 000:573.591 - 0.515ms returns FALSE
T7BD8 000:573.598 JLINK_HasError()
T7BD8 000:575.077 JLINK_IsHalted()
T7BD8 000:575.609 - 0.531ms returns FALSE
T7BD8 000:575.619 JLINK_HasError()
T7BD8 000:577.076 JLINK_IsHalted()
T7BD8 000:577.595 - 0.518ms returns FALSE
T7BD8 000:577.605 JLINK_HasError()
T7BD8 000:579.074 JLINK_IsHalted()
T7BD8 000:579.593 - 0.518ms returns FALSE
T7BD8 000:579.606 JLINK_HasError()
T7BD8 000:581.078 JLINK_IsHalted()
T7BD8 000:581.610 - 0.532ms returns FALSE
T7BD8 000:581.618 JLINK_HasError()
T7BD8 000:583.074 JLINK_IsHalted()
T7BD8 000:583.548 - 0.473ms returns FALSE
T7BD8 000:583.557 JLINK_HasError()
T7BD8 000:585.077 JLINK_IsHalted()
T7BD8 000:585.575 - 0.497ms returns FALSE
T7BD8 000:585.582 JLINK_HasError()
T7BD8 000:587.079 JLINK_IsHalted()
T7BD8 000:587.569 - 0.489ms returns FALSE
T7BD8 000:587.577 JLINK_HasError()
T7BD8 000:589.581 JLINK_IsHalted()
T7BD8 000:590.107 - 0.526ms returns FALSE
T7BD8 000:590.117 JLINK_HasError()
T7BD8 000:591.582 JLINK_IsHalted()
T7BD8 000:592.098 - 0.515ms returns FALSE
T7BD8 000:592.106 JLINK_HasError()
T7BD8 000:593.581 JLINK_IsHalted()
T7BD8 000:594.070 - 0.489ms returns FALSE
T7BD8 000:594.076 JLINK_HasError()
T7BD8 000:595.586 JLINK_IsHalted()
T7BD8 000:596.067 - 0.481ms returns FALSE
T7BD8 000:596.079 JLINK_HasError()
T7BD8 000:597.587 JLINK_IsHalted()
T7BD8 000:598.109 - 0.521ms returns FALSE
T7BD8 000:598.116 JLINK_HasError()
T7BD8 000:599.583 JLINK_IsHalted()
T7BD8 000:600.108 - 0.524ms returns FALSE
T7BD8 000:600.115 JLINK_HasError()
T7BD8 000:601.580 JLINK_IsHalted()
T7BD8 000:602.060 - 0.480ms returns FALSE
T7BD8 000:602.066 JLINK_HasError()
T7BD8 000:603.583 JLINK_IsHalted()
T7BD8 000:604.071 - 0.488ms returns FALSE
T7BD8 000:604.083 JLINK_HasError()
T7BD8 000:606.584 JLINK_IsHalted()
T7BD8 000:607.107 - 0.523ms returns FALSE
T7BD8 000:607.129 JLINK_HasError()
T7BD8 000:608.581 JLINK_IsHalted()
T7BD8 000:609.113 - 0.531ms returns FALSE
T7BD8 000:609.131 JLINK_HasError()
T7BD8 000:610.582 JLINK_IsHalted()
T7BD8 000:611.111 - 0.528ms returns FALSE
T7BD8 000:611.118 JLINK_HasError()
T7BD8 000:612.583 JLINK_IsHalted()
T7BD8 000:613.096 - 0.513ms returns FALSE
T7BD8 000:613.105 JLINK_HasError()
T7BD8 000:614.580 JLINK_IsHalted()
T7BD8 000:615.068 - 0.488ms returns FALSE
T7BD8 000:615.076 JLINK_HasError()
T7BD8 000:616.582 JLINK_IsHalted()
T7BD8 000:617.075 - 0.492ms returns FALSE
T7BD8 000:617.093 JLINK_HasError()
T7BD8 000:618.586 JLINK_IsHalted()
T7BD8 000:619.143 - 0.557ms returns FALSE
T7BD8 000:619.151 JLINK_HasError()
T7BD8 000:620.585 JLINK_IsHalted()
T7BD8 000:621.060 - 0.474ms returns FALSE
T7BD8 000:621.073 JLINK_HasError()
T7BD8 000:622.582 JLINK_IsHalted()
T7BD8 000:623.068 - 0.486ms returns FALSE
T7BD8 000:623.075 JLINK_HasError()
T7BD8 000:624.582 JLINK_IsHalted()
T7BD8 000:625.068 - 0.485ms returns FALSE
T7BD8 000:625.075 JLINK_HasError()
T7BD8 000:626.582 JLINK_IsHalted()
T7BD8 000:627.070 - 0.488ms returns FALSE
T7BD8 000:627.078 JLINK_HasError()
T7BD8 000:628.583 JLINK_IsHalted()
T7BD8 000:629.120 - 0.536ms returns FALSE
T7BD8 000:629.138 JLINK_HasError()
T7BD8 000:630.584 JLINK_IsHalted()
T7BD8 000:631.110 - 0.526ms returns FALSE
T7BD8 000:631.117 JLINK_HasError()
T7BD8 000:632.579 JLINK_IsHalted()
T7BD8 000:633.068 - 0.488ms returns FALSE
T7BD8 000:633.076 JLINK_HasError()
T7BD8 000:634.588 JLINK_IsHalted()
T7BD8 000:635.109 - 0.520ms returns FALSE
T7BD8 000:635.117 JLINK_HasError()
T7BD8 000:636.580 JLINK_IsHalted()
T7BD8 000:637.065 - 0.485ms returns FALSE
T7BD8 000:637.080 JLINK_HasError()
T7BD8 000:638.585 JLINK_IsHalted()
T7BD8 000:639.127 - 0.541ms returns FALSE
T7BD8 000:639.139 JLINK_HasError()
T7BD8 000:640.582 JLINK_IsHalted()
T7BD8 000:641.108 - 0.525ms returns FALSE
T7BD8 000:641.116 JLINK_HasError()
T7BD8 000:642.584 JLINK_IsHalted()
T7BD8 000:643.099 - 0.514ms returns FALSE
T7BD8 000:643.108 JLINK_HasError()
T7BD8 000:644.582 JLINK_IsHalted()
T7BD8 000:645.069 - 0.487ms returns FALSE
T7BD8 000:645.085 JLINK_HasError()
T7BD8 000:646.584 JLINK_IsHalted()
T7BD8 000:647.073 - 0.488ms returns FALSE
T7BD8 000:647.088 JLINK_HasError()
T7BD8 000:648.584 JLINK_IsHalted()
T7BD8 000:649.107 - 0.522ms returns FALSE
T7BD8 000:649.114 JLINK_HasError()
T7BD8 000:651.582 JLINK_IsHalted()
T7BD8 000:652.111 - 0.528ms returns FALSE
T7BD8 000:652.119 JLINK_HasError()
T7BD8 000:653.583 JLINK_IsHalted()
T7BD8 000:654.111 - 0.527ms returns FALSE
T7BD8 000:654.117 JLINK_HasError()
T7BD8 000:655.581 JLINK_IsHalted()
T7BD8 000:656.069 - 0.487ms returns FALSE
T7BD8 000:656.075 JLINK_HasError()
T7BD8 000:657.584 JLINK_IsHalted()
T7BD8 000:658.145 - 0.561ms returns FALSE
T7BD8 000:658.157 JLINK_HasError()
T7BD8 000:659.582 JLINK_IsHalted()
T7BD8 000:660.110 - 0.527ms returns FALSE
T7BD8 000:660.117 JLINK_HasError()
T7BD8 000:661.585 JLINK_IsHalted()
T7BD8 000:662.069 - 0.483ms returns FALSE
T7BD8 000:662.077 JLINK_HasError()
T7BD8 000:663.582 JLINK_IsHalted()
T7BD8 000:664.112 - 0.529ms returns FALSE
T7BD8 000:664.122 JLINK_HasError()
T7BD8 000:666.585 JLINK_IsHalted()
T7BD8 000:667.074 - 0.488ms returns FALSE
T7BD8 000:667.092 JLINK_HasError()
T7BD8 000:668.584 JLINK_IsHalted()
T7BD8 000:669.101 - 0.517ms returns FALSE
T7BD8 000:669.116 JLINK_HasError()
T7BD8 000:670.577 JLINK_IsHalted()
T7BD8 000:671.059 - 0.481ms returns FALSE
T7BD8 000:671.066 JLINK_HasError()
T7BD8 000:672.590 JLINK_IsHalted()
T7BD8 000:673.115 - 0.524ms returns FALSE
T7BD8 000:673.126 JLINK_HasError()
T7BD8 000:674.582 JLINK_IsHalted()
T7BD8 000:675.097 - 0.514ms returns FALSE
T7BD8 000:675.105 JLINK_HasError()
T7BD8 000:676.587 JLINK_IsHalted()
T7BD8 000:677.105 - 0.517ms returns FALSE
T7BD8 000:677.117 JLINK_HasError()
T7BD8 000:678.582 JLINK_IsHalted()
T7BD8 000:679.113 - 0.530ms returns FALSE
T7BD8 000:679.128 JLINK_HasError()
T7BD8 000:680.584 JLINK_IsHalted()
T7BD8 000:681.110 - 0.526ms returns FALSE
T7BD8 000:681.117 JLINK_HasError()
T7BD8 000:682.584 JLINK_IsHalted()
T7BD8 000:683.070 - 0.485ms returns FALSE
T7BD8 000:683.078 JLINK_HasError()
T7BD8 000:684.585 JLINK_IsHalted()
T7BD8 000:685.098 - 0.512ms returns FALSE
T7BD8 000:685.105 JLINK_HasError()
T7BD8 000:686.582 JLINK_IsHalted()
T7BD8 000:687.074 - 0.491ms returns FALSE
T7BD8 000:687.090 JLINK_HasError()
T7BD8 000:688.587 JLINK_IsHalted()
T7BD8 000:689.115 - 0.527ms returns FALSE
T7BD8 000:689.131 JLINK_HasError()
T7BD8 000:691.089 JLINK_IsHalted()
T7BD8 000:691.614 - 0.524ms returns FALSE
T7BD8 000:691.621 JLINK_HasError()
T7BD8 000:693.096 JLINK_IsHalted()
T7BD8 000:693.571 - 0.474ms returns FALSE
T7BD8 000:693.580 JLINK_HasError()
T7BD8 000:695.090 JLINK_IsHalted()
T7BD8 000:695.627 - 0.536ms returns FALSE
T7BD8 000:695.640 JLINK_HasError()
T7BD8 000:697.089 JLINK_IsHalted()
T7BD8 000:697.614 - 0.524ms returns FALSE
T7BD8 000:697.640 JLINK_HasError()
T7BD8 000:699.096 JLINK_IsHalted()
T7BD8 000:699.636 - 0.540ms returns FALSE
T7BD8 000:699.645 JLINK_HasError()
T7BD8 000:701.086 JLINK_IsHalted()
T7BD8 000:701.581 - 0.494ms returns FALSE
T7BD8 000:701.588 JLINK_HasError()
T7BD8 000:703.088 JLINK_IsHalted()
T7BD8 000:703.598 - 0.509ms returns FALSE
T7BD8 000:703.605 JLINK_HasError()
T7BD8 000:705.085 JLINK_IsHalted()
T7BD8 000:705.580 - 0.494ms returns FALSE
T7BD8 000:705.586 JLINK_HasError()
T7BD8 000:707.029 JLINK_IsHalted()
T7BD8 000:707.555 - 0.525ms returns FALSE
T7BD8 000:707.565 JLINK_HasError()
T7BD8 000:709.535 JLINK_IsHalted()
T7BD8 000:710.053 - 0.518ms returns FALSE
T7BD8 000:710.059 JLINK_HasError()
T7BD8 000:711.533 JLINK_IsHalted()
T7BD8 000:712.069 - 0.535ms returns FALSE
T7BD8 000:712.075 JLINK_HasError()
T7BD8 000:713.533 JLINK_IsHalted()
T7BD8 000:714.027 - 0.493ms returns FALSE
T7BD8 000:714.037 JLINK_HasError()
T7BD8 000:715.531 JLINK_IsHalted()
T7BD8 000:716.025 - 0.493ms returns FALSE
T7BD8 000:716.032 JLINK_HasError()
T7BD8 000:717.535 JLINK_IsHalted()
T7BD8 000:718.065 - 0.529ms returns FALSE
T7BD8 000:718.074 JLINK_HasError()
T7BD8 000:719.533 JLINK_IsHalted()
T7BD8 000:720.064 - 0.530ms returns FALSE
T7BD8 000:720.075 JLINK_HasError()
T7BD8 000:721.536 JLINK_IsHalted()
T7BD8 000:722.072 - 0.535ms returns FALSE
T7BD8 000:722.079 JLINK_HasError()
T7BD8 000:723.531 JLINK_IsHalted()
T7BD8 000:724.018 - 0.486ms returns FALSE
T7BD8 000:724.026 JLINK_HasError()
T7BD8 000:725.540 JLINK_IsHalted()
T7BD8 000:726.027 - 0.487ms returns FALSE
T7BD8 000:726.036 JLINK_HasError()
T7BD8 000:727.532 JLINK_IsHalted()
T7BD8 000:728.074 - 0.541ms returns FALSE
T7BD8 000:728.088 JLINK_HasError()
T7BD8 000:729.534 JLINK_IsHalted()
T7BD8 000:730.062 - 0.527ms returns FALSE
T7BD8 000:730.075 JLINK_HasError()
T7BD8 000:731.532 JLINK_IsHalted()
T7BD8 000:732.026 - 0.493ms returns FALSE
T7BD8 000:732.033 JLINK_HasError()
T7BD8 000:733.535 JLINK_IsHalted()
T7BD8 000:734.024 - 0.488ms returns FALSE
T7BD8 000:734.031 JLINK_HasError()
T7BD8 000:735.531 JLINK_IsHalted()
T7BD8 000:736.024 - 0.493ms returns FALSE
T7BD8 000:736.030 JLINK_HasError()
T7BD8 000:737.536 JLINK_IsHalted()
T7BD8 000:738.066 - 0.529ms returns FALSE
T7BD8 000:738.113 JLINK_HasError()
T7BD8 000:739.532 JLINK_IsHalted()
T7BD8 000:740.060 - 0.527ms returns FALSE
T7BD8 000:740.068 JLINK_HasError()
T7BD8 000:741.534 JLINK_IsHalted()
T7BD8 000:742.070 - 0.535ms returns FALSE
T7BD8 000:742.081 JLINK_HasError()
T7BD8 000:743.533 JLINK_IsHalted()
T7BD8 000:744.027 - 0.493ms returns FALSE
T7BD8 000:744.035 JLINK_HasError()
T7BD8 000:745.534 JLINK_IsHalted()
T7BD8 000:746.026 - 0.492ms returns FALSE
T7BD8 000:746.034 JLINK_HasError()
T7BD8 000:747.536 JLINK_IsHalted()
T7BD8 000:748.028 - 0.491ms returns FALSE
T7BD8 000:748.040 JLINK_HasError()
T7BD8 000:749.532 JLINK_IsHalted()
T7BD8 000:750.060 - 0.527ms returns FALSE
T7BD8 000:750.073 JLINK_HasError()
T7BD8 000:751.534 JLINK_IsHalted()
T7BD8 000:752.072 - 0.537ms returns FALSE
T7BD8 000:752.080 JLINK_HasError()
T7BD8 000:753.535 JLINK_IsHalted()
T7BD8 000:754.026 - 0.491ms returns FALSE
T7BD8 000:754.036 JLINK_HasError()
T7BD8 000:755.535 JLINK_IsHalted()
T7BD8 000:756.056 - 0.521ms returns FALSE
T7BD8 000:756.064 JLINK_HasError()
T7BD8 000:757.534 JLINK_IsHalted()
T7BD8 000:758.065 - 0.530ms returns FALSE
T7BD8 000:758.080 JLINK_HasError()
T7BD8 000:759.534 JLINK_IsHalted()
T7BD8 000:760.054 - 0.519ms returns FALSE
T7BD8 000:760.064 JLINK_HasError()
T7BD8 000:761.531 JLINK_IsHalted()
T7BD8 000:762.024 - 0.493ms returns FALSE
T7BD8 000:762.031 JLINK_HasError()
T7BD8 000:763.534 JLINK_IsHalted()
T7BD8 000:764.027 - 0.493ms returns FALSE
T7BD8 000:764.034 JLINK_HasError()
T7BD8 000:765.531 JLINK_IsHalted()
T7BD8 000:766.026 - 0.494ms returns FALSE
T7BD8 000:766.031 JLINK_HasError()
T7BD8 000:767.530 JLINK_IsHalted()
T7BD8 000:768.069 - 0.538ms returns FALSE
T7BD8 000:768.075 JLINK_HasError()
T7BD8 000:769.532 JLINK_IsHalted()
T7BD8 000:770.068 - 0.535ms returns FALSE
T7BD8 000:770.074 JLINK_HasError()
T7BD8 000:771.532 JLINK_IsHalted()
T7BD8 000:772.027 - 0.494ms returns FALSE
T7BD8 000:772.034 JLINK_HasError()
T7BD8 000:773.535 JLINK_IsHalted()
T7BD8 000:774.056 - 0.520ms returns FALSE
T7BD8 000:774.067 JLINK_HasError()
T7BD8 000:776.544 JLINK_IsHalted()
T7BD8 000:777.057 - 0.515ms returns FALSE
T7BD8 000:777.068 JLINK_HasError()
T7BD8 000:778.544 JLINK_IsHalted()
T7BD8 000:779.079 - 0.534ms returns FALSE
T7BD8 000:779.097 JLINK_HasError()
T7BD8 000:780.537 JLINK_IsHalted()
T7BD8 000:781.069 - 0.532ms returns FALSE
T7BD8 000:781.076 JLINK_HasError()
T7BD8 000:782.533 JLINK_IsHalted()
T7BD8 000:783.018 - 0.484ms returns FALSE
T7BD8 000:783.027 JLINK_HasError()
T7BD8 000:784.540 JLINK_IsHalted()
T7BD8 000:785.062 - 0.522ms returns FALSE
T7BD8 000:785.122 JLINK_HasError()
T7BD8 000:786.533 JLINK_IsHalted()
T7BD8 000:787.025 - 0.491ms returns FALSE
T7BD8 000:787.032 JLINK_HasError()
T7BD8 000:788.496 JLINK_IsHalted()
T7BD8 000:789.017 - 0.521ms returns FALSE
T7BD8 000:789.024 JLINK_HasError()
T7BD8 000:790.509 JLINK_IsHalted()
T7BD8 000:791.013 - 0.503ms returns FALSE
T7BD8 000:791.020 JLINK_HasError()
T7BD8 000:792.505 JLINK_IsHalted()
T7BD8 000:792.982 - 0.476ms returns FALSE
T7BD8 000:792.988 JLINK_HasError()
T7BD8 000:794.503 JLINK_IsHalted()
T7BD8 000:794.984 - 0.480ms returns FALSE
T7BD8 000:794.989 JLINK_HasError()
T7BD8 000:796.503 JLINK_IsHalted()
T7BD8 000:796.990 - 0.485ms returns FALSE
T7BD8 000:797.002 JLINK_HasError()
T7BD8 000:798.517 JLINK_IsHalted()
T7BD8 000:799.028 - 0.510ms returns FALSE
T7BD8 000:799.039 JLINK_HasError()
T7BD8 000:800.504 JLINK_IsHalted()
T7BD8 000:800.963 - 0.459ms returns FALSE
T7BD8 000:800.969 JLINK_HasError()
T7BD8 000:802.504 JLINK_IsHalted()
T7BD8 000:803.008 - 0.504ms returns FALSE
T7BD8 000:803.016 JLINK_HasError()
T7BD8 000:804.504 JLINK_IsHalted()
T7BD8 000:804.984 - 0.480ms returns FALSE
T7BD8 000:804.990 JLINK_HasError()
T7BD8 000:806.503 JLINK_IsHalted()
T7BD8 000:806.985 - 0.481ms returns FALSE
T7BD8 000:806.991 JLINK_HasError()
T7BD8 000:808.506 JLINK_IsHalted()
T7BD8 000:809.025 - 0.518ms returns FALSE
T7BD8 000:809.032 JLINK_HasError()
T7BD8 000:810.504 JLINK_IsHalted()
T7BD8 000:810.963 - 0.459ms returns FALSE
T7BD8 000:810.968 JLINK_HasError()
T7BD8 000:812.503 JLINK_IsHalted()
T7BD8 000:812.984 - 0.481ms returns FALSE
T7BD8 000:812.990 JLINK_HasError()
T7BD8 000:814.503 JLINK_IsHalted()
T7BD8 000:814.984 - 0.480ms returns FALSE
T7BD8 000:814.990 JLINK_HasError()
T7BD8 000:816.503 JLINK_IsHalted()
T7BD8 000:816.990 - 0.486ms returns FALSE
T7BD8 000:817.004 JLINK_HasError()
T7BD8 000:818.507 JLINK_IsHalted()
T7BD8 000:819.030 - 0.522ms returns FALSE
T7BD8 000:819.040 JLINK_HasError()
T7BD8 000:820.506 JLINK_IsHalted()
T7BD8 000:820.982 - 0.475ms returns FALSE
T7BD8 000:820.988 JLINK_HasError()
T7BD8 000:822.503 JLINK_IsHalted()
T7BD8 000:822.984 - 0.481ms returns FALSE
T7BD8 000:822.990 JLINK_HasError()
T7BD8 000:824.503 JLINK_IsHalted()
T7BD8 000:824.984 - 0.480ms returns FALSE
T7BD8 000:824.989 JLINK_HasError()
T7BD8 000:826.504 JLINK_IsHalted()
T7BD8 000:826.968 - 0.464ms returns FALSE
T7BD8 000:826.980 JLINK_HasError()
T7BD8 000:828.507 JLINK_IsHalted()
T7BD8 000:829.029 - 0.521ms returns FALSE
T7BD8 000:829.036 JLINK_HasError()
T7BD8 000:830.508 JLINK_IsHalted()
T7BD8 000:831.025 - 0.517ms returns FALSE
T7BD8 000:831.032 JLINK_HasError()
T7BD8 000:832.504 JLINK_IsHalted()
T7BD8 000:832.964 - 0.459ms returns FALSE
T7BD8 000:832.969 JLINK_HasError()
T7BD8 000:834.506 JLINK_IsHalted()
T7BD8 000:835.017 - 0.510ms returns FALSE
T7BD8 000:835.026 JLINK_HasError()
T7BD8 000:836.504 JLINK_IsHalted()
T7BD8 000:836.968 - 0.463ms returns FALSE
T7BD8 000:836.980 JLINK_HasError()
T7BD8 000:838.508 JLINK_IsHalted()
T7BD8 000:839.027 - 0.518ms returns FALSE
T7BD8 000:839.039 JLINK_HasError()
T7BD8 000:840.510 JLINK_IsHalted()
T7BD8 000:840.981 - 0.471ms returns FALSE
T7BD8 000:840.988 JLINK_HasError()
T7BD8 000:842.503 JLINK_IsHalted()
T7BD8 000:842.984 - 0.481ms returns FALSE
T7BD8 000:842.990 JLINK_HasError()
T7BD8 000:844.503 JLINK_IsHalted()
T7BD8 000:844.984 - 0.480ms returns FALSE
T7BD8 000:844.989 JLINK_HasError()
T7BD8 000:846.503 JLINK_IsHalted()
T7BD8 000:846.990 - 0.486ms returns FALSE
T7BD8 000:846.998 JLINK_HasError()
T7BD8 000:848.507 JLINK_IsHalted()
T7BD8 000:849.024 - 0.517ms returns FALSE
T7BD8 000:849.030 JLINK_HasError()
T7BD8 000:850.504 JLINK_IsHalted()
T7BD8 000:851.014 - 0.510ms returns FALSE
T7BD8 000:851.028 JLINK_HasError()
T7BD8 000:852.506 JLINK_IsHalted()
T7BD8 000:852.984 - 0.477ms returns FALSE
T7BD8 000:852.995 JLINK_HasError()
T7BD8 000:854.505 JLINK_IsHalted()
T7BD8 000:854.980 - 0.475ms returns FALSE
T7BD8 000:854.986 JLINK_HasError()
T7BD8 000:856.503 JLINK_IsHalted()
T7BD8 000:856.989 - 0.486ms returns FALSE
T7BD8 000:856.997 JLINK_HasError()
T7BD8 000:858.506 JLINK_IsHalted()
T7BD8 000:859.034 - 0.527ms returns FALSE
T7BD8 000:859.048 JLINK_HasError()
T7BD8 000:860.504 JLINK_IsHalted()
T7BD8 000:860.965 - 0.461ms returns FALSE
T7BD8 000:860.971 JLINK_HasError()
T7BD8 000:862.505 JLINK_IsHalted()
T7BD8 000:862.984 - 0.479ms returns FALSE
T7BD8 000:862.990 JLINK_HasError()
T7BD8 000:864.504 JLINK_IsHalted()
T7BD8 000:864.984 - 0.480ms returns FALSE
T7BD8 000:864.990 JLINK_HasError()
T7BD8 000:866.503 JLINK_IsHalted()
T7BD8 000:866.983 - 0.479ms returns FALSE
T7BD8 000:866.991 JLINK_HasError()
T7BD8 000:868.507 JLINK_IsHalted()
T7BD8 000:869.009 - 0.501ms returns FALSE
T7BD8 000:869.015 JLINK_HasError()
T7BD8 000:870.503 JLINK_IsHalted()
T7BD8 000:870.980 - 0.476ms returns FALSE
T7BD8 000:870.986 JLINK_HasError()
T7BD8 000:872.503 JLINK_IsHalted()
T7BD8 000:872.984 - 0.481ms returns FALSE
T7BD8 000:872.990 JLINK_HasError()
T7BD8 000:874.504 JLINK_IsHalted()
T7BD8 000:874.985 - 0.480ms returns FALSE
T7BD8 000:874.990 JLINK_HasError()
T7BD8 000:876.507 JLINK_IsHalted()
T7BD8 000:876.992 - 0.485ms returns FALSE
T7BD8 000:877.006 JLINK_HasError()
T7BD8 000:878.509 JLINK_IsHalted()
T7BD8 000:879.009 - 0.500ms returns FALSE
T7BD8 000:879.016 JLINK_HasError()
T7BD8 000:880.505 JLINK_IsHalted()
T7BD8 000:880.963 - 0.457ms returns FALSE
T7BD8 000:880.969 JLINK_HasError()
T7BD8 000:882.505 JLINK_IsHalted()
T7BD8 000:883.018 - 0.512ms returns FALSE
T7BD8 000:883.027 JLINK_HasError()
T7BD8 000:885.506 JLINK_IsHalted()
T7BD8 000:886.014 - 0.507ms returns FALSE
T7BD8 000:886.020 JLINK_HasError()
T7BD8 000:887.508 JLINK_IsHalted()
T7BD8 000:888.194 - 0.685ms returns FALSE
T7BD8 000:888.201 JLINK_HasError()
T7BD8 000:889.509 JLINK_IsHalted()
T7BD8 000:890.013 - 0.504ms returns FALSE
T7BD8 000:890.019 JLINK_HasError()
T7BD8 000:892.013 JLINK_IsHalted()
T7BD8 000:892.489 - 0.475ms returns FALSE
T7BD8 000:892.496 JLINK_HasError()
T7BD8 000:894.010 JLINK_IsHalted()
T7BD8 000:894.487 - 0.476ms returns FALSE
T7BD8 000:894.492 JLINK_HasError()
T7BD8 000:896.010 JLINK_IsHalted()
T7BD8 000:896.487 - 0.477ms returns FALSE
T7BD8 000:896.492 JLINK_HasError()
T7BD8 000:898.013 JLINK_IsHalted()
T7BD8 000:898.770 - 0.756ms returns FALSE
T7BD8 000:898.784 JLINK_HasError()
T7BD8 000:900.017 JLINK_IsHalted()
T7BD8 000:900.531 - 0.513ms returns FALSE
T7BD8 000:900.538 JLINK_HasError()
T7BD8 000:902.013 JLINK_IsHalted()
T7BD8 000:902.524 - 0.511ms returns FALSE
T7BD8 000:902.533 JLINK_HasError()
T7BD8 000:904.011 JLINK_IsHalted()
T7BD8 000:904.502 - 0.490ms returns FALSE
T7BD8 000:904.507 JLINK_HasError()
T7BD8 000:906.010 JLINK_IsHalted()
T7BD8 000:906.464 - 0.453ms returns FALSE
T7BD8 000:906.469 JLINK_HasError()
T7BD8 000:908.013 JLINK_IsHalted()
T7BD8 000:908.532 - 0.518ms returns FALSE
T7BD8 000:908.539 JLINK_HasError()
T7BD8 000:910.010 JLINK_IsHalted()
T7BD8 000:910.468 - 0.457ms returns FALSE
T7BD8 000:910.473 JLINK_HasError()
T7BD8 000:912.010 JLINK_IsHalted()
T7BD8 000:912.463 - 0.452ms returns FALSE
T7BD8 000:912.468 JLINK_HasError()
T7BD8 000:914.010 JLINK_IsHalted()
T7BD8 000:914.520 - 0.510ms returns FALSE
T7BD8 000:914.533 JLINK_HasError()
T7BD8 000:916.010 JLINK_IsHalted()
T7BD8 000:916.494 - 0.483ms returns FALSE
T7BD8 000:916.500 JLINK_HasError()
T7BD8 000:918.013 JLINK_IsHalted()
T7BD8 000:918.491 - 0.478ms returns FALSE
T7BD8 000:918.505 JLINK_HasError()
T7BD8 000:920.012 JLINK_IsHalted()
T7BD8 000:920.509 - 0.497ms returns FALSE
T7BD8 000:920.516 JLINK_HasError()
T7BD8 000:922.010 JLINK_IsHalted()
T7BD8 000:922.501 - 0.491ms returns FALSE
T7BD8 000:922.507 JLINK_HasError()
T7BD8 000:924.015 JLINK_IsHalted()
T7BD8 000:924.481 - 0.466ms returns FALSE
T7BD8 000:924.488 JLINK_HasError()
T7BD8 000:926.010 JLINK_IsHalted()
T7BD8 000:926.463 - 0.452ms returns FALSE
T7BD8 000:926.468 JLINK_HasError()
T7BD8 000:928.014 JLINK_IsHalted()
T7BD8 000:928.510 - 0.495ms returns FALSE
T7BD8 000:928.525 JLINK_HasError()
T7BD8 000:930.010 JLINK_IsHalted()
T7BD8 000:930.481 - 0.470ms returns FALSE
T7BD8 000:930.488 JLINK_HasError()
T7BD8 000:932.010 JLINK_IsHalted()
T7BD8 000:932.463 - 0.452ms returns FALSE
T7BD8 000:932.468 JLINK_HasError()
T7BD8 000:934.010 JLINK_IsHalted()
T7BD8 000:934.463 - 0.453ms returns FALSE
T7BD8 000:934.469 JLINK_HasError()
T7BD8 000:936.010 JLINK_IsHalted()
T7BD8 000:936.463 - 0.452ms returns FALSE
T7BD8 000:936.469 JLINK_HasError()
T7BD8 000:938.013 JLINK_IsHalted()
T7BD8 000:938.518 - 0.505ms returns FALSE
T7BD8 000:938.532 JLINK_HasError()
T7BD8 000:940.012 JLINK_IsHalted()
T7BD8 000:940.480 - 0.468ms returns FALSE
T7BD8 000:940.486 JLINK_HasError()
T7BD8 000:942.010 JLINK_IsHalted()
T7BD8 000:942.462 - 0.452ms returns FALSE
T7BD8 000:942.468 JLINK_HasError()
T7BD8 000:944.010 JLINK_IsHalted()
T7BD8 000:944.463 - 0.453ms returns FALSE
T7BD8 000:944.469 JLINK_HasError()
T7BD8 000:946.010 JLINK_IsHalted()
T7BD8 000:946.519 - 0.509ms returns FALSE
T7BD8 000:946.531 JLINK_HasError()
T7BD8 000:948.013 JLINK_IsHalted()
T7BD8 000:948.536 - 0.522ms returns FALSE
T7BD8 000:948.551 JLINK_HasError()
T7BD8 000:950.013 JLINK_IsHalted()
T7BD8 000:950.552 - 0.538ms returns FALSE
T7BD8 000:950.559 JLINK_HasError()
T7BD8 000:952.010 JLINK_IsHalted()
T7BD8 000:952.463 - 0.452ms returns FALSE
T7BD8 000:952.468 JLINK_HasError()
T7BD8 000:954.010 JLINK_IsHalted()
T7BD8 000:956.273   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 000:956.753 - 2.743ms returns TRUE
T7BD8 000:956.759 JLINK_ReadReg(R15 (PC))
T7BD8 000:956.764 - 0.005ms returns 0x20000000
T7BD8 000:956.769 JLINK_ClrBPEx(BPHandle = 0x00000003)
T7BD8 000:956.772 - 0.003ms returns 0x00
T7BD8 000:956.777 JLINK_ReadReg(R0)
T7BD8 000:956.780 - 0.003ms returns 0x00000000
T7BD8 000:957.169 JLINK_HasError()
T7BD8 000:957.178 JLINK_WriteReg(R0, 0x08004000)
T7BD8 000:957.183 - 0.004ms returns 0
T7BD8 000:957.187 JLINK_WriteReg(R1, 0x00004000)
T7BD8 000:957.191 - 0.004ms returns 0
T7BD8 000:957.195 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 000:957.199 - 0.003ms returns 0
T7BD8 000:957.203 JLINK_WriteReg(R3, 0x00000000)
T7BD8 000:957.206 - 0.003ms returns 0
T7BD8 000:957.210 JLINK_WriteReg(R4, 0x00000000)
T7BD8 000:957.213 - 0.003ms returns 0
T7BD8 000:957.217 JLINK_WriteReg(R5, 0x00000000)
T7BD8 000:957.221 - 0.003ms returns 0
T7BD8 000:957.225 JLINK_WriteReg(R6, 0x00000000)
T7BD8 000:957.229 - 0.003ms returns 0
T7BD8 000:957.233 JLINK_WriteReg(R7, 0x00000000)
T7BD8 000:957.236 - 0.003ms returns 0
T7BD8 000:957.240 JLINK_WriteReg(R8, 0x00000000)
T7BD8 000:957.243 - 0.003ms returns 0
T7BD8 000:957.247 JLINK_WriteReg(R9, 0x20000180)
T7BD8 000:957.251 - 0.003ms returns 0
T7BD8 000:957.255 JLINK_WriteReg(R10, 0x00000000)
T7BD8 000:957.258 - 0.003ms returns 0
T7BD8 000:957.262 JLINK_WriteReg(R11, 0x00000000)
T7BD8 000:957.266 - 0.003ms returns 0
T7BD8 000:957.270 JLINK_WriteReg(R12, 0x00000000)
T7BD8 000:957.273 - 0.003ms returns 0
T7BD8 000:957.278 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 000:957.282 - 0.004ms returns 0
T7BD8 000:957.286 JLINK_WriteReg(R14, 0x20000001)
T7BD8 000:957.289 - 0.003ms returns 0
T7BD8 000:957.293 JLINK_WriteReg(R15 (PC), 0x20000020)
T7BD8 000:957.297 - 0.003ms returns 0
T7BD8 000:957.302 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 000:957.305 - 0.004ms returns 0
T7BD8 000:957.309 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 000:957.313 - 0.003ms returns 0
T7BD8 000:957.317 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 000:957.321 - 0.003ms returns 0
T7BD8 000:957.325 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 000:957.328 - 0.003ms returns 0
T7BD8 000:957.333 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 000:957.337 - 0.004ms returns 0x00000004
T7BD8 000:957.341 JLINK_Go()
T7BD8 000:957.350   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:960.319 - 2.976ms 
T7BD8 000:960.344 JLINK_IsHalted()
T7BD8 000:962.704   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 000:963.172 - 2.827ms returns TRUE
T7BD8 000:963.180 JLINK_ReadReg(R15 (PC))
T7BD8 000:963.185 - 0.005ms returns 0x20000000
T7BD8 000:963.195 JLINK_ClrBPEx(BPHandle = 0x00000004)
T7BD8 000:963.199 - 0.004ms returns 0x00
T7BD8 000:963.203 JLINK_ReadReg(R0)
T7BD8 000:963.207 - 0.004ms returns 0x00000001
T7BD8 000:963.212 JLINK_HasError()
T7BD8 000:963.217 JLINK_WriteReg(R0, 0x08004000)
T7BD8 000:963.221 - 0.003ms returns 0
T7BD8 000:963.225 JLINK_WriteReg(R1, 0x00004000)
T7BD8 000:963.228 - 0.003ms returns 0
T7BD8 000:963.232 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 000:963.236 - 0.003ms returns 0
T7BD8 000:963.240 JLINK_WriteReg(R3, 0x00000000)
T7BD8 000:963.244 - 0.003ms returns 0
T7BD8 000:963.248 JLINK_WriteReg(R4, 0x00000000)
T7BD8 000:963.251 - 0.003ms returns 0
T7BD8 000:963.255 JLINK_WriteReg(R5, 0x00000000)
T7BD8 000:963.258 - 0.003ms returns 0
T7BD8 000:963.262 JLINK_WriteReg(R6, 0x00000000)
T7BD8 000:963.266 - 0.003ms returns 0
T7BD8 000:963.270 JLINK_WriteReg(R7, 0x00000000)
T7BD8 000:963.273 - 0.003ms returns 0
T7BD8 000:963.277 JLINK_WriteReg(R8, 0x00000000)
T7BD8 000:963.281 - 0.003ms returns 0
T7BD8 000:963.285 JLINK_WriteReg(R9, 0x20000180)
T7BD8 000:963.288 - 0.003ms returns 0
T7BD8 000:963.292 JLINK_WriteReg(R10, 0x00000000)
T7BD8 000:963.295 - 0.003ms returns 0
T7BD8 000:963.299 JLINK_WriteReg(R11, 0x00000000)
T7BD8 000:963.303 - 0.003ms returns 0
T7BD8 000:963.307 JLINK_WriteReg(R12, 0x00000000)
T7BD8 000:963.310 - 0.003ms returns 0
T7BD8 000:963.314 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 000:963.318 - 0.003ms returns 0
T7BD8 000:963.322 JLINK_WriteReg(R14, 0x20000001)
T7BD8 000:963.325 - 0.003ms returns 0
T7BD8 000:963.330 JLINK_WriteReg(R15 (PC), 0x200000C0)
T7BD8 000:963.333 - 0.003ms returns 0
T7BD8 000:963.337 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 000:963.341 - 0.003ms returns 0
T7BD8 000:963.345 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 000:963.348 - 0.003ms returns 0
T7BD8 000:963.352 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 000:963.356 - 0.003ms returns 0
T7BD8 000:963.360 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 000:963.363 - 0.003ms returns 0
T7BD8 000:963.367 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 000:963.371 - 0.004ms returns 0x00000005
T7BD8 000:963.375 JLINK_Go()
T7BD8 000:963.383   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 000:966.109 - 2.733ms 
T7BD8 000:966.115 JLINK_IsHalted()
T7BD8 000:966.566 - 0.451ms returns FALSE
T7BD8 000:966.572 JLINK_HasError()
T7BD8 000:968.014 JLINK_IsHalted()
T7BD8 000:968.531 - 0.517ms returns FALSE
T7BD8 000:968.539 JLINK_HasError()
T7BD8 000:970.010 JLINK_IsHalted()
T7BD8 000:970.480 - 0.469ms returns FALSE
T7BD8 000:970.486 JLINK_HasError()
T7BD8 000:972.010 JLINK_IsHalted()
T7BD8 000:972.463 - 0.453ms returns FALSE
T7BD8 000:972.469 JLINK_HasError()
T7BD8 000:974.011 JLINK_IsHalted()
T7BD8 000:974.471 - 0.460ms returns FALSE
T7BD8 000:974.476 JLINK_HasError()
T7BD8 000:976.012 JLINK_IsHalted()
T7BD8 000:976.482 - 0.470ms returns FALSE
T7BD8 000:976.492 JLINK_HasError()
T7BD8 000:978.016 JLINK_IsHalted()
T7BD8 000:978.553 - 0.536ms returns FALSE
T7BD8 000:978.567 JLINK_HasError()
T7BD8 000:980.015 JLINK_IsHalted()
T7BD8 000:980.539 - 0.523ms returns FALSE
T7BD8 000:980.545 JLINK_HasError()
T7BD8 000:982.012 JLINK_IsHalted()
T7BD8 000:982.472 - 0.459ms returns FALSE
T7BD8 000:982.477 JLINK_HasError()
T7BD8 000:984.011 JLINK_IsHalted()
T7BD8 000:984.471 - 0.459ms returns FALSE
T7BD8 000:984.477 JLINK_HasError()
T7BD8 000:986.016 JLINK_IsHalted()
T7BD8 000:986.507 - 0.490ms returns FALSE
T7BD8 000:986.513 JLINK_HasError()
T7BD8 000:988.015 JLINK_IsHalted()
T7BD8 000:988.531 - 0.515ms returns FALSE
T7BD8 000:988.538 JLINK_HasError()
T7BD8 000:990.516 JLINK_IsHalted()
T7BD8 000:990.980 - 0.464ms returns FALSE
T7BD8 000:990.985 JLINK_HasError()
T7BD8 000:992.516 JLINK_IsHalted()
T7BD8 000:993.025 - 0.508ms returns FALSE
T7BD8 000:993.031 JLINK_HasError()
T7BD8 000:995.517 JLINK_IsHalted()
T7BD8 000:996.025 - 0.507ms returns FALSE
T7BD8 000:996.031 JLINK_HasError()
T7BD8 000:997.519 JLINK_IsHalted()
T7BD8 000:998.013 - 0.493ms returns FALSE
T7BD8 000:998.021 JLINK_HasError()
T7BD8 000:999.518 JLINK_IsHalted()
T7BD8 001:000.009 - 0.490ms returns FALSE
T7BD8 001:000.015 JLINK_HasError()
T7BD8 001:001.517 JLINK_IsHalted()
T7BD8 001:001.971 - 0.453ms returns FALSE
T7BD8 001:001.977 JLINK_HasError()
T7BD8 001:003.518 JLINK_IsHalted()
T7BD8 001:003.982 - 0.464ms returns FALSE
T7BD8 001:003.992 JLINK_HasError()
T7BD8 001:005.515 JLINK_IsHalted()
T7BD8 001:005.971 - 0.455ms returns FALSE
T7BD8 001:005.976 JLINK_HasError()
T7BD8 001:007.518 JLINK_IsHalted()
T7BD8 001:008.157 - 0.638ms returns FALSE
T7BD8 001:008.169 JLINK_HasError()
T7BD8 001:009.520 JLINK_IsHalted()
T7BD8 001:010.016 - 0.496ms returns FALSE
T7BD8 001:010.022 JLINK_HasError()
T7BD8 001:011.518 JLINK_IsHalted()
T7BD8 001:011.980 - 0.462ms returns FALSE
T7BD8 001:011.986 JLINK_HasError()
T7BD8 001:013.520 JLINK_IsHalted()
T7BD8 001:014.054 - 0.534ms returns FALSE
T7BD8 001:014.063 JLINK_HasError()
T7BD8 001:015.516 JLINK_IsHalted()
T7BD8 001:015.981 - 0.464ms returns FALSE
T7BD8 001:015.986 JLINK_HasError()
T7BD8 001:017.522 JLINK_IsHalted()
T7BD8 001:018.020 - 0.498ms returns FALSE
T7BD8 001:018.034 JLINK_HasError()
T7BD8 001:019.518 JLINK_IsHalted()
T7BD8 001:020.014 - 0.495ms returns FALSE
T7BD8 001:020.026 JLINK_HasError()
T7BD8 001:021.519 JLINK_IsHalted()
T7BD8 001:022.008 - 0.489ms returns FALSE
T7BD8 001:022.015 JLINK_HasError()
T7BD8 001:023.516 JLINK_IsHalted()
T7BD8 001:023.984 - 0.467ms returns FALSE
T7BD8 001:023.992 JLINK_HasError()
T7BD8 001:025.516 JLINK_IsHalted()
T7BD8 001:025.980 - 0.464ms returns FALSE
T7BD8 001:025.986 JLINK_HasError()
T7BD8 001:027.522 JLINK_IsHalted()
T7BD8 001:028.017 - 0.494ms returns FALSE
T7BD8 001:028.025 JLINK_HasError()
T7BD8 001:029.516 JLINK_IsHalted()
T7BD8 001:029.982 - 0.466ms returns FALSE
T7BD8 001:029.989 JLINK_HasError()
T7BD8 001:031.516 JLINK_IsHalted()
T7BD8 001:031.971 - 0.455ms returns FALSE
T7BD8 001:031.976 JLINK_HasError()
T7BD8 001:033.515 JLINK_IsHalted()
T7BD8 001:033.971 - 0.455ms returns FALSE
T7BD8 001:033.977 JLINK_HasError()
T7BD8 001:035.515 JLINK_IsHalted()
T7BD8 001:035.971 - 0.455ms returns FALSE
T7BD8 001:035.977 JLINK_HasError()
T7BD8 001:037.519 JLINK_IsHalted()
T7BD8 001:038.017 - 0.498ms returns FALSE
T7BD8 001:038.023 JLINK_HasError()
T7BD8 001:039.517 JLINK_IsHalted()
T7BD8 001:040.059 - 0.542ms returns FALSE
T7BD8 001:040.068 JLINK_HasError()
T7BD8 001:041.516 JLINK_IsHalted()
T7BD8 001:041.980 - 0.464ms returns FALSE
T7BD8 001:041.986 JLINK_HasError()
T7BD8 001:043.516 JLINK_IsHalted()
T7BD8 001:043.971 - 0.455ms returns FALSE
T7BD8 001:043.977 JLINK_HasError()
T7BD8 001:045.515 JLINK_IsHalted()
T7BD8 001:045.971 - 0.455ms returns FALSE
T7BD8 001:045.976 JLINK_HasError()
T7BD8 001:047.519 JLINK_IsHalted()
T7BD8 001:048.010 - 0.490ms returns FALSE
T7BD8 001:048.017 JLINK_HasError()
T7BD8 001:049.516 JLINK_IsHalted()
T7BD8 001:049.981 - 0.464ms returns FALSE
T7BD8 001:049.987 JLINK_HasError()
T7BD8 001:051.517 JLINK_IsHalted()
T7BD8 001:051.971 - 0.453ms returns FALSE
T7BD8 001:051.977 JLINK_HasError()
T7BD8 001:053.543 JLINK_IsHalted()
T7BD8 001:054.025 - 0.482ms returns FALSE
T7BD8 001:054.032 JLINK_HasError()
T7BD8 001:055.516 JLINK_IsHalted()
T7BD8 001:056.068 - 0.552ms returns FALSE
T7BD8 001:056.077 JLINK_HasError()
T7BD8 001:057.524 JLINK_IsHalted()
T7BD8 001:058.283 - 0.759ms returns FALSE
T7BD8 001:058.293 JLINK_HasError()
T7BD8 001:059.517 JLINK_IsHalted()
T7BD8 001:060.012 - 0.494ms returns FALSE
T7BD8 001:060.018 JLINK_HasError()
T7BD8 001:061.518 JLINK_IsHalted()
T7BD8 001:062.009 - 0.491ms returns FALSE
T7BD8 001:062.016 JLINK_HasError()
T7BD8 001:063.515 JLINK_IsHalted()
T7BD8 001:063.972 - 0.456ms returns FALSE
T7BD8 001:063.977 JLINK_HasError()
T7BD8 001:065.515 JLINK_IsHalted()
T7BD8 001:065.971 - 0.455ms returns FALSE
T7BD8 001:065.977 JLINK_HasError()
T7BD8 001:067.517 JLINK_IsHalted()
T7BD8 001:068.261 - 0.743ms returns FALSE
T7BD8 001:068.272 JLINK_HasError()
T7BD8 001:069.520 JLINK_IsHalted()
T7BD8 001:070.016 - 0.495ms returns FALSE
T7BD8 001:070.027 JLINK_HasError()
T7BD8 001:071.516 JLINK_IsHalted()
T7BD8 001:072.018 - 0.502ms returns FALSE
T7BD8 001:072.028 JLINK_HasError()
T7BD8 001:073.517 JLINK_IsHalted()
T7BD8 001:073.985 - 0.468ms returns FALSE
T7BD8 001:073.991 JLINK_HasError()
T7BD8 001:075.518 JLINK_IsHalted()
T7BD8 001:075.977 - 0.458ms returns FALSE
T7BD8 001:075.982 JLINK_HasError()
T7BD8 001:077.521 JLINK_IsHalted()
T7BD8 001:078.903 - 1.382ms returns FALSE
T7BD8 001:078.921 JLINK_HasError()
T7BD8 001:080.518 JLINK_IsHalted()
T7BD8 001:080.981 - 0.462ms returns FALSE
T7BD8 001:080.987 JLINK_HasError()
T7BD8 001:082.518 JLINK_IsHalted()
T7BD8 001:082.980 - 0.462ms returns FALSE
T7BD8 001:082.986 JLINK_HasError()
T7BD8 001:084.518 JLINK_IsHalted()
T7BD8 001:084.972 - 0.454ms returns FALSE
T7BD8 001:084.977 JLINK_HasError()
T7BD8 001:086.518 JLINK_IsHalted()
T7BD8 001:086.989 - 0.471ms returns FALSE
T7BD8 001:087.002 JLINK_HasError()
T7BD8 001:088.522 JLINK_IsHalted()
T7BD8 001:089.029 - 0.506ms returns FALSE
T7BD8 001:089.035 JLINK_HasError()
T7BD8 001:091.024 JLINK_IsHalted()
T7BD8 001:091.516 - 0.492ms returns FALSE
T7BD8 001:091.522 JLINK_HasError()
T7BD8 001:093.022 JLINK_IsHalted()
T7BD8 001:093.485 - 0.463ms returns FALSE
T7BD8 001:093.490 JLINK_HasError()
T7BD8 001:095.022 JLINK_IsHalted()
T7BD8 001:095.481 - 0.459ms returns FALSE
T7BD8 001:095.486 JLINK_HasError()
T7BD8 001:097.023 JLINK_IsHalted()
T7BD8 001:097.765 - 0.741ms returns FALSE
T7BD8 001:097.779 JLINK_HasError()
T7BD8 001:099.025 JLINK_IsHalted()
T7BD8 001:099.537 - 0.511ms returns FALSE
T7BD8 001:099.544 JLINK_HasError()
T7BD8 001:101.022 JLINK_IsHalted()
T7BD8 001:101.481 - 0.459ms returns FALSE
T7BD8 001:101.487 JLINK_HasError()
T7BD8 001:103.022 JLINK_IsHalted()
T7BD8 001:103.529 - 0.507ms returns FALSE
T7BD8 001:103.537 JLINK_HasError()
T7BD8 001:106.023 JLINK_IsHalted()
T7BD8 001:106.517 - 0.494ms returns FALSE
T7BD8 001:106.523 JLINK_HasError()
T7BD8 001:108.025 JLINK_IsHalted()
T7BD8 001:108.833 - 0.807ms returns FALSE
T7BD8 001:108.844 JLINK_HasError()
T7BD8 001:110.022 JLINK_IsHalted()
T7BD8 001:110.486 - 0.463ms returns FALSE
T7BD8 001:110.492 JLINK_HasError()
T7BD8 001:112.022 JLINK_IsHalted()
T7BD8 001:112.506 - 0.484ms returns FALSE
T7BD8 001:112.511 JLINK_HasError()
T7BD8 001:114.021 JLINK_IsHalted()
T7BD8 001:114.480 - 0.458ms returns FALSE
T7BD8 001:114.485 JLINK_HasError()
T7BD8 001:116.022 JLINK_IsHalted()
T7BD8 001:116.485 - 0.463ms returns FALSE
T7BD8 001:116.491 JLINK_HasError()
T7BD8 001:118.026 JLINK_IsHalted()
T7BD8 001:118.553 - 0.527ms returns FALSE
T7BD8 001:118.561 JLINK_HasError()
T7BD8 001:120.023 JLINK_IsHalted()
T7BD8 001:120.530 - 0.506ms returns FALSE
T7BD8 001:120.536 JLINK_HasError()
T7BD8 001:122.022 JLINK_IsHalted()
T7BD8 001:122.480 - 0.458ms returns FALSE
T7BD8 001:122.485 JLINK_HasError()
T7BD8 001:124.021 JLINK_IsHalted()
T7BD8 001:124.480 - 0.458ms returns FALSE
T7BD8 001:124.485 JLINK_HasError()
T7BD8 001:126.021 JLINK_IsHalted()
T7BD8 001:126.481 - 0.459ms returns FALSE
T7BD8 001:126.486 JLINK_HasError()
T7BD8 001:128.026 JLINK_IsHalted()
T7BD8 001:128.559 - 0.533ms returns FALSE
T7BD8 001:128.574 JLINK_HasError()
T7BD8 001:130.029 JLINK_IsHalted()
T7BD8 001:130.562 - 0.533ms returns FALSE
T7BD8 001:130.579 JLINK_HasError()
T7BD8 001:132.022 JLINK_IsHalted()
T7BD8 001:132.485 - 0.463ms returns FALSE
T7BD8 001:132.491 JLINK_HasError()
T7BD8 001:134.021 JLINK_IsHalted()
T7BD8 001:134.480 - 0.458ms returns FALSE
T7BD8 001:134.486 JLINK_HasError()
T7BD8 001:136.023 JLINK_IsHalted()
T7BD8 001:136.530 - 0.507ms returns FALSE
T7BD8 001:136.536 JLINK_HasError()
T7BD8 001:138.025 JLINK_IsHalted()
T7BD8 001:138.848 - 0.823ms returns FALSE
T7BD8 001:138.858 JLINK_HasError()
T7BD8 001:140.022 JLINK_IsHalted()
T7BD8 001:140.529 - 0.506ms returns FALSE
T7BD8 001:140.535 JLINK_HasError()
T7BD8 001:142.026 JLINK_IsHalted()
T7BD8 001:142.480 - 0.453ms returns FALSE
T7BD8 001:142.486 JLINK_HasError()
T7BD8 001:144.021 JLINK_IsHalted()
T7BD8 001:144.505 - 0.484ms returns FALSE
T7BD8 001:144.511 JLINK_HasError()
T7BD8 001:146.021 JLINK_IsHalted()
T7BD8 001:146.486 - 0.464ms returns FALSE
T7BD8 001:146.491 JLINK_HasError()
T7BD8 001:148.026 JLINK_IsHalted()
T7BD8 001:148.553 - 0.527ms returns FALSE
T7BD8 001:148.559 JLINK_HasError()
T7BD8 001:150.022 JLINK_IsHalted()
T7BD8 001:150.485 - 0.463ms returns FALSE
T7BD8 001:150.491 JLINK_HasError()
T7BD8 001:152.023 JLINK_IsHalted()
T7BD8 001:152.508 - 0.484ms returns FALSE
T7BD8 001:152.514 JLINK_HasError()
T7BD8 001:154.022 JLINK_IsHalted()
T7BD8 001:154.506 - 0.484ms returns FALSE
T7BD8 001:154.511 JLINK_HasError()
T7BD8 001:156.022 JLINK_IsHalted()
T7BD8 001:156.481 - 0.458ms returns FALSE
T7BD8 001:156.486 JLINK_HasError()
T7BD8 001:158.025 JLINK_IsHalted()
T7BD8 001:158.572 - 0.546ms returns FALSE
T7BD8 001:158.586 JLINK_HasError()
T7BD8 001:160.024 JLINK_IsHalted()
T7BD8 001:160.556 - 0.532ms returns FALSE
T7BD8 001:160.569 JLINK_HasError()
T7BD8 001:162.022 JLINK_IsHalted()
T7BD8 001:162.486 - 0.463ms returns FALSE
T7BD8 001:162.492 JLINK_HasError()
T7BD8 001:164.022 JLINK_IsHalted()
T7BD8 001:164.485 - 0.463ms returns FALSE
T7BD8 001:164.490 JLINK_HasError()
T7BD8 001:166.021 JLINK_IsHalted()
T7BD8 001:166.507 - 0.485ms returns FALSE
T7BD8 001:166.512 JLINK_HasError()
T7BD8 001:168.029 JLINK_IsHalted()
T7BD8 001:168.659 - 0.629ms returns FALSE
T7BD8 001:168.673 JLINK_HasError()
T7BD8 001:170.022 JLINK_IsHalted()
T7BD8 001:170.481 - 0.459ms returns FALSE
T7BD8 001:170.487 JLINK_HasError()
T7BD8 001:172.022 JLINK_IsHalted()
T7BD8 001:172.480 - 0.458ms returns FALSE
T7BD8 001:172.485 JLINK_HasError()
T7BD8 001:174.026 JLINK_IsHalted()
T7BD8 001:174.528 - 0.501ms returns FALSE
T7BD8 001:174.534 JLINK_HasError()
T7BD8 001:176.027 JLINK_IsHalted()
T7BD8 001:176.510 - 0.482ms returns FALSE
T7BD8 001:176.517 JLINK_HasError()
T7BD8 001:178.026 JLINK_IsHalted()
T7BD8 001:178.553 - 0.527ms returns FALSE
T7BD8 001:178.561 JLINK_HasError()
T7BD8 001:180.025 JLINK_IsHalted()
T7BD8 001:180.487 - 0.461ms returns FALSE
T7BD8 001:180.496 JLINK_HasError()
T7BD8 001:182.024 JLINK_IsHalted()
T7BD8 001:182.480 - 0.456ms returns FALSE
T7BD8 001:182.486 JLINK_HasError()
T7BD8 001:183.903 JLINK_IsHalted()
T7BD8 001:184.413 - 0.509ms returns FALSE
T7BD8 001:184.419 JLINK_HasError()
T7BD8 001:185.902 JLINK_IsHalted()
T7BD8 001:186.382 - 0.479ms returns FALSE
T7BD8 001:186.387 JLINK_HasError()
T7BD8 001:187.908 JLINK_IsHalted()
T7BD8 001:188.429 - 0.521ms returns FALSE
T7BD8 001:188.436 JLINK_HasError()
T7BD8 001:190.410 JLINK_IsHalted()
T7BD8 001:190.928 - 0.517ms returns FALSE
T7BD8 001:190.934 JLINK_HasError()
T7BD8 001:192.406 JLINK_IsHalted()
T7BD8 001:192.882 - 0.475ms returns FALSE
T7BD8 001:192.888 JLINK_HasError()
T7BD8 001:194.406 JLINK_IsHalted()
T7BD8 001:194.924 - 0.517ms returns FALSE
T7BD8 001:194.929 JLINK_HasError()
T7BD8 001:196.406 JLINK_IsHalted()
T7BD8 001:196.927 - 0.520ms returns FALSE
T7BD8 001:196.933 JLINK_HasError()
T7BD8 001:198.411 JLINK_IsHalted()
T7BD8 001:199.032 - 0.620ms returns FALSE
T7BD8 001:199.045 JLINK_HasError()
T7BD8 001:200.411 JLINK_IsHalted()
T7BD8 001:200.930 - 0.518ms returns FALSE
T7BD8 001:200.942 JLINK_HasError()
T7BD8 001:202.407 JLINK_IsHalted()
T7BD8 001:202.888 - 0.481ms returns FALSE
T7BD8 001:202.893 JLINK_HasError()
T7BD8 001:204.406 JLINK_IsHalted()
T7BD8 001:204.882 - 0.475ms returns FALSE
T7BD8 001:204.887 JLINK_HasError()
T7BD8 001:206.406 JLINK_IsHalted()
T7BD8 001:206.927 - 0.521ms returns FALSE
T7BD8 001:206.933 JLINK_HasError()
T7BD8 001:208.411 JLINK_IsHalted()
T7BD8 001:208.931 - 0.519ms returns FALSE
T7BD8 001:208.944 JLINK_HasError()
T7BD8 001:210.407 JLINK_IsHalted()
T7BD8 001:210.899 - 0.492ms returns FALSE
T7BD8 001:210.905 JLINK_HasError()
T7BD8 001:212.406 JLINK_IsHalted()
T7BD8 001:212.924 - 0.517ms returns FALSE
T7BD8 001:212.929 JLINK_HasError()
T7BD8 001:214.406 JLINK_IsHalted()
T7BD8 001:214.937 - 0.530ms returns FALSE
T7BD8 001:214.950 JLINK_HasError()
T7BD8 001:217.409 JLINK_IsHalted()
T7BD8 001:218.013 - 0.604ms returns FALSE
T7BD8 001:218.024 JLINK_HasError()
T7BD8 001:219.409 JLINK_IsHalted()
T7BD8 001:219.938 - 0.528ms returns FALSE
T7BD8 001:219.944 JLINK_HasError()
T7BD8 001:221.409 JLINK_IsHalted()
T7BD8 001:221.927 - 0.518ms returns FALSE
T7BD8 001:221.934 JLINK_HasError()
T7BD8 001:223.406 JLINK_IsHalted()
T7BD8 001:223.888 - 0.481ms returns FALSE
T7BD8 001:223.894 JLINK_HasError()
T7BD8 001:225.406 JLINK_IsHalted()
T7BD8 001:225.924 - 0.517ms returns FALSE
T7BD8 001:225.929 JLINK_HasError()
T7BD8 001:227.410 JLINK_IsHalted()
T7BD8 001:228.806 - 1.396ms returns FALSE
T7BD8 001:228.819 JLINK_HasError()
T7BD8 001:230.407 JLINK_IsHalted()
T7BD8 001:230.923 - 0.515ms returns FALSE
T7BD8 001:230.932 JLINK_HasError()
T7BD8 001:232.407 JLINK_IsHalted()
T7BD8 001:232.898 - 0.491ms returns FALSE
T7BD8 001:232.904 JLINK_HasError()
T7BD8 001:234.406 JLINK_IsHalted()
T7BD8 001:234.889 - 0.482ms returns FALSE
T7BD8 001:234.895 JLINK_HasError()
T7BD8 001:236.408 JLINK_IsHalted()
T7BD8 001:236.884 - 0.476ms returns FALSE
T7BD8 001:236.890 JLINK_HasError()
T7BD8 001:238.410 JLINK_IsHalted()
T7BD8 001:238.929 - 0.518ms returns FALSE
T7BD8 001:238.936 JLINK_HasError()
T7BD8 001:240.407 JLINK_IsHalted()
T7BD8 001:240.889 - 0.482ms returns FALSE
T7BD8 001:240.895 JLINK_HasError()
T7BD8 001:242.406 JLINK_IsHalted()
T7BD8 001:242.924 - 0.517ms returns FALSE
T7BD8 001:242.929 JLINK_HasError()
T7BD8 001:244.406 JLINK_IsHalted()
T7BD8 001:244.924 - 0.517ms returns FALSE
T7BD8 001:244.929 JLINK_HasError()
T7BD8 001:246.407 JLINK_IsHalted()
T7BD8 001:246.885 - 0.478ms returns FALSE
T7BD8 001:246.895 JLINK_HasError()
T7BD8 001:248.409 JLINK_IsHalted()
T7BD8 001:248.938 - 0.529ms returns FALSE
T7BD8 001:248.951 JLINK_HasError()
T7BD8 001:250.410 JLINK_IsHalted()
T7BD8 001:250.928 - 0.517ms returns FALSE
T7BD8 001:250.934 JLINK_HasError()
T7BD8 001:252.406 JLINK_IsHalted()
T7BD8 001:252.882 - 0.475ms returns FALSE
T7BD8 001:252.888 JLINK_HasError()
T7BD8 001:254.406 JLINK_IsHalted()
T7BD8 001:254.924 - 0.517ms returns FALSE
T7BD8 001:254.929 JLINK_HasError()
T7BD8 001:256.408 JLINK_IsHalted()
T7BD8 001:256.890 - 0.482ms returns FALSE
T7BD8 001:256.896 JLINK_HasError()
T7BD8 001:258.409 JLINK_IsHalted()
T7BD8 001:258.937 - 0.527ms returns FALSE
T7BD8 001:258.944 JLINK_HasError()
T7BD8 001:260.409 JLINK_IsHalted()
T7BD8 001:260.885 - 0.475ms returns FALSE
T7BD8 001:260.891 JLINK_HasError()
T7BD8 001:262.407 JLINK_IsHalted()
T7BD8 001:262.925 - 0.517ms returns FALSE
T7BD8 001:262.931 JLINK_HasError()
T7BD8 001:264.407 JLINK_IsHalted()
T7BD8 001:264.885 - 0.478ms returns FALSE
T7BD8 001:264.894 JLINK_HasError()
T7BD8 001:266.407 JLINK_IsHalted()
T7BD8 001:266.891 - 0.483ms returns FALSE
T7BD8 001:266.896 JLINK_HasError()
T7BD8 001:268.414 JLINK_IsHalted()
T7BD8 001:268.927 - 0.512ms returns FALSE
T7BD8 001:268.943 JLINK_HasError()
T7BD8 001:270.407 JLINK_IsHalted()
T7BD8 001:270.898 - 0.491ms returns FALSE
T7BD8 001:270.904 JLINK_HasError()
T7BD8 001:272.406 JLINK_IsHalted()
T7BD8 001:272.924 - 0.517ms returns FALSE
T7BD8 001:272.930 JLINK_HasError()
T7BD8 001:274.407 JLINK_IsHalted()
T7BD8 001:274.924 - 0.517ms returns FALSE
T7BD8 001:274.930 JLINK_HasError()
T7BD8 001:276.408 JLINK_IsHalted()
T7BD8 001:276.971 - 0.562ms returns FALSE
T7BD8 001:276.985 JLINK_HasError()
T7BD8 001:278.410 JLINK_IsHalted()
T7BD8 001:278.927 - 0.516ms returns FALSE
T7BD8 001:278.933 JLINK_HasError()
T7BD8 001:280.408 JLINK_IsHalted()
T7BD8 001:280.888 - 0.479ms returns FALSE
T7BD8 001:280.894 JLINK_HasError()
T7BD8 001:282.409 JLINK_IsHalted()
T7BD8 001:282.889 - 0.479ms returns FALSE
T7BD8 001:282.899 JLINK_HasError()
T7BD8 001:284.408 JLINK_IsHalted()
T7BD8 001:284.889 - 0.481ms returns FALSE
T7BD8 001:284.894 JLINK_HasError()
T7BD8 001:286.408 JLINK_IsHalted()
T7BD8 001:286.890 - 0.482ms returns FALSE
T7BD8 001:286.896 JLINK_HasError()
T7BD8 001:288.415 JLINK_IsHalted()
T7BD8 001:289.031 - 0.615ms returns FALSE
T7BD8 001:289.049 JLINK_HasError()
T7BD8 001:290.913 JLINK_IsHalted()
T7BD8 001:291.383 - 0.470ms returns FALSE
T7BD8 001:291.389 JLINK_HasError()
T7BD8 001:292.912 JLINK_IsHalted()
T7BD8 001:293.383 - 0.470ms returns FALSE
T7BD8 001:293.389 JLINK_HasError()
T7BD8 001:294.912 JLINK_IsHalted()
T7BD8 001:295.403 - 0.490ms returns FALSE
T7BD8 001:295.408 JLINK_HasError()
T7BD8 001:296.912 JLINK_IsHalted()
T7BD8 001:298.362 - 1.449ms returns FALSE
T7BD8 001:298.381 JLINK_HasError()
T7BD8 001:299.914 JLINK_IsHalted()
T7BD8 001:300.442 - 0.526ms returns FALSE
T7BD8 001:300.457 JLINK_HasError()
T7BD8 001:301.912 JLINK_IsHalted()
T7BD8 001:302.429 - 0.517ms returns FALSE
T7BD8 001:302.435 JLINK_HasError()
T7BD8 001:303.914 JLINK_IsHalted()
T7BD8 001:304.383 - 0.468ms returns FALSE
T7BD8 001:304.389 JLINK_HasError()
T7BD8 001:305.912 JLINK_IsHalted()
T7BD8 001:306.368 - 0.456ms returns FALSE
T7BD8 001:306.374 JLINK_HasError()
T7BD8 001:307.916 JLINK_IsHalted()
T7BD8 001:309.029 - 1.112ms returns FALSE
T7BD8 001:309.046 JLINK_HasError()
T7BD8 001:310.915 JLINK_IsHalted()
T7BD8 001:311.435 - 0.519ms returns FALSE
T7BD8 001:311.441 JLINK_HasError()
T7BD8 001:312.918 JLINK_IsHalted()
T7BD8 001:313.401 - 0.482ms returns FALSE
T7BD8 001:313.407 JLINK_HasError()
T7BD8 001:314.912 JLINK_IsHalted()
T7BD8 001:315.377 - 0.464ms returns FALSE
T7BD8 001:315.382 JLINK_HasError()
T7BD8 001:316.912 JLINK_IsHalted()
T7BD8 001:318.061 - 1.148ms returns FALSE
T7BD8 001:318.073 JLINK_HasError()
T7BD8 001:319.916 JLINK_IsHalted()
T7BD8 001:320.427 - 0.510ms returns FALSE
T7BD8 001:320.433 JLINK_HasError()
T7BD8 001:321.912 JLINK_IsHalted()
T7BD8 001:322.399 - 0.487ms returns FALSE
T7BD8 001:322.404 JLINK_HasError()
T7BD8 001:323.912 JLINK_IsHalted()
T7BD8 001:324.555 - 0.643ms returns FALSE
T7BD8 001:324.568 JLINK_HasError()
T7BD8 001:326.913 JLINK_IsHalted()
T7BD8 001:328.021 - 1.107ms returns FALSE
T7BD8 001:328.036 JLINK_HasError()
T7BD8 001:329.915 JLINK_IsHalted()
T7BD8 001:330.437 - 0.520ms returns FALSE
T7BD8 001:330.449 JLINK_HasError()
T7BD8 001:331.912 JLINK_IsHalted()
T7BD8 001:332.382 - 0.470ms returns FALSE
T7BD8 001:332.388 JLINK_HasError()
T7BD8 001:333.912 JLINK_IsHalted()
T7BD8 001:334.377 - 0.465ms returns FALSE
T7BD8 001:334.383 JLINK_HasError()
T7BD8 001:335.912 JLINK_IsHalted()
T7BD8 001:336.377 - 0.465ms returns FALSE
T7BD8 001:336.382 JLINK_HasError()
T7BD8 001:337.916 JLINK_IsHalted()
T7BD8 001:338.435 - 0.519ms returns FALSE
T7BD8 001:338.443 JLINK_HasError()
T7BD8 001:339.915 JLINK_IsHalted()
T7BD8 001:340.433 - 0.517ms returns FALSE
T7BD8 001:340.439 JLINK_HasError()
T7BD8 001:341.912 JLINK_IsHalted()
T7BD8 001:342.379 - 0.467ms returns FALSE
T7BD8 001:342.385 JLINK_HasError()
T7BD8 001:343.912 JLINK_IsHalted()
T7BD8 001:344.383 - 0.471ms returns FALSE
T7BD8 001:344.389 JLINK_HasError()
T7BD8 001:345.912 JLINK_IsHalted()
T7BD8 001:346.368 - 0.455ms returns FALSE
T7BD8 001:346.373 JLINK_HasError()
T7BD8 001:347.914 JLINK_IsHalted()
T7BD8 001:348.581 - 0.666ms returns FALSE
T7BD8 001:348.595 JLINK_HasError()
T7BD8 001:349.914 JLINK_IsHalted()
T7BD8 001:350.427 - 0.511ms returns FALSE
T7BD8 001:350.433 JLINK_HasError()
T7BD8 001:351.912 JLINK_IsHalted()
T7BD8 001:352.378 - 0.465ms returns FALSE
T7BD8 001:352.383 JLINK_HasError()
T7BD8 001:353.912 JLINK_IsHalted()
T7BD8 001:354.368 - 0.456ms returns FALSE
T7BD8 001:354.374 JLINK_HasError()
T7BD8 001:355.914 JLINK_IsHalted()
T7BD8 001:356.426 - 0.512ms returns FALSE
T7BD8 001:356.432 JLINK_HasError()
T7BD8 001:357.916 JLINK_IsHalted()
T7BD8 001:358.435 - 0.518ms returns FALSE
T7BD8 001:358.443 JLINK_HasError()
T7BD8 001:359.915 JLINK_IsHalted()
T7BD8 001:360.436 - 0.520ms returns FALSE
T7BD8 001:360.451 JLINK_HasError()
T7BD8 001:361.912 JLINK_IsHalted()
T7BD8 001:362.382 - 0.469ms returns FALSE
T7BD8 001:362.394 JLINK_HasError()
T7BD8 001:363.912 JLINK_IsHalted()
T7BD8 001:364.383 - 0.470ms returns FALSE
T7BD8 001:364.388 JLINK_HasError()
T7BD8 001:365.912 JLINK_IsHalted()
T7BD8 001:366.377 - 0.464ms returns FALSE
T7BD8 001:366.382 JLINK_HasError()
T7BD8 001:367.915 JLINK_IsHalted()
T7BD8 001:368.436 - 0.520ms returns FALSE
T7BD8 001:368.444 JLINK_HasError()
T7BD8 001:369.912 JLINK_IsHalted()
T7BD8 001:370.401 - 0.488ms returns FALSE
T7BD8 001:370.407 JLINK_HasError()
T7BD8 001:371.912 JLINK_IsHalted()
T7BD8 001:372.383 - 0.470ms returns FALSE
T7BD8 001:372.390 JLINK_HasError()
T7BD8 001:373.912 JLINK_IsHalted()
T7BD8 001:374.377 - 0.464ms returns FALSE
T7BD8 001:374.382 JLINK_HasError()
T7BD8 001:375.913 JLINK_IsHalted()
T7BD8 001:376.377 - 0.463ms returns FALSE
T7BD8 001:376.382 JLINK_HasError()
T7BD8 001:377.916 JLINK_IsHalted()
T7BD8 001:378.498 - 0.581ms returns FALSE
T7BD8 001:378.509 JLINK_HasError()
T7BD8 001:379.914 JLINK_IsHalted()
T7BD8 001:382.264   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 001:382.766 - 2.851ms returns TRUE
T7BD8 001:382.772 JLINK_ReadReg(R15 (PC))
T7BD8 001:382.778 - 0.006ms returns 0x20000000
T7BD8 001:382.783 JLINK_ClrBPEx(BPHandle = 0x00000005)
T7BD8 001:382.787 - 0.003ms returns 0x00
T7BD8 001:382.791 JLINK_ReadReg(R0)
T7BD8 001:382.795 - 0.003ms returns 0x00000000
T7BD8 001:383.156 JLINK_HasError()
T7BD8 001:383.165 JLINK_WriteReg(R0, 0x08008000)
T7BD8 001:383.169 - 0.004ms returns 0
T7BD8 001:383.174 JLINK_WriteReg(R1, 0x00004000)
T7BD8 001:383.177 - 0.003ms returns 0
T7BD8 001:383.181 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 001:383.185 - 0.003ms returns 0
T7BD8 001:383.189 JLINK_WriteReg(R3, 0x00000000)
T7BD8 001:383.192 - 0.003ms returns 0
T7BD8 001:383.196 JLINK_WriteReg(R4, 0x00000000)
T7BD8 001:383.199 - 0.003ms returns 0
T7BD8 001:383.203 JLINK_WriteReg(R5, 0x00000000)
T7BD8 001:383.207 - 0.003ms returns 0
T7BD8 001:383.211 JLINK_WriteReg(R6, 0x00000000)
T7BD8 001:383.215 - 0.004ms returns 0
T7BD8 001:383.219 JLINK_WriteReg(R7, 0x00000000)
T7BD8 001:383.222 - 0.003ms returns 0
T7BD8 001:383.226 JLINK_WriteReg(R8, 0x00000000)
T7BD8 001:383.230 - 0.003ms returns 0
T7BD8 001:383.234 JLINK_WriteReg(R9, 0x20000180)
T7BD8 001:383.237 - 0.003ms returns 0
T7BD8 001:383.242 JLINK_WriteReg(R10, 0x00000000)
T7BD8 001:383.245 - 0.003ms returns 0
T7BD8 001:383.249 JLINK_WriteReg(R11, 0x00000000)
T7BD8 001:383.253 - 0.003ms returns 0
T7BD8 001:383.257 JLINK_WriteReg(R12, 0x00000000)
T7BD8 001:383.260 - 0.003ms returns 0
T7BD8 001:383.264 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 001:383.268 - 0.003ms returns 0
T7BD8 001:383.272 JLINK_WriteReg(R14, 0x20000001)
T7BD8 001:383.275 - 0.003ms returns 0
T7BD8 001:383.279 JLINK_WriteReg(R15 (PC), 0x20000020)
T7BD8 001:383.283 - 0.003ms returns 0
T7BD8 001:383.287 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 001:383.290 - 0.003ms returns 0
T7BD8 001:383.294 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 001:383.298 - 0.003ms returns 0
T7BD8 001:383.302 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 001:383.305 - 0.003ms returns 0
T7BD8 001:383.310 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 001:383.313 - 0.003ms returns 0
T7BD8 001:383.317 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 001:383.322 - 0.004ms returns 0x00000006
T7BD8 001:383.326 JLINK_Go()
T7BD8 001:383.334   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 001:386.027 - 2.701ms 
T7BD8 001:386.037 JLINK_IsHalted()
T7BD8 001:388.332   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 001:388.832 - 2.794ms returns TRUE
T7BD8 001:388.843 JLINK_ReadReg(R15 (PC))
T7BD8 001:388.848 - 0.005ms returns 0x20000000
T7BD8 001:388.880 JLINK_ClrBPEx(BPHandle = 0x00000006)
T7BD8 001:388.885 - 0.005ms returns 0x00
T7BD8 001:388.890 JLINK_ReadReg(R0)
T7BD8 001:388.893 - 0.003ms returns 0x00000001
T7BD8 001:388.898 JLINK_HasError()
T7BD8 001:388.903 JLINK_WriteReg(R0, 0x08008000)
T7BD8 001:388.907 - 0.004ms returns 0
T7BD8 001:388.911 JLINK_WriteReg(R1, 0x00004000)
T7BD8 001:388.914 - 0.003ms returns 0
T7BD8 001:388.919 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 001:388.922 - 0.003ms returns 0
T7BD8 001:388.926 JLINK_WriteReg(R3, 0x00000000)
T7BD8 001:388.930 - 0.003ms returns 0
T7BD8 001:388.937 JLINK_WriteReg(R4, 0x00000000)
T7BD8 001:388.942 - 0.005ms returns 0
T7BD8 001:388.946 JLINK_WriteReg(R5, 0x00000000)
T7BD8 001:388.950 - 0.003ms returns 0
T7BD8 001:388.954 JLINK_WriteReg(R6, 0x00000000)
T7BD8 001:388.957 - 0.003ms returns 0
T7BD8 001:388.961 JLINK_WriteReg(R7, 0x00000000)
T7BD8 001:388.965 - 0.003ms returns 0
T7BD8 001:388.969 JLINK_WriteReg(R8, 0x00000000)
T7BD8 001:388.972 - 0.003ms returns 0
T7BD8 001:388.976 JLINK_WriteReg(R9, 0x20000180)
T7BD8 001:388.980 - 0.003ms returns 0
T7BD8 001:388.984 JLINK_WriteReg(R10, 0x00000000)
T7BD8 001:388.987 - 0.003ms returns 0
T7BD8 001:388.991 JLINK_WriteReg(R11, 0x00000000)
T7BD8 001:388.994 - 0.003ms returns 0
T7BD8 001:388.999 JLINK_WriteReg(R12, 0x00000000)
T7BD8 001:389.002 - 0.003ms returns 0
T7BD8 001:389.006 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 001:389.010 - 0.003ms returns 0
T7BD8 001:389.014 JLINK_WriteReg(R14, 0x20000001)
T7BD8 001:389.017 - 0.003ms returns 0
T7BD8 001:389.021 JLINK_WriteReg(R15 (PC), 0x200000C0)
T7BD8 001:389.025 - 0.003ms returns 0
T7BD8 001:389.029 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 001:389.032 - 0.003ms returns 0
T7BD8 001:389.037 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 001:389.040 - 0.003ms returns 0
T7BD8 001:389.044 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 001:389.047 - 0.003ms returns 0
T7BD8 001:389.051 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 001:389.055 - 0.003ms returns 0
T7BD8 001:389.059 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 001:389.063 - 0.004ms returns 0x00000007
T7BD8 001:389.067 JLINK_Go()
T7BD8 001:389.074   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 001:391.801 - 2.733ms 
T7BD8 001:391.810 JLINK_IsHalted()
T7BD8 001:392.273 - 0.462ms returns FALSE
T7BD8 001:392.279 JLINK_HasError()
T7BD8 001:393.417 JLINK_IsHalted()
T7BD8 001:393.882 - 0.465ms returns FALSE
T7BD8 001:393.888 JLINK_HasError()
T7BD8 001:395.417 JLINK_IsHalted()
T7BD8 001:395.882 - 0.464ms returns FALSE
T7BD8 001:395.887 JLINK_HasError()
T7BD8 001:397.421 JLINK_IsHalted()
T7BD8 001:398.248 - 0.826ms returns FALSE
T7BD8 001:398.259 JLINK_HasError()
T7BD8 001:399.418 JLINK_IsHalted()
T7BD8 001:399.933 - 0.514ms returns FALSE
T7BD8 001:399.939 JLINK_HasError()
T7BD8 001:401.417 JLINK_IsHalted()
T7BD8 001:401.882 - 0.464ms returns FALSE
T7BD8 001:401.887 JLINK_HasError()
T7BD8 001:403.418 JLINK_IsHalted()
T7BD8 001:403.882 - 0.464ms returns FALSE
T7BD8 001:403.888 JLINK_HasError()
T7BD8 001:405.417 JLINK_IsHalted()
T7BD8 001:405.925 - 0.507ms returns FALSE
T7BD8 001:405.930 JLINK_HasError()
T7BD8 001:407.420 JLINK_IsHalted()
T7BD8 001:407.949 - 0.528ms returns FALSE
T7BD8 001:407.959 JLINK_HasError()
T7BD8 001:409.418 JLINK_IsHalted()
T7BD8 001:409.927 - 0.508ms returns FALSE
T7BD8 001:409.933 JLINK_HasError()
T7BD8 001:411.417 JLINK_IsHalted()
T7BD8 001:411.882 - 0.464ms returns FALSE
T7BD8 001:411.887 JLINK_HasError()
T7BD8 001:413.421 JLINK_IsHalted()
T7BD8 001:413.900 - 0.478ms returns FALSE
T7BD8 001:413.910 JLINK_HasError()
T7BD8 001:415.420 JLINK_IsHalted()
T7BD8 001:415.882 - 0.461ms returns FALSE
T7BD8 001:415.887 JLINK_HasError()
T7BD8 001:417.419 JLINK_IsHalted()
T7BD8 001:418.116 - 0.696ms returns FALSE
T7BD8 001:418.130 JLINK_HasError()
T7BD8 001:419.422 JLINK_IsHalted()
T7BD8 001:419.933 - 0.510ms returns FALSE
T7BD8 001:419.947 JLINK_HasError()
T7BD8 001:421.421 JLINK_IsHalted()
T7BD8 001:421.929 - 0.507ms returns FALSE
T7BD8 001:421.934 JLINK_HasError()
T7BD8 001:423.418 JLINK_IsHalted()
T7BD8 001:423.886 - 0.468ms returns FALSE
T7BD8 001:423.892 JLINK_HasError()
T7BD8 001:425.421 JLINK_IsHalted()
T7BD8 001:425.933 - 0.511ms returns FALSE
T7BD8 001:425.939 JLINK_HasError()
T7BD8 001:427.420 JLINK_IsHalted()
T7BD8 001:427.947 - 0.527ms returns FALSE
T7BD8 001:427.960 JLINK_HasError()
T7BD8 001:429.418 JLINK_IsHalted()
T7BD8 001:429.935 - 0.516ms returns FALSE
T7BD8 001:429.948 JLINK_HasError()
T7BD8 001:431.418 JLINK_IsHalted()
T7BD8 001:431.883 - 0.464ms returns FALSE
T7BD8 001:431.889 JLINK_HasError()
T7BD8 001:433.423 JLINK_IsHalted()
T7BD8 001:433.913 - 0.489ms returns FALSE
T7BD8 001:433.919 JLINK_HasError()
T7BD8 001:436.418 JLINK_IsHalted()
T7BD8 001:436.896 - 0.477ms returns FALSE
T7BD8 001:436.912 JLINK_HasError()
T7BD8 001:438.422 JLINK_IsHalted()
T7BD8 001:438.931 - 0.508ms returns FALSE
T7BD8 001:438.952 JLINK_HasError()
T7BD8 001:440.418 JLINK_IsHalted()
T7BD8 001:440.883 - 0.465ms returns FALSE
T7BD8 001:440.889 JLINK_HasError()
T7BD8 001:442.417 JLINK_IsHalted()
T7BD8 001:442.882 - 0.464ms returns FALSE
T7BD8 001:442.888 JLINK_HasError()
T7BD8 001:444.421 JLINK_IsHalted()
T7BD8 001:444.970 - 0.548ms returns FALSE
T7BD8 001:444.975 JLINK_HasError()
T7BD8 001:446.417 JLINK_IsHalted()
T7BD8 001:446.889 - 0.471ms returns FALSE
T7BD8 001:446.897 JLINK_HasError()
T7BD8 001:448.429 JLINK_IsHalted()
T7BD8 001:448.938 - 0.509ms returns FALSE
T7BD8 001:448.951 JLINK_HasError()
T7BD8 001:450.417 JLINK_IsHalted()
T7BD8 001:450.883 - 0.465ms returns FALSE
T7BD8 001:450.888 JLINK_HasError()
T7BD8 001:452.417 JLINK_IsHalted()
T7BD8 001:452.882 - 0.464ms returns FALSE
T7BD8 001:452.887 JLINK_HasError()
T7BD8 001:454.417 JLINK_IsHalted()
T7BD8 001:454.882 - 0.464ms returns FALSE
T7BD8 001:454.888 JLINK_HasError()
T7BD8 001:456.417 JLINK_IsHalted()
T7BD8 001:456.890 - 0.472ms returns FALSE
T7BD8 001:456.903 JLINK_HasError()
T7BD8 001:458.420 JLINK_IsHalted()
T7BD8 001:458.934 - 0.514ms returns FALSE
T7BD8 001:458.940 JLINK_HasError()
T7BD8 001:460.417 JLINK_IsHalted()
T7BD8 001:460.883 - 0.465ms returns FALSE
T7BD8 001:460.888 JLINK_HasError()
T7BD8 001:462.417 JLINK_IsHalted()
T7BD8 001:462.882 - 0.464ms returns FALSE
T7BD8 001:462.887 JLINK_HasError()
T7BD8 001:464.421 JLINK_IsHalted()
T7BD8 001:464.888 - 0.467ms returns FALSE
T7BD8 001:464.898 JLINK_HasError()
T7BD8 001:466.418 JLINK_IsHalted()
T7BD8 001:466.890 - 0.472ms returns FALSE
T7BD8 001:466.902 JLINK_HasError()
T7BD8 001:468.436 JLINK_IsHalted()
T7BD8 001:468.941 - 0.505ms returns FALSE
T7BD8 001:468.952 JLINK_HasError()
T7BD8 001:470.421 JLINK_IsHalted()
T7BD8 001:470.935 - 0.513ms returns FALSE
T7BD8 001:470.941 JLINK_HasError()
T7BD8 001:472.417 JLINK_IsHalted()
T7BD8 001:472.882 - 0.464ms returns FALSE
T7BD8 001:472.888 JLINK_HasError()
T7BD8 001:474.418 JLINK_IsHalted()
T7BD8 001:474.882 - 0.464ms returns FALSE
T7BD8 001:474.888 JLINK_HasError()
T7BD8 001:476.419 JLINK_IsHalted()
T7BD8 001:476.883 - 0.463ms returns FALSE
T7BD8 001:476.889 JLINK_HasError()
T7BD8 001:478.422 JLINK_IsHalted()
T7BD8 001:478.928 - 0.505ms returns FALSE
T7BD8 001:478.934 JLINK_HasError()
T7BD8 001:480.421 JLINK_IsHalted()
T7BD8 001:480.900 - 0.478ms returns FALSE
T7BD8 001:480.906 JLINK_HasError()
T7BD8 001:482.419 JLINK_IsHalted()
T7BD8 001:482.882 - 0.463ms returns FALSE
T7BD8 001:482.888 JLINK_HasError()
T7BD8 001:484.419 JLINK_IsHalted()
T7BD8 001:484.882 - 0.463ms returns FALSE
T7BD8 001:484.887 JLINK_HasError()
T7BD8 001:486.422 JLINK_IsHalted()
T7BD8 001:486.883 - 0.460ms returns FALSE
T7BD8 001:486.889 JLINK_HasError()
T7BD8 001:488.422 JLINK_IsHalted()
T7BD8 001:488.932 - 0.508ms returns FALSE
T7BD8 001:488.945 JLINK_HasError()
T7BD8 001:490.924 JLINK_IsHalted()
T7BD8 001:491.390 - 0.465ms returns FALSE
T7BD8 001:491.396 JLINK_HasError()
T7BD8 001:492.923 JLINK_IsHalted()
T7BD8 001:493.382 - 0.458ms returns FALSE
T7BD8 001:493.388 JLINK_HasError()
T7BD8 001:494.923 JLINK_IsHalted()
T7BD8 001:495.494 - 0.570ms returns FALSE
T7BD8 001:495.508 JLINK_HasError()
T7BD8 001:496.930 JLINK_IsHalted()
T7BD8 001:497.757 - 0.827ms returns FALSE
T7BD8 001:497.770 JLINK_HasError()
T7BD8 001:498.924 JLINK_IsHalted()
T7BD8 001:499.442 - 0.517ms returns FALSE
T7BD8 001:499.449 JLINK_HasError()
T7BD8 001:500.924 JLINK_IsHalted()
T7BD8 001:501.383 - 0.458ms returns FALSE
T7BD8 001:501.388 JLINK_HasError()
T7BD8 001:502.923 JLINK_IsHalted()
T7BD8 001:503.405 - 0.481ms returns FALSE
T7BD8 001:503.410 JLINK_HasError()
T7BD8 001:504.923 JLINK_IsHalted()
T7BD8 001:505.403 - 0.479ms returns FALSE
T7BD8 001:505.412 JLINK_HasError()
T7BD8 001:506.925 JLINK_IsHalted()
T7BD8 001:507.930 - 1.004ms returns FALSE
T7BD8 001:507.945 JLINK_HasError()
T7BD8 001:509.927 JLINK_IsHalted()
T7BD8 001:510.411 - 0.483ms returns FALSE
T7BD8 001:510.417 JLINK_HasError()
T7BD8 001:511.928 JLINK_IsHalted()
T7BD8 001:512.449 - 0.520ms returns FALSE
T7BD8 001:512.455 JLINK_HasError()
T7BD8 001:513.923 JLINK_IsHalted()
T7BD8 001:514.382 - 0.458ms returns FALSE
T7BD8 001:514.388 JLINK_HasError()
T7BD8 001:515.923 JLINK_IsHalted()
T7BD8 001:516.382 - 0.458ms returns FALSE
T7BD8 001:516.388 JLINK_HasError()
T7BD8 001:517.928 JLINK_IsHalted()
T7BD8 001:518.451 - 0.523ms returns FALSE
T7BD8 001:518.459 JLINK_HasError()
T7BD8 001:519.924 JLINK_IsHalted()
T7BD8 001:520.421 - 0.496ms returns FALSE
T7BD8 001:520.426 JLINK_HasError()
T7BD8 001:521.923 JLINK_IsHalted()
T7BD8 001:522.383 - 0.459ms returns FALSE
T7BD8 001:522.388 JLINK_HasError()
T7BD8 001:523.923 JLINK_IsHalted()
T7BD8 001:524.382 - 0.458ms returns FALSE
T7BD8 001:524.387 JLINK_HasError()
T7BD8 001:525.923 JLINK_IsHalted()
T7BD8 001:526.382 - 0.458ms returns FALSE
T7BD8 001:526.387 JLINK_HasError()
T7BD8 001:527.927 JLINK_IsHalted()
T7BD8 001:528.436 - 0.508ms returns FALSE
T7BD8 001:528.443 JLINK_HasError()
T7BD8 001:529.926 JLINK_IsHalted()
T7BD8 001:530.417 - 0.491ms returns FALSE
T7BD8 001:530.431 JLINK_HasError()
T7BD8 001:531.924 JLINK_IsHalted()
T7BD8 001:532.406 - 0.482ms returns FALSE
T7BD8 001:532.412 JLINK_HasError()
T7BD8 001:533.923 JLINK_IsHalted()
T7BD8 001:534.382 - 0.458ms returns FALSE
T7BD8 001:534.387 JLINK_HasError()
T7BD8 001:535.923 JLINK_IsHalted()
T7BD8 001:536.382 - 0.458ms returns FALSE
T7BD8 001:536.387 JLINK_HasError()
T7BD8 001:537.927 JLINK_IsHalted()
T7BD8 001:538.444 - 0.517ms returns FALSE
T7BD8 001:538.452 JLINK_HasError()
T7BD8 001:539.927 JLINK_IsHalted()
T7BD8 001:540.434 - 0.507ms returns FALSE
T7BD8 001:540.444 JLINK_HasError()
T7BD8 001:541.923 JLINK_IsHalted()
T7BD8 001:542.383 - 0.459ms returns FALSE
T7BD8 001:542.388 JLINK_HasError()
T7BD8 001:543.924 JLINK_IsHalted()
T7BD8 001:544.399 - 0.474ms returns FALSE
T7BD8 001:544.405 JLINK_HasError()
T7BD8 001:546.925 JLINK_IsHalted()
T7BD8 001:548.407 - 1.481ms returns FALSE
T7BD8 001:548.423 JLINK_HasError()
T7BD8 001:549.927 JLINK_IsHalted()
T7BD8 001:550.471 - 0.544ms returns FALSE
T7BD8 001:550.478 JLINK_HasError()
T7BD8 001:551.923 JLINK_IsHalted()
T7BD8 001:552.382 - 0.458ms returns FALSE
T7BD8 001:552.388 JLINK_HasError()
T7BD8 001:553.923 JLINK_IsHalted()
T7BD8 001:554.383 - 0.459ms returns FALSE
T7BD8 001:554.388 JLINK_HasError()
T7BD8 001:555.925 JLINK_IsHalted()
T7BD8 001:556.403 - 0.477ms returns FALSE
T7BD8 001:556.408 JLINK_HasError()
T7BD8 001:557.927 JLINK_IsHalted()
T7BD8 001:558.538 - 0.610ms returns FALSE
T7BD8 001:558.551 JLINK_HasError()
T7BD8 001:559.930 JLINK_IsHalted()
T7BD8 001:560.437 - 0.506ms returns FALSE
T7BD8 001:560.450 JLINK_HasError()
T7BD8 001:561.926 JLINK_IsHalted()
T7BD8 001:562.400 - 0.474ms returns FALSE
T7BD8 001:562.407 JLINK_HasError()
T7BD8 001:563.924 JLINK_IsHalted()
T7BD8 001:564.383 - 0.458ms returns FALSE
T7BD8 001:564.388 JLINK_HasError()
T7BD8 001:565.924 JLINK_IsHalted()
T7BD8 001:566.382 - 0.457ms returns FALSE
T7BD8 001:566.391 JLINK_HasError()
T7BD8 001:567.927 JLINK_IsHalted()
T7BD8 001:568.419 - 0.491ms returns FALSE
T7BD8 001:568.432 JLINK_HasError()
T7BD8 001:569.925 JLINK_IsHalted()
T7BD8 001:570.399 - 0.474ms returns FALSE
T7BD8 001:570.405 JLINK_HasError()
T7BD8 001:571.923 JLINK_IsHalted()
T7BD8 001:572.382 - 0.458ms returns FALSE
T7BD8 001:572.387 JLINK_HasError()
T7BD8 001:573.925 JLINK_IsHalted()
T7BD8 001:574.421 - 0.495ms returns FALSE
T7BD8 001:574.427 JLINK_HasError()
T7BD8 001:575.925 JLINK_IsHalted()
T7BD8 001:576.403 - 0.477ms returns FALSE
T7BD8 001:576.408 JLINK_HasError()
T7BD8 001:577.929 JLINK_IsHalted()
T7BD8 001:578.665 - 0.736ms returns FALSE
T7BD8 001:578.676 JLINK_HasError()
T7BD8 001:579.926 JLINK_IsHalted()
T7BD8 001:580.442 - 0.515ms returns FALSE
T7BD8 001:580.449 JLINK_HasError()
T7BD8 001:581.926 JLINK_IsHalted()
T7BD8 001:582.382 - 0.456ms returns FALSE
T7BD8 001:582.388 JLINK_HasError()
T7BD8 001:583.925 JLINK_IsHalted()
T7BD8 001:584.383 - 0.458ms returns FALSE
T7BD8 001:584.388 JLINK_HasError()
T7BD8 001:585.925 JLINK_IsHalted()
T7BD8 001:586.405 - 0.479ms returns FALSE
T7BD8 001:586.411 JLINK_HasError()
T7BD8 001:587.930 JLINK_IsHalted()
T7BD8 001:588.480 - 0.549ms returns FALSE
T7BD8 001:588.493 JLINK_HasError()
T7BD8 001:590.431 JLINK_IsHalted()
T7BD8 001:590.926 - 0.495ms returns FALSE
T7BD8 001:590.933 JLINK_HasError()
T7BD8 001:592.429 JLINK_IsHalted()
T7BD8 001:592.924 - 0.494ms returns FALSE
T7BD8 001:592.930 JLINK_HasError()
T7BD8 001:594.429 JLINK_IsHalted()
T7BD8 001:594.899 - 0.469ms returns FALSE
T7BD8 001:594.904 JLINK_HasError()
T7BD8 001:596.429 JLINK_IsHalted()
T7BD8 001:596.889 - 0.460ms returns FALSE
T7BD8 001:596.895 JLINK_HasError()
T7BD8 001:598.432 JLINK_IsHalted()
T7BD8 001:598.935 - 0.502ms returns FALSE
T7BD8 001:598.942 JLINK_HasError()
T7BD8 001:600.430 JLINK_IsHalted()
T7BD8 001:600.899 - 0.468ms returns FALSE
T7BD8 001:600.904 JLINK_HasError()
T7BD8 001:602.429 JLINK_IsHalted()
T7BD8 001:602.898 - 0.468ms returns FALSE
T7BD8 001:602.903 JLINK_HasError()
T7BD8 001:604.429 JLINK_IsHalted()
T7BD8 001:604.883 - 0.453ms returns FALSE
T7BD8 001:604.888 JLINK_HasError()
T7BD8 001:606.431 JLINK_IsHalted()
T7BD8 001:606.927 - 0.495ms returns FALSE
T7BD8 001:606.941 JLINK_HasError()
T7BD8 001:609.433 JLINK_IsHalted()
T7BD8 001:609.939 - 0.505ms returns FALSE
T7BD8 001:609.950 JLINK_HasError()
T7BD8 001:611.430 JLINK_IsHalted()
T7BD8 001:611.899 - 0.468ms returns FALSE
T7BD8 001:611.905 JLINK_HasError()
T7BD8 001:613.429 JLINK_IsHalted()
T7BD8 001:613.898 - 0.468ms returns FALSE
T7BD8 001:613.904 JLINK_HasError()
T7BD8 001:615.429 JLINK_IsHalted()
T7BD8 001:615.883 - 0.454ms returns FALSE
T7BD8 001:615.888 JLINK_HasError()
T7BD8 001:617.431 JLINK_IsHalted()
T7BD8 001:618.055 - 0.623ms returns FALSE
T7BD8 001:618.068 JLINK_HasError()
T7BD8 001:619.430 JLINK_IsHalted()
T7BD8 001:619.926 - 0.495ms returns FALSE
T7BD8 001:619.932 JLINK_HasError()
T7BD8 001:621.429 JLINK_IsHalted()
T7BD8 001:621.926 - 0.496ms returns FALSE
T7BD8 001:621.934 JLINK_HasError()
T7BD8 001:623.429 JLINK_IsHalted()
T7BD8 001:623.898 - 0.468ms returns FALSE
T7BD8 001:623.903 JLINK_HasError()
T7BD8 001:625.429 JLINK_IsHalted()
T7BD8 001:625.925 - 0.496ms returns FALSE
T7BD8 001:625.931 JLINK_HasError()
T7BD8 001:627.432 JLINK_IsHalted()
T7BD8 001:628.238 - 0.805ms returns FALSE
T7BD8 001:628.249 JLINK_HasError()
T7BD8 001:629.430 JLINK_IsHalted()
T7BD8 001:629.932 - 0.501ms returns FALSE
T7BD8 001:629.946 JLINK_HasError()
T7BD8 001:631.430 JLINK_IsHalted()
T7BD8 001:631.899 - 0.468ms returns FALSE
T7BD8 001:631.905 JLINK_HasError()
T7BD8 001:633.429 JLINK_IsHalted()
T7BD8 001:633.924 - 0.494ms returns FALSE
T7BD8 001:633.929 JLINK_HasError()
T7BD8 001:635.429 JLINK_IsHalted()
T7BD8 001:635.924 - 0.495ms returns FALSE
T7BD8 001:635.930 JLINK_HasError()
T7BD8 001:637.434 JLINK_IsHalted()
T7BD8 001:638.559 - 1.125ms returns FALSE
T7BD8 001:638.574 JLINK_HasError()
T7BD8 001:640.434 JLINK_IsHalted()
T7BD8 001:640.939 - 0.503ms returns FALSE
T7BD8 001:640.956 JLINK_HasError()
T7BD8 001:642.433 JLINK_IsHalted()
T7BD8 001:642.925 - 0.491ms returns FALSE
T7BD8 001:642.939 JLINK_HasError()
T7BD8 001:644.429 JLINK_IsHalted()
T7BD8 001:644.935 - 0.505ms returns FALSE
T7BD8 001:644.940 JLINK_HasError()
T7BD8 001:646.429 JLINK_IsHalted()
T7BD8 001:646.934 - 0.504ms returns FALSE
T7BD8 001:646.947 JLINK_HasError()
T7BD8 001:648.435 JLINK_IsHalted()
T7BD8 001:648.947 - 0.511ms returns FALSE
T7BD8 001:648.959 JLINK_HasError()
T7BD8 001:650.432 JLINK_IsHalted()
T7BD8 001:650.921 - 0.489ms returns FALSE
T7BD8 001:650.928 JLINK_HasError()
T7BD8 001:652.433 JLINK_IsHalted()
T7BD8 001:652.925 - 0.492ms returns FALSE
T7BD8 001:652.931 JLINK_HasError()
T7BD8 001:654.432 JLINK_IsHalted()
T7BD8 001:654.943 - 0.510ms returns FALSE
T7BD8 001:654.949 JLINK_HasError()
T7BD8 001:657.432 JLINK_IsHalted()
T7BD8 001:657.941 - 0.509ms returns FALSE
T7BD8 001:657.954 JLINK_HasError()
T7BD8 001:659.431 JLINK_IsHalted()
T7BD8 001:659.934 - 0.503ms returns FALSE
T7BD8 001:659.948 JLINK_HasError()
T7BD8 001:661.431 JLINK_IsHalted()
T7BD8 001:661.932 - 0.500ms returns FALSE
T7BD8 001:661.937 JLINK_HasError()
T7BD8 001:663.934 JLINK_IsHalted()
T7BD8 001:664.398 - 0.464ms returns FALSE
T7BD8 001:664.404 JLINK_HasError()
T7BD8 001:665.933 JLINK_IsHalted()
T7BD8 001:666.420 - 0.486ms returns FALSE
T7BD8 001:666.425 JLINK_HasError()
T7BD8 001:667.939 JLINK_IsHalted()
T7BD8 001:668.468 - 0.529ms returns FALSE
T7BD8 001:668.487 JLINK_HasError()
T7BD8 001:669.940 JLINK_IsHalted()
T7BD8 001:670.471 - 0.530ms returns FALSE
T7BD8 001:670.478 JLINK_HasError()
T7BD8 001:671.936 JLINK_IsHalted()
T7BD8 001:672.435 - 0.499ms returns FALSE
T7BD8 001:672.442 JLINK_HasError()
T7BD8 001:673.934 JLINK_IsHalted()
T7BD8 001:674.420 - 0.485ms returns FALSE
T7BD8 001:674.425 JLINK_HasError()
T7BD8 001:675.935 JLINK_IsHalted()
T7BD8 001:676.398 - 0.463ms returns FALSE
T7BD8 001:676.403 JLINK_HasError()
T7BD8 001:677.938 JLINK_IsHalted()
T7BD8 001:678.436 - 0.497ms returns FALSE
T7BD8 001:678.443 JLINK_HasError()
T7BD8 001:679.936 JLINK_IsHalted()
T7BD8 001:680.435 - 0.499ms returns FALSE
T7BD8 001:680.441 JLINK_HasError()
T7BD8 001:681.935 JLINK_IsHalted()
T7BD8 001:682.398 - 0.463ms returns FALSE
T7BD8 001:682.404 JLINK_HasError()
T7BD8 001:683.935 JLINK_IsHalted()
T7BD8 001:684.398 - 0.463ms returns FALSE
T7BD8 001:684.403 JLINK_HasError()
T7BD8 001:685.940 JLINK_IsHalted()
T7BD8 001:686.423 - 0.483ms returns FALSE
T7BD8 001:686.432 JLINK_HasError()
T7BD8 001:687.939 JLINK_IsHalted()
T7BD8 001:688.434 - 0.493ms returns FALSE
T7BD8 001:688.447 JLINK_HasError()
T7BD8 001:690.439 JLINK_IsHalted()
T7BD8 001:690.922 - 0.482ms returns FALSE
T7BD8 001:690.928 JLINK_HasError()
T7BD8 001:692.438 JLINK_IsHalted()
T7BD8 001:692.921 - 0.482ms returns FALSE
T7BD8 001:692.926 JLINK_HasError()
T7BD8 001:694.442 JLINK_IsHalted()
T7BD8 001:694.920 - 0.477ms returns FALSE
T7BD8 001:694.925 JLINK_HasError()
T7BD8 001:696.439 JLINK_IsHalted()
T7BD8 001:696.933 - 0.493ms returns FALSE
T7BD8 001:696.949 JLINK_HasError()
T7BD8 001:698.441 JLINK_IsHalted()
T7BD8 001:698.934 - 0.493ms returns FALSE
T7BD8 001:698.941 JLINK_HasError()
T7BD8 001:700.440 JLINK_IsHalted()
T7BD8 001:701.061 - 0.620ms returns FALSE
T7BD8 001:701.071 JLINK_HasError()
T7BD8 001:702.439 JLINK_IsHalted()
T7BD8 001:702.925 - 0.486ms returns FALSE
T7BD8 001:702.931 JLINK_HasError()
T7BD8 001:704.439 JLINK_IsHalted()
T7BD8 001:704.920 - 0.481ms returns FALSE
T7BD8 001:704.925 JLINK_HasError()
T7BD8 001:706.438 JLINK_IsHalted()
T7BD8 001:706.925 - 0.486ms returns FALSE
T7BD8 001:706.934 JLINK_HasError()
T7BD8 001:708.441 JLINK_IsHalted()
T7BD8 001:708.972 - 0.529ms returns FALSE
T7BD8 001:708.985 JLINK_HasError()
T7BD8 001:710.442 JLINK_IsHalted()
T7BD8 001:710.933 - 0.491ms returns FALSE
T7BD8 001:710.939 JLINK_HasError()
T7BD8 001:712.439 JLINK_IsHalted()
T7BD8 001:712.922 - 0.482ms returns FALSE
T7BD8 001:712.927 JLINK_HasError()
T7BD8 001:714.441 JLINK_IsHalted()
T7BD8 001:714.925 - 0.484ms returns FALSE
T7BD8 001:714.930 JLINK_HasError()
T7BD8 001:716.443 JLINK_IsHalted()
T7BD8 001:716.945 - 0.501ms returns FALSE
T7BD8 001:716.958 JLINK_HasError()
T7BD8 001:718.469 JLINK_IsHalted()
T7BD8 001:718.968 - 0.498ms returns FALSE
T7BD8 001:718.981 JLINK_HasError()
T7BD8 001:720.439 JLINK_IsHalted()
T7BD8 001:720.927 - 0.487ms returns FALSE
T7BD8 001:720.932 JLINK_HasError()
T7BD8 001:722.438 JLINK_IsHalted()
T7BD8 001:722.920 - 0.481ms returns FALSE
T7BD8 001:722.926 JLINK_HasError()
T7BD8 001:724.438 JLINK_IsHalted()
T7BD8 001:724.941 - 0.502ms returns FALSE
T7BD8 001:724.946 JLINK_HasError()
T7BD8 001:726.442 JLINK_IsHalted()
T7BD8 001:726.949 - 0.506ms returns FALSE
T7BD8 001:726.967 JLINK_HasError()
T7BD8 001:728.443 JLINK_IsHalted()
T7BD8 001:728.969 - 0.526ms returns FALSE
T7BD8 001:728.976 JLINK_HasError()
T7BD8 001:730.442 JLINK_IsHalted()
T7BD8 001:731.026 - 0.584ms returns FALSE
T7BD8 001:731.035 JLINK_HasError()
T7BD8 001:732.439 JLINK_IsHalted()
T7BD8 001:732.925 - 0.486ms returns FALSE
T7BD8 001:732.931 JLINK_HasError()
T7BD8 001:734.439 JLINK_IsHalted()
T7BD8 001:734.920 - 0.481ms returns FALSE
T7BD8 001:734.926 JLINK_HasError()
T7BD8 001:736.439 JLINK_IsHalted()
T7BD8 001:736.923 - 0.483ms returns FALSE
T7BD8 001:736.930 JLINK_HasError()
T7BD8 001:738.444 JLINK_IsHalted()
T7BD8 001:738.990 - 0.545ms returns FALSE
T7BD8 001:739.003 JLINK_HasError()
T7BD8 001:740.442 JLINK_IsHalted()
T7BD8 001:740.934 - 0.492ms returns FALSE
T7BD8 001:740.940 JLINK_HasError()
T7BD8 001:742.439 JLINK_IsHalted()
T7BD8 001:742.922 - 0.483ms returns FALSE
T7BD8 001:742.928 JLINK_HasError()
T7BD8 001:744.439 JLINK_IsHalted()
T7BD8 001:744.925 - 0.486ms returns FALSE
T7BD8 001:744.931 JLINK_HasError()
T7BD8 001:746.438 JLINK_IsHalted()
T7BD8 001:746.955 - 0.516ms returns FALSE
T7BD8 001:746.966 JLINK_HasError()
T7BD8 001:748.447 JLINK_IsHalted()
T7BD8 001:749.074 - 0.627ms returns FALSE
T7BD8 001:749.089 JLINK_HasError()
T7BD8 001:750.441 JLINK_IsHalted()
T7BD8 001:750.938 - 0.496ms returns FALSE
T7BD8 001:750.951 JLINK_HasError()
T7BD8 001:752.439 JLINK_IsHalted()
T7BD8 001:752.926 - 0.487ms returns FALSE
T7BD8 001:752.932 JLINK_HasError()
T7BD8 001:754.438 JLINK_IsHalted()
T7BD8 001:754.925 - 0.486ms returns FALSE
T7BD8 001:754.931 JLINK_HasError()
T7BD8 001:756.438 JLINK_IsHalted()
T7BD8 001:756.928 - 0.489ms returns FALSE
T7BD8 001:756.936 JLINK_HasError()
T7BD8 001:758.466 JLINK_IsHalted()
T7BD8 001:758.986 - 0.520ms returns FALSE
T7BD8 001:758.999 JLINK_HasError()
T7BD8 001:760.439 JLINK_IsHalted()
T7BD8 001:760.926 - 0.486ms returns FALSE
T7BD8 001:760.932 JLINK_HasError()
T7BD8 001:762.441 JLINK_IsHalted()
T7BD8 001:762.937 - 0.495ms returns FALSE
T7BD8 001:762.944 JLINK_HasError()
T7BD8 001:765.443 JLINK_IsHalted()
T7BD8 001:765.938 - 0.495ms returns FALSE
T7BD8 001:765.944 JLINK_HasError()
T7BD8 001:767.442 JLINK_IsHalted()
T7BD8 001:767.936 - 0.494ms returns FALSE
T7BD8 001:767.943 JLINK_HasError()
T7BD8 001:769.439 JLINK_IsHalted()
T7BD8 001:769.940 - 0.499ms returns FALSE
T7BD8 001:769.953 JLINK_HasError()
T7BD8 001:771.441 JLINK_IsHalted()
T7BD8 001:771.934 - 0.493ms returns FALSE
T7BD8 001:771.940 JLINK_HasError()
T7BD8 001:773.438 JLINK_IsHalted()
T7BD8 001:773.921 - 0.482ms returns FALSE
T7BD8 001:773.926 JLINK_HasError()
T7BD8 001:775.440 JLINK_IsHalted()
T7BD8 001:775.920 - 0.479ms returns FALSE
T7BD8 001:775.925 JLINK_HasError()
T7BD8 001:777.086 JLINK_IsHalted()
T7BD8 001:777.613 - 0.526ms returns FALSE
T7BD8 001:777.625 JLINK_HasError()
T7BD8 001:779.088 JLINK_IsHalted()
T7BD8 001:779.591 - 0.502ms returns FALSE
T7BD8 001:779.600 JLINK_HasError()
T7BD8 001:781.089 JLINK_IsHalted()
T7BD8 001:783.398   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 001:783.893 - 2.804ms returns TRUE
T7BD8 001:783.900 JLINK_ReadReg(R15 (PC))
T7BD8 001:783.910 - 0.005ms returns 0x20000000
T7BD8 001:783.915 JLINK_ClrBPEx(BPHandle = 0x00000007)
T7BD8 001:783.919 - 0.004ms returns 0x00
T7BD8 001:783.923 JLINK_ReadReg(R0)
T7BD8 001:783.927 - 0.003ms returns 0x00000000
T7BD8 001:784.319 JLINK_HasError()
T7BD8 001:784.328 JLINK_WriteReg(R0, 0x0800C000)
T7BD8 001:784.333 - 0.005ms returns 0
T7BD8 001:784.338 JLINK_WriteReg(R1, 0x00004000)
T7BD8 001:784.341 - 0.003ms returns 0
T7BD8 001:784.345 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 001:784.349 - 0.003ms returns 0
T7BD8 001:784.353 JLINK_WriteReg(R3, 0x00000000)
T7BD8 001:784.356 - 0.003ms returns 0
T7BD8 001:784.360 JLINK_WriteReg(R4, 0x00000000)
T7BD8 001:784.364 - 0.003ms returns 0
T7BD8 001:784.368 JLINK_WriteReg(R5, 0x00000000)
T7BD8 001:784.371 - 0.003ms returns 0
T7BD8 001:784.375 JLINK_WriteReg(R6, 0x00000000)
T7BD8 001:784.379 - 0.003ms returns 0
T7BD8 001:784.387 JLINK_WriteReg(R7, 0x00000000)
T7BD8 001:784.390 - 0.003ms returns 0
T7BD8 001:784.394 JLINK_WriteReg(R8, 0x00000000)
T7BD8 001:784.398 - 0.003ms returns 0
T7BD8 001:784.402 JLINK_WriteReg(R9, 0x20000180)
T7BD8 001:784.405 - 0.003ms returns 0
T7BD8 001:784.409 JLINK_WriteReg(R10, 0x00000000)
T7BD8 001:784.413 - 0.003ms returns 0
T7BD8 001:784.417 JLINK_WriteReg(R11, 0x00000000)
T7BD8 001:784.420 - 0.003ms returns 0
T7BD8 001:784.425 JLINK_WriteReg(R12, 0x00000000)
T7BD8 001:784.428 - 0.003ms returns 0
T7BD8 001:784.432 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 001:784.436 - 0.004ms returns 0
T7BD8 001:784.440 JLINK_WriteReg(R14, 0x20000001)
T7BD8 001:784.443 - 0.003ms returns 0
T7BD8 001:784.448 JLINK_WriteReg(R15 (PC), 0x20000020)
T7BD8 001:784.451 - 0.003ms returns 0
T7BD8 001:784.455 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 001:784.458 - 0.003ms returns 0
T7BD8 001:784.462 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 001:784.466 - 0.003ms returns 0
T7BD8 001:784.470 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 001:784.473 - 0.003ms returns 0
T7BD8 001:784.477 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 001:784.481 - 0.003ms returns 0
T7BD8 001:784.485 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 001:784.490 - 0.004ms returns 0x00000008
T7BD8 001:784.494 JLINK_Go()
T7BD8 001:784.502   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 001:787.305 - 2.810ms 
T7BD8 001:787.316 JLINK_IsHalted()
T7BD8 001:789.613   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 001:790.115 - 2.798ms returns TRUE
T7BD8 001:790.128 JLINK_ReadReg(R15 (PC))
T7BD8 001:790.134 - 0.005ms returns 0x20000000
T7BD8 001:790.138 JLINK_ClrBPEx(BPHandle = 0x00000008)
T7BD8 001:790.143 - 0.004ms returns 0x00
T7BD8 001:790.147 JLINK_ReadReg(R0)
T7BD8 001:790.151 - 0.003ms returns 0x00000001
T7BD8 001:790.155 JLINK_HasError()
T7BD8 001:790.160 JLINK_WriteReg(R0, 0x0800C000)
T7BD8 001:790.164 - 0.003ms returns 0
T7BD8 001:790.168 JLINK_WriteReg(R1, 0x00004000)
T7BD8 001:790.172 - 0.003ms returns 0
T7BD8 001:790.176 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 001:790.179 - 0.003ms returns 0
T7BD8 001:790.183 JLINK_WriteReg(R3, 0x00000000)
T7BD8 001:790.187 - 0.003ms returns 0
T7BD8 001:790.191 JLINK_WriteReg(R4, 0x00000000)
T7BD8 001:790.194 - 0.003ms returns 0
T7BD8 001:790.198 JLINK_WriteReg(R5, 0x00000000)
T7BD8 001:790.202 - 0.003ms returns 0
T7BD8 001:790.206 JLINK_WriteReg(R6, 0x00000000)
T7BD8 001:790.209 - 0.003ms returns 0
T7BD8 001:790.213 JLINK_WriteReg(R7, 0x00000000)
T7BD8 001:790.216 - 0.003ms returns 0
T7BD8 001:790.220 JLINK_WriteReg(R8, 0x00000000)
T7BD8 001:790.224 - 0.003ms returns 0
T7BD8 001:790.228 JLINK_WriteReg(R9, 0x20000180)
T7BD8 001:790.232 - 0.003ms returns 0
T7BD8 001:790.236 JLINK_WriteReg(R10, 0x00000000)
T7BD8 001:790.239 - 0.003ms returns 0
T7BD8 001:790.243 JLINK_WriteReg(R11, 0x00000000)
T7BD8 001:790.247 - 0.003ms returns 0
T7BD8 001:790.251 JLINK_WriteReg(R12, 0x00000000)
T7BD8 001:790.254 - 0.003ms returns 0
T7BD8 001:790.258 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 001:790.262 - 0.003ms returns 0
T7BD8 001:790.266 JLINK_WriteReg(R14, 0x20000001)
T7BD8 001:790.269 - 0.003ms returns 0
T7BD8 001:790.274 JLINK_WriteReg(R15 (PC), 0x200000C0)
T7BD8 001:790.277 - 0.003ms returns 0
T7BD8 001:790.281 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 001:790.285 - 0.003ms returns 0
T7BD8 001:790.289 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 001:790.292 - 0.003ms returns 0
T7BD8 001:790.296 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 001:790.299 - 0.003ms returns 0
T7BD8 001:790.304 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 001:790.307 - 0.003ms returns 0
T7BD8 001:790.311 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 001:790.315 - 0.004ms returns 0x00000009
T7BD8 001:790.319 JLINK_Go()
T7BD8 001:790.327   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 001:793.027 - 2.707ms 
T7BD8 001:793.044 JLINK_IsHalted()
T7BD8 001:793.510 - 0.466ms returns FALSE
T7BD8 001:793.517 JLINK_HasError()
T7BD8 001:794.596 JLINK_IsHalted()
T7BD8 001:795.110 - 0.514ms returns FALSE
T7BD8 001:795.123 JLINK_HasError()
T7BD8 001:796.595 JLINK_IsHalted()
T7BD8 001:797.120 - 0.524ms returns FALSE
T7BD8 001:797.139 JLINK_HasError()
T7BD8 001:798.600 JLINK_IsHalted()
T7BD8 001:799.161 - 0.561ms returns FALSE
T7BD8 001:799.170 JLINK_HasError()
T7BD8 001:800.597 JLINK_IsHalted()
T7BD8 001:801.119 - 0.521ms returns FALSE
T7BD8 001:801.134 JLINK_HasError()
T7BD8 001:802.593 JLINK_IsHalted()
T7BD8 001:803.112 - 0.518ms returns FALSE
T7BD8 001:803.123 JLINK_HasError()
T7BD8 001:804.596 JLINK_IsHalted()
T7BD8 001:805.097 - 0.501ms returns FALSE
T7BD8 001:805.105 JLINK_HasError()
T7BD8 001:806.592 JLINK_IsHalted()
T7BD8 001:807.122 - 0.529ms returns FALSE
T7BD8 001:807.144 JLINK_HasError()
T7BD8 001:808.594 JLINK_IsHalted()
T7BD8 001:809.140 - 0.545ms returns FALSE
T7BD8 001:809.155 JLINK_HasError()
T7BD8 001:810.592 JLINK_IsHalted()
T7BD8 001:811.111 - 0.518ms returns FALSE
T7BD8 001:811.121 JLINK_HasError()
T7BD8 001:812.596 JLINK_IsHalted()
T7BD8 001:813.112 - 0.515ms returns FALSE
T7BD8 001:813.119 JLINK_HasError()
T7BD8 001:814.593 JLINK_IsHalted()
T7BD8 001:815.108 - 0.515ms returns FALSE
T7BD8 001:815.116 JLINK_HasError()
T7BD8 001:816.592 JLINK_IsHalted()
T7BD8 001:817.111 - 0.517ms returns FALSE
T7BD8 001:817.121 JLINK_HasError()
T7BD8 001:818.599 JLINK_IsHalted()
T7BD8 001:819.098 - 0.498ms returns FALSE
T7BD8 001:819.108 JLINK_HasError()
T7BD8 001:820.595 JLINK_IsHalted()
T7BD8 001:821.113 - 0.517ms returns FALSE
T7BD8 001:821.120 JLINK_HasError()
T7BD8 001:822.593 JLINK_IsHalted()
T7BD8 001:823.101 - 0.507ms returns FALSE
T7BD8 001:823.111 JLINK_HasError()
T7BD8 001:824.596 JLINK_IsHalted()
T7BD8 001:825.111 - 0.515ms returns FALSE
T7BD8 001:825.120 JLINK_HasError()
T7BD8 001:826.593 JLINK_IsHalted()
T7BD8 001:827.116 - 0.522ms returns FALSE
T7BD8 001:827.135 JLINK_HasError()
T7BD8 001:828.597 JLINK_IsHalted()
T7BD8 001:829.120 - 0.522ms returns FALSE
T7BD8 001:829.135 JLINK_HasError()
T7BD8 001:830.591 JLINK_IsHalted()
T7BD8 001:831.110 - 0.519ms returns FALSE
T7BD8 001:831.119 JLINK_HasError()
T7BD8 001:832.604 JLINK_IsHalted()
T7BD8 001:833.112 - 0.507ms returns FALSE
T7BD8 001:833.119 JLINK_HasError()
T7BD8 001:834.593 JLINK_IsHalted()
T7BD8 001:835.069 - 0.476ms returns FALSE
T7BD8 001:835.076 JLINK_HasError()
T7BD8 001:836.594 JLINK_IsHalted()
T7BD8 001:837.120 - 0.525ms returns FALSE
T7BD8 001:837.138 JLINK_HasError()
T7BD8 001:838.596 JLINK_IsHalted()
T7BD8 001:839.114 - 0.517ms returns FALSE
T7BD8 001:839.129 JLINK_HasError()
T7BD8 001:840.592 JLINK_IsHalted()
T7BD8 001:841.109 - 0.517ms returns FALSE
T7BD8 001:841.118 JLINK_HasError()
T7BD8 001:842.595 JLINK_IsHalted()
T7BD8 001:843.070 - 0.475ms returns FALSE
T7BD8 001:843.078 JLINK_HasError()
T7BD8 001:844.594 JLINK_IsHalted()
T7BD8 001:845.106 - 0.512ms returns FALSE
T7BD8 001:845.118 JLINK_HasError()
T7BD8 001:846.594 JLINK_IsHalted()
T7BD8 001:847.118 - 0.524ms returns FALSE
T7BD8 001:847.136 JLINK_HasError()
T7BD8 001:848.595 JLINK_IsHalted()
T7BD8 001:849.122 - 0.526ms returns FALSE
T7BD8 001:849.137 JLINK_HasError()
T7BD8 001:850.595 JLINK_IsHalted()
T7BD8 001:851.110 - 0.515ms returns FALSE
T7BD8 001:851.116 JLINK_HasError()
T7BD8 001:852.593 JLINK_IsHalted()
T7BD8 001:853.113 - 0.519ms returns FALSE
T7BD8 001:853.126 JLINK_HasError()
T7BD8 001:854.598 JLINK_IsHalted()
T7BD8 001:855.112 - 0.514ms returns FALSE
T7BD8 001:855.121 JLINK_HasError()
T7BD8 001:856.592 JLINK_IsHalted()
T7BD8 001:857.105 - 0.512ms returns FALSE
T7BD8 001:857.121 JLINK_HasError()
T7BD8 001:858.594 JLINK_IsHalted()
T7BD8 001:859.117 - 0.523ms returns FALSE
T7BD8 001:859.131 JLINK_HasError()
T7BD8 001:860.593 JLINK_IsHalted()
T7BD8 001:861.115 - 0.521ms returns FALSE
T7BD8 001:861.126 JLINK_HasError()
T7BD8 001:862.600 JLINK_IsHalted()
T7BD8 001:863.070 - 0.470ms returns FALSE
T7BD8 001:863.076 JLINK_HasError()
T7BD8 001:864.596 JLINK_IsHalted()
T7BD8 001:865.072 - 0.475ms returns FALSE
T7BD8 001:865.078 JLINK_HasError()
T7BD8 001:866.596 JLINK_IsHalted()
T7BD8 001:867.121 - 0.523ms returns FALSE
T7BD8 001:867.140 JLINK_HasError()
T7BD8 001:868.599 JLINK_IsHalted()
T7BD8 001:869.147 - 0.548ms returns FALSE
T7BD8 001:869.162 JLINK_HasError()
T7BD8 001:871.594 JLINK_IsHalted()
T7BD8 001:872.114 - 0.519ms returns FALSE
T7BD8 001:872.123 JLINK_HasError()
T7BD8 001:873.596 JLINK_IsHalted()
T7BD8 001:874.113 - 0.516ms returns FALSE
T7BD8 001:874.121 JLINK_HasError()
T7BD8 001:875.604 JLINK_IsHalted()
T7BD8 001:876.114 - 0.510ms returns FALSE
T7BD8 001:876.122 JLINK_HasError()
T7BD8 001:877.599 JLINK_IsHalted()
T7BD8 001:878.117 - 0.517ms returns FALSE
T7BD8 001:878.130 JLINK_HasError()
T7BD8 001:879.595 JLINK_IsHalted()
T7BD8 001:880.112 - 0.517ms returns FALSE
T7BD8 001:880.127 JLINK_HasError()
T7BD8 001:881.596 JLINK_IsHalted()
T7BD8 001:882.114 - 0.517ms returns FALSE
T7BD8 001:882.122 JLINK_HasError()
T7BD8 001:883.594 JLINK_IsHalted()
T7BD8 001:884.110 - 0.515ms returns FALSE
T7BD8 001:884.118 JLINK_HasError()
T7BD8 001:885.600 JLINK_IsHalted()
T7BD8 001:886.111 - 0.510ms returns FALSE
T7BD8 001:886.119 JLINK_HasError()
T7BD8 001:887.598 JLINK_IsHalted()
T7BD8 001:888.120 - 0.521ms returns FALSE
T7BD8 001:888.132 JLINK_HasError()
T7BD8 001:889.599 JLINK_IsHalted()
T7BD8 001:890.114 - 0.514ms returns FALSE
T7BD8 001:890.120 JLINK_HasError()
T7BD8 001:892.103 JLINK_IsHalted()
T7BD8 001:892.623 - 0.519ms returns FALSE
T7BD8 001:892.633 JLINK_HasError()
T7BD8 001:894.101 JLINK_IsHalted()
T7BD8 001:894.590 - 0.488ms returns FALSE
T7BD8 001:894.597 JLINK_HasError()
T7BD8 001:896.140 JLINK_IsHalted()
T7BD8 001:896.611 - 0.470ms returns FALSE
T7BD8 001:896.617 JLINK_HasError()
T7BD8 001:898.098 JLINK_IsHalted()
T7BD8 001:898.626 - 0.527ms returns FALSE
T7BD8 001:898.639 JLINK_HasError()
T7BD8 001:900.103 JLINK_IsHalted()
T7BD8 001:900.637 - 0.533ms returns FALSE
T7BD8 001:900.645 JLINK_HasError()
T7BD8 001:902.100 JLINK_IsHalted()
T7BD8 001:902.596 - 0.496ms returns FALSE
T7BD8 001:902.612 JLINK_HasError()
T7BD8 001:904.102 JLINK_IsHalted()
T7BD8 001:904.600 - 0.498ms returns FALSE
T7BD8 001:904.610 JLINK_HasError()
T7BD8 001:906.102 JLINK_IsHalted()
T7BD8 001:906.613 - 0.511ms returns FALSE
T7BD8 001:906.621 JLINK_HasError()
T7BD8 001:908.110 JLINK_IsHalted()
T7BD8 001:908.683 - 0.572ms returns FALSE
T7BD8 001:908.699 JLINK_HasError()
T7BD8 001:910.115 JLINK_IsHalted()
T7BD8 001:910.657 - 0.541ms returns FALSE
T7BD8 001:910.671 JLINK_HasError()
T7BD8 001:912.104 JLINK_IsHalted()
T7BD8 001:912.622 - 0.517ms returns FALSE
T7BD8 001:912.633 JLINK_HasError()
T7BD8 001:914.101 JLINK_IsHalted()
T7BD8 001:914.614 - 0.513ms returns FALSE
T7BD8 001:914.622 JLINK_HasError()
T7BD8 001:916.102 JLINK_IsHalted()
T7BD8 001:916.612 - 0.509ms returns FALSE
T7BD8 001:916.618 JLINK_HasError()
T7BD8 001:918.110 JLINK_IsHalted()
T7BD8 001:918.656 - 0.545ms returns FALSE
T7BD8 001:918.671 JLINK_HasError()
T7BD8 001:920.099 JLINK_IsHalted()
T7BD8 001:920.568 - 0.468ms returns FALSE
T7BD8 001:920.574 JLINK_HasError()
T7BD8 001:922.103 JLINK_IsHalted()
T7BD8 001:922.572 - 0.468ms returns FALSE
T7BD8 001:922.586 JLINK_HasError()
T7BD8 001:924.102 JLINK_IsHalted()
T7BD8 001:924.598 - 0.495ms returns FALSE
T7BD8 001:924.605 JLINK_HasError()
T7BD8 001:926.101 JLINK_IsHalted()
T7BD8 001:926.570 - 0.469ms returns FALSE
T7BD8 001:926.577 JLINK_HasError()
T7BD8 001:928.104 JLINK_IsHalted()
T7BD8 001:928.649 - 0.543ms returns FALSE
T7BD8 001:928.666 JLINK_HasError()
T7BD8 001:930.100 JLINK_IsHalted()
T7BD8 001:930.624 - 0.524ms returns FALSE
T7BD8 001:930.634 JLINK_HasError()
T7BD8 001:932.104 JLINK_IsHalted()
T7BD8 001:932.611 - 0.507ms returns FALSE
T7BD8 001:932.618 JLINK_HasError()
T7BD8 001:934.101 JLINK_IsHalted()
T7BD8 001:934.591 - 0.490ms returns FALSE
T7BD8 001:934.598 JLINK_HasError()
T7BD8 001:936.099 JLINK_IsHalted()
T7BD8 001:936.606 - 0.506ms returns FALSE
T7BD8 001:936.612 JLINK_HasError()
T7BD8 001:938.103 JLINK_IsHalted()
T7BD8 001:938.653 - 0.549ms returns FALSE
T7BD8 001:938.669 JLINK_HasError()
T7BD8 001:940.104 JLINK_IsHalted()
T7BD8 001:940.652 - 0.547ms returns FALSE
T7BD8 001:940.659 JLINK_HasError()
T7BD8 001:942.100 JLINK_IsHalted()
T7BD8 001:942.613 - 0.513ms returns FALSE
T7BD8 001:942.622 JLINK_HasError()
T7BD8 001:944.102 JLINK_IsHalted()
T7BD8 001:944.620 - 0.518ms returns FALSE
T7BD8 001:944.628 JLINK_HasError()
T7BD8 001:946.100 JLINK_IsHalted()
T7BD8 001:946.591 - 0.490ms returns FALSE
T7BD8 001:946.600 JLINK_HasError()
T7BD8 001:948.098 JLINK_IsHalted()
T7BD8 001:948.569 - 0.470ms returns FALSE
T7BD8 001:948.575 JLINK_HasError()
T7BD8 001:950.104 JLINK_IsHalted()
T7BD8 001:950.636 - 0.531ms returns FALSE
T7BD8 001:950.644 JLINK_HasError()
T7BD8 001:952.100 JLINK_IsHalted()
T7BD8 001:952.577 - 0.477ms returns FALSE
T7BD8 001:952.587 JLINK_HasError()
T7BD8 001:954.102 JLINK_IsHalted()
T7BD8 001:954.608 - 0.506ms returns FALSE
T7BD8 001:954.615 JLINK_HasError()
T7BD8 001:956.099 JLINK_IsHalted()
T7BD8 001:956.612 - 0.512ms returns FALSE
T7BD8 001:956.619 JLINK_HasError()
T7BD8 001:958.106 JLINK_IsHalted()
T7BD8 001:958.627 - 0.519ms returns FALSE
T7BD8 001:958.642 JLINK_HasError()
T7BD8 001:960.101 JLINK_IsHalted()
T7BD8 001:960.600 - 0.498ms returns FALSE
T7BD8 001:960.609 JLINK_HasError()
T7BD8 001:962.100 JLINK_IsHalted()
T7BD8 001:962.568 - 0.468ms returns FALSE
T7BD8 001:962.576 JLINK_HasError()
T7BD8 001:964.101 JLINK_IsHalted()
T7BD8 001:964.611 - 0.509ms returns FALSE
T7BD8 001:964.618 JLINK_HasError()
T7BD8 001:966.098 JLINK_IsHalted()
T7BD8 001:966.568 - 0.469ms returns FALSE
T7BD8 001:966.574 JLINK_HasError()
T7BD8 001:968.105 JLINK_IsHalted()
T7BD8 001:968.646 - 0.540ms returns FALSE
T7BD8 001:968.660 JLINK_HasError()
T7BD8 001:970.102 JLINK_IsHalted()
T7BD8 001:970.595 - 0.492ms returns FALSE
T7BD8 001:970.608 JLINK_HasError()
T7BD8 001:972.102 JLINK_IsHalted()
T7BD8 001:972.582 - 0.479ms returns FALSE
T7BD8 001:972.591 JLINK_HasError()
T7BD8 001:974.101 JLINK_IsHalted()
T7BD8 001:974.649 - 0.546ms returns FALSE
T7BD8 001:974.676 JLINK_HasError()
T7BD8 001:977.104 JLINK_IsHalted()
T7BD8 001:977.648 - 0.543ms returns FALSE
T7BD8 001:977.665 JLINK_HasError()
T7BD8 001:979.104 JLINK_IsHalted()
T7BD8 001:979.764 - 0.659ms returns FALSE
T7BD8 001:979.775 JLINK_HasError()
T7BD8 001:981.104 JLINK_IsHalted()
T7BD8 001:981.614 - 0.509ms returns FALSE
T7BD8 001:981.621 JLINK_HasError()
T7BD8 001:983.107 JLINK_IsHalted()
T7BD8 001:983.621 - 0.513ms returns FALSE
T7BD8 001:983.629 JLINK_HasError()
T7BD8 001:985.104 JLINK_IsHalted()
T7BD8 001:985.635 - 0.531ms returns FALSE
T7BD8 001:985.643 JLINK_HasError()
T7BD8 001:987.103 JLINK_IsHalted()
T7BD8 001:987.611 - 0.508ms returns FALSE
T7BD8 001:987.619 JLINK_HasError()
T7BD8 001:989.613 JLINK_IsHalted()
T7BD8 001:990.158 - 0.544ms returns FALSE
T7BD8 001:990.165 JLINK_HasError()
T7BD8 001:991.609 JLINK_IsHalted()
T7BD8 001:992.112 - 0.502ms returns FALSE
T7BD8 001:992.120 JLINK_HasError()
T7BD8 001:993.612 JLINK_IsHalted()
T7BD8 001:994.108 - 0.495ms returns FALSE
T7BD8 001:994.115 JLINK_HasError()
T7BD8 001:995.608 JLINK_IsHalted()
T7BD8 001:996.070 - 0.462ms returns FALSE
T7BD8 001:996.076 JLINK_HasError()
T7BD8 001:997.615 JLINK_IsHalted()
T7BD8 001:998.158 - 0.542ms returns FALSE
T7BD8 001:998.173 JLINK_HasError()
T7BD8 001:999.611 JLINK_IsHalted()
T7BD8 002:000.153 - 0.541ms returns FALSE
T7BD8 002:000.159 JLINK_HasError()
T7BD8 002:001.612 JLINK_IsHalted()
T7BD8 002:002.112 - 0.498ms returns FALSE
T7BD8 002:002.120 JLINK_HasError()
T7BD8 002:003.611 JLINK_IsHalted()
T7BD8 002:004.108 - 0.496ms returns FALSE
T7BD8 002:004.115 JLINK_HasError()
T7BD8 002:005.609 JLINK_IsHalted()
T7BD8 002:006.148 - 0.538ms returns FALSE
T7BD8 002:006.165 JLINK_HasError()
T7BD8 002:007.619 JLINK_IsHalted()
T7BD8 002:008.154 - 0.534ms returns FALSE
T7BD8 002:008.160 JLINK_HasError()
T7BD8 002:009.609 JLINK_IsHalted()
T7BD8 002:010.115 - 0.505ms returns FALSE
T7BD8 002:010.132 JLINK_HasError()
T7BD8 002:011.617 JLINK_IsHalted()
T7BD8 002:012.152 - 0.534ms returns FALSE
T7BD8 002:012.166 JLINK_HasError()
T7BD8 002:013.607 JLINK_IsHalted()
T7BD8 002:014.068 - 0.460ms returns FALSE
T7BD8 002:014.075 JLINK_HasError()
T7BD8 002:015.613 JLINK_IsHalted()
T7BD8 002:016.114 - 0.500ms returns FALSE
T7BD8 002:016.123 JLINK_HasError()
T7BD8 002:017.614 JLINK_IsHalted()
T7BD8 002:018.149 - 0.535ms returns FALSE
T7BD8 002:018.160 JLINK_HasError()
T7BD8 002:019.611 JLINK_IsHalted()
T7BD8 002:020.143 - 0.532ms returns FALSE
T7BD8 002:020.154 JLINK_HasError()
T7BD8 002:021.609 JLINK_IsHalted()
T7BD8 002:022.163 - 0.553ms returns FALSE
T7BD8 002:022.177 JLINK_HasError()
T7BD8 002:023.609 JLINK_IsHalted()
T7BD8 002:024.101 - 0.491ms returns FALSE
T7BD8 002:024.120 JLINK_HasError()
T7BD8 002:025.610 JLINK_IsHalted()
T7BD8 002:026.112 - 0.502ms returns FALSE
T7BD8 002:026.122 JLINK_HasError()
T7BD8 002:027.615 JLINK_IsHalted()
T7BD8 002:028.121 - 0.505ms returns FALSE
T7BD8 002:028.136 JLINK_HasError()
T7BD8 002:029.609 JLINK_IsHalted()
T7BD8 002:030.121 - 0.511ms returns FALSE
T7BD8 002:030.128 JLINK_HasError()
T7BD8 002:031.612 JLINK_IsHalted()
T7BD8 002:032.100 - 0.487ms returns FALSE
T7BD8 002:032.111 JLINK_HasError()
T7BD8 002:033.623 JLINK_IsHalted()
T7BD8 002:034.158 - 0.535ms returns FALSE
T7BD8 002:034.166 JLINK_HasError()
T7BD8 002:035.608 JLINK_IsHalted()
T7BD8 002:036.114 - 0.504ms returns FALSE
T7BD8 002:036.123 JLINK_HasError()
T7BD8 002:037.616 JLINK_IsHalted()
T7BD8 002:038.194 - 0.577ms returns FALSE
T7BD8 002:038.207 JLINK_HasError()
T7BD8 002:039.611 JLINK_IsHalted()
T7BD8 002:040.114 - 0.503ms returns FALSE
T7BD8 002:040.120 JLINK_HasError()
T7BD8 002:041.611 JLINK_IsHalted()
T7BD8 002:042.113 - 0.501ms returns FALSE
T7BD8 002:042.121 JLINK_HasError()
T7BD8 002:043.612 JLINK_IsHalted()
T7BD8 002:044.112 - 0.500ms returns FALSE
T7BD8 002:044.119 JLINK_HasError()
T7BD8 002:045.609 JLINK_IsHalted()
T7BD8 002:046.069 - 0.460ms returns FALSE
T7BD8 002:046.076 JLINK_HasError()
T7BD8 002:047.612 JLINK_IsHalted()
T7BD8 002:048.149 - 0.536ms returns FALSE
T7BD8 002:048.164 JLINK_HasError()
T7BD8 002:049.608 JLINK_IsHalted()
T7BD8 002:050.105 - 0.496ms returns FALSE
T7BD8 002:050.120 JLINK_HasError()
T7BD8 002:051.610 JLINK_IsHalted()
T7BD8 002:052.114 - 0.503ms returns FALSE
T7BD8 002:052.125 JLINK_HasError()
T7BD8 002:053.610 JLINK_IsHalted()
T7BD8 002:054.113 - 0.502ms returns FALSE
T7BD8 002:054.127 JLINK_HasError()
T7BD8 002:055.609 JLINK_IsHalted()
T7BD8 002:056.109 - 0.499ms returns FALSE
T7BD8 002:056.117 JLINK_HasError()
T7BD8 002:057.612 JLINK_IsHalted()
T7BD8 002:058.112 - 0.500ms returns FALSE
T7BD8 002:058.119 JLINK_HasError()
T7BD8 002:059.607 JLINK_IsHalted()
T7BD8 002:060.104 - 0.496ms returns FALSE
T7BD8 002:060.118 JLINK_HasError()
T7BD8 002:061.612 JLINK_IsHalted()
T7BD8 002:062.109 - 0.496ms returns FALSE
T7BD8 002:062.118 JLINK_HasError()
T7BD8 002:063.611 JLINK_IsHalted()
T7BD8 002:064.099 - 0.487ms returns FALSE
T7BD8 002:064.106 JLINK_HasError()
T7BD8 002:065.620 JLINK_IsHalted()
T7BD8 002:066.109 - 0.489ms returns FALSE
T7BD8 002:066.116 JLINK_HasError()
T7BD8 002:067.609 JLINK_IsHalted()
T7BD8 002:068.113 - 0.503ms returns FALSE
T7BD8 002:068.122 JLINK_HasError()
T7BD8 002:069.609 JLINK_IsHalted()
T7BD8 002:070.118 - 0.508ms returns FALSE
T7BD8 002:070.128 JLINK_HasError()
T7BD8 002:071.611 JLINK_IsHalted()
T7BD8 002:072.113 - 0.501ms returns FALSE
T7BD8 002:072.121 JLINK_HasError()
T7BD8 002:073.610 JLINK_IsHalted()
T7BD8 002:074.098 - 0.487ms returns FALSE
T7BD8 002:074.105 JLINK_HasError()
T7BD8 002:075.612 JLINK_IsHalted()
T7BD8 002:076.149 - 0.536ms returns FALSE
T7BD8 002:076.155 JLINK_HasError()
T7BD8 002:077.618 JLINK_IsHalted()
T7BD8 002:078.148 - 0.529ms returns FALSE
T7BD8 002:078.164 JLINK_HasError()
T7BD8 002:079.615 JLINK_IsHalted()
T7BD8 002:080.148 - 0.532ms returns FALSE
T7BD8 002:080.162 JLINK_HasError()
T7BD8 002:081.617 JLINK_IsHalted()
T7BD8 002:082.144 - 0.527ms returns FALSE
T7BD8 002:082.151 JLINK_HasError()
T7BD8 002:083.613 JLINK_IsHalted()
T7BD8 002:084.114 - 0.500ms returns FALSE
T7BD8 002:084.122 JLINK_HasError()
T7BD8 002:085.616 JLINK_IsHalted()
T7BD8 002:086.160 - 0.543ms returns FALSE
T7BD8 002:086.173 JLINK_HasError()
T7BD8 002:088.620 JLINK_IsHalted()
T7BD8 002:089.157 - 0.536ms returns FALSE
T7BD8 002:089.165 JLINK_HasError()
T7BD8 002:091.119 JLINK_IsHalted()
T7BD8 002:091.592 - 0.473ms returns FALSE
T7BD8 002:091.598 JLINK_HasError()
T7BD8 002:093.118 JLINK_IsHalted()
T7BD8 002:093.635 - 0.516ms returns FALSE
T7BD8 002:093.643 JLINK_HasError()
T7BD8 002:095.120 JLINK_IsHalted()
T7BD8 002:095.590 - 0.469ms returns FALSE
T7BD8 002:095.596 JLINK_HasError()
T7BD8 002:097.118 JLINK_IsHalted()
T7BD8 002:097.656 - 0.538ms returns FALSE
T7BD8 002:097.669 JLINK_HasError()
T7BD8 002:099.119 JLINK_IsHalted()
T7BD8 002:099.644 - 0.524ms returns FALSE
T7BD8 002:099.655 JLINK_HasError()
T7BD8 002:101.120 JLINK_IsHalted()
T7BD8 002:101.656 - 0.535ms returns FALSE
T7BD8 002:101.665 JLINK_HasError()
T7BD8 002:104.123 JLINK_IsHalted()
T7BD8 002:104.666 - 0.542ms returns FALSE
T7BD8 002:104.674 JLINK_HasError()
T7BD8 002:106.116 JLINK_IsHalted()
T7BD8 002:106.606 - 0.489ms returns FALSE
T7BD8 002:106.611 JLINK_HasError()
T7BD8 002:108.122 JLINK_IsHalted()
T7BD8 002:108.689 - 0.567ms returns FALSE
T7BD8 002:108.700 JLINK_HasError()
T7BD8 002:110.122 JLINK_IsHalted()
T7BD8 002:110.656 - 0.534ms returns FALSE
T7BD8 002:110.663 JLINK_HasError()
T7BD8 002:112.118 JLINK_IsHalted()
T7BD8 002:112.612 - 0.492ms returns FALSE
T7BD8 002:112.622 JLINK_HasError()
T7BD8 002:114.121 JLINK_IsHalted()
T7BD8 002:114.635 - 0.514ms returns FALSE
T7BD8 002:114.642 JLINK_HasError()
T7BD8 002:116.118 JLINK_IsHalted()
T7BD8 002:116.612 - 0.494ms returns FALSE
T7BD8 002:116.619 JLINK_HasError()
T7BD8 002:118.121 JLINK_IsHalted()
T7BD8 002:118.665 - 0.543ms returns FALSE
T7BD8 002:118.683 JLINK_HasError()
T7BD8 002:120.121 JLINK_IsHalted()
T7BD8 002:120.649 - 0.528ms returns FALSE
T7BD8 002:120.664 JLINK_HasError()
T7BD8 002:122.118 JLINK_IsHalted()
T7BD8 002:122.623 - 0.505ms returns FALSE
T7BD8 002:122.630 JLINK_HasError()
T7BD8 002:124.118 JLINK_IsHalted()
T7BD8 002:124.638 - 0.519ms returns FALSE
T7BD8 002:124.647 JLINK_HasError()
T7BD8 002:126.122 JLINK_IsHalted()
T7BD8 002:126.633 - 0.511ms returns FALSE
T7BD8 002:126.646 JLINK_HasError()
T7BD8 002:128.134 JLINK_IsHalted()
T7BD8 002:128.684 - 0.550ms returns FALSE
T7BD8 002:128.700 JLINK_HasError()
T7BD8 002:130.118 JLINK_IsHalted()
T7BD8 002:130.636 - 0.517ms returns FALSE
T7BD8 002:130.646 JLINK_HasError()
T7BD8 002:132.119 JLINK_IsHalted()
T7BD8 002:132.613 - 0.494ms returns FALSE
T7BD8 002:132.621 JLINK_HasError()
T7BD8 002:134.123 JLINK_IsHalted()
T7BD8 002:134.661 - 0.538ms returns FALSE
T7BD8 002:134.675 JLINK_HasError()
T7BD8 002:136.123 JLINK_IsHalted()
T7BD8 002:136.636 - 0.512ms returns FALSE
T7BD8 002:136.643 JLINK_HasError()
T7BD8 002:138.121 JLINK_IsHalted()
T7BD8 002:138.664 - 0.542ms returns FALSE
T7BD8 002:138.684 JLINK_HasError()
T7BD8 002:140.120 JLINK_IsHalted()
T7BD8 002:140.652 - 0.531ms returns FALSE
T7BD8 002:140.658 JLINK_HasError()
T7BD8 002:142.119 JLINK_IsHalted()
T7BD8 002:142.614 - 0.494ms returns FALSE
T7BD8 002:142.624 JLINK_HasError()
T7BD8 002:144.119 JLINK_IsHalted()
T7BD8 002:144.611 - 0.492ms returns FALSE
T7BD8 002:144.618 JLINK_HasError()
T7BD8 002:146.118 JLINK_IsHalted()
T7BD8 002:146.591 - 0.472ms returns FALSE
T7BD8 002:146.597 JLINK_HasError()
T7BD8 002:148.120 JLINK_IsHalted()
T7BD8 002:148.661 - 0.540ms returns FALSE
T7BD8 002:148.674 JLINK_HasError()
T7BD8 002:150.118 JLINK_IsHalted()
T7BD8 002:150.624 - 0.505ms returns FALSE
T7BD8 002:150.634 JLINK_HasError()
T7BD8 002:152.118 JLINK_IsHalted()
T7BD8 002:152.613 - 0.494ms returns FALSE
T7BD8 002:152.622 JLINK_HasError()
T7BD8 002:154.121 JLINK_IsHalted()
T7BD8 002:154.613 - 0.492ms returns FALSE
T7BD8 002:154.620 JLINK_HasError()
T7BD8 002:156.118 JLINK_IsHalted()
T7BD8 002:156.590 - 0.472ms returns FALSE
T7BD8 002:156.601 JLINK_HasError()
T7BD8 002:158.126 JLINK_IsHalted()
T7BD8 002:158.660 - 0.534ms returns FALSE
T7BD8 002:158.674 JLINK_HasError()
T7BD8 002:160.122 JLINK_IsHalted()
T7BD8 002:160.643 - 0.520ms returns FALSE
T7BD8 002:160.654 JLINK_HasError()
T7BD8 002:162.121 JLINK_IsHalted()
T7BD8 002:162.621 - 0.499ms returns FALSE
T7BD8 002:162.627 JLINK_HasError()
T7BD8 002:164.122 JLINK_IsHalted()
T7BD8 002:164.590 - 0.468ms returns FALSE
T7BD8 002:164.597 JLINK_HasError()
T7BD8 002:166.120 JLINK_IsHalted()
T7BD8 002:166.640 - 0.520ms returns FALSE
T7BD8 002:166.647 JLINK_HasError()
T7BD8 002:168.119 JLINK_IsHalted()
T7BD8 002:168.658 - 0.538ms returns FALSE
T7BD8 002:168.675 JLINK_HasError()
T7BD8 002:170.118 JLINK_IsHalted()
T7BD8 002:170.646 - 0.527ms returns FALSE
T7BD8 002:170.665 JLINK_HasError()
T7BD8 002:172.118 JLINK_IsHalted()
T7BD8 002:172.635 - 0.517ms returns FALSE
T7BD8 002:172.644 JLINK_HasError()
T7BD8 002:174.128 JLINK_IsHalted()
T7BD8 002:174.650 - 0.521ms returns FALSE
T7BD8 002:174.658 JLINK_HasError()
T7BD8 002:176.124 JLINK_IsHalted()
T7BD8 002:176.637 - 0.513ms returns FALSE
T7BD8 002:176.650 JLINK_HasError()
T7BD8 002:178.125 JLINK_IsHalted()
T7BD8 002:178.680 - 0.555ms returns FALSE
T7BD8 002:178.698 JLINK_HasError()
T7BD8 002:180.121 JLINK_IsHalted()
T7BD8 002:180.659 - 0.537ms returns FALSE
T7BD8 002:180.671 JLINK_HasError()
T7BD8 002:182.011 JLINK_IsHalted()
T7BD8 002:182.487 - 0.475ms returns FALSE
T7BD8 002:182.495 JLINK_HasError()
T7BD8 002:183.720 JLINK_IsHalted()
T7BD8 002:184.234 - 0.514ms returns FALSE
T7BD8 002:184.242 JLINK_HasError()
T7BD8 002:185.726 JLINK_IsHalted()
T7BD8 002:186.193 - 0.466ms returns FALSE
T7BD8 002:186.198 JLINK_HasError()
T7BD8 002:187.476 JLINK_IsHalted()
T7BD8 002:188.013 - 0.537ms returns FALSE
T7BD8 002:188.022 JLINK_HasError()
T7BD8 002:189.454 JLINK_IsHalted()
T7BD8 002:189.965 - 0.510ms returns FALSE
T7BD8 002:189.972 JLINK_HasError()
T7BD8 002:191.737 JLINK_IsHalted()
T7BD8 002:192.217 - 0.479ms returns FALSE
T7BD8 002:192.225 JLINK_HasError()
T7BD8 002:193.351 JLINK_IsHalted()
T7BD8 002:193.840 - 0.488ms returns FALSE
T7BD8 002:193.846 JLINK_HasError()
T7BD8 002:195.355 JLINK_IsHalted()
T7BD8 002:195.882 - 0.526ms returns FALSE
T7BD8 002:195.890 JLINK_HasError()
T7BD8 002:198.358 JLINK_IsHalted()
T7BD8 002:198.907 - 0.548ms returns FALSE
T7BD8 002:198.923 JLINK_HasError()
T7BD8 002:200.354 JLINK_IsHalted()
T7BD8 002:200.894 - 0.540ms returns FALSE
T7BD8 002:200.905 JLINK_HasError()
T7BD8 002:202.360 JLINK_IsHalted()
T7BD8 002:202.887 - 0.530ms returns FALSE
T7BD8 002:202.895 JLINK_HasError()
T7BD8 002:204.355 JLINK_IsHalted()
T7BD8 002:204.903 - 0.548ms returns FALSE
T7BD8 002:204.910 JLINK_HasError()
T7BD8 002:206.354 JLINK_IsHalted()
T7BD8 002:206.842 - 0.488ms returns FALSE
T7BD8 002:206.848 JLINK_HasError()
T7BD8 002:208.360 JLINK_IsHalted()
T7BD8 002:210.775   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:211.307 - 2.947ms returns TRUE
T7BD8 002:211.315 JLINK_ReadReg(R15 (PC))
T7BD8 002:211.321 - 0.005ms returns 0x20000000
T7BD8 002:211.325 JLINK_ClrBPEx(BPHandle = 0x00000009)
T7BD8 002:211.340 - 0.014ms returns 0x00
T7BD8 002:211.345 JLINK_ReadReg(R0)
T7BD8 002:211.349 - 0.003ms returns 0x00000000
T7BD8 002:211.852 JLINK_HasError()
T7BD8 002:211.864 JLINK_WriteReg(R0, 0x00000001)
T7BD8 002:211.870 - 0.005ms returns 0
T7BD8 002:211.874 JLINK_WriteReg(R1, 0x00004000)
T7BD8 002:211.877 - 0.003ms returns 0
T7BD8 002:211.882 JLINK_WriteReg(R2, 0x000000FF)
T7BD8 002:211.886 - 0.003ms returns 0
T7BD8 002:211.890 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:211.893 - 0.003ms returns 0
T7BD8 002:211.897 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:211.900 - 0.003ms returns 0
T7BD8 002:211.905 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:211.908 - 0.003ms returns 0
T7BD8 002:211.912 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:211.916 - 0.003ms returns 0
T7BD8 002:211.920 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:211.924 - 0.004ms returns 0
T7BD8 002:211.928 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:211.936 - 0.008ms returns 0
T7BD8 002:211.942 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:211.946 - 0.003ms returns 0
T7BD8 002:211.950 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:211.953 - 0.003ms returns 0
T7BD8 002:211.957 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:211.961 - 0.003ms returns 0
T7BD8 002:211.965 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:211.968 - 0.003ms returns 0
T7BD8 002:211.972 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:211.976 - 0.004ms returns 0
T7BD8 002:211.980 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:211.984 - 0.003ms returns 0
T7BD8 002:211.989 JLINK_WriteReg(R15 (PC), 0x20000086)
T7BD8 002:211.993 - 0.004ms returns 0
T7BD8 002:211.997 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:212.000 - 0.003ms returns 0
T7BD8 002:212.004 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:212.008 - 0.003ms returns 0
T7BD8 002:212.012 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:212.015 - 0.003ms returns 0
T7BD8 002:212.019 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:212.022 - 0.003ms returns 0
T7BD8 002:212.027 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:212.032 - 0.004ms returns 0x0000000A
T7BD8 002:212.036 JLINK_Go()
T7BD8 002:212.048   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:214.805 - 2.768ms 
T7BD8 002:214.816 JLINK_IsHalted()
T7BD8 002:217.197   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:217.763 - 2.945ms returns TRUE
T7BD8 002:217.778 JLINK_ReadReg(R15 (PC))
T7BD8 002:217.783 - 0.005ms returns 0x20000000
T7BD8 002:217.788 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T7BD8 002:217.792 - 0.004ms returns 0x00
T7BD8 002:217.796 JLINK_ReadReg(R0)
T7BD8 002:217.800 - 0.003ms returns 0x00000000
T7BD8 002:271.844 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T7BD8 002:271.858   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T7BD8 002:271.875   CPU_WriteMem(388 bytes @ 0x20000000)
T7BD8 002:273.765 - 1.920ms returns 0x184
T7BD8 002:273.798 JLINK_HasError()
T7BD8 002:273.804 JLINK_WriteReg(R0, 0x08000000)
T7BD8 002:273.810 - 0.006ms returns 0
T7BD8 002:273.814 JLINK_WriteReg(R1, 0x0ABA9500)
T7BD8 002:273.818 - 0.003ms returns 0
T7BD8 002:273.822 JLINK_WriteReg(R2, 0x00000002)
T7BD8 002:273.826 - 0.003ms returns 0
T7BD8 002:273.830 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:273.833 - 0.003ms returns 0
T7BD8 002:273.838 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:273.841 - 0.003ms returns 0
T7BD8 002:273.845 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:273.848 - 0.003ms returns 0
T7BD8 002:273.852 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:273.856 - 0.003ms returns 0
T7BD8 002:273.860 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:273.863 - 0.003ms returns 0
T7BD8 002:273.867 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:273.871 - 0.003ms returns 0
T7BD8 002:273.875 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:273.878 - 0.003ms returns 0
T7BD8 002:273.882 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:273.886 - 0.003ms returns 0
T7BD8 002:273.890 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:273.893 - 0.003ms returns 0
T7BD8 002:273.897 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:273.901 - 0.003ms returns 0
T7BD8 002:273.905 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:273.909 - 0.004ms returns 0
T7BD8 002:273.913 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:273.916 - 0.003ms returns 0
T7BD8 002:273.920 JLINK_WriteReg(R15 (PC), 0x20000054)
T7BD8 002:273.924 - 0.003ms returns 0
T7BD8 002:273.928 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:273.932 - 0.003ms returns 0
T7BD8 002:273.936 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:273.939 - 0.003ms returns 0
T7BD8 002:273.943 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:273.946 - 0.003ms returns 0
T7BD8 002:273.950 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:273.954 - 0.003ms returns 0
T7BD8 002:273.958 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:273.965   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:274.444 - 0.486ms returns 0x0000000B
T7BD8 002:274.450 JLINK_Go()
T7BD8 002:274.455   CPU_WriteMem(2 bytes @ 0x20000000)
T7BD8 002:274.946   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:277.850 - 3.398ms 
T7BD8 002:277.863 JLINK_IsHalted()
T7BD8 002:280.265   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:280.755 - 2.891ms returns TRUE
T7BD8 002:280.767 JLINK_ReadReg(R15 (PC))
T7BD8 002:280.772 - 0.005ms returns 0x20000000
T7BD8 002:280.790 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T7BD8 002:280.794 - 0.004ms returns 0x00
T7BD8 002:280.825 JLINK_ReadReg(R0)
T7BD8 002:280.830 - 0.005ms returns 0x00000000
T7BD8 002:281.060 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:281.066   Data:  88 57 00 20 C1 01 00 08 2D 28 00 08 11 25 00 08 ...
T7BD8 002:281.079   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:283.659 - 2.598ms returns 0x27C
T7BD8 002:283.665 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:283.668   Data:  4F F0 00 0B 23 F0 00 43 50 EA 01 04 5E D0 52 EA ...
T7BD8 002:283.676   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:285.528 - 1.863ms returns 0x184
T7BD8 002:285.534 JLINK_HasError()
T7BD8 002:285.539 JLINK_WriteReg(R0, 0x08000000)
T7BD8 002:285.543 - 0.004ms returns 0
T7BD8 002:285.548 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:285.551 - 0.003ms returns 0
T7BD8 002:285.555 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:285.559 - 0.003ms returns 0
T7BD8 002:285.563 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:285.566 - 0.003ms returns 0
T7BD8 002:285.570 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:285.574 - 0.003ms returns 0
T7BD8 002:285.578 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:285.581 - 0.003ms returns 0
T7BD8 002:285.586 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:285.589 - 0.003ms returns 0
T7BD8 002:285.593 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:285.596 - 0.003ms returns 0
T7BD8 002:285.601 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:285.604 - 0.003ms returns 0
T7BD8 002:285.608 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:285.611 - 0.003ms returns 0
T7BD8 002:285.616 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:285.619 - 0.003ms returns 0
T7BD8 002:285.623 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:285.626 - 0.003ms returns 0
T7BD8 002:285.630 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:285.634 - 0.003ms returns 0
T7BD8 002:285.638 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:285.641 - 0.003ms returns 0
T7BD8 002:285.646 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:285.649 - 0.003ms returns 0
T7BD8 002:285.653 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:285.656 - 0.003ms returns 0
T7BD8 002:285.660 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:285.664 - 0.003ms returns 0
T7BD8 002:285.668 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:285.672 - 0.003ms returns 0
T7BD8 002:285.676 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:285.679 - 0.003ms returns 0
T7BD8 002:285.683 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:285.686 - 0.003ms returns 0
T7BD8 002:285.691 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:285.695 - 0.004ms returns 0x0000000C
T7BD8 002:285.699 JLINK_Go()
T7BD8 002:285.706   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:288.656 - 2.956ms 
T7BD8 002:288.671 JLINK_IsHalted()
T7BD8 002:289.154 - 0.483ms returns FALSE
T7BD8 002:289.160 JLINK_HasError()
T7BD8 002:291.858 JLINK_IsHalted()
T7BD8 002:292.361 - 0.502ms returns FALSE
T7BD8 002:292.367 JLINK_HasError()
T7BD8 002:293.856 JLINK_IsHalted()
T7BD8 002:296.123   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:296.590 - 2.733ms returns TRUE
T7BD8 002:296.596 JLINK_ReadReg(R15 (PC))
T7BD8 002:296.600 - 0.004ms returns 0x20000000
T7BD8 002:296.605 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T7BD8 002:296.609 - 0.003ms returns 0x00
T7BD8 002:296.613 JLINK_ReadReg(R0)
T7BD8 002:296.617 - 0.004ms returns 0x00000000
T7BD8 002:296.918 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:296.927   Data:  20 FA 03 F3 19 43 90 40 70 47 20 2A 04 DB 20 3A ...
T7BD8 002:296.937   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:299.667 - 2.748ms returns 0x27C
T7BD8 002:299.682 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:299.686   Data:  00 45 14 46 4F F0 00 0A 23 F0 00 41 50 EA 05 02 ...
T7BD8 002:299.697   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:301.575 - 1.893ms returns 0x184
T7BD8 002:301.619 JLINK_HasError()
T7BD8 002:301.624 JLINK_WriteReg(R0, 0x08000400)
T7BD8 002:301.628 - 0.004ms returns 0
T7BD8 002:301.633 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:301.636 - 0.003ms returns 0
T7BD8 002:301.640 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:301.644 - 0.003ms returns 0
T7BD8 002:301.648 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:301.652 - 0.003ms returns 0
T7BD8 002:301.657 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:301.661 - 0.003ms returns 0
T7BD8 002:301.665 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:301.668 - 0.003ms returns 0
T7BD8 002:301.672 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:301.676 - 0.003ms returns 0
T7BD8 002:301.680 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:301.684 - 0.003ms returns 0
T7BD8 002:301.688 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:301.692 - 0.003ms returns 0
T7BD8 002:301.696 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:301.699 - 0.003ms returns 0
T7BD8 002:301.703 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:301.706 - 0.003ms returns 0
T7BD8 002:301.710 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:301.714 - 0.003ms returns 0
T7BD8 002:301.718 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:301.721 - 0.003ms returns 0
T7BD8 002:301.725 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:301.729 - 0.003ms returns 0
T7BD8 002:301.733 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:301.736 - 0.003ms returns 0
T7BD8 002:301.741 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:301.744 - 0.003ms returns 0
T7BD8 002:301.749 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:301.752 - 0.004ms returns 0
T7BD8 002:301.756 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:301.760 - 0.003ms returns 0
T7BD8 002:301.764 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:301.767 - 0.003ms returns 0
T7BD8 002:301.771 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:301.774 - 0.003ms returns 0
T7BD8 002:301.779 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:301.784 - 0.004ms returns 0x0000000D
T7BD8 002:301.788 JLINK_Go()
T7BD8 002:301.795   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:304.503 - 2.715ms 
T7BD8 002:304.509 JLINK_IsHalted()
T7BD8 002:304.969 - 0.454ms returns FALSE
T7BD8 002:304.974 JLINK_HasError()
T7BD8 002:306.858 JLINK_IsHalted()
T7BD8 002:307.413 - 0.554ms returns FALSE
T7BD8 002:307.429 JLINK_HasError()
T7BD8 002:309.867 JLINK_IsHalted()
T7BD8 002:312.344   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:312.918 - 3.050ms returns TRUE
T7BD8 002:312.934 JLINK_ReadReg(R15 (PC))
T7BD8 002:312.940 - 0.007ms returns 0x20000000
T7BD8 002:312.945 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T7BD8 002:312.948 - 0.004ms returns 0x00
T7BD8 002:312.953 JLINK_ReadReg(R0)
T7BD8 002:312.957 - 0.003ms returns 0x00000000
T7BD8 002:313.400 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:313.409   Data:  00 29 A8 BF 70 47 40 1C 49 00 08 BF 20 F0 01 00 ...
T7BD8 002:313.421   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:316.048 - 2.647ms returns 0x27C
T7BD8 002:316.086 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:316.095   Data:  00 00 00 00 2D E9 F0 4F 81 B0 41 F6 00 49 C4 F2 ...
T7BD8 002:316.115   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:318.039 - 1.950ms returns 0x184
T7BD8 002:318.077 JLINK_HasError()
T7BD8 002:318.089 JLINK_WriteReg(R0, 0x08000800)
T7BD8 002:318.100 - 0.011ms returns 0
T7BD8 002:318.109 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:318.116 - 0.007ms returns 0
T7BD8 002:318.124 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:318.131 - 0.006ms returns 0
T7BD8 002:318.139 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:318.146 - 0.007ms returns 0
T7BD8 002:318.154 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:318.161 - 0.006ms returns 0
T7BD8 002:318.168 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:318.175 - 0.006ms returns 0
T7BD8 002:318.184 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:318.190 - 0.007ms returns 0
T7BD8 002:318.199 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:318.206 - 0.007ms returns 0
T7BD8 002:318.214 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:318.221 - 0.007ms returns 0
T7BD8 002:318.229 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:318.295 - 0.065ms returns 0
T7BD8 002:318.309 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:318.316 - 0.007ms returns 0
T7BD8 002:318.324 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:318.330 - 0.006ms returns 0
T7BD8 002:318.338 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:318.345 - 0.006ms returns 0
T7BD8 002:318.353 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:318.360 - 0.007ms returns 0
T7BD8 002:318.368 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:318.375 - 0.006ms returns 0
T7BD8 002:318.383 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:318.390 - 0.006ms returns 0
T7BD8 002:318.398 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:318.405 - 0.007ms returns 0
T7BD8 002:318.413 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:318.419 - 0.006ms returns 0
T7BD8 002:318.427 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:318.434 - 0.006ms returns 0
T7BD8 002:318.442 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:318.448 - 0.006ms returns 0
T7BD8 002:318.457 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:318.466 - 0.008ms returns 0x0000000E
T7BD8 002:318.474 JLINK_Go()
T7BD8 002:318.491   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:321.358 - 2.883ms 
T7BD8 002:321.390 JLINK_IsHalted()
T7BD8 002:321.909 - 0.518ms returns FALSE
T7BD8 002:321.922 JLINK_HasError()
T7BD8 002:323.870 JLINK_IsHalted()
T7BD8 002:324.418 - 0.548ms returns FALSE
T7BD8 002:324.438 JLINK_HasError()
T7BD8 002:325.869 JLINK_IsHalted()
T7BD8 002:328.362   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:328.941 - 3.072ms returns TRUE
T7BD8 002:328.962 JLINK_ReadReg(R15 (PC))
T7BD8 002:328.973 - 0.011ms returns 0x20000000
T7BD8 002:328.982 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T7BD8 002:328.989 - 0.007ms returns 0x00
T7BD8 002:328.997 JLINK_ReadReg(R0)
T7BD8 002:329.004 - 0.007ms returns 0x00000000
T7BD8 002:329.655 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:329.676   Data:  02 04 04 F5 A0 56 05 46 C2 09 30 46 4F F4 80 51 ...
T7BD8 002:329.691   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:332.340 - 2.683ms returns 0x27C
T7BD8 002:332.384 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:332.392   Data:  FF F7 DA FF 94 ED 02 0A 9F ED 31 8A 9F ED 31 9A ...
T7BD8 002:332.413   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:334.338 - 1.954ms returns 0x184
T7BD8 002:334.407 JLINK_HasError()
T7BD8 002:334.424 JLINK_WriteReg(R0, 0x08000C00)
T7BD8 002:334.430 - 0.006ms returns 0
T7BD8 002:334.434 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:334.438 - 0.003ms returns 0
T7BD8 002:334.442 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:334.445 - 0.003ms returns 0
T7BD8 002:334.449 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:334.452 - 0.003ms returns 0
T7BD8 002:334.456 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:334.460 - 0.003ms returns 0
T7BD8 002:334.464 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:334.467 - 0.003ms returns 0
T7BD8 002:334.471 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:334.475 - 0.003ms returns 0
T7BD8 002:334.479 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:334.482 - 0.003ms returns 0
T7BD8 002:334.486 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:334.490 - 0.003ms returns 0
T7BD8 002:334.494 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:334.498 - 0.004ms returns 0
T7BD8 002:334.502 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:334.505 - 0.003ms returns 0
T7BD8 002:334.509 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:334.513 - 0.004ms returns 0
T7BD8 002:334.517 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:334.521 - 0.003ms returns 0
T7BD8 002:334.525 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:334.529 - 0.004ms returns 0
T7BD8 002:334.533 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:334.536 - 0.003ms returns 0
T7BD8 002:334.540 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:334.544 - 0.003ms returns 0
T7BD8 002:334.548 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:334.551 - 0.003ms returns 0
T7BD8 002:334.555 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:334.559 - 0.003ms returns 0
T7BD8 002:334.563 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:334.566 - 0.003ms returns 0
T7BD8 002:334.570 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:334.633 - 0.063ms returns 0
T7BD8 002:334.638 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:334.643 - 0.005ms returns 0x0000000F
T7BD8 002:334.648 JLINK_Go()
T7BD8 002:334.658   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:337.499 - 2.850ms 
T7BD8 002:337.517 JLINK_IsHalted()
T7BD8 002:338.032 - 0.515ms returns FALSE
T7BD8 002:338.051 JLINK_HasError()
T7BD8 002:339.865 JLINK_IsHalted()
T7BD8 002:340.443 - 0.576ms returns FALSE
T7BD8 002:340.472 JLINK_HasError()
T7BD8 002:341.870 JLINK_IsHalted()
T7BD8 002:344.278   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:344.800 - 2.929ms returns TRUE
T7BD8 002:344.815 JLINK_ReadReg(R15 (PC))
T7BD8 002:344.825 - 0.010ms returns 0x20000000
T7BD8 002:344.834 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T7BD8 002:344.841 - 0.007ms returns 0x00
T7BD8 002:344.849 JLINK_ReadReg(R0)
T7BD8 002:344.856 - 0.007ms returns 0x00000000
T7BD8 002:345.669 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:345.691   Data:  FE E7 00 00 B3 F5 80 4F 03 46 18 D1 B0 B5 53 F8 ...
T7BD8 002:345.708   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:348.324 - 2.654ms returns 0x27C
T7BD8 002:348.343 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:348.348   Data:  00 F0 B0 60 01 20 84 F8 35 00 00 20 00 21 84 F8 ...
T7BD8 002:348.359   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:350.290 - 1.947ms returns 0x184
T7BD8 002:350.305 JLINK_HasError()
T7BD8 002:350.311 JLINK_WriteReg(R0, 0x08001000)
T7BD8 002:350.316 - 0.005ms returns 0
T7BD8 002:350.320 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:350.324 - 0.003ms returns 0
T7BD8 002:350.328 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:350.331 - 0.003ms returns 0
T7BD8 002:350.336 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:350.339 - 0.003ms returns 0
T7BD8 002:350.343 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:350.346 - 0.003ms returns 0
T7BD8 002:350.350 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:350.354 - 0.003ms returns 0
T7BD8 002:350.358 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:350.361 - 0.003ms returns 0
T7BD8 002:350.365 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:350.368 - 0.003ms returns 0
T7BD8 002:350.372 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:350.376 - 0.003ms returns 0
T7BD8 002:350.380 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:350.383 - 0.003ms returns 0
T7BD8 002:350.388 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:350.391 - 0.003ms returns 0
T7BD8 002:350.395 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:350.398 - 0.003ms returns 0
T7BD8 002:350.402 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:350.406 - 0.003ms returns 0
T7BD8 002:350.410 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:350.414 - 0.003ms returns 0
T7BD8 002:350.418 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:350.421 - 0.003ms returns 0
T7BD8 002:350.425 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:350.429 - 0.003ms returns 0
T7BD8 002:350.433 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:350.436 - 0.003ms returns 0
T7BD8 002:350.440 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:350.444 - 0.003ms returns 0
T7BD8 002:350.448 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:350.451 - 0.003ms returns 0
T7BD8 002:350.455 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:350.458 - 0.003ms returns 0
T7BD8 002:350.463 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:350.467 - 0.004ms returns 0x00000010
T7BD8 002:350.471 JLINK_Go()
T7BD8 002:350.480   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:353.193 - 2.721ms 
T7BD8 002:353.199 JLINK_IsHalted()
T7BD8 002:353.756 - 0.557ms returns FALSE
T7BD8 002:353.762 JLINK_HasError()
T7BD8 002:354.857 JLINK_IsHalted()
T7BD8 002:355.362 - 0.505ms returns FALSE
T7BD8 002:355.369 JLINK_HasError()
T7BD8 002:356.862 JLINK_IsHalted()
T7BD8 002:357.370 - 0.507ms returns FALSE
T7BD8 002:357.386 JLINK_HasError()
T7BD8 002:358.868 JLINK_IsHalted()
T7BD8 002:361.271   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:361.767 - 2.899ms returns TRUE
T7BD8 002:361.780 JLINK_ReadReg(R15 (PC))
T7BD8 002:361.786 - 0.006ms returns 0x20000000
T7BD8 002:362.340 JLINK_ClrBPEx(BPHandle = 0x00000010)
T7BD8 002:362.358 - 0.018ms returns 0x00
T7BD8 002:362.364 JLINK_ReadReg(R0)
T7BD8 002:362.368 - 0.004ms returns 0x00000000
T7BD8 002:362.726 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:362.733   Data:  52 F8 0C 30 07 F0 0C 06 0F 25 B5 40 AB 43 01 9D ...
T7BD8 002:362.743   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:365.359 - 2.633ms returns 0x27C
T7BD8 002:365.366 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:365.370   Data:  00 20 02 B0 70 BD 00 00 70 47 00 00 00 28 04 BF ...
T7BD8 002:365.379   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:367.273 - 1.906ms returns 0x184
T7BD8 002:367.286 JLINK_HasError()
T7BD8 002:367.291 JLINK_WriteReg(R0, 0x08001400)
T7BD8 002:367.296 - 0.005ms returns 0
T7BD8 002:367.301 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:367.304 - 0.003ms returns 0
T7BD8 002:367.308 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:367.312 - 0.003ms returns 0
T7BD8 002:367.316 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:367.319 - 0.003ms returns 0
T7BD8 002:367.323 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:367.327 - 0.003ms returns 0
T7BD8 002:367.331 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:367.334 - 0.003ms returns 0
T7BD8 002:367.338 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:367.342 - 0.003ms returns 0
T7BD8 002:367.346 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:367.349 - 0.003ms returns 0
T7BD8 002:367.353 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:367.356 - 0.003ms returns 0
T7BD8 002:367.360 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:367.364 - 0.003ms returns 0
T7BD8 002:367.368 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:367.372 - 0.004ms returns 0
T7BD8 002:367.376 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:367.379 - 0.003ms returns 0
T7BD8 002:367.383 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:367.387 - 0.003ms returns 0
T7BD8 002:367.391 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:367.395 - 0.004ms returns 0
T7BD8 002:367.399 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:367.402 - 0.003ms returns 0
T7BD8 002:367.406 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:367.410 - 0.003ms returns 0
T7BD8 002:367.414 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:367.417 - 0.003ms returns 0
T7BD8 002:367.421 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:367.424 - 0.003ms returns 0
T7BD8 002:367.428 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:367.432 - 0.003ms returns 0
T7BD8 002:367.436 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:367.439 - 0.003ms returns 0
T7BD8 002:367.444 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:367.448 - 0.004ms returns 0x00000011
T7BD8 002:367.452 JLINK_Go()
T7BD8 002:367.460   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:370.202 - 2.749ms 
T7BD8 002:370.216 JLINK_IsHalted()
T7BD8 002:370.756 - 0.539ms returns FALSE
T7BD8 002:370.762 JLINK_HasError()
T7BD8 002:371.857 JLINK_IsHalted()
T7BD8 002:372.316 - 0.459ms returns FALSE
T7BD8 002:372.321 JLINK_HasError()
T7BD8 002:373.858 JLINK_IsHalted()
T7BD8 002:374.317 - 0.459ms returns FALSE
T7BD8 002:374.322 JLINK_HasError()
T7BD8 002:375.858 JLINK_IsHalted()
T7BD8 002:378.197   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:378.693 - 2.834ms returns TRUE
T7BD8 002:378.707 JLINK_ReadReg(R15 (PC))
T7BD8 002:378.712 - 0.005ms returns 0x20000000
T7BD8 002:378.717 JLINK_ClrBPEx(BPHandle = 0x00000011)
T7BD8 002:378.721 - 0.004ms returns 0x00
T7BD8 002:378.725 JLINK_ReadReg(R0)
T7BD8 002:378.729 - 0.003ms returns 0x00000000
T7BD8 002:379.067 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:379.075   Data:  C4 F2 02 01 00 68 09 68 45 F2 88 32 C1 F3 82 21 ...
T7BD8 002:379.085   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:381.660 - 2.592ms returns 0x27C
T7BD8 002:381.668 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:381.671   Data:  38 68 C0 05 26 D4 38 68 40 F4 80 70 38 60 FF F7 ...
T7BD8 002:381.686   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:383.528 - 1.860ms returns 0x184
T7BD8 002:383.535 JLINK_HasError()
T7BD8 002:383.540 JLINK_WriteReg(R0, 0x08001800)
T7BD8 002:383.544 - 0.004ms returns 0
T7BD8 002:383.552 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:383.558 - 0.005ms returns 0
T7BD8 002:383.562 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:383.565 - 0.003ms returns 0
T7BD8 002:383.569 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:383.572 - 0.003ms returns 0
T7BD8 002:383.576 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:383.580 - 0.003ms returns 0
T7BD8 002:383.584 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:383.587 - 0.003ms returns 0
T7BD8 002:383.591 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:383.594 - 0.003ms returns 0
T7BD8 002:383.598 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:383.602 - 0.003ms returns 0
T7BD8 002:383.606 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:383.609 - 0.003ms returns 0
T7BD8 002:383.613 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:383.617 - 0.003ms returns 0
T7BD8 002:383.621 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:383.624 - 0.003ms returns 0
T7BD8 002:383.628 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:383.632 - 0.003ms returns 0
T7BD8 002:383.636 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:383.639 - 0.003ms returns 0
T7BD8 002:383.643 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:383.647 - 0.003ms returns 0
T7BD8 002:383.651 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:383.654 - 0.003ms returns 0
T7BD8 002:383.659 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:383.662 - 0.003ms returns 0
T7BD8 002:383.666 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:383.670 - 0.003ms returns 0
T7BD8 002:383.674 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:383.677 - 0.003ms returns 0
T7BD8 002:383.681 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:383.684 - 0.003ms returns 0
T7BD8 002:383.688 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:383.692 - 0.003ms returns 0
T7BD8 002:383.696 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:383.700 - 0.004ms returns 0x00000012
T7BD8 002:383.704 JLINK_Go()
T7BD8 002:383.712   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:386.513 - 2.808ms 
T7BD8 002:386.521 JLINK_IsHalted()
T7BD8 002:387.073 - 0.551ms returns FALSE
T7BD8 002:387.088 JLINK_HasError()
T7BD8 002:389.366 JLINK_IsHalted()
T7BD8 002:389.929 - 0.562ms returns FALSE
T7BD8 002:389.936 JLINK_HasError()
T7BD8 002:391.363 JLINK_IsHalted()
T7BD8 002:393.651   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:394.107 - 2.743ms returns TRUE
T7BD8 002:394.112 JLINK_ReadReg(R15 (PC))
T7BD8 002:394.117 - 0.004ms returns 0x20000000
T7BD8 002:394.121 JLINK_ClrBPEx(BPHandle = 0x00000012)
T7BD8 002:394.125 - 0.003ms returns 0x00
T7BD8 002:394.130 JLINK_ReadReg(R0)
T7BD8 002:394.133 - 0.003ms returns 0x00000000
T7BD8 002:394.478 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:394.486   Data:  05 60 08 43 70 60 01 20 38 66 FF F7 3B FC 04 46 ...
T7BD8 002:394.496   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:397.095 - 2.617ms returns 0x27C
T7BD8 002:397.105 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:397.109   Data:  21 F4 80 71 42 E8 03 13 00 2B F5 D1 01 68 51 E8 ...
T7BD8 002:397.117   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:399.108 - 2.002ms returns 0x184
T7BD8 002:399.124 JLINK_HasError()
T7BD8 002:399.130 JLINK_WriteReg(R0, 0x08001C00)
T7BD8 002:399.135 - 0.005ms returns 0
T7BD8 002:399.139 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:399.143 - 0.003ms returns 0
T7BD8 002:399.147 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:399.150 - 0.003ms returns 0
T7BD8 002:399.154 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:399.158 - 0.003ms returns 0
T7BD8 002:399.162 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:399.165 - 0.003ms returns 0
T7BD8 002:399.169 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:399.173 - 0.003ms returns 0
T7BD8 002:399.177 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:399.180 - 0.003ms returns 0
T7BD8 002:399.184 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:399.188 - 0.003ms returns 0
T7BD8 002:399.193 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:399.197 - 0.004ms returns 0
T7BD8 002:399.201 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:399.205 - 0.003ms returns 0
T7BD8 002:399.209 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:399.212 - 0.003ms returns 0
T7BD8 002:399.220 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:399.225 - 0.005ms returns 0
T7BD8 002:399.230 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:399.233 - 0.003ms returns 0
T7BD8 002:399.237 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:399.241 - 0.003ms returns 0
T7BD8 002:399.245 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:399.248 - 0.003ms returns 0
T7BD8 002:399.252 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:399.256 - 0.003ms returns 0
T7BD8 002:399.260 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:399.264 - 0.003ms returns 0
T7BD8 002:399.268 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:399.271 - 0.003ms returns 0
T7BD8 002:399.275 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:399.279 - 0.003ms returns 0
T7BD8 002:399.283 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:399.286 - 0.003ms returns 0
T7BD8 002:399.291 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:399.295 - 0.004ms returns 0x00000013
T7BD8 002:399.299 JLINK_Go()
T7BD8 002:399.307   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:402.052 - 2.752ms 
T7BD8 002:402.063 JLINK_IsHalted()
T7BD8 002:402.526 - 0.462ms returns FALSE
T7BD8 002:402.532 JLINK_HasError()
T7BD8 002:404.366 JLINK_IsHalted()
T7BD8 002:404.891 - 0.524ms returns FALSE
T7BD8 002:404.898 JLINK_HasError()
T7BD8 002:406.363 JLINK_IsHalted()
T7BD8 002:408.880   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:409.363 - 2.999ms returns TRUE
T7BD8 002:409.370 JLINK_ReadReg(R15 (PC))
T7BD8 002:409.375 - 0.005ms returns 0x20000000
T7BD8 002:409.380 JLINK_ClrBPEx(BPHandle = 0x00000013)
T7BD8 002:409.384 - 0.003ms returns 0x00
T7BD8 002:409.388 JLINK_ReadReg(R0)
T7BD8 002:409.392 - 0.003ms returns 0x00000000
T7BD8 002:409.735 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:409.743   Data:  43 E8 03 25 00 2D F5 D1 02 68 52 E8 05 2F 03 68 ...
T7BD8 002:409.753   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:412.371 - 2.635ms returns 0x27C
T7BD8 002:412.390 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:412.394   Data:  00 21 0C F8 0E 10 00 E0 00 21 C1 80 04 E0 00 BF ...
T7BD8 002:412.404   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:414.282 - 1.891ms returns 0x184
T7BD8 002:414.293 JLINK_HasError()
T7BD8 002:414.322 JLINK_WriteReg(R0, 0x08002000)
T7BD8 002:414.328 - 0.005ms returns 0
T7BD8 002:414.332 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:414.336 - 0.003ms returns 0
T7BD8 002:414.340 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:414.343 - 0.003ms returns 0
T7BD8 002:414.347 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:414.351 - 0.003ms returns 0
T7BD8 002:414.355 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:414.358 - 0.003ms returns 0
T7BD8 002:414.362 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:414.366 - 0.003ms returns 0
T7BD8 002:414.370 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:414.373 - 0.003ms returns 0
T7BD8 002:414.377 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:414.381 - 0.003ms returns 0
T7BD8 002:414.385 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:414.388 - 0.003ms returns 0
T7BD8 002:414.392 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:414.396 - 0.003ms returns 0
T7BD8 002:414.400 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:414.403 - 0.003ms returns 0
T7BD8 002:414.407 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:414.410 - 0.003ms returns 0
T7BD8 002:414.414 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:414.418 - 0.003ms returns 0
T7BD8 002:414.422 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:414.426 - 0.004ms returns 0
T7BD8 002:414.430 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:414.434 - 0.003ms returns 0
T7BD8 002:414.438 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:414.442 - 0.003ms returns 0
T7BD8 002:414.446 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:414.449 - 0.003ms returns 0
T7BD8 002:414.453 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:414.457 - 0.003ms returns 0
T7BD8 002:414.461 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:414.464 - 0.003ms returns 0
T7BD8 002:414.468 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:414.472 - 0.003ms returns 0
T7BD8 002:414.476 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:414.480 - 0.004ms returns 0x00000014
T7BD8 002:414.489 JLINK_Go()
T7BD8 002:414.497   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:417.304 - 2.814ms 
T7BD8 002:417.315 JLINK_IsHalted()
T7BD8 002:417.947 - 0.631ms returns FALSE
T7BD8 002:417.960 JLINK_HasError()
T7BD8 002:420.367 JLINK_IsHalted()
T7BD8 002:420.885 - 0.518ms returns FALSE
T7BD8 002:420.892 JLINK_HasError()
T7BD8 002:422.363 JLINK_IsHalted()
T7BD8 002:424.644   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:425.111 - 2.748ms returns TRUE
T7BD8 002:425.118 JLINK_ReadReg(R15 (PC))
T7BD8 002:425.122 - 0.005ms returns 0x20000000
T7BD8 002:425.127 JLINK_ClrBPEx(BPHandle = 0x00000014)
T7BD8 002:425.131 - 0.003ms returns 0x00
T7BD8 002:425.135 JLINK_ReadReg(R0)
T7BD8 002:425.139 - 0.003ms returns 0x00000000
T7BD8 002:425.462 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:425.470   Data:  09 F1 01 05 0A E0 00 BF 97 B1 17 F8 01 0B 21 68 ...
T7BD8 002:425.480   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:428.119 - 2.656ms returns 0x27C
T7BD8 002:428.135 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:428.140   Data:  CD F8 04 90 01 68 41 F0 08 01 01 60 01 68 01 F0 ...
T7BD8 002:428.150   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:430.013 - 1.878ms returns 0x184
T7BD8 002:430.025 JLINK_HasError()
T7BD8 002:430.030 JLINK_WriteReg(R0, 0x08002400)
T7BD8 002:430.036 - 0.005ms returns 0
T7BD8 002:430.040 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:430.043 - 0.003ms returns 0
T7BD8 002:430.047 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:430.051 - 0.003ms returns 0
T7BD8 002:430.055 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:430.059 - 0.003ms returns 0
T7BD8 002:430.063 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:430.066 - 0.003ms returns 0
T7BD8 002:430.070 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:430.073 - 0.003ms returns 0
T7BD8 002:430.077 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:430.081 - 0.003ms returns 0
T7BD8 002:430.085 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:430.088 - 0.003ms returns 0
T7BD8 002:430.092 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:430.096 - 0.003ms returns 0
T7BD8 002:430.100 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:430.103 - 0.003ms returns 0
T7BD8 002:430.107 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:430.111 - 0.003ms returns 0
T7BD8 002:430.115 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:430.118 - 0.003ms returns 0
T7BD8 002:430.122 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:430.125 - 0.003ms returns 0
T7BD8 002:430.129 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:430.133 - 0.003ms returns 0
T7BD8 002:430.137 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:430.140 - 0.003ms returns 0
T7BD8 002:430.145 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:430.148 - 0.003ms returns 0
T7BD8 002:430.152 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:430.156 - 0.003ms returns 0
T7BD8 002:430.160 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:430.163 - 0.003ms returns 0
T7BD8 002:430.167 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:430.170 - 0.003ms returns 0
T7BD8 002:430.175 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:430.178 - 0.003ms returns 0
T7BD8 002:430.183 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:430.187 - 0.004ms returns 0x00000015
T7BD8 002:430.191 JLINK_Go()
T7BD8 002:430.200   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:432.985 - 2.793ms 
T7BD8 002:432.993 JLINK_IsHalted()
T7BD8 002:433.450 - 0.456ms returns FALSE
T7BD8 002:433.456 JLINK_HasError()
T7BD8 002:435.366 JLINK_IsHalted()
T7BD8 002:435.845 - 0.478ms returns FALSE
T7BD8 002:435.850 JLINK_HasError()
T7BD8 002:437.367 JLINK_IsHalted()
T7BD8 002:439.771   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:440.245 - 2.877ms returns TRUE
T7BD8 002:440.257 JLINK_ReadReg(R15 (PC))
T7BD8 002:440.263 - 0.005ms returns 0x20000000
T7BD8 002:440.295 JLINK_ClrBPEx(BPHandle = 0x00000015)
T7BD8 002:440.300 - 0.005ms returns 0x00
T7BD8 002:440.305 JLINK_ReadReg(R0)
T7BD8 002:440.309 - 0.003ms returns 0x00000000
T7BD8 002:440.652 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:440.658   Data:  4F F0 0C 0C 4F F4 E1 32 80 E8 0E 00 C0 E9 03 33 ...
T7BD8 002:440.674   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:443.263 - 2.611ms returns 0x27C
T7BD8 002:443.269 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:443.272   Data:  10 80 72 B6 FE E7 00 BF FE F7 B0 FE 12 B0 10 BD ...
T7BD8 002:443.290   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:445.089 - 1.820ms returns 0x184
T7BD8 002:445.095 JLINK_HasError()
T7BD8 002:445.100 JLINK_WriteReg(R0, 0x08002800)
T7BD8 002:445.104 - 0.004ms returns 0
T7BD8 002:445.108 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:445.111 - 0.003ms returns 0
T7BD8 002:445.115 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:445.119 - 0.003ms returns 0
T7BD8 002:445.123 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:445.126 - 0.003ms returns 0
T7BD8 002:445.130 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:445.134 - 0.003ms returns 0
T7BD8 002:445.138 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:445.142 - 0.003ms returns 0
T7BD8 002:445.146 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:445.149 - 0.003ms returns 0
T7BD8 002:445.153 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:445.156 - 0.003ms returns 0
T7BD8 002:445.160 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:445.164 - 0.003ms returns 0
T7BD8 002:445.168 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:445.172 - 0.003ms returns 0
T7BD8 002:445.176 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:445.179 - 0.003ms returns 0
T7BD8 002:445.183 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:445.186 - 0.003ms returns 0
T7BD8 002:445.190 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:445.194 - 0.003ms returns 0
T7BD8 002:445.198 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:445.202 - 0.003ms returns 0
T7BD8 002:445.206 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:445.209 - 0.003ms returns 0
T7BD8 002:445.213 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:445.217 - 0.003ms returns 0
T7BD8 002:445.221 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:445.224 - 0.003ms returns 0
T7BD8 002:445.228 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:445.232 - 0.003ms returns 0
T7BD8 002:445.236 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:445.239 - 0.003ms returns 0
T7BD8 002:445.243 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:445.246 - 0.003ms returns 0
T7BD8 002:445.251 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:445.255 - 0.004ms returns 0x00000016
T7BD8 002:445.259 JLINK_Go()
T7BD8 002:445.266   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:447.984 - 2.724ms 
T7BD8 002:448.000 JLINK_IsHalted()
T7BD8 002:448.554 - 0.553ms returns FALSE
T7BD8 002:448.568 JLINK_HasError()
T7BD8 002:450.366 JLINK_IsHalted()
T7BD8 002:450.885 - 0.518ms returns FALSE
T7BD8 002:450.892 JLINK_HasError()
T7BD8 002:452.364 JLINK_IsHalted()
T7BD8 002:454.761   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:455.232 - 2.868ms returns TRUE
T7BD8 002:455.238 JLINK_ReadReg(R15 (PC))
T7BD8 002:455.243 - 0.004ms returns 0x20000000
T7BD8 002:455.247 JLINK_ClrBPEx(BPHandle = 0x00000016)
T7BD8 002:455.252 - 0.004ms returns 0x00
T7BD8 002:455.256 JLINK_ReadReg(R0)
T7BD8 002:455.260 - 0.003ms returns 0x00000000
T7BD8 002:455.616 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:455.622   Data:  11 10 00 EB 02 10 C1 F3 42 11 18 E0 9A 00 9B 0F ...
T7BD8 002:455.633   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:458.247 - 2.631ms returns 0x27C
T7BD8 002:458.262 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:458.266   Data:  20 EE 00 0A 21 EE 01 1A 30 EE 01 8A B0 EE 48 0A ...
T7BD8 002:458.276   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:460.140 - 1.877ms returns 0x184
T7BD8 002:460.152 JLINK_HasError()
T7BD8 002:460.158 JLINK_WriteReg(R0, 0x08002C00)
T7BD8 002:460.163 - 0.005ms returns 0
T7BD8 002:460.167 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:460.170 - 0.003ms returns 0
T7BD8 002:460.174 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:460.178 - 0.003ms returns 0
T7BD8 002:460.182 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:460.185 - 0.003ms returns 0
T7BD8 002:460.190 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:460.193 - 0.003ms returns 0
T7BD8 002:460.197 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:460.204 - 0.007ms returns 0
T7BD8 002:460.210 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:460.213 - 0.003ms returns 0
T7BD8 002:460.217 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:460.221 - 0.003ms returns 0
T7BD8 002:460.225 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:460.228 - 0.003ms returns 0
T7BD8 002:460.232 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:460.236 - 0.003ms returns 0
T7BD8 002:460.240 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:460.243 - 0.003ms returns 0
T7BD8 002:460.247 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:460.250 - 0.003ms returns 0
T7BD8 002:460.255 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:460.258 - 0.003ms returns 0
T7BD8 002:460.262 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:460.266 - 0.003ms returns 0
T7BD8 002:460.270 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:460.274 - 0.003ms returns 0
T7BD8 002:460.278 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:460.281 - 0.003ms returns 0
T7BD8 002:460.285 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:460.288 - 0.003ms returns 0
T7BD8 002:460.292 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:460.296 - 0.003ms returns 0
T7BD8 002:460.300 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:460.304 - 0.003ms returns 0
T7BD8 002:460.308 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:460.311 - 0.003ms returns 0
T7BD8 002:460.315 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:460.320 - 0.004ms returns 0x00000017
T7BD8 002:460.324 JLINK_Go()
T7BD8 002:460.332   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:462.985 - 2.661ms 
T7BD8 002:462.993 JLINK_IsHalted()
T7BD8 002:463.481 - 0.487ms returns FALSE
T7BD8 002:463.486 JLINK_HasError()
T7BD8 002:465.365 JLINK_IsHalted()
T7BD8 002:465.840 - 0.474ms returns FALSE
T7BD8 002:465.849 JLINK_HasError()
T7BD8 002:467.367 JLINK_IsHalted()
T7BD8 002:469.828   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:470.318 - 2.950ms returns TRUE
T7BD8 002:470.325 JLINK_ReadReg(R15 (PC))
T7BD8 002:470.330 - 0.005ms returns 0x20000000
T7BD8 002:470.335 JLINK_ClrBPEx(BPHandle = 0x00000017)
T7BD8 002:470.338 - 0.004ms returns 0x00
T7BD8 002:470.343 JLINK_ReadReg(R0)
T7BD8 002:470.347 - 0.004ms returns 0x00000000
T7BD8 002:470.680 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:470.686   Data:  49 07 06 96 4F EA C9 06 FF 00 CD E9 04 62 16 1D ...
T7BD8 002:470.696   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:473.306 - 2.627ms returns 0x27C
T7BD8 002:473.314 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:473.318   Data:  4A ED 01 3A 8A ED 00 7A 8B ED 00 6A CB ED 01 1A ...
T7BD8 002:473.325   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:475.098 - 1.783ms returns 0x184
T7BD8 002:475.103 JLINK_HasError()
T7BD8 002:475.109 JLINK_WriteReg(R0, 0x08003000)
T7BD8 002:475.113 - 0.004ms returns 0
T7BD8 002:475.117 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:475.121 - 0.003ms returns 0
T7BD8 002:475.125 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:475.128 - 0.003ms returns 0
T7BD8 002:475.132 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:475.136 - 0.003ms returns 0
T7BD8 002:475.140 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:475.143 - 0.003ms returns 0
T7BD8 002:475.147 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:475.151 - 0.003ms returns 0
T7BD8 002:475.154 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:475.158 - 0.003ms returns 0
T7BD8 002:475.162 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:475.166 - 0.003ms returns 0
T7BD8 002:475.170 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:475.173 - 0.003ms returns 0
T7BD8 002:475.177 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:475.180 - 0.003ms returns 0
T7BD8 002:475.185 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:475.188 - 0.003ms returns 0
T7BD8 002:475.192 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:475.195 - 0.003ms returns 0
T7BD8 002:475.199 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:475.203 - 0.003ms returns 0
T7BD8 002:475.207 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:475.211 - 0.004ms returns 0
T7BD8 002:475.215 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:475.218 - 0.003ms returns 0
T7BD8 002:475.223 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:475.229 - 0.006ms returns 0
T7BD8 002:475.235 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:475.238 - 0.003ms returns 0
T7BD8 002:475.242 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:475.246 - 0.003ms returns 0
T7BD8 002:475.250 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:475.253 - 0.003ms returns 0
T7BD8 002:475.258 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:475.261 - 0.003ms returns 0
T7BD8 002:475.265 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:475.269 - 0.004ms returns 0x00000018
T7BD8 002:475.273 JLINK_Go()
T7BD8 002:475.281   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:478.244 - 2.970ms 
T7BD8 002:478.260 JLINK_IsHalted()
T7BD8 002:478.762 - 0.501ms returns FALSE
T7BD8 002:478.775 JLINK_HasError()
T7BD8 002:480.368 JLINK_IsHalted()
T7BD8 002:480.900 - 0.532ms returns FALSE
T7BD8 002:480.906 JLINK_HasError()
T7BD8 002:482.365 JLINK_IsHalted()
T7BD8 002:484.650   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:485.112 - 2.746ms returns TRUE
T7BD8 002:485.118 JLINK_ReadReg(R15 (PC))
T7BD8 002:485.122 - 0.004ms returns 0x20000000
T7BD8 002:485.127 JLINK_ClrBPEx(BPHandle = 0x00000018)
T7BD8 002:485.131 - 0.003ms returns 0x00
T7BD8 002:485.135 JLINK_ReadReg(R0)
T7BD8 002:485.139 - 0.003ms returns 0x00000000
T7BD8 002:485.495 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:485.503   Data:  73 EE E4 1A 31 EE 04 1A 32 EE 45 2A 14 ED 01 4A ...
T7BD8 002:485.513   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:488.105 - 2.610ms returns 0x27C
T7BD8 002:488.120 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:488.124   Data:  20 EE 80 4A 26 EE 00 6A 25 EE 00 5A 21 EE 80 7A ...
T7BD8 002:488.134   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:490.108 - 1.987ms returns 0x184
T7BD8 002:490.122 JLINK_HasError()
T7BD8 002:490.127 JLINK_WriteReg(R0, 0x08003400)
T7BD8 002:490.133 - 0.005ms returns 0
T7BD8 002:490.137 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:490.140 - 0.003ms returns 0
T7BD8 002:490.145 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:490.148 - 0.003ms returns 0
T7BD8 002:490.152 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:490.156 - 0.003ms returns 0
T7BD8 002:490.160 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:490.163 - 0.003ms returns 0
T7BD8 002:490.167 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:490.170 - 0.003ms returns 0
T7BD8 002:490.174 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:490.178 - 0.003ms returns 0
T7BD8 002:490.182 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:490.185 - 0.003ms returns 0
T7BD8 002:490.190 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:490.193 - 0.004ms returns 0
T7BD8 002:490.197 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:490.201 - 0.003ms returns 0
T7BD8 002:490.205 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:490.208 - 0.003ms returns 0
T7BD8 002:490.212 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:490.216 - 0.003ms returns 0
T7BD8 002:490.220 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:490.223 - 0.003ms returns 0
T7BD8 002:490.227 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:490.231 - 0.003ms returns 0
T7BD8 002:490.235 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:490.238 - 0.003ms returns 0
T7BD8 002:490.242 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:490.246 - 0.003ms returns 0
T7BD8 002:490.250 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:490.253 - 0.003ms returns 0
T7BD8 002:490.257 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:490.261 - 0.003ms returns 0
T7BD8 002:490.265 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:490.268 - 0.003ms returns 0
T7BD8 002:490.272 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:490.276 - 0.003ms returns 0
T7BD8 002:490.281 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:490.285 - 0.004ms returns 0x00000019
T7BD8 002:490.290 JLINK_Go()
T7BD8 002:490.297   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:493.039 - 2.748ms 
T7BD8 002:493.059 JLINK_IsHalted()
T7BD8 002:493.575 - 0.515ms returns FALSE
T7BD8 002:493.590 JLINK_HasError()
T7BD8 002:494.870 JLINK_IsHalted()
T7BD8 002:495.361 - 0.491ms returns FALSE
T7BD8 002:495.367 JLINK_HasError()
T7BD8 002:496.870 JLINK_IsHalted()
T7BD8 002:499.575   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:500.068 - 3.198ms returns TRUE
T7BD8 002:500.075 JLINK_ReadReg(R15 (PC))
T7BD8 002:500.079 - 0.004ms returns 0x20000000
T7BD8 002:500.084 JLINK_ClrBPEx(BPHandle = 0x00000019)
T7BD8 002:500.088 - 0.003ms returns 0x00
T7BD8 002:500.092 JLINK_ReadReg(R0)
T7BD8 002:500.096 - 0.003ms returns 0x00000000
T7BD8 002:500.442 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:500.450   Data:  03 1A 92 ED 04 4A 21 EE 00 1A 31 EE 01 1A 82 ED ...
T7BD8 002:500.460   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:503.095 - 2.652ms returns 0x27C
T7BD8 002:503.101 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:503.105   Data:  03 2A 95 ED 00 3A 32 EE 01 1A 23 EE 03 2A 13 ED ...
T7BD8 002:503.112   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:505.098 - 1.996ms returns 0x184
T7BD8 002:505.104 JLINK_HasError()
T7BD8 002:505.109 JLINK_WriteReg(R0, 0x08003800)
T7BD8 002:505.113 - 0.004ms returns 0
T7BD8 002:505.117 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:505.121 - 0.003ms returns 0
T7BD8 002:505.125 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:505.129 - 0.003ms returns 0
T7BD8 002:505.133 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:505.136 - 0.003ms returns 0
T7BD8 002:505.140 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:505.144 - 0.003ms returns 0
T7BD8 002:505.148 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:505.151 - 0.003ms returns 0
T7BD8 002:505.155 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:505.158 - 0.003ms returns 0
T7BD8 002:505.162 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:505.166 - 0.003ms returns 0
T7BD8 002:505.170 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:505.173 - 0.003ms returns 0
T7BD8 002:505.177 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:505.181 - 0.003ms returns 0
T7BD8 002:505.185 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:505.188 - 0.003ms returns 0
T7BD8 002:505.192 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:505.196 - 0.003ms returns 0
T7BD8 002:505.200 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:505.203 - 0.003ms returns 0
T7BD8 002:505.207 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:505.211 - 0.003ms returns 0
T7BD8 002:505.215 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:505.219 - 0.003ms returns 0
T7BD8 002:505.223 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:505.226 - 0.003ms returns 0
T7BD8 002:505.230 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:505.234 - 0.003ms returns 0
T7BD8 002:505.238 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:505.241 - 0.003ms returns 0
T7BD8 002:505.245 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:505.249 - 0.003ms returns 0
T7BD8 002:505.253 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:505.256 - 0.003ms returns 0
T7BD8 002:505.261 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:505.264 - 0.004ms returns 0x0000001A
T7BD8 002:505.269 JLINK_Go()
T7BD8 002:505.276   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:508.430 - 3.160ms 
T7BD8 002:508.447 JLINK_IsHalted()
T7BD8 002:509.071 - 0.622ms returns FALSE
T7BD8 002:509.084 JLINK_HasError()
T7BD8 002:510.870 JLINK_IsHalted()
T7BD8 002:511.339 - 0.469ms returns FALSE
T7BD8 002:511.345 JLINK_HasError()
T7BD8 002:512.869 JLINK_IsHalted()
T7BD8 002:515.170   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:515.634 - 2.764ms returns TRUE
T7BD8 002:515.641 JLINK_ReadReg(R15 (PC))
T7BD8 002:515.646 - 0.005ms returns 0x20000000
T7BD8 002:515.651 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T7BD8 002:515.654 - 0.003ms returns 0x00
T7BD8 002:515.659 JLINK_ReadReg(R0)
T7BD8 002:515.662 - 0.003ms returns 0x00000000
T7BD8 002:516.017 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:516.025   Data:  92 ED 01 2A 9F ED 51 3A 22 EE 03 3A 23 EE 02 2A ...
T7BD8 002:516.035   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:518.697 - 2.679ms returns 0x27C
T7BD8 002:518.720 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:518.724   Data:  20 71 00 20 FD F7 6C F8 FC F7 56 FF 20 79 4B A1 ...
T7BD8 002:518.734   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:520.642 - 1.921ms returns 0x184
T7BD8 002:520.664 JLINK_HasError()
T7BD8 002:520.696 JLINK_WriteReg(R0, 0x08003C00)
T7BD8 002:520.702 - 0.006ms returns 0
T7BD8 002:520.706 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:520.710 - 0.003ms returns 0
T7BD8 002:520.714 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:520.717 - 0.003ms returns 0
T7BD8 002:520.721 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:520.725 - 0.003ms returns 0
T7BD8 002:520.729 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:520.733 - 0.004ms returns 0
T7BD8 002:520.737 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:520.740 - 0.003ms returns 0
T7BD8 002:520.744 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:520.748 - 0.003ms returns 0
T7BD8 002:520.752 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:520.755 - 0.003ms returns 0
T7BD8 002:520.759 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:520.762 - 0.003ms returns 0
T7BD8 002:520.766 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:520.770 - 0.003ms returns 0
T7BD8 002:520.774 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:520.778 - 0.003ms returns 0
T7BD8 002:520.782 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:520.785 - 0.003ms returns 0
T7BD8 002:520.789 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:520.792 - 0.003ms returns 0
T7BD8 002:520.796 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:520.800 - 0.003ms returns 0
T7BD8 002:520.804 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:520.808 - 0.003ms returns 0
T7BD8 002:520.812 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:520.815 - 0.003ms returns 0
T7BD8 002:520.819 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:520.823 - 0.003ms returns 0
T7BD8 002:520.827 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:520.830 - 0.003ms returns 0
T7BD8 002:520.834 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:520.838 - 0.003ms returns 0
T7BD8 002:520.842 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:520.845 - 0.003ms returns 0
T7BD8 002:520.850 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:520.854 - 0.004ms returns 0x0000001B
T7BD8 002:520.859 JLINK_Go()
T7BD8 002:520.868   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:523.654 - 2.795ms 
T7BD8 002:523.665 JLINK_IsHalted()
T7BD8 002:524.154 - 0.488ms returns FALSE
T7BD8 002:524.160 JLINK_HasError()
T7BD8 002:525.870 JLINK_IsHalted()
T7BD8 002:526.384 - 0.513ms returns FALSE
T7BD8 002:526.390 JLINK_HasError()
T7BD8 002:527.873 JLINK_IsHalted()
T7BD8 002:530.257   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:530.758 - 2.885ms returns TRUE
T7BD8 002:530.764 JLINK_ReadReg(R15 (PC))
T7BD8 002:530.769 - 0.004ms returns 0x20000000
T7BD8 002:530.774 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T7BD8 002:530.778 - 0.003ms returns 0x00
T7BD8 002:530.783 JLINK_ReadReg(R0)
T7BD8 002:530.786 - 0.003ms returns 0x00000000
T7BD8 002:531.174 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:531.182   Data:  20 41 4D 50 4C 49 54 55 44 45 20 6D 6F 64 65 0D ...
T7BD8 002:531.191   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:533.841 - 2.667ms returns 0x27C
T7BD8 002:533.848 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:533.852   Data:  8E F8 41 EC 10 0B 48 46 51 46 32 46 3B 46 8D ED ...
T7BD8 002:533.859   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:535.762 - 1.913ms returns 0x184
T7BD8 002:535.768 JLINK_HasError()
T7BD8 002:535.772 JLINK_WriteReg(R0, 0x08004000)
T7BD8 002:535.777 - 0.004ms returns 0
T7BD8 002:535.781 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:535.784 - 0.003ms returns 0
T7BD8 002:535.789 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:535.792 - 0.003ms returns 0
T7BD8 002:535.796 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:535.799 - 0.003ms returns 0
T7BD8 002:535.803 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:535.807 - 0.003ms returns 0
T7BD8 002:535.811 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:535.814 - 0.003ms returns 0
T7BD8 002:535.818 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:535.822 - 0.003ms returns 0
T7BD8 002:535.826 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:535.829 - 0.003ms returns 0
T7BD8 002:535.833 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:535.837 - 0.003ms returns 0
T7BD8 002:535.841 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:535.844 - 0.003ms returns 0
T7BD8 002:535.853 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:535.857 - 0.003ms returns 0
T7BD8 002:535.861 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:535.864 - 0.003ms returns 0
T7BD8 002:535.868 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:535.872 - 0.003ms returns 0
T7BD8 002:535.876 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:535.880 - 0.003ms returns 0
T7BD8 002:535.884 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:535.887 - 0.003ms returns 0
T7BD8 002:535.891 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:535.894 - 0.003ms returns 0
T7BD8 002:535.898 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:535.902 - 0.003ms returns 0
T7BD8 002:535.906 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:535.909 - 0.003ms returns 0
T7BD8 002:535.914 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:535.917 - 0.003ms returns 0
T7BD8 002:535.921 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:535.924 - 0.003ms returns 0
T7BD8 002:535.929 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:535.933 - 0.004ms returns 0x0000001C
T7BD8 002:535.937 JLINK_Go()
T7BD8 002:535.944   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:538.764 - 2.826ms 
T7BD8 002:538.779 JLINK_IsHalted()
T7BD8 002:539.289 - 0.509ms returns FALSE
T7BD8 002:539.302 JLINK_HasError()
T7BD8 002:541.873 JLINK_IsHalted()
T7BD8 002:542.387 - 0.513ms returns FALSE
T7BD8 002:542.399 JLINK_HasError()
T7BD8 002:543.869 JLINK_IsHalted()
T7BD8 002:546.166   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:546.634 - 2.764ms returns TRUE
T7BD8 002:546.644 JLINK_ReadReg(R15 (PC))
T7BD8 002:546.649 - 0.005ms returns 0x20000000
T7BD8 002:546.678 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T7BD8 002:546.683 - 0.005ms returns 0x00
T7BD8 002:546.688 JLINK_ReadReg(R0)
T7BD8 002:546.692 - 0.003ms returns 0x00000000
T7BD8 002:547.009 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:547.016   Data:  4A 0A F0 EE 48 0A FF F7 DB FA 9F ED BF DA B0 EE ...
T7BD8 002:547.027   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:550.194 - 3.184ms returns 0x27C
T7BD8 002:550.213 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:550.217   Data:  00 00 00 00 00 00 00 00 00 00 00 00 3D 3D 3D 20 ...
T7BD8 002:550.227   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:552.098 - 1.885ms returns 0x184
T7BD8 002:552.104 JLINK_HasError()
T7BD8 002:552.110 JLINK_WriteReg(R0, 0x08004400)
T7BD8 002:552.114 - 0.004ms returns 0
T7BD8 002:552.118 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:552.122 - 0.003ms returns 0
T7BD8 002:552.126 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:552.129 - 0.003ms returns 0
T7BD8 002:552.134 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:552.137 - 0.003ms returns 0
T7BD8 002:552.141 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:552.144 - 0.003ms returns 0
T7BD8 002:552.148 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:552.152 - 0.003ms returns 0
T7BD8 002:552.156 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:552.159 - 0.003ms returns 0
T7BD8 002:552.163 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:552.167 - 0.003ms returns 0
T7BD8 002:552.171 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:552.174 - 0.003ms returns 0
T7BD8 002:552.178 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:552.182 - 0.003ms returns 0
T7BD8 002:552.186 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:552.189 - 0.003ms returns 0
T7BD8 002:552.193 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:552.197 - 0.003ms returns 0
T7BD8 002:552.201 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:552.204 - 0.003ms returns 0
T7BD8 002:552.208 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:552.212 - 0.003ms returns 0
T7BD8 002:552.216 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:552.219 - 0.003ms returns 0
T7BD8 002:552.223 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:552.227 - 0.003ms returns 0
T7BD8 002:552.231 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:552.234 - 0.003ms returns 0
T7BD8 002:552.238 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:552.241 - 0.003ms returns 0
T7BD8 002:552.245 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:552.249 - 0.003ms returns 0
T7BD8 002:552.253 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:552.260 - 0.007ms returns 0
T7BD8 002:552.266 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:552.270 - 0.004ms returns 0x0000001D
T7BD8 002:552.275 JLINK_Go()
T7BD8 002:552.282   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:555.035 - 2.759ms 
T7BD8 002:555.052 JLINK_IsHalted()
T7BD8 002:555.526 - 0.473ms returns FALSE
T7BD8 002:555.535 JLINK_HasError()
T7BD8 002:556.870 JLINK_IsHalted()
T7BD8 002:557.762 - 0.891ms returns FALSE
T7BD8 002:557.775 JLINK_HasError()
T7BD8 002:558.870 JLINK_IsHalted()
T7BD8 002:559.383 - 0.512ms returns FALSE
T7BD8 002:559.391 JLINK_HasError()
T7BD8 002:560.869 JLINK_IsHalted()
T7BD8 002:563.165   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:563.634 - 2.764ms returns TRUE
T7BD8 002:563.640 JLINK_ReadReg(R15 (PC))
T7BD8 002:563.644 - 0.004ms returns 0x20000000
T7BD8 002:563.649 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T7BD8 002:563.653 - 0.003ms returns 0x00
T7BD8 002:563.657 JLINK_ReadReg(R0)
T7BD8 002:563.661 - 0.003ms returns 0x00000000
T7BD8 002:564.010 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:564.018   Data:  51 EA 02 00 36 D0 30 EE 60 1A 30 EE 20 0A C1 EE ...
T7BD8 002:564.027   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:566.612 - 2.601ms returns 0x27C
T7BD8 002:566.618 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:566.622   Data:  02 0D DD F8 40 B0 03 43 18 D0 44 F6 10 50 A2 F2 ...
T7BD8 002:566.629   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:568.504 - 1.884ms returns 0x184
T7BD8 002:568.521 JLINK_HasError()
T7BD8 002:568.526 JLINK_WriteReg(R0, 0x08004800)
T7BD8 002:568.532 - 0.005ms returns 0
T7BD8 002:568.536 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:568.540 - 0.003ms returns 0
T7BD8 002:568.544 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:568.547 - 0.003ms returns 0
T7BD8 002:568.551 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:568.554 - 0.003ms returns 0
T7BD8 002:568.564 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:568.567 - 0.008ms returns 0
T7BD8 002:568.572 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:568.575 - 0.003ms returns 0
T7BD8 002:568.579 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:568.582 - 0.003ms returns 0
T7BD8 002:568.586 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:568.590 - 0.003ms returns 0
T7BD8 002:568.594 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:568.597 - 0.003ms returns 0
T7BD8 002:568.601 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:568.604 - 0.003ms returns 0
T7BD8 002:568.608 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:568.612 - 0.003ms returns 0
T7BD8 002:568.616 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:568.620 - 0.003ms returns 0
T7BD8 002:568.624 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:568.627 - 0.003ms returns 0
T7BD8 002:568.631 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:568.635 - 0.003ms returns 0
T7BD8 002:568.639 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:568.642 - 0.003ms returns 0
T7BD8 002:568.647 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:568.650 - 0.003ms returns 0
T7BD8 002:568.654 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:568.658 - 0.003ms returns 0
T7BD8 002:568.662 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:568.665 - 0.003ms returns 0
T7BD8 002:568.669 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:568.672 - 0.003ms returns 0
T7BD8 002:568.676 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:568.680 - 0.003ms returns 0
T7BD8 002:568.685 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:568.689 - 0.004ms returns 0x0000001E
T7BD8 002:568.693 JLINK_Go()
T7BD8 002:568.701   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:571.516 - 2.823ms 
T7BD8 002:571.532 JLINK_IsHalted()
T7BD8 002:572.024 - 0.492ms returns FALSE
T7BD8 002:572.033 JLINK_HasError()
T7BD8 002:573.871 JLINK_IsHalted()
T7BD8 002:574.360 - 0.488ms returns FALSE
T7BD8 002:574.365 JLINK_HasError()
T7BD8 002:575.871 JLINK_IsHalted()
T7BD8 002:578.153   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:578.666 - 2.794ms returns TRUE
T7BD8 002:578.677 JLINK_ReadReg(R15 (PC))
T7BD8 002:578.682 - 0.005ms returns 0x20000000
T7BD8 002:578.687 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T7BD8 002:578.695 - 0.008ms returns 0x00
T7BD8 002:578.702 JLINK_ReadReg(R0)
T7BD8 002:578.705 - 0.003ms returns 0x00000000
T7BD8 002:579.045 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:579.052   Data:  00 25 0F E2 25 28 77 D1 00 24 27 46 F8 4A 01 21 ...
T7BD8 002:579.062   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:581.660 - 2.614ms returns 0x27C
T7BD8 002:581.666 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:581.670   Data:  10 22 4F F0 00 0A 44 F0 04 04 08 27 00 92 03 E0 ...
T7BD8 002:581.677   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:583.528 - 1.861ms returns 0x184
T7BD8 002:583.534 JLINK_HasError()
T7BD8 002:583.539 JLINK_WriteReg(R0, 0x08004C00)
T7BD8 002:583.543 - 0.004ms returns 0
T7BD8 002:583.547 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:583.551 - 0.003ms returns 0
T7BD8 002:583.555 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:583.558 - 0.003ms returns 0
T7BD8 002:583.562 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:583.566 - 0.003ms returns 0
T7BD8 002:583.570 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:583.573 - 0.003ms returns 0
T7BD8 002:583.577 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:583.581 - 0.003ms returns 0
T7BD8 002:583.585 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:583.588 - 0.003ms returns 0
T7BD8 002:583.592 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:583.595 - 0.003ms returns 0
T7BD8 002:583.600 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:583.603 - 0.003ms returns 0
T7BD8 002:583.607 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:583.610 - 0.003ms returns 0
T7BD8 002:583.615 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:583.618 - 0.003ms returns 0
T7BD8 002:583.622 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:583.625 - 0.003ms returns 0
T7BD8 002:583.630 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:583.633 - 0.003ms returns 0
T7BD8 002:583.637 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:583.641 - 0.003ms returns 0
T7BD8 002:583.645 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:583.648 - 0.003ms returns 0
T7BD8 002:583.652 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:583.656 - 0.003ms returns 0
T7BD8 002:583.660 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:583.663 - 0.003ms returns 0
T7BD8 002:583.668 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:583.671 - 0.003ms returns 0
T7BD8 002:583.675 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:583.678 - 0.003ms returns 0
T7BD8 002:583.682 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:583.686 - 0.003ms returns 0
T7BD8 002:583.690 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:583.694 - 0.004ms returns 0x0000001F
T7BD8 002:583.698 JLINK_Go()
T7BD8 002:583.705   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:586.507 - 2.808ms 
T7BD8 002:586.513 JLINK_IsHalted()
T7BD8 002:587.028 - 0.515ms returns FALSE
T7BD8 002:587.040 JLINK_HasError()
T7BD8 002:589.377 JLINK_IsHalted()
T7BD8 002:589.932 - 0.554ms returns FALSE
T7BD8 002:589.938 JLINK_HasError()
T7BD8 002:591.378 JLINK_IsHalted()
T7BD8 002:593.768   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:594.237 - 2.859ms returns TRUE
T7BD8 002:594.243 JLINK_ReadReg(R15 (PC))
T7BD8 002:594.248 - 0.004ms returns 0x20000000
T7BD8 002:594.253 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T7BD8 002:594.256 - 0.003ms returns 0x00
T7BD8 002:594.261 JLINK_ReadReg(R0)
T7BD8 002:594.264 - 0.003ms returns 0x00000000
T7BD8 002:594.627 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:594.635   Data:  63 64 65 66 00 00 00 00 30 31 32 33 34 35 36 37 ...
T7BD8 002:594.646   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:597.232 - 2.604ms returns 0x27C
T7BD8 002:597.244 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:597.248   Data:  04 98 05 F1 01 05 40 1C 04 90 01 98 40 1E 01 90 ...
T7BD8 002:597.258   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:599.366 - 2.121ms returns 0x184
T7BD8 002:599.410 JLINK_HasError()
T7BD8 002:599.416 JLINK_WriteReg(R0, 0x08005000)
T7BD8 002:599.421 - 0.005ms returns 0
T7BD8 002:599.426 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:599.430 - 0.003ms returns 0
T7BD8 002:599.434 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:599.437 - 0.003ms returns 0
T7BD8 002:599.446 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:599.450 - 0.003ms returns 0
T7BD8 002:599.454 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:599.457 - 0.003ms returns 0
T7BD8 002:599.461 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:599.465 - 0.003ms returns 0
T7BD8 002:599.478 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:599.482 - 0.003ms returns 0
T7BD8 002:599.486 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:599.489 - 0.003ms returns 0
T7BD8 002:599.493 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:599.497 - 0.003ms returns 0
T7BD8 002:599.501 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:599.504 - 0.003ms returns 0
T7BD8 002:599.508 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:599.512 - 0.003ms returns 0
T7BD8 002:599.516 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:599.519 - 0.003ms returns 0
T7BD8 002:599.523 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:599.527 - 0.003ms returns 0
T7BD8 002:599.531 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:599.535 - 0.004ms returns 0
T7BD8 002:599.539 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:599.542 - 0.003ms returns 0
T7BD8 002:599.546 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:599.550 - 0.003ms returns 0
T7BD8 002:599.554 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:599.557 - 0.003ms returns 0
T7BD8 002:599.562 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:599.565 - 0.003ms returns 0
T7BD8 002:599.569 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:599.572 - 0.003ms returns 0
T7BD8 002:599.576 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:599.580 - 0.003ms returns 0
T7BD8 002:599.584 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:599.588 - 0.004ms returns 0x00000020
T7BD8 002:599.592 JLINK_Go()
T7BD8 002:599.601   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:602.278 - 2.685ms 
T7BD8 002:602.284 JLINK_IsHalted()
T7BD8 002:602.760 - 0.476ms returns FALSE
T7BD8 002:602.769 JLINK_HasError()
T7BD8 002:604.379 JLINK_IsHalted()
T7BD8 002:604.890 - 0.511ms returns FALSE
T7BD8 002:604.896 JLINK_HasError()
T7BD8 002:606.375 JLINK_IsHalted()
T7BD8 002:609.009   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:609.488 - 3.112ms returns TRUE
T7BD8 002:609.494 JLINK_ReadReg(R15 (PC))
T7BD8 002:609.499 - 0.005ms returns 0x20000000
T7BD8 002:609.504 JLINK_ClrBPEx(BPHandle = 0x00000020)
T7BD8 002:609.507 - 0.003ms returns 0x00
T7BD8 002:609.512 JLINK_ReadReg(R0)
T7BD8 002:609.515 - 0.003ms returns 0x00000000
T7BD8 002:609.839 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:609.847   Data:  A0 01 A0 05 A0 03 A0 07 60 00 60 04 60 02 60 06 ...
T7BD8 002:609.857   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:612.445 - 2.605ms returns 0x27C
T7BD8 002:612.451 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:612.455   Data:  B4 02 B4 06 B4 01 B4 05 B4 03 B4 07 74 00 74 04 ...
T7BD8 002:612.462   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:614.348 - 1.896ms returns 0x184
T7BD8 002:614.357 JLINK_HasError()
T7BD8 002:614.362 JLINK_WriteReg(R0, 0x08005400)
T7BD8 002:614.366 - 0.004ms returns 0
T7BD8 002:614.371 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:614.374 - 0.003ms returns 0
T7BD8 002:614.378 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:614.408 - 0.003ms returns 0
T7BD8 002:614.413 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:614.416 - 0.003ms returns 0
T7BD8 002:614.420 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:614.424 - 0.003ms returns 0
T7BD8 002:614.428 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:614.431 - 0.003ms returns 0
T7BD8 002:614.436 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:614.439 - 0.003ms returns 0
T7BD8 002:614.443 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:614.446 - 0.003ms returns 0
T7BD8 002:614.450 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:614.454 - 0.003ms returns 0
T7BD8 002:614.458 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:614.461 - 0.003ms returns 0
T7BD8 002:614.465 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:614.468 - 0.003ms returns 0
T7BD8 002:614.472 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:614.476 - 0.003ms returns 0
T7BD8 002:614.480 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:614.483 - 0.003ms returns 0
T7BD8 002:614.493 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:614.496 - 0.003ms returns 0
T7BD8 002:614.500 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:614.504 - 0.003ms returns 0
T7BD8 002:614.508 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:614.511 - 0.003ms returns 0
T7BD8 002:614.516 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:614.519 - 0.003ms returns 0
T7BD8 002:614.523 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:614.527 - 0.003ms returns 0
T7BD8 002:614.531 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:614.534 - 0.003ms returns 0
T7BD8 002:614.538 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:614.542 - 0.003ms returns 0
T7BD8 002:614.546 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:614.550 - 0.004ms returns 0x00000021
T7BD8 002:614.554 JLINK_Go()
T7BD8 002:614.561   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:617.766 - 3.211ms 
T7BD8 002:617.781 JLINK_IsHalted()
T7BD8 002:618.418 - 0.636ms returns FALSE
T7BD8 002:618.431 JLINK_HasError()
T7BD8 002:620.378 JLINK_IsHalted()
T7BD8 002:620.900 - 0.521ms returns FALSE
T7BD8 002:620.906 JLINK_HasError()
T7BD8 002:622.375 JLINK_IsHalted()
T7BD8 002:624.761   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:625.253 - 2.878ms returns TRUE
T7BD8 002:625.259 JLINK_ReadReg(R15 (PC))
T7BD8 002:625.264 - 0.005ms returns 0x20000000
T7BD8 002:625.269 JLINK_ClrBPEx(BPHandle = 0x00000021)
T7BD8 002:625.273 - 0.004ms returns 0x00
T7BD8 002:625.277 JLINK_ReadReg(R0)
T7BD8 002:625.281 - 0.003ms returns 0x00000000
T7BD8 002:625.643 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:625.651   Data:  A2 01 A2 05 A2 03 A2 07 62 00 62 04 62 02 62 06 ...
T7BD8 002:625.660   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:628.272 - 2.628ms returns 0x27C
T7BD8 002:628.287 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:628.292   Data:  B6 02 B6 06 B6 01 B6 05 B6 03 B6 07 76 00 76 04 ...
T7BD8 002:628.302   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:630.108 - 1.821ms returns 0x184
T7BD8 002:630.122 JLINK_HasError()
T7BD8 002:630.128 JLINK_WriteReg(R0, 0x08005800)
T7BD8 002:630.133 - 0.005ms returns 0
T7BD8 002:630.138 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:630.141 - 0.003ms returns 0
T7BD8 002:630.145 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:630.149 - 0.003ms returns 0
T7BD8 002:630.153 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:630.156 - 0.003ms returns 0
T7BD8 002:630.160 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:630.164 - 0.003ms returns 0
T7BD8 002:630.168 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:630.171 - 0.003ms returns 0
T7BD8 002:630.175 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:630.179 - 0.003ms returns 0
T7BD8 002:630.183 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:630.186 - 0.003ms returns 0
T7BD8 002:630.190 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:630.194 - 0.003ms returns 0
T7BD8 002:630.197 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:630.201 - 0.003ms returns 0
T7BD8 002:630.205 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:630.208 - 0.003ms returns 0
T7BD8 002:630.212 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:630.216 - 0.003ms returns 0
T7BD8 002:630.220 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:630.223 - 0.003ms returns 0
T7BD8 002:630.227 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:630.231 - 0.003ms returns 0
T7BD8 002:630.235 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:630.238 - 0.003ms returns 0
T7BD8 002:630.242 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:630.246 - 0.003ms returns 0
T7BD8 002:630.250 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:630.254 - 0.003ms returns 0
T7BD8 002:630.258 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:630.261 - 0.003ms returns 0
T7BD8 002:630.265 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:630.269 - 0.003ms returns 0
T7BD8 002:630.273 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:630.276 - 0.003ms returns 0
T7BD8 002:630.281 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:630.285 - 0.004ms returns 0x00000022
T7BD8 002:630.289 JLINK_Go()
T7BD8 002:630.297   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:633.070 - 2.780ms 
T7BD8 002:633.083 JLINK_IsHalted()
T7BD8 002:633.567 - 0.484ms returns FALSE
T7BD8 002:633.573 JLINK_HasError()
T7BD8 002:635.376 JLINK_IsHalted()
T7BD8 002:635.884 - 0.507ms returns FALSE
T7BD8 002:635.890 JLINK_HasError()
T7BD8 002:637.377 JLINK_IsHalted()
T7BD8 002:639.833   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:640.318 - 2.940ms returns TRUE
T7BD8 002:640.325 JLINK_ReadReg(R15 (PC))
T7BD8 002:640.330 - 0.004ms returns 0x20000000
T7BD8 002:640.334 JLINK_ClrBPEx(BPHandle = 0x00000022)
T7BD8 002:640.338 - 0.004ms returns 0x00
T7BD8 002:640.343 JLINK_ReadReg(R0)
T7BD8 002:640.346 - 0.003ms returns 0x00000000
T7BD8 002:640.677 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:640.684   Data:  7F 9A 6C 3E CC CF 78 3E C0 7D 82 3E 93 8E 88 3E ...
T7BD8 002:640.693   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:643.306 - 2.629ms returns 0x27C
T7BD8 002:643.313 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:643.317   Data:  3D 4D 51 3F 20 7A 4F 3F 02 9F 4D 3F F8 BB 4B 3F ...
T7BD8 002:643.324   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:645.097 - 1.784ms returns 0x184
T7BD8 002:645.103 JLINK_HasError()
T7BD8 002:645.108 JLINK_WriteReg(R0, 0x08005C00)
T7BD8 002:645.112 - 0.004ms returns 0
T7BD8 002:645.117 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:645.120 - 0.003ms returns 0
T7BD8 002:645.124 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:645.128 - 0.003ms returns 0
T7BD8 002:645.132 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:645.135 - 0.003ms returns 0
T7BD8 002:645.139 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:645.143 - 0.003ms returns 0
T7BD8 002:645.147 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:645.150 - 0.003ms returns 0
T7BD8 002:645.154 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:645.158 - 0.003ms returns 0
T7BD8 002:645.162 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:645.165 - 0.003ms returns 0
T7BD8 002:645.169 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:645.173 - 0.003ms returns 0
T7BD8 002:645.177 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:645.180 - 0.003ms returns 0
T7BD8 002:645.184 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:645.188 - 0.003ms returns 0
T7BD8 002:645.192 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:645.195 - 0.003ms returns 0
T7BD8 002:645.199 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:645.202 - 0.003ms returns 0
T7BD8 002:645.206 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:645.210 - 0.003ms returns 0
T7BD8 002:645.214 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:645.217 - 0.003ms returns 0
T7BD8 002:645.222 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:645.225 - 0.003ms returns 0
T7BD8 002:645.229 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:645.232 - 0.003ms returns 0
T7BD8 002:645.237 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:645.240 - 0.003ms returns 0
T7BD8 002:645.244 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:645.248 - 0.003ms returns 0
T7BD8 002:645.252 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:645.255 - 0.003ms returns 0
T7BD8 002:645.259 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:645.264 - 0.004ms returns 0x00000023
T7BD8 002:645.268 JLINK_Go()
T7BD8 002:645.274   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:648.195 - 2.926ms 
T7BD8 002:648.209 JLINK_IsHalted()
T7BD8 002:648.758 - 0.549ms returns FALSE
T7BD8 002:648.764 JLINK_HasError()
T7BD8 002:650.404 JLINK_IsHalted()
T7BD8 002:650.927 - 0.523ms returns FALSE
T7BD8 002:650.934 JLINK_HasError()
T7BD8 002:652.376 JLINK_IsHalted()
T7BD8 002:654.760   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:655.214 - 2.838ms returns TRUE
T7BD8 002:655.220 JLINK_ReadReg(R15 (PC))
T7BD8 002:655.225 - 0.004ms returns 0x20000000
T7BD8 002:655.229 JLINK_ClrBPEx(BPHandle = 0x00000023)
T7BD8 002:655.233 - 0.003ms returns 0x00
T7BD8 002:655.238 JLINK_ReadReg(R0)
T7BD8 002:655.241 - 0.003ms returns 0x00000000
T7BD8 002:655.615 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:655.624   Data:  7F 9A 6C BE CC CF 78 BE C0 7D 82 BE 93 8E 88 BE ...
T7BD8 002:655.635   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:658.228 - 2.612ms returns 0x27C
T7BD8 002:658.244 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:658.253   Data:  3D 4D 51 BF 20 7A 4F BF 02 9F 4D BF F8 BB 4B BF ...
T7BD8 002:658.263   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:660.142 - 1.898ms returns 0x184
T7BD8 002:660.156 JLINK_HasError()
T7BD8 002:660.162 JLINK_WriteReg(R0, 0x08006000)
T7BD8 002:660.167 - 0.004ms returns 0
T7BD8 002:660.171 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:660.174 - 0.003ms returns 0
T7BD8 002:660.178 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:660.182 - 0.003ms returns 0
T7BD8 002:660.186 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:660.190 - 0.003ms returns 0
T7BD8 002:660.194 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:660.197 - 0.003ms returns 0
T7BD8 002:660.201 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:660.204 - 0.003ms returns 0
T7BD8 002:660.208 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:660.212 - 0.003ms returns 0
T7BD8 002:660.216 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:660.220 - 0.003ms returns 0
T7BD8 002:660.224 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:660.227 - 0.003ms returns 0
T7BD8 002:660.231 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:660.234 - 0.003ms returns 0
T7BD8 002:660.239 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:660.242 - 0.003ms returns 0
T7BD8 002:660.246 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:660.249 - 0.003ms returns 0
T7BD8 002:660.253 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:660.257 - 0.003ms returns 0
T7BD8 002:660.261 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:660.264 - 0.003ms returns 0
T7BD8 002:660.268 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:660.272 - 0.003ms returns 0
T7BD8 002:660.276 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:660.279 - 0.003ms returns 0
T7BD8 002:660.283 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:660.287 - 0.003ms returns 0
T7BD8 002:660.291 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:660.294 - 0.003ms returns 0
T7BD8 002:660.298 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:660.302 - 0.003ms returns 0
T7BD8 002:660.306 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:660.310 - 0.003ms returns 0
T7BD8 002:660.314 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:660.318 - 0.004ms returns 0x00000024
T7BD8 002:660.322 JLINK_Go()
T7BD8 002:660.330   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:663.027 - 2.705ms 
T7BD8 002:663.033 JLINK_IsHalted()
T7BD8 002:663.526 - 0.492ms returns FALSE
T7BD8 002:663.533 JLINK_HasError()
T7BD8 002:665.375 JLINK_IsHalted()
T7BD8 002:665.925 - 0.549ms returns FALSE
T7BD8 002:665.930 JLINK_HasError()
T7BD8 002:667.377 JLINK_IsHalted()
T7BD8 002:669.694   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:670.199 - 2.821ms returns TRUE
T7BD8 002:670.205 JLINK_ReadReg(R15 (PC))
T7BD8 002:670.210 - 0.004ms returns 0x20000000
T7BD8 002:670.214 JLINK_ClrBPEx(BPHandle = 0x00000024)
T7BD8 002:670.218 - 0.004ms returns 0x00
T7BD8 002:670.223 JLINK_ReadReg(R0)
T7BD8 002:670.226 - 0.003ms returns 0x00000000
T7BD8 002:670.569 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:670.576   Data:  66 20 48 7A 0D 0A 00 54 52 49 41 4E 47 4C 45 00 ...
T7BD8 002:670.586   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:673.182 - 2.613ms returns 0x27C
T7BD8 002:673.189 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:673.193   Data:  E4 16 7F 3F 2B 95 AC 3D 58 0E 7F 3F 80 B6 AF 3D ...
T7BD8 002:673.200   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:675.098 - 1.908ms returns 0x184
T7BD8 002:675.104 JLINK_HasError()
T7BD8 002:675.109 JLINK_WriteReg(R0, 0x08006400)
T7BD8 002:675.113 - 0.004ms returns 0
T7BD8 002:675.117 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:675.120 - 0.003ms returns 0
T7BD8 002:675.124 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:675.128 - 0.003ms returns 0
T7BD8 002:675.132 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:675.136 - 0.003ms returns 0
T7BD8 002:675.140 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:675.143 - 0.003ms returns 0
T7BD8 002:675.147 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:675.150 - 0.003ms returns 0
T7BD8 002:675.154 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:675.158 - 0.003ms returns 0
T7BD8 002:675.162 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:675.170 - 0.008ms returns 0
T7BD8 002:675.174 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:675.178 - 0.003ms returns 0
T7BD8 002:675.182 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:675.186 - 0.003ms returns 0
T7BD8 002:675.190 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:675.193 - 0.003ms returns 0
T7BD8 002:675.197 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:675.201 - 0.003ms returns 0
T7BD8 002:675.205 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:675.208 - 0.003ms returns 0
T7BD8 002:675.212 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:675.216 - 0.003ms returns 0
T7BD8 002:675.220 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:675.223 - 0.003ms returns 0
T7BD8 002:675.227 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:675.231 - 0.003ms returns 0
T7BD8 002:675.235 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:675.238 - 0.003ms returns 0
T7BD8 002:675.242 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:675.246 - 0.003ms returns 0
T7BD8 002:675.250 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:675.253 - 0.003ms returns 0
T7BD8 002:675.257 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:675.260 - 0.003ms returns 0
T7BD8 002:675.265 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:675.269 - 0.004ms returns 0x00000025
T7BD8 002:675.273 JLINK_Go()
T7BD8 002:675.280   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:678.187 - 2.913ms 
T7BD8 002:678.207 JLINK_IsHalted()
T7BD8 002:678.757 - 0.549ms returns FALSE
T7BD8 002:678.772 JLINK_HasError()
T7BD8 002:680.378 JLINK_IsHalted()
T7BD8 002:680.889 - 0.511ms returns FALSE
T7BD8 002:680.895 JLINK_HasError()
T7BD8 002:682.376 JLINK_IsHalted()
T7BD8 002:684.768   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:685.232 - 2.855ms returns TRUE
T7BD8 002:685.239 JLINK_ReadReg(R15 (PC))
T7BD8 002:685.243 - 0.004ms returns 0x20000000
T7BD8 002:685.248 JLINK_ClrBPEx(BPHandle = 0x00000025)
T7BD8 002:685.252 - 0.003ms returns 0x00
T7BD8 002:685.256 JLINK_ReadReg(R0)
T7BD8 002:685.260 - 0.003ms returns 0x00000000
T7BD8 002:685.610 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:685.617   Data:  A5 1E 21 3E C9 BF 7C 3F B6 AB 22 3E BD AF 7C 3F ...
T7BD8 002:685.626   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:688.249 - 2.638ms returns 0x27C
T7BD8 002:688.266 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:688.270   Data:  B3 FA 75 3F FC D8 8D 3E C6 DE 75 3F 22 9A 8E 3E ...
T7BD8 002:688.281   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:690.109 - 1.842ms returns 0x184
T7BD8 002:690.123 JLINK_HasError()
T7BD8 002:690.129 JLINK_WriteReg(R0, 0x08006800)
T7BD8 002:690.134 - 0.005ms returns 0
T7BD8 002:690.138 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:690.142 - 0.003ms returns 0
T7BD8 002:690.146 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:690.149 - 0.003ms returns 0
T7BD8 002:690.153 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:690.156 - 0.003ms returns 0
T7BD8 002:690.160 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:690.164 - 0.003ms returns 0
T7BD8 002:690.168 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:690.171 - 0.003ms returns 0
T7BD8 002:690.175 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:690.178 - 0.003ms returns 0
T7BD8 002:690.182 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:690.186 - 0.003ms returns 0
T7BD8 002:690.190 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:690.193 - 0.003ms returns 0
T7BD8 002:690.197 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:690.201 - 0.003ms returns 0
T7BD8 002:690.205 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:690.209 - 0.003ms returns 0
T7BD8 002:690.213 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:690.216 - 0.003ms returns 0
T7BD8 002:690.220 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:690.224 - 0.003ms returns 0
T7BD8 002:690.228 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:690.231 - 0.003ms returns 0
T7BD8 002:690.235 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:690.238 - 0.003ms returns 0
T7BD8 002:690.243 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:690.246 - 0.003ms returns 0
T7BD8 002:690.250 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:690.254 - 0.003ms returns 0
T7BD8 002:690.262 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:690.273 - 0.011ms returns 0
T7BD8 002:690.278 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:690.281 - 0.003ms returns 0
T7BD8 002:690.285 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:690.288 - 0.003ms returns 0
T7BD8 002:690.293 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:690.297 - 0.004ms returns 0x00000026
T7BD8 002:690.301 JLINK_Go()
T7BD8 002:690.309   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:692.985 - 2.683ms 
T7BD8 002:692.991 JLINK_IsHalted()
T7BD8 002:693.442 - 0.450ms returns FALSE
T7BD8 002:693.448 JLINK_HasError()
T7BD8 002:694.882 JLINK_IsHalted()
T7BD8 002:695.404 - 0.522ms returns FALSE
T7BD8 002:695.412 JLINK_HasError()
T7BD8 002:696.882 JLINK_IsHalted()
T7BD8 002:699.573   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:700.068 - 3.185ms returns TRUE
T7BD8 002:700.075 JLINK_ReadReg(R15 (PC))
T7BD8 002:700.080 - 0.005ms returns 0x20000000
T7BD8 002:700.084 JLINK_ClrBPEx(BPHandle = 0x00000026)
T7BD8 002:700.088 - 0.003ms returns 0x00
T7BD8 002:700.093 JLINK_ReadReg(R0)
T7BD8 002:700.096 - 0.003ms returns 0x00000000
T7BD8 002:700.465 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:700.473   Data:  6B A7 B1 3E 73 F5 6F 3F EF 63 B2 3E 5A D2 6F 3F ...
T7BD8 002:700.482   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:703.091 - 2.625ms returns 0x27C
T7BD8 002:703.097 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:703.101   Data:  95 6A 63 3F 30 19 EB 3E 5A 3C 63 3F BB CB EB 3E ...
T7BD8 002:703.107   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:704.972 - 1.875ms returns 0x184
T7BD8 002:704.983 JLINK_HasError()
T7BD8 002:705.016 JLINK_WriteReg(R0, 0x08006C00)
T7BD8 002:705.021 - 0.005ms returns 0
T7BD8 002:705.026 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:705.029 - 0.003ms returns 0
T7BD8 002:705.033 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:705.037 - 0.003ms returns 0
T7BD8 002:705.041 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:705.044 - 0.003ms returns 0
T7BD8 002:705.048 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:705.051 - 0.003ms returns 0
T7BD8 002:705.056 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:705.060 - 0.004ms returns 0
T7BD8 002:705.065 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:705.068 - 0.003ms returns 0
T7BD8 002:705.072 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:705.075 - 0.003ms returns 0
T7BD8 002:705.080 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:705.083 - 0.003ms returns 0
T7BD8 002:705.087 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:705.090 - 0.003ms returns 0
T7BD8 002:705.094 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:705.098 - 0.003ms returns 0
T7BD8 002:705.102 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:705.105 - 0.003ms returns 0
T7BD8 002:705.110 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:705.113 - 0.003ms returns 0
T7BD8 002:705.117 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:705.121 - 0.003ms returns 0
T7BD8 002:705.125 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:705.128 - 0.003ms returns 0
T7BD8 002:705.132 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:705.136 - 0.003ms returns 0
T7BD8 002:705.140 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:705.143 - 0.003ms returns 0
T7BD8 002:705.147 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:705.150 - 0.003ms returns 0
T7BD8 002:705.154 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:705.158 - 0.003ms returns 0
T7BD8 002:705.162 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:705.165 - 0.003ms returns 0
T7BD8 002:705.170 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:705.174 - 0.004ms returns 0x00000027
T7BD8 002:705.178 JLINK_Go()
T7BD8 002:705.185   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:708.196 - 3.017ms 
T7BD8 002:708.212 JLINK_IsHalted()
T7BD8 002:708.754 - 0.542ms returns FALSE
T7BD8 002:708.760 JLINK_HasError()
T7BD8 002:709.882 JLINK_IsHalted()
T7BD8 002:710.378 - 0.495ms returns FALSE
T7BD8 002:710.383 JLINK_HasError()
T7BD8 002:711.883 JLINK_IsHalted()
T7BD8 002:712.399 - 0.516ms returns FALSE
T7BD8 002:712.405 JLINK_HasError()
T7BD8 002:713.885 JLINK_IsHalted()
T7BD8 002:716.260   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:716.766 - 2.881ms returns TRUE
T7BD8 002:716.772 JLINK_ReadReg(R15 (PC))
T7BD8 002:716.777 - 0.004ms returns 0x20000000
T7BD8 002:716.781 JLINK_ClrBPEx(BPHandle = 0x00000027)
T7BD8 002:716.785 - 0.003ms returns 0x00
T7BD8 002:716.790 JLINK_ReadReg(R0)
T7BD8 002:716.793 - 0.003ms returns 0x00000000
T7BD8 002:717.222 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:717.232   Data:  E2 F5 05 3F 6A F2 59 3F 82 4B 06 3F 9C BD 59 3F ...
T7BD8 002:717.242   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:720.149 - 2.927ms returns 0x27C
T7BD8 002:720.167 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:720.171   Data:  27 1D 48 3F 41 A8 1F 3F 65 DE 47 3F CB F6 1F 3F ...
T7BD8 002:720.180   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:722.090 - 1.923ms returns 0x184
T7BD8 002:722.098 JLINK_HasError()
T7BD8 002:722.103 JLINK_WriteReg(R0, 0x08007000)
T7BD8 002:722.108 - 0.004ms returns 0
T7BD8 002:722.112 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:722.115 - 0.003ms returns 0
T7BD8 002:722.119 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:722.123 - 0.003ms returns 0
T7BD8 002:722.127 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:722.130 - 0.003ms returns 0
T7BD8 002:722.134 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:722.138 - 0.003ms returns 0
T7BD8 002:722.142 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:722.145 - 0.003ms returns 0
T7BD8 002:722.149 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:722.152 - 0.003ms returns 0
T7BD8 002:722.156 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:722.160 - 0.003ms returns 0
T7BD8 002:722.164 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:722.167 - 0.003ms returns 0
T7BD8 002:722.171 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:722.175 - 0.003ms returns 0
T7BD8 002:722.184 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:722.188 - 0.003ms returns 0
T7BD8 002:722.192 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:722.195 - 0.003ms returns 0
T7BD8 002:722.200 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:722.203 - 0.003ms returns 0
T7BD8 002:722.207 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:722.211 - 0.004ms returns 0
T7BD8 002:722.215 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:722.218 - 0.003ms returns 0
T7BD8 002:722.223 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:722.226 - 0.003ms returns 0
T7BD8 002:722.230 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:722.234 - 0.003ms returns 0
T7BD8 002:722.238 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:722.241 - 0.003ms returns 0
T7BD8 002:722.245 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:722.248 - 0.003ms returns 0
T7BD8 002:722.252 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:722.256 - 0.003ms returns 0
T7BD8 002:722.260 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:722.264 - 0.004ms returns 0x00000028
T7BD8 002:722.268 JLINK_Go()
T7BD8 002:722.276   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:724.986 - 2.717ms 
T7BD8 002:724.992 JLINK_IsHalted()
T7BD8 002:725.442 - 0.449ms returns FALSE
T7BD8 002:725.447 JLINK_HasError()
T7BD8 002:726.884 JLINK_IsHalted()
T7BD8 002:727.500 - 0.616ms returns FALSE
T7BD8 002:727.512 JLINK_HasError()
T7BD8 002:728.883 JLINK_IsHalted()
T7BD8 002:731.252   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:731.758 - 2.875ms returns TRUE
T7BD8 002:731.769 JLINK_ReadReg(R15 (PC))
T7BD8 002:731.774 - 0.005ms returns 0x20000000
T7BD8 002:731.807 JLINK_ClrBPEx(BPHandle = 0x00000028)
T7BD8 002:731.812 - 0.004ms returns 0x00
T7BD8 002:731.816 JLINK_ReadReg(R0)
T7BD8 002:731.820 - 0.003ms returns 0x00000000
T7BD8 002:732.168 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:732.175   Data:  29 F2 2D 3F 3B 8F 3B 3F DE 3B 2E 3F C1 4A 3B 3F ...
T7BD8 002:732.184   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:734.826 - 2.658ms returns 0x27C
T7BD8 002:734.833 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:734.837   Data:  04 1F 25 3F 38 A1 43 3F 25 D2 24 3F 00 E2 43 3F ...
T7BD8 002:734.844   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:736.765 - 1.932ms returns 0x184
T7BD8 002:736.771 JLINK_HasError()
T7BD8 002:736.776 JLINK_WriteReg(R0, 0x08007400)
T7BD8 002:736.786 - 0.010ms returns 0
T7BD8 002:736.790 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:736.794 - 0.003ms returns 0
T7BD8 002:736.798 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:736.801 - 0.003ms returns 0
T7BD8 002:736.806 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:736.810 - 0.003ms returns 0
T7BD8 002:736.814 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:736.817 - 0.003ms returns 0
T7BD8 002:736.822 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:736.825 - 0.003ms returns 0
T7BD8 002:736.829 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:736.833 - 0.003ms returns 0
T7BD8 002:736.837 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:736.840 - 0.003ms returns 0
T7BD8 002:736.844 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:736.848 - 0.003ms returns 0
T7BD8 002:736.852 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:736.855 - 0.003ms returns 0
T7BD8 002:736.859 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:736.863 - 0.003ms returns 0
T7BD8 002:736.867 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:736.870 - 0.003ms returns 0
T7BD8 002:736.874 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:736.878 - 0.003ms returns 0
T7BD8 002:736.882 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:736.886 - 0.004ms returns 0
T7BD8 002:736.890 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:736.893 - 0.003ms returns 0
T7BD8 002:736.897 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:736.901 - 0.003ms returns 0
T7BD8 002:736.905 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:736.908 - 0.003ms returns 0
T7BD8 002:736.912 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:736.916 - 0.003ms returns 0
T7BD8 002:736.920 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:736.923 - 0.003ms returns 0
T7BD8 002:736.927 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:736.931 - 0.003ms returns 0
T7BD8 002:736.935 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:736.939 - 0.004ms returns 0x00000029
T7BD8 002:736.943 JLINK_Go()
T7BD8 002:736.950   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:740.026 - 3.082ms 
T7BD8 002:740.043 JLINK_IsHalted()
T7BD8 002:740.508 - 0.464ms returns FALSE
T7BD8 002:740.513 JLINK_HasError()
T7BD8 002:741.882 JLINK_IsHalted()
T7BD8 002:742.428 - 0.546ms returns FALSE
T7BD8 002:742.437 JLINK_HasError()
T7BD8 002:744.882 JLINK_IsHalted()
T7BD8 002:747.266   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:748.014 - 3.131ms returns TRUE
T7BD8 002:748.027 JLINK_ReadReg(R15 (PC))
T7BD8 002:748.033 - 0.006ms returns 0x20000000
T7BD8 002:748.038 JLINK_ClrBPEx(BPHandle = 0x00000029)
T7BD8 002:748.042 - 0.004ms returns 0x00
T7BD8 002:748.046 JLINK_ReadReg(R0)
T7BD8 002:748.049 - 0.003ms returns 0x00000000
T7BD8 002:748.387 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:748.394   Data:  2B 3F 4F 3F D9 F6 15 3F 1F 7A 4F 3F 54 A5 15 3F ...
T7BD8 002:748.405   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:751.021 - 2.633ms returns 0x27C
T7BD8 002:751.036 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:751.045   Data:  DC 90 F7 3E 96 15 60 3F CB E0 F6 3E 21 46 60 3F ...
T7BD8 002:751.054   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:752.929 - 1.893ms returns 0x184
T7BD8 002:752.936 JLINK_HasError()
T7BD8 002:752.941 JLINK_WriteReg(R0, 0x08007800)
T7BD8 002:752.945 - 0.004ms returns 0
T7BD8 002:752.949 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:752.953 - 0.003ms returns 0
T7BD8 002:752.957 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:752.960 - 0.003ms returns 0
T7BD8 002:752.964 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:752.968 - 0.003ms returns 0
T7BD8 002:752.972 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:752.975 - 0.003ms returns 0
T7BD8 002:752.979 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:752.983 - 0.003ms returns 0
T7BD8 002:752.987 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:752.990 - 0.003ms returns 0
T7BD8 002:752.994 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:752.998 - 0.003ms returns 0
T7BD8 002:753.002 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:753.005 - 0.003ms returns 0
T7BD8 002:753.009 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:753.013 - 0.003ms returns 0
T7BD8 002:753.017 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:753.026 - 0.008ms returns 0
T7BD8 002:753.030 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:753.033 - 0.003ms returns 0
T7BD8 002:753.037 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:753.052 - 0.014ms returns 0
T7BD8 002:753.056 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:753.069 - 0.013ms returns 0
T7BD8 002:753.074 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:753.077 - 0.003ms returns 0
T7BD8 002:753.081 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:753.085 - 0.003ms returns 0
T7BD8 002:753.089 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:753.092 - 0.003ms returns 0
T7BD8 002:753.096 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:753.100 - 0.003ms returns 0
T7BD8 002:753.104 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:753.107 - 0.003ms returns 0
T7BD8 002:753.111 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:753.114 - 0.003ms returns 0
T7BD8 002:753.119 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:753.123 - 0.004ms returns 0x0000002A
T7BD8 002:753.128 JLINK_Go()
T7BD8 002:753.135   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:755.796 - 2.668ms 
T7BD8 002:755.802 JLINK_IsHalted()
T7BD8 002:756.258 - 0.455ms returns FALSE
T7BD8 002:756.268 JLINK_HasError()
T7BD8 002:757.886 JLINK_IsHalted()
T7BD8 002:758.470 - 0.583ms returns FALSE
T7BD8 002:758.480 JLINK_HasError()
T7BD8 002:759.883 JLINK_IsHalted()
T7BD8 002:762.261   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:762.754 - 2.871ms returns TRUE
T7BD8 002:762.761 JLINK_ReadReg(R15 (PC))
T7BD8 002:762.766 - 0.004ms returns 0x20000000
T7BD8 002:762.770 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T7BD8 002:762.774 - 0.003ms returns 0x00
T7BD8 002:762.778 JLINK_ReadReg(R0)
T7BD8 002:762.782 - 0.003ms returns 0x00000000
T7BD8 002:763.129 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:763.137   Data:  4C 95 68 3F 41 36 D5 3E 3C BF 68 3F 64 7F D4 3E ...
T7BD8 002:763.146   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:765.762 - 2.632ms returns 0x27C
T7BD8 002:765.768 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:765.772   Data:  27 60 9B 3E 6E ED 73 3F 86 A0 9A 3E DD 0B 74 3F ...
T7BD8 002:765.779   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:767.642 - 1.873ms returns 0x184
T7BD8 002:767.656 JLINK_HasError()
T7BD8 002:767.662 JLINK_WriteReg(R0, 0x08007C00)
T7BD8 002:767.667 - 0.005ms returns 0
T7BD8 002:767.672 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:767.675 - 0.003ms returns 0
T7BD8 002:767.679 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:767.682 - 0.003ms returns 0
T7BD8 002:767.687 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:767.690 - 0.003ms returns 0
T7BD8 002:767.694 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:767.698 - 0.003ms returns 0
T7BD8 002:767.702 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:767.705 - 0.003ms returns 0
T7BD8 002:767.709 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:767.713 - 0.003ms returns 0
T7BD8 002:767.717 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:767.720 - 0.003ms returns 0
T7BD8 002:767.724 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:767.728 - 0.003ms returns 0
T7BD8 002:767.732 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:767.735 - 0.003ms returns 0
T7BD8 002:767.739 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:767.743 - 0.004ms returns 0
T7BD8 002:767.747 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:767.751 - 0.003ms returns 0
T7BD8 002:767.755 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:767.758 - 0.003ms returns 0
T7BD8 002:767.762 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:767.766 - 0.003ms returns 0
T7BD8 002:767.770 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:767.774 - 0.003ms returns 0
T7BD8 002:767.778 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:767.781 - 0.003ms returns 0
T7BD8 002:767.785 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:767.789 - 0.003ms returns 0
T7BD8 002:767.793 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:767.796 - 0.003ms returns 0
T7BD8 002:767.800 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:767.804 - 0.003ms returns 0
T7BD8 002:767.808 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:767.811 - 0.003ms returns 0
T7BD8 002:767.819 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:767.825 - 0.006ms returns 0x0000002B
T7BD8 002:767.829 JLINK_Go()
T7BD8 002:767.838   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:770.771 - 2.940ms 
T7BD8 002:770.789 JLINK_IsHalted()
T7BD8 002:771.285 - 0.496ms returns FALSE
T7BD8 002:771.300 JLINK_HasError()
T7BD8 002:772.884 JLINK_IsHalted()
T7BD8 002:773.390 - 0.505ms returns FALSE
T7BD8 002:773.396 JLINK_HasError()
T7BD8 002:774.783 JLINK_IsHalted()
T7BD8 002:777.142   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:777.764 - 2.980ms returns TRUE
T7BD8 002:777.775 JLINK_ReadReg(R15 (PC))
T7BD8 002:777.781 - 0.006ms returns 0x20000000
T7BD8 002:777.786 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T7BD8 002:777.790 - 0.004ms returns 0x00
T7BD8 002:777.795 JLINK_ReadReg(R0)
T7BD8 002:777.799 - 0.004ms returns 0x00000000
T7BD8 002:778.180 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:778.189   Data:  4A FB 78 3F 7F 9A 6C 3E 98 12 79 3F 2F 13 6B 3E ...
T7BD8 002:778.200   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:780.837 - 2.656ms returns 0x27C
T7BD8 002:780.852 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:780.856   Data:  76 DB E4 3D 88 65 7E 3F 2E BC E1 3D B0 70 7E 3F ...
T7BD8 002:780.866   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:782.758 - 1.906ms returns 0x184
T7BD8 002:782.769 JLINK_HasError()
T7BD8 002:782.776 JLINK_WriteReg(R0, 0x08008000)
T7BD8 002:782.782 - 0.006ms returns 0
T7BD8 002:782.818 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:782.824 - 0.005ms returns 0
T7BD8 002:782.829 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:782.833 - 0.004ms returns 0
T7BD8 002:782.838 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:782.843 - 0.004ms returns 0
T7BD8 002:782.848 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:782.852 - 0.004ms returns 0
T7BD8 002:782.857 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:782.862 - 0.004ms returns 0
T7BD8 002:782.867 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:782.871 - 0.004ms returns 0
T7BD8 002:782.876 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:782.880 - 0.004ms returns 0
T7BD8 002:782.885 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:782.890 - 0.004ms returns 0
T7BD8 002:782.895 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:782.900 - 0.004ms returns 0
T7BD8 002:782.905 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:782.909 - 0.004ms returns 0
T7BD8 002:782.914 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:782.918 - 0.004ms returns 0
T7BD8 002:782.923 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:782.928 - 0.004ms returns 0
T7BD8 002:782.933 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:782.938 - 0.004ms returns 0
T7BD8 002:782.943 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:782.947 - 0.004ms returns 0
T7BD8 002:782.952 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:782.957 - 0.004ms returns 0
T7BD8 002:782.962 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:782.966 - 0.004ms returns 0
T7BD8 002:782.971 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:782.976 - 0.004ms returns 0
T7BD8 002:782.981 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:782.985 - 0.004ms returns 0
T7BD8 002:782.990 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:782.995 - 0.005ms returns 0
T7BD8 002:783.000 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:783.004 - 0.004ms returns 0x0000002C
T7BD8 002:783.008 JLINK_Go()
T7BD8 002:783.016   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:785.763 - 2.754ms 
T7BD8 002:785.769 JLINK_IsHalted()
T7BD8 002:786.231 - 0.461ms returns FALSE
T7BD8 002:786.236 JLINK_HasError()
T7BD8 002:787.788 JLINK_IsHalted()
T7BD8 002:788.318 - 0.529ms returns FALSE
T7BD8 002:788.332 JLINK_HasError()
T7BD8 002:790.290 JLINK_IsHalted()
T7BD8 002:792.615   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:793.108 - 2.818ms returns TRUE
T7BD8 002:793.114 JLINK_ReadReg(R15 (PC))
T7BD8 002:793.119 - 0.004ms returns 0x20000000
T7BD8 002:793.124 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T7BD8 002:793.128 - 0.003ms returns 0x00
T7BD8 002:793.132 JLINK_ReadReg(R0)
T7BD8 002:793.135 - 0.003ms returns 0x00000000
T7BD8 002:793.490 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:793.500   Data:  D1 CF 7F 3F 2C C3 16 3D 98 D3 7F 3F B8 7B 10 3D ...
T7BD8 002:793.512   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:796.095 - 2.605ms returns 0x27C
T7BD8 002:796.102 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:796.105   Data:  2B 95 AC BD E4 16 7F 3F 80 B6 AF BD 58 0E 7F 3F ...
T7BD8 002:796.113   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:798.100 - 1.998ms returns 0x184
T7BD8 002:798.117 JLINK_HasError()
T7BD8 002:798.122 JLINK_WriteReg(R0, 0x08008400)
T7BD8 002:798.127 - 0.005ms returns 0
T7BD8 002:798.132 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:798.135 - 0.003ms returns 0
T7BD8 002:798.139 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:798.142 - 0.003ms returns 0
T7BD8 002:798.147 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:798.150 - 0.003ms returns 0
T7BD8 002:798.154 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:798.157 - 0.003ms returns 0
T7BD8 002:798.162 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:798.168 - 0.007ms returns 0
T7BD8 002:798.173 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:798.176 - 0.003ms returns 0
T7BD8 002:798.180 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:798.184 - 0.003ms returns 0
T7BD8 002:798.188 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:798.191 - 0.003ms returns 0
T7BD8 002:798.195 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:798.199 - 0.003ms returns 0
T7BD8 002:798.203 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:798.206 - 0.003ms returns 0
T7BD8 002:798.210 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:798.214 - 0.003ms returns 0
T7BD8 002:798.218 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:798.221 - 0.003ms returns 0
T7BD8 002:798.225 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:798.254 - 0.029ms returns 0
T7BD8 002:798.258 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:798.262 - 0.003ms returns 0
T7BD8 002:798.266 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:798.270 - 0.004ms returns 0
T7BD8 002:798.274 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:798.277 - 0.003ms returns 0
T7BD8 002:798.281 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:798.285 - 0.003ms returns 0
T7BD8 002:798.289 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:798.292 - 0.003ms returns 0
T7BD8 002:798.366 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:798.370 - 0.004ms returns 0
T7BD8 002:798.375 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:798.380 - 0.005ms returns 0x0000002D
T7BD8 002:798.384 JLINK_Go()
T7BD8 002:798.393   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:801.154 - 2.769ms 
T7BD8 002:801.168 JLINK_IsHalted()
T7BD8 002:801.633 - 0.464ms returns FALSE
T7BD8 002:801.639 JLINK_HasError()
T7BD8 002:803.288 JLINK_IsHalted()
T7BD8 002:803.752 - 0.464ms returns FALSE
T7BD8 002:803.758 JLINK_HasError()
T7BD8 002:805.290 JLINK_IsHalted()
T7BD8 002:807.638   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:808.165 - 2.874ms returns TRUE
T7BD8 002:808.181 JLINK_ReadReg(R15 (PC))
T7BD8 002:808.187 - 0.006ms returns 0x20000000
T7BD8 002:808.191 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T7BD8 002:808.195 - 0.004ms returns 0x00
T7BD8 002:808.200 JLINK_ReadReg(R0)
T7BD8 002:808.203 - 0.003ms returns 0x00000000
T7BD8 002:808.617 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:808.625   Data:  AE CF 7C 3F B6 AB 22 BE C9 BF 7C 3F AD 38 24 BE ...
T7BD8 002:808.637   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:811.280 - 2.661ms returns 0x27C
T7BD8 002:811.303 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:811.308   Data:  FC D8 8D BE B3 FA 75 3F 22 9A 8E BE C6 DE 75 3F ...
T7BD8 002:811.321   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:813.100 - 1.796ms returns 0x184
T7BD8 002:813.107 JLINK_HasError()
T7BD8 002:813.112 JLINK_WriteReg(R0, 0x08008800)
T7BD8 002:813.117 - 0.004ms returns 0
T7BD8 002:813.122 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:813.125 - 0.003ms returns 0
T7BD8 002:813.129 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:813.132 - 0.003ms returns 0
T7BD8 002:813.136 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:813.140 - 0.003ms returns 0
T7BD8 002:813.144 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:813.151 - 0.006ms returns 0
T7BD8 002:813.157 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:813.160 - 0.003ms returns 0
T7BD8 002:813.164 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:813.168 - 0.003ms returns 0
T7BD8 002:813.172 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:813.175 - 0.003ms returns 0
T7BD8 002:813.180 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:813.183 - 0.003ms returns 0
T7BD8 002:813.187 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:813.190 - 0.003ms returns 0
T7BD8 002:813.194 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:813.198 - 0.003ms returns 0
T7BD8 002:813.202 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:813.205 - 0.003ms returns 0
T7BD8 002:813.209 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:813.213 - 0.003ms returns 0
T7BD8 002:813.217 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:813.221 - 0.004ms returns 0
T7BD8 002:813.225 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:813.228 - 0.003ms returns 0
T7BD8 002:813.232 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:813.236 - 0.003ms returns 0
T7BD8 002:813.240 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:813.244 - 0.003ms returns 0
T7BD8 002:813.248 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:813.251 - 0.003ms returns 0
T7BD8 002:813.255 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:813.258 - 0.003ms returns 0
T7BD8 002:813.262 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:813.266 - 0.003ms returns 0
T7BD8 002:813.270 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:813.275 - 0.004ms returns 0x0000002E
T7BD8 002:813.279 JLINK_Go()
T7BD8 002:813.286   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:815.982 - 2.703ms 
T7BD8 002:815.988 JLINK_IsHalted()
T7BD8 002:816.443 - 0.454ms returns FALSE
T7BD8 002:816.448 JLINK_HasError()
T7BD8 002:818.292 JLINK_IsHalted()
T7BD8 002:818.799 - 0.506ms returns FALSE
T7BD8 002:818.809 JLINK_HasError()
T7BD8 002:820.289 JLINK_IsHalted()
T7BD8 002:822.616   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:823.109 - 2.819ms returns TRUE
T7BD8 002:823.115 JLINK_ReadReg(R15 (PC))
T7BD8 002:823.120 - 0.004ms returns 0x20000000
T7BD8 002:823.124 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T7BD8 002:823.128 - 0.004ms returns 0x00
T7BD8 002:823.133 JLINK_ReadReg(R0)
T7BD8 002:823.136 - 0.003ms returns 0x00000000
T7BD8 002:823.510 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:823.518   Data:  67 18 70 3F EF 63 B2 BE 73 F5 6F 3F 58 20 B3 BE ...
T7BD8 002:823.528   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:826.139 - 2.629ms returns 0x27C
T7BD8 002:826.146 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:826.149   Data:  30 19 EB BE 95 6A 63 3F BB CB EB BE 5A 3C 63 3F ...
T7BD8 002:826.157   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:828.022 - 1.875ms returns 0x184
T7BD8 002:828.039 JLINK_HasError()
T7BD8 002:828.045 JLINK_WriteReg(R0, 0x08008C00)
T7BD8 002:828.050 - 0.005ms returns 0
T7BD8 002:828.055 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:828.059 - 0.003ms returns 0
T7BD8 002:828.063 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:828.066 - 0.003ms returns 0
T7BD8 002:828.070 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:828.074 - 0.003ms returns 0
T7BD8 002:828.078 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:828.082 - 0.004ms returns 0
T7BD8 002:828.086 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:828.089 - 0.003ms returns 0
T7BD8 002:828.093 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:828.097 - 0.003ms returns 0
T7BD8 002:828.101 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:828.104 - 0.003ms returns 0
T7BD8 002:828.108 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:828.112 - 0.003ms returns 0
T7BD8 002:828.116 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:828.119 - 0.003ms returns 0
T7BD8 002:828.123 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:828.127 - 0.003ms returns 0
T7BD8 002:828.131 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:828.134 - 0.003ms returns 0
T7BD8 002:828.138 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:828.142 - 0.003ms returns 0
T7BD8 002:828.146 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:828.150 - 0.003ms returns 0
T7BD8 002:828.154 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:828.161 - 0.007ms returns 0
T7BD8 002:828.167 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:828.171 - 0.003ms returns 0
T7BD8 002:828.175 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:828.178 - 0.003ms returns 0
T7BD8 002:828.182 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:828.186 - 0.003ms returns 0
T7BD8 002:828.190 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:828.193 - 0.003ms returns 0
T7BD8 002:828.197 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:828.201 - 0.003ms returns 0
T7BD8 002:828.205 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:828.210 - 0.004ms returns 0x0000002F
T7BD8 002:828.214 JLINK_Go()
T7BD8 002:828.223   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:831.026 - 2.811ms 
T7BD8 002:831.040 JLINK_IsHalted()
T7BD8 002:831.502 - 0.462ms returns FALSE
T7BD8 002:831.508 JLINK_HasError()
T7BD8 002:833.288 JLINK_IsHalted()
T7BD8 002:833.753 - 0.465ms returns FALSE
T7BD8 002:833.759 JLINK_HasError()
T7BD8 002:835.289 JLINK_IsHalted()
T7BD8 002:837.637   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:838.158 - 2.869ms returns TRUE
T7BD8 002:838.173 JLINK_ReadReg(R15 (PC))
T7BD8 002:838.179 - 0.005ms returns 0x20000000
T7BD8 002:838.183 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T7BD8 002:838.187 - 0.004ms returns 0x00
T7BD8 002:838.192 JLINK_ReadReg(R0)
T7BD8 002:838.195 - 0.003ms returns 0x00000000
T7BD8 002:838.592 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:838.600   Data:  16 27 5A 3F 82 4B 06 BF 6A F2 59 3F 0E A1 06 BF ...
T7BD8 002:838.618   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:841.267 - 2.675ms returns 0x27C
T7BD8 002:841.278 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:841.282   Data:  41 A8 1F BF 27 1D 48 3F CB F6 1F BF 65 DE 47 3F ...
T7BD8 002:841.290   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:843.089 - 1.811ms returns 0x184
T7BD8 002:843.096 JLINK_HasError()
T7BD8 002:843.101 JLINK_WriteReg(R0, 0x08009000)
T7BD8 002:843.106 - 0.004ms returns 0
T7BD8 002:843.110 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:843.113 - 0.003ms returns 0
T7BD8 002:843.117 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:843.121 - 0.003ms returns 0
T7BD8 002:843.125 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:843.128 - 0.003ms returns 0
T7BD8 002:843.132 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:843.135 - 0.003ms returns 0
T7BD8 002:843.140 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:843.143 - 0.003ms returns 0
T7BD8 002:843.147 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:843.150 - 0.003ms returns 0
T7BD8 002:843.154 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:843.158 - 0.003ms returns 0
T7BD8 002:843.162 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:843.165 - 0.003ms returns 0
T7BD8 002:843.169 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:843.173 - 0.003ms returns 0
T7BD8 002:843.177 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:843.180 - 0.003ms returns 0
T7BD8 002:843.184 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:843.188 - 0.003ms returns 0
T7BD8 002:843.192 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:843.195 - 0.003ms returns 0
T7BD8 002:843.200 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:843.204 - 0.004ms returns 0
T7BD8 002:843.208 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:843.211 - 0.003ms returns 0
T7BD8 002:843.215 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:843.219 - 0.003ms returns 0
T7BD8 002:843.223 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:843.226 - 0.003ms returns 0
T7BD8 002:843.230 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:843.234 - 0.003ms returns 0
T7BD8 002:843.238 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:843.241 - 0.003ms returns 0
T7BD8 002:843.245 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:843.248 - 0.003ms returns 0
T7BD8 002:843.253 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:843.257 - 0.004ms returns 0x00000030
T7BD8 002:843.261 JLINK_Go()
T7BD8 002:843.268   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:845.969 - 2.707ms 
T7BD8 002:845.975 JLINK_IsHalted()
T7BD8 002:846.533 - 0.558ms returns FALSE
T7BD8 002:846.539 JLINK_HasError()
T7BD8 002:848.291 JLINK_IsHalted()
T7BD8 002:848.832 - 0.540ms returns FALSE
T7BD8 002:848.843 JLINK_HasError()
T7BD8 002:850.291 JLINK_IsHalted()
T7BD8 002:852.617   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:853.112 - 2.821ms returns TRUE
T7BD8 002:853.118 JLINK_ReadReg(R15 (PC))
T7BD8 002:853.123 - 0.004ms returns 0x20000000
T7BD8 002:853.128 JLINK_ClrBPEx(BPHandle = 0x00000030)
T7BD8 002:853.132 - 0.004ms returns 0x00
T7BD8 002:853.136 JLINK_ReadReg(R0)
T7BD8 002:853.139 - 0.003ms returns 0x00000000
T7BD8 002:853.492 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:853.500   Data:  98 D3 3B 3F DE 3B 2E BF 3B 8F 3B 3F 78 85 2E BF ...
T7BD8 002:853.514   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:856.092 - 2.600ms returns 0x27C
T7BD8 002:856.099 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:856.103   Data:  38 A1 43 BF 04 1F 25 3F 00 E2 43 BF 25 D2 24 3F ...
T7BD8 002:856.111   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:857.978 - 1.879ms returns 0x184
T7BD8 002:857.990 JLINK_HasError()
T7BD8 002:857.996 JLINK_WriteReg(R0, 0x08009400)
T7BD8 002:858.001 - 0.005ms returns 0
T7BD8 002:858.005 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:858.008 - 0.003ms returns 0
T7BD8 002:858.012 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:858.016 - 0.003ms returns 0
T7BD8 002:858.020 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:858.023 - 0.003ms returns 0
T7BD8 002:858.027 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:858.031 - 0.003ms returns 0
T7BD8 002:858.035 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:858.038 - 0.003ms returns 0
T7BD8 002:858.042 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:858.046 - 0.003ms returns 0
T7BD8 002:858.050 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:858.053 - 0.003ms returns 0
T7BD8 002:858.057 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:858.060 - 0.003ms returns 0
T7BD8 002:858.065 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:858.068 - 0.003ms returns 0
T7BD8 002:858.072 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:858.076 - 0.003ms returns 0
T7BD8 002:858.080 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:858.083 - 0.003ms returns 0
T7BD8 002:858.088 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:858.091 - 0.003ms returns 0
T7BD8 002:858.095 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:858.099 - 0.003ms returns 0
T7BD8 002:858.103 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:858.106 - 0.003ms returns 0
T7BD8 002:858.110 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:858.114 - 0.003ms returns 0
T7BD8 002:858.118 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:858.121 - 0.003ms returns 0
T7BD8 002:858.125 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:858.129 - 0.003ms returns 0
T7BD8 002:858.133 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:858.136 - 0.003ms returns 0
T7BD8 002:858.140 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:858.144 - 0.003ms returns 0
T7BD8 002:858.148 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:858.152 - 0.004ms returns 0x00000031
T7BD8 002:858.157 JLINK_Go()
T7BD8 002:858.165   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:860.883 - 2.725ms 
T7BD8 002:860.898 JLINK_IsHalted()
T7BD8 002:861.356 - 0.458ms returns FALSE
T7BD8 002:861.365 JLINK_HasError()
T7BD8 002:863.289 JLINK_IsHalted()
T7BD8 002:863.803 - 0.513ms returns FALSE
T7BD8 002:863.809 JLINK_HasError()
T7BD8 002:865.288 JLINK_IsHalted()
T7BD8 002:867.589   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:868.121 - 2.833ms returns TRUE
T7BD8 002:868.134 JLINK_ReadReg(R15 (PC))
T7BD8 002:868.140 - 0.006ms returns 0x20000000
T7BD8 002:868.145 JLINK_ClrBPEx(BPHandle = 0x00000031)
T7BD8 002:868.149 - 0.004ms returns 0x00
T7BD8 002:868.154 JLINK_ReadReg(R0)
T7BD8 002:868.157 - 0.003ms returns 0x00000000
T7BD8 002:868.561 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:868.569   Data:  47 48 16 3F 1F 7A 4F BF D9 F6 15 3F F4 B4 4F BF ...
T7BD8 002:868.580   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:871.272 - 2.710ms returns 0x27C
T7BD8 002:871.289 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:871.293   Data:  96 15 60 BF DC 90 F7 3E 21 46 60 BF CB E0 F6 3E ...
T7BD8 002:871.303   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:873.133 - 1.844ms returns 0x184
T7BD8 002:873.140 JLINK_HasError()
T7BD8 002:873.146 JLINK_WriteReg(R0, 0x08009800)
T7BD8 002:873.152 - 0.006ms returns 0
T7BD8 002:873.156 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:873.159 - 0.003ms returns 0
T7BD8 002:873.163 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:873.167 - 0.003ms returns 0
T7BD8 002:873.171 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:873.174 - 0.003ms returns 0
T7BD8 002:873.178 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:873.182 - 0.003ms returns 0
T7BD8 002:873.186 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:873.189 - 0.003ms returns 0
T7BD8 002:873.193 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:873.196 - 0.003ms returns 0
T7BD8 002:873.200 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:873.204 - 0.003ms returns 0
T7BD8 002:873.208 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:873.211 - 0.003ms returns 0
T7BD8 002:873.215 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:873.218 - 0.003ms returns 0
T7BD8 002:873.222 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:873.226 - 0.003ms returns 0
T7BD8 002:873.230 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:873.234 - 0.003ms returns 0
T7BD8 002:873.238 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:873.241 - 0.003ms returns 0
T7BD8 002:873.245 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:873.249 - 0.003ms returns 0
T7BD8 002:873.253 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:873.257 - 0.003ms returns 0
T7BD8 002:873.261 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:873.264 - 0.003ms returns 0
T7BD8 002:873.268 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:873.272 - 0.003ms returns 0
T7BD8 002:873.276 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:873.279 - 0.003ms returns 0
T7BD8 002:873.283 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:873.287 - 0.003ms returns 0
T7BD8 002:873.291 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:873.294 - 0.003ms returns 0
T7BD8 002:873.299 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:873.303 - 0.004ms returns 0x00000032
T7BD8 002:873.307 JLINK_Go()
T7BD8 002:873.315   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:876.026 - 2.719ms 
T7BD8 002:876.034 JLINK_IsHalted()
T7BD8 002:876.522 - 0.488ms returns FALSE
T7BD8 002:876.531 JLINK_HasError()
T7BD8 002:878.296 JLINK_IsHalted()
T7BD8 002:878.832 - 0.536ms returns FALSE
T7BD8 002:878.839 JLINK_HasError()
T7BD8 002:880.293 JLINK_IsHalted()
T7BD8 002:882.621   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:883.117 - 2.823ms returns TRUE
T7BD8 002:883.124 JLINK_ReadReg(R15 (PC))
T7BD8 002:883.129 - 0.005ms returns 0x20000000
T7BD8 002:883.133 JLINK_ClrBPEx(BPHandle = 0x00000032)
T7BD8 002:883.137 - 0.003ms returns 0x00
T7BD8 002:883.142 JLINK_ReadReg(R0)
T7BD8 002:883.146 - 0.004ms returns 0x00000000
T7BD8 002:883.497 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:883.505   Data:  FD EC D5 3E 3C BF 68 BF 41 36 D5 3E 07 E9 68 BF ...
T7BD8 002:883.515   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:886.095 - 2.598ms returns 0x27C
T7BD8 002:886.102 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:886.105   Data:  6E ED 73 BF 27 60 9B 3E DD 0B 74 BF 86 A0 9A 3E ...
T7BD8 002:886.112   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:887.983 - 1.881ms returns 0x184
T7BD8 002:888.004 JLINK_HasError()
T7BD8 002:888.039 JLINK_WriteReg(R0, 0x08009C00)
T7BD8 002:888.059 - 0.020ms returns 0
T7BD8 002:888.064 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:888.068 - 0.003ms returns 0
T7BD8 002:888.072 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:888.076 - 0.003ms returns 0
T7BD8 002:888.080 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:888.083 - 0.003ms returns 0
T7BD8 002:888.087 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:888.090 - 0.003ms returns 0
T7BD8 002:888.094 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:888.098 - 0.003ms returns 0
T7BD8 002:888.102 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:888.106 - 0.003ms returns 0
T7BD8 002:888.110 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:888.113 - 0.003ms returns 0
T7BD8 002:888.117 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:888.120 - 0.003ms returns 0
T7BD8 002:888.129 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:888.134 - 0.005ms returns 0
T7BD8 002:888.138 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:888.141 - 0.003ms returns 0
T7BD8 002:888.145 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:888.149 - 0.003ms returns 0
T7BD8 002:888.153 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:888.160 - 0.006ms returns 0
T7BD8 002:888.164 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:888.168 - 0.003ms returns 0
T7BD8 002:888.172 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:888.175 - 0.003ms returns 0
T7BD8 002:888.179 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:888.182 - 0.003ms returns 0
T7BD8 002:888.187 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:888.190 - 0.003ms returns 0
T7BD8 002:888.194 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:888.198 - 0.003ms returns 0
T7BD8 002:888.202 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:888.205 - 0.003ms returns 0
T7BD8 002:888.209 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:888.212 - 0.003ms returns 0
T7BD8 002:888.217 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:888.222 - 0.005ms returns 0x00000033
T7BD8 002:888.226 JLINK_Go()
T7BD8 002:888.235   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:890.971 - 2.744ms 
T7BD8 002:890.984 JLINK_IsHalted()
T7BD8 002:891.442 - 0.457ms returns FALSE
T7BD8 002:891.447 JLINK_HasError()
T7BD8 002:892.795 JLINK_IsHalted()
T7BD8 002:893.257 - 0.462ms returns FALSE
T7BD8 002:893.263 JLINK_HasError()
T7BD8 002:894.794 JLINK_IsHalted()
T7BD8 002:895.257 - 0.463ms returns FALSE
T7BD8 002:895.263 JLINK_HasError()
T7BD8 002:896.794 JLINK_IsHalted()
T7BD8 002:899.153   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:899.673 - 2.878ms returns TRUE
T7BD8 002:899.680 JLINK_ReadReg(R15 (PC))
T7BD8 002:899.685 - 0.005ms returns 0x20000000
T7BD8 002:899.689 JLINK_ClrBPEx(BPHandle = 0x00000033)
T7BD8 002:899.693 - 0.004ms returns 0x00
T7BD8 002:899.698 JLINK_ReadReg(R0)
T7BD8 002:899.701 - 0.003ms returns 0x00000000
T7BD8 002:900.080 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:900.088   Data:  AB 21 6E 3E 98 12 79 BF 7F 9A 6C 3E BF 29 79 BF ...
T7BD8 002:900.098   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:902.767 - 2.687ms returns 0x27C
T7BD8 002:902.775 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:902.778   Data:  88 65 7E BF 76 DB E4 3D B0 70 7E BF 2E BC E1 3D ...
T7BD8 002:902.786   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:904.632 - 1.857ms returns 0x184
T7BD8 002:904.638 JLINK_HasError()
T7BD8 002:904.643 JLINK_WriteReg(R0, 0x0800A000)
T7BD8 002:904.648 - 0.004ms returns 0
T7BD8 002:904.652 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:904.655 - 0.003ms returns 0
T7BD8 002:904.659 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:904.663 - 0.003ms returns 0
T7BD8 002:904.666 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:904.670 - 0.003ms returns 0
T7BD8 002:904.674 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:904.677 - 0.003ms returns 0
T7BD8 002:904.681 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:904.685 - 0.003ms returns 0
T7BD8 002:904.689 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:904.692 - 0.003ms returns 0
T7BD8 002:904.696 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:904.700 - 0.003ms returns 0
T7BD8 002:904.704 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:904.707 - 0.003ms returns 0
T7BD8 002:904.714 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:904.718 - 0.007ms returns 0
T7BD8 002:904.722 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:904.726 - 0.003ms returns 0
T7BD8 002:904.730 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:904.733 - 0.003ms returns 0
T7BD8 002:904.737 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:904.741 - 0.003ms returns 0
T7BD8 002:904.745 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:904.749 - 0.003ms returns 0
T7BD8 002:904.753 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:904.756 - 0.003ms returns 0
T7BD8 002:904.760 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:904.764 - 0.003ms returns 0
T7BD8 002:904.768 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:904.771 - 0.003ms returns 0
T7BD8 002:904.776 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:904.852 - 0.075ms returns 0
T7BD8 002:904.856 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:904.860 - 0.003ms returns 0
T7BD8 002:904.864 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:904.867 - 0.003ms returns 0
T7BD8 002:904.872 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:904.876 - 0.004ms returns 0x00000034
T7BD8 002:904.889 JLINK_Go()
T7BD8 002:904.896   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:907.616 - 2.725ms 
T7BD8 002:907.630 JLINK_IsHalted()
T7BD8 002:908.111 - 0.481ms returns FALSE
T7BD8 002:908.117 JLINK_HasError()
T7BD8 002:909.798 JLINK_IsHalted()
T7BD8 002:910.319 - 0.520ms returns FALSE
T7BD8 002:910.325 JLINK_HasError()
T7BD8 002:911.795 JLINK_IsHalted()
T7BD8 002:914.091   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:914.568 - 2.773ms returns TRUE
T7BD8 002:914.579 JLINK_ReadReg(R15 (PC))
T7BD8 002:914.584 - 0.005ms returns 0x20000000
T7BD8 002:914.609 JLINK_ClrBPEx(BPHandle = 0x00000034)
T7BD8 002:914.614 - 0.005ms returns 0x00
T7BD8 002:914.618 JLINK_ReadReg(R0)
T7BD8 002:914.622 - 0.003ms returns 0x00000000
T7BD8 002:914.991 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:914.999   Data:  88 0A 1D 3D 98 D3 7F BF 2C C3 16 3D 37 D7 7F BF ...
T7BD8 002:915.009   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:917.625 - 2.634ms returns 0x27C
T7BD8 002:917.638 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:917.642   Data:  E4 16 7F BF 2B 95 AC BD 58 0E 7F BF 80 B6 AF BD ...
T7BD8 002:917.652   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:919.535 - 1.897ms returns 0x184
T7BD8 002:919.548 JLINK_HasError()
T7BD8 002:919.554 JLINK_WriteReg(R0, 0x0800A400)
T7BD8 002:919.559 - 0.005ms returns 0
T7BD8 002:919.563 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:919.567 - 0.003ms returns 0
T7BD8 002:919.571 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:919.575 - 0.003ms returns 0
T7BD8 002:919.579 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:919.582 - 0.003ms returns 0
T7BD8 002:919.586 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:919.590 - 0.003ms returns 0
T7BD8 002:919.594 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:919.597 - 0.003ms returns 0
T7BD8 002:919.601 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:919.604 - 0.003ms returns 0
T7BD8 002:919.608 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:919.612 - 0.003ms returns 0
T7BD8 002:919.616 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:919.619 - 0.003ms returns 0
T7BD8 002:919.623 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:919.626 - 0.003ms returns 0
T7BD8 002:919.630 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:919.634 - 0.003ms returns 0
T7BD8 002:919.638 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:919.641 - 0.003ms returns 0
T7BD8 002:919.645 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:919.648 - 0.003ms returns 0
T7BD8 002:919.653 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:919.656 - 0.003ms returns 0
T7BD8 002:919.660 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:919.664 - 0.003ms returns 0
T7BD8 002:919.668 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:919.671 - 0.003ms returns 0
T7BD8 002:919.676 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:919.679 - 0.003ms returns 0
T7BD8 002:919.683 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:919.690 - 0.007ms returns 0
T7BD8 002:919.695 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:919.698 - 0.003ms returns 0
T7BD8 002:919.702 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:919.705 - 0.003ms returns 0
T7BD8 002:919.710 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:919.714 - 0.004ms returns 0x00000035
T7BD8 002:919.718 JLINK_Go()
T7BD8 002:919.727   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:922.491 - 2.772ms 
T7BD8 002:922.497 JLINK_IsHalted()
T7BD8 002:922.981 - 0.483ms returns FALSE
T7BD8 002:922.986 JLINK_HasError()
T7BD8 002:924.794 JLINK_IsHalted()
T7BD8 002:925.257 - 0.462ms returns FALSE
T7BD8 002:925.262 JLINK_HasError()
T7BD8 002:926.796 JLINK_IsHalted()
T7BD8 002:929.268   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:929.775 - 2.978ms returns TRUE
T7BD8 002:929.793 JLINK_ReadReg(R15 (PC))
T7BD8 002:929.798 - 0.005ms returns 0x20000000
T7BD8 002:929.859 JLINK_ClrBPEx(BPHandle = 0x00000035)
T7BD8 002:929.863 - 0.004ms returns 0x00
T7BD8 002:929.868 JLINK_ReadReg(R0)
T7BD8 002:929.872 - 0.003ms returns 0x00000000
T7BD8 002:930.241 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:930.248   Data:  A5 1E 21 BE C9 BF 7C BF B6 AB 22 BE BD AF 7C BF ...
T7BD8 002:930.258   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:932.846 - 2.604ms returns 0x27C
T7BD8 002:932.859 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:932.863   Data:  B3 FA 75 BF FC D8 8D BE C6 DE 75 BF 22 9A 8E BE ...
T7BD8 002:932.872   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:934.765 - 1.905ms returns 0x184
T7BD8 002:934.771 JLINK_HasError()
T7BD8 002:934.776 JLINK_WriteReg(R0, 0x0800A800)
T7BD8 002:934.780 - 0.004ms returns 0
T7BD8 002:934.784 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:934.788 - 0.003ms returns 0
T7BD8 002:934.792 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:934.796 - 0.003ms returns 0
T7BD8 002:934.800 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:934.804 - 0.003ms returns 0
T7BD8 002:934.808 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:934.811 - 0.003ms returns 0
T7BD8 002:934.815 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:934.818 - 0.003ms returns 0
T7BD8 002:934.822 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:934.826 - 0.003ms returns 0
T7BD8 002:934.830 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:934.833 - 0.003ms returns 0
T7BD8 002:934.837 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:934.841 - 0.003ms returns 0
T7BD8 002:934.845 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:934.848 - 0.003ms returns 0
T7BD8 002:934.852 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:934.856 - 0.003ms returns 0
T7BD8 002:934.860 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:934.863 - 0.003ms returns 0
T7BD8 002:934.867 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:934.871 - 0.003ms returns 0
T7BD8 002:934.875 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:934.879 - 0.003ms returns 0
T7BD8 002:934.883 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:934.886 - 0.003ms returns 0
T7BD8 002:934.890 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:934.894 - 0.003ms returns 0
T7BD8 002:934.898 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:934.902 - 0.003ms returns 0
T7BD8 002:934.906 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:934.909 - 0.003ms returns 0
T7BD8 002:934.913 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:934.916 - 0.003ms returns 0
T7BD8 002:934.920 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:934.924 - 0.003ms returns 0
T7BD8 002:934.929 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:934.933 - 0.004ms returns 0x00000036
T7BD8 002:934.937 JLINK_Go()
T7BD8 002:934.944   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:937.805 - 2.868ms 
T7BD8 002:937.819 JLINK_IsHalted()
T7BD8 002:938.318 - 0.499ms returns FALSE
T7BD8 002:938.324 JLINK_HasError()
T7BD8 002:939.796 JLINK_IsHalted()
T7BD8 002:940.255 - 0.458ms returns FALSE
T7BD8 002:940.261 JLINK_HasError()
T7BD8 002:941.795 JLINK_IsHalted()
T7BD8 002:944.086   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:944.568 - 2.773ms returns TRUE
T7BD8 002:944.574 JLINK_ReadReg(R15 (PC))
T7BD8 002:944.579 - 0.004ms returns 0x20000000
T7BD8 002:944.584 JLINK_ClrBPEx(BPHandle = 0x00000036)
T7BD8 002:944.588 - 0.004ms returns 0x00
T7BD8 002:944.592 JLINK_ReadReg(R0)
T7BD8 002:944.596 - 0.003ms returns 0x00000000
T7BD8 002:944.978 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:944.986   Data:  6B A7 B1 BE 73 F5 6F BF EF 63 B2 BE 5A D2 6F BF ...
T7BD8 002:944.995   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:947.622 - 2.644ms returns 0x27C
T7BD8 002:947.637 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:947.641   Data:  95 6A 63 BF 30 19 EB BE 5A 3C 63 BF BB CB EB BE ...
T7BD8 002:947.650   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:949.530 - 1.893ms returns 0x184
T7BD8 002:949.540 JLINK_HasError()
T7BD8 002:949.546 JLINK_WriteReg(R0, 0x0800AC00)
T7BD8 002:949.551 - 0.005ms returns 0
T7BD8 002:949.555 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:949.558 - 0.003ms returns 0
T7BD8 002:949.595 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:949.599 - 0.003ms returns 0
T7BD8 002:949.604 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:949.607 - 0.003ms returns 0
T7BD8 002:949.611 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:949.614 - 0.003ms returns 0
T7BD8 002:949.619 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:949.622 - 0.003ms returns 0
T7BD8 002:949.626 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:949.630 - 0.003ms returns 0
T7BD8 002:949.634 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:949.637 - 0.003ms returns 0
T7BD8 002:949.641 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:949.644 - 0.003ms returns 0
T7BD8 002:949.648 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:949.652 - 0.003ms returns 0
T7BD8 002:949.656 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:949.659 - 0.003ms returns 0
T7BD8 002:949.663 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:949.667 - 0.003ms returns 0
T7BD8 002:949.671 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:949.674 - 0.003ms returns 0
T7BD8 002:949.678 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:949.682 - 0.003ms returns 0
T7BD8 002:949.686 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:949.689 - 0.003ms returns 0
T7BD8 002:949.694 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:949.697 - 0.003ms returns 0
T7BD8 002:949.701 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:949.705 - 0.003ms returns 0
T7BD8 002:949.709 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:949.712 - 0.003ms returns 0
T7BD8 002:949.716 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:949.720 - 0.003ms returns 0
T7BD8 002:949.724 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:949.727 - 0.003ms returns 0
T7BD8 002:949.732 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:949.736 - 0.004ms returns 0x00000037
T7BD8 002:949.740 JLINK_Go()
T7BD8 002:949.748   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:952.485 - 2.744ms 
T7BD8 002:952.491 JLINK_IsHalted()
T7BD8 002:952.981 - 0.489ms returns FALSE
T7BD8 002:952.986 JLINK_HasError()
T7BD8 002:954.794 JLINK_IsHalted()
T7BD8 002:955.257 - 0.462ms returns FALSE
T7BD8 002:955.262 JLINK_HasError()
T7BD8 002:956.794 JLINK_IsHalted()
T7BD8 002:959.139   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:959.642 - 2.847ms returns TRUE
T7BD8 002:959.650 JLINK_ReadReg(R15 (PC))
T7BD8 002:959.655 - 0.005ms returns 0x20000000
T7BD8 002:959.660 JLINK_ClrBPEx(BPHandle = 0x00000037)
T7BD8 002:959.664 - 0.004ms returns 0x00
T7BD8 002:959.668 JLINK_ReadReg(R0)
T7BD8 002:959.672 - 0.003ms returns 0x00000000
T7BD8 002:960.050 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:960.059   Data:  E2 F5 05 BF 6A F2 59 BF 82 4B 06 BF 9C BD 59 BF ...
T7BD8 002:960.069   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:962.673 - 2.623ms returns 0x27C
T7BD8 002:962.680 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:962.683   Data:  27 1D 48 BF 41 A8 1F BF 65 DE 47 BF CB F6 1F BF ...
T7BD8 002:962.690   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:964.529 - 1.848ms returns 0x184
T7BD8 002:964.535 JLINK_HasError()
T7BD8 002:964.540 JLINK_WriteReg(R0, 0x0800B000)
T7BD8 002:964.544 - 0.004ms returns 0
T7BD8 002:964.548 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:964.552 - 0.003ms returns 0
T7BD8 002:964.556 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:964.559 - 0.003ms returns 0
T7BD8 002:964.563 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:964.567 - 0.003ms returns 0
T7BD8 002:964.571 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:964.574 - 0.003ms returns 0
T7BD8 002:964.578 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:964.582 - 0.003ms returns 0
T7BD8 002:964.586 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:964.589 - 0.003ms returns 0
T7BD8 002:964.593 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:964.598 - 0.004ms returns 0
T7BD8 002:964.602 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:964.605 - 0.003ms returns 0
T7BD8 002:964.609 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:964.612 - 0.003ms returns 0
T7BD8 002:964.617 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:964.620 - 0.003ms returns 0
T7BD8 002:964.624 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:964.628 - 0.003ms returns 0
T7BD8 002:964.664 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:964.667 - 0.003ms returns 0
T7BD8 002:964.671 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:964.675 - 0.003ms returns 0
T7BD8 002:964.679 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:964.683 - 0.003ms returns 0
T7BD8 002:964.687 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:964.690 - 0.003ms returns 0
T7BD8 002:964.694 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:964.698 - 0.003ms returns 0
T7BD8 002:964.702 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:964.705 - 0.003ms returns 0
T7BD8 002:964.709 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:964.713 - 0.003ms returns 0
T7BD8 002:964.717 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:964.720 - 0.003ms returns 0
T7BD8 002:964.725 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:964.729 - 0.004ms returns 0x00000038
T7BD8 002:964.733 JLINK_Go()
T7BD8 002:964.740   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:967.450 - 2.716ms 
T7BD8 002:967.469 JLINK_IsHalted()
T7BD8 002:968.074 - 0.604ms returns FALSE
T7BD8 002:968.091 JLINK_HasError()
T7BD8 002:969.799 JLINK_IsHalted()
T7BD8 002:970.289 - 0.489ms returns FALSE
T7BD8 002:970.303 JLINK_HasError()
T7BD8 002:971.795 JLINK_IsHalted()
T7BD8 002:974.082   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:974.568 - 2.773ms returns TRUE
T7BD8 002:974.575 JLINK_ReadReg(R15 (PC))
T7BD8 002:974.580 - 0.004ms returns 0x20000000
T7BD8 002:974.584 JLINK_ClrBPEx(BPHandle = 0x00000038)
T7BD8 002:974.588 - 0.004ms returns 0x00
T7BD8 002:974.593 JLINK_ReadReg(R0)
T7BD8 002:974.597 - 0.003ms returns 0x00000000
T7BD8 002:974.950 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:974.958   Data:  29 F2 2D BF 3B 8F 3B BF DE 3B 2E BF C1 4A 3B BF ...
T7BD8 002:974.968   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:977.589 - 2.639ms returns 0x27C
T7BD8 002:977.604 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:977.609   Data:  04 1F 25 BF 38 A1 43 BF 25 D2 24 BF 00 E2 43 BF ...
T7BD8 002:977.618   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:979.623 - 2.018ms returns 0x184
T7BD8 002:979.637 JLINK_HasError()
T7BD8 002:979.642 JLINK_WriteReg(R0, 0x0800B400)
T7BD8 002:979.647 - 0.005ms returns 0
T7BD8 002:979.652 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:979.655 - 0.003ms returns 0
T7BD8 002:979.659 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:979.662 - 0.003ms returns 0
T7BD8 002:979.666 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:979.670 - 0.003ms returns 0
T7BD8 002:979.674 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:979.677 - 0.003ms returns 0
T7BD8 002:979.681 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:979.685 - 0.003ms returns 0
T7BD8 002:979.689 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:979.693 - 0.003ms returns 0
T7BD8 002:979.697 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:979.700 - 0.003ms returns 0
T7BD8 002:979.704 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:979.708 - 0.003ms returns 0
T7BD8 002:979.712 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:979.715 - 0.003ms returns 0
T7BD8 002:979.719 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:979.723 - 0.003ms returns 0
T7BD8 002:979.727 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:979.730 - 0.003ms returns 0
T7BD8 002:979.734 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:979.738 - 0.003ms returns 0
T7BD8 002:979.742 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:979.745 - 0.003ms returns 0
T7BD8 002:979.750 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:979.753 - 0.003ms returns 0
T7BD8 002:979.757 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:979.761 - 0.003ms returns 0
T7BD8 002:979.765 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:979.768 - 0.003ms returns 0
T7BD8 002:979.772 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:979.776 - 0.003ms returns 0
T7BD8 002:979.780 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:979.783 - 0.003ms returns 0
T7BD8 002:979.787 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:979.790 - 0.003ms returns 0
T7BD8 002:979.795 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:979.800 - 0.004ms returns 0x00000039
T7BD8 002:979.804 JLINK_Go()
T7BD8 002:979.813   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:982.537 - 2.732ms 
T7BD8 002:982.543 JLINK_IsHalted()
T7BD8 002:983.024 - 0.480ms returns FALSE
T7BD8 002:983.029 JLINK_HasError()
T7BD8 002:984.796 JLINK_IsHalted()
T7BD8 002:985.257 - 0.461ms returns FALSE
T7BD8 002:985.263 JLINK_HasError()
T7BD8 002:986.798 JLINK_IsHalted()
T7BD8 002:989.284   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 002:989.806 - 3.008ms returns TRUE
T7BD8 002:989.818 JLINK_ReadReg(R15 (PC))
T7BD8 002:989.823 - 0.005ms returns 0x20000000
T7BD8 002:989.828 JLINK_ClrBPEx(BPHandle = 0x00000039)
T7BD8 002:989.832 - 0.004ms returns 0x00
T7BD8 002:989.836 JLINK_ReadReg(R0)
T7BD8 002:989.840 - 0.003ms returns 0x00000000
T7BD8 002:990.180 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 002:990.188   Data:  2B 3F 4F BF D9 F6 15 BF 1F 7A 4F BF 54 A5 15 BF ...
T7BD8 002:990.197   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 002:992.828 - 2.648ms returns 0x27C
T7BD8 002:992.838 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 002:992.842   Data:  DC 90 F7 BE 96 15 60 BF CB E0 F6 BE 21 46 60 BF ...
T7BD8 002:992.850   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 002:994.761 - 1.923ms returns 0x184
T7BD8 002:994.771 JLINK_HasError()
T7BD8 002:994.802 JLINK_WriteReg(R0, 0x0800B800)
T7BD8 002:994.808 - 0.005ms returns 0
T7BD8 002:994.812 JLINK_WriteReg(R1, 0x00000400)
T7BD8 002:994.816 - 0.003ms returns 0
T7BD8 002:994.820 JLINK_WriteReg(R2, 0x20000184)
T7BD8 002:994.823 - 0.003ms returns 0
T7BD8 002:994.827 JLINK_WriteReg(R3, 0x00000000)
T7BD8 002:994.830 - 0.003ms returns 0
T7BD8 002:994.834 JLINK_WriteReg(R4, 0x00000000)
T7BD8 002:994.838 - 0.003ms returns 0
T7BD8 002:994.842 JLINK_WriteReg(R5, 0x00000000)
T7BD8 002:994.845 - 0.003ms returns 0
T7BD8 002:994.849 JLINK_WriteReg(R6, 0x00000000)
T7BD8 002:994.853 - 0.003ms returns 0
T7BD8 002:994.857 JLINK_WriteReg(R7, 0x00000000)
T7BD8 002:994.860 - 0.003ms returns 0
T7BD8 002:994.864 JLINK_WriteReg(R8, 0x00000000)
T7BD8 002:994.868 - 0.003ms returns 0
T7BD8 002:994.872 JLINK_WriteReg(R9, 0x20000180)
T7BD8 002:994.875 - 0.003ms returns 0
T7BD8 002:994.879 JLINK_WriteReg(R10, 0x00000000)
T7BD8 002:994.882 - 0.003ms returns 0
T7BD8 002:994.887 JLINK_WriteReg(R11, 0x00000000)
T7BD8 002:994.890 - 0.003ms returns 0
T7BD8 002:994.894 JLINK_WriteReg(R12, 0x00000000)
T7BD8 002:994.898 - 0.003ms returns 0
T7BD8 002:994.902 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 002:994.906 - 0.003ms returns 0
T7BD8 002:994.910 JLINK_WriteReg(R14, 0x20000001)
T7BD8 002:994.913 - 0.003ms returns 0
T7BD8 002:994.917 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 002:994.920 - 0.003ms returns 0
T7BD8 002:994.924 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 002:994.928 - 0.003ms returns 0
T7BD8 002:994.932 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 002:994.935 - 0.003ms returns 0
T7BD8 002:994.939 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 002:994.943 - 0.003ms returns 0
T7BD8 002:994.947 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 002:994.950 - 0.003ms returns 0
T7BD8 002:994.955 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 002:994.959 - 0.004ms returns 0x0000003A
T7BD8 002:994.964 JLINK_Go()
T7BD8 002:994.970   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 002:998.149 - 3.185ms 
T7BD8 002:998.166 JLINK_IsHalted()
T7BD8 002:998.684 - 0.517ms returns FALSE
T7BD8 002:998.696 JLINK_HasError()
T7BD8 003:000.304 JLINK_IsHalted()
T7BD8 003:000.797 - 0.492ms returns FALSE
T7BD8 003:000.804 JLINK_HasError()
T7BD8 003:002.300 JLINK_IsHalted()
T7BD8 003:004.602   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:005.172 - 2.871ms returns TRUE
T7BD8 003:005.178 JLINK_ReadReg(R15 (PC))
T7BD8 003:005.183 - 0.005ms returns 0x20000000
T7BD8 003:005.188 JLINK_ClrBPEx(BPHandle = 0x0000003A)
T7BD8 003:005.191 - 0.003ms returns 0x00
T7BD8 003:005.196 JLINK_ReadReg(R0)
T7BD8 003:005.200 - 0.004ms returns 0x00000000
T7BD8 003:005.711 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:005.721   Data:  4C 95 68 BF 41 36 D5 BE 3C BF 68 BF 64 7F D4 BE ...
T7BD8 003:005.732   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:008.373 - 2.661ms returns 0x27C
T7BD8 003:008.392 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:008.401   Data:  27 60 9B BE 6E ED 73 BF 86 A0 9A BE DD 0B 74 BF ...
T7BD8 003:008.412   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:010.280 - 1.888ms returns 0x184
T7BD8 003:010.288 JLINK_HasError()
T7BD8 003:010.295 JLINK_WriteReg(R0, 0x0800BC00)
T7BD8 003:010.300 - 0.005ms returns 0
T7BD8 003:010.304 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:010.307 - 0.003ms returns 0
T7BD8 003:010.312 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:010.315 - 0.003ms returns 0
T7BD8 003:010.319 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:010.322 - 0.003ms returns 0
T7BD8 003:010.326 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:010.330 - 0.003ms returns 0
T7BD8 003:010.334 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:010.337 - 0.003ms returns 0
T7BD8 003:010.341 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:010.345 - 0.003ms returns 0
T7BD8 003:010.349 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:010.353 - 0.004ms returns 0
T7BD8 003:010.357 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:010.360 - 0.003ms returns 0
T7BD8 003:010.364 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:010.368 - 0.003ms returns 0
T7BD8 003:010.372 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:010.375 - 0.003ms returns 0
T7BD8 003:010.379 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:010.383 - 0.003ms returns 0
T7BD8 003:010.387 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:010.390 - 0.003ms returns 0
T7BD8 003:010.394 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:010.398 - 0.004ms returns 0
T7BD8 003:010.402 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:010.406 - 0.003ms returns 0
T7BD8 003:010.410 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:010.413 - 0.003ms returns 0
T7BD8 003:010.418 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:010.421 - 0.003ms returns 0
T7BD8 003:010.425 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:010.429 - 0.003ms returns 0
T7BD8 003:010.433 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:010.436 - 0.003ms returns 0
T7BD8 003:010.440 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:010.443 - 0.003ms returns 0
T7BD8 003:010.448 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:010.452 - 0.004ms returns 0x0000003B
T7BD8 003:010.456 JLINK_Go()
T7BD8 003:010.464   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:013.150 - 2.693ms 
T7BD8 003:013.157 JLINK_IsHalted()
T7BD8 003:013.611 - 0.453ms returns FALSE
T7BD8 003:013.617 JLINK_HasError()
T7BD8 003:015.303 JLINK_IsHalted()
T7BD8 003:015.844 - 0.540ms returns FALSE
T7BD8 003:015.853 JLINK_HasError()
T7BD8 003:017.305 JLINK_IsHalted()
T7BD8 003:019.841   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:020.320 - 3.015ms returns TRUE
T7BD8 003:020.332 JLINK_ReadReg(R15 (PC))
T7BD8 003:020.338 - 0.006ms returns 0x20000000
T7BD8 003:020.921 JLINK_ClrBPEx(BPHandle = 0x0000003B)
T7BD8 003:020.934 - 0.013ms returns 0x00
T7BD8 003:020.940 JLINK_ReadReg(R0)
T7BD8 003:020.944 - 0.004ms returns 0x00000000
T7BD8 003:021.300 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:021.307   Data:  4A FB 78 BF 7F 9A 6C BE 98 12 79 BF 2F 13 6B BE ...
T7BD8 003:021.319   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:023.828 - 2.528ms returns 0x27C
T7BD8 003:023.839 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:023.843   Data:  76 DB E4 BD 88 65 7E BF 2E BC E1 BD B0 70 7E BF ...
T7BD8 003:023.851   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:025.761 - 1.922ms returns 0x184
T7BD8 003:025.767 JLINK_HasError()
T7BD8 003:025.772 JLINK_WriteReg(R0, 0x0800C000)
T7BD8 003:025.777 - 0.004ms returns 0
T7BD8 003:025.781 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:025.784 - 0.003ms returns 0
T7BD8 003:025.788 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:025.792 - 0.003ms returns 0
T7BD8 003:025.796 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:025.799 - 0.003ms returns 0
T7BD8 003:025.803 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:025.807 - 0.003ms returns 0
T7BD8 003:025.811 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:025.814 - 0.003ms returns 0
T7BD8 003:025.819 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:025.825 - 0.006ms returns 0
T7BD8 003:025.829 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:025.833 - 0.003ms returns 0
T7BD8 003:025.837 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:025.840 - 0.003ms returns 0
T7BD8 003:025.844 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:025.847 - 0.003ms returns 0
T7BD8 003:025.852 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:025.855 - 0.003ms returns 0
T7BD8 003:025.859 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:025.862 - 0.003ms returns 0
T7BD8 003:025.866 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:025.870 - 0.003ms returns 0
T7BD8 003:025.874 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:025.878 - 0.003ms returns 0
T7BD8 003:025.882 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:025.885 - 0.003ms returns 0
T7BD8 003:025.889 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:025.893 - 0.003ms returns 0
T7BD8 003:025.897 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:025.900 - 0.003ms returns 0
T7BD8 003:025.904 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:025.908 - 0.003ms returns 0
T7BD8 003:025.912 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:025.915 - 0.003ms returns 0
T7BD8 003:025.919 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:025.923 - 0.003ms returns 0
T7BD8 003:025.927 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:025.931 - 0.004ms returns 0x0000003C
T7BD8 003:025.935 JLINK_Go()
T7BD8 003:025.942   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:028.776 - 2.840ms 
T7BD8 003:028.794 JLINK_IsHalted()
T7BD8 003:029.309 - 0.514ms returns FALSE
T7BD8 003:029.323 JLINK_HasError()
T7BD8 003:031.303 JLINK_IsHalted()
T7BD8 003:031.796 - 0.493ms returns FALSE
T7BD8 003:031.802 JLINK_HasError()
T7BD8 003:033.306 JLINK_IsHalted()
T7BD8 003:035.601   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:036.193 - 2.886ms returns TRUE
T7BD8 003:036.199 JLINK_ReadReg(R15 (PC))
T7BD8 003:036.204 - 0.004ms returns 0x20000000
T7BD8 003:036.208 JLINK_ClrBPEx(BPHandle = 0x0000003C)
T7BD8 003:036.212 - 0.003ms returns 0x00
T7BD8 003:036.216 JLINK_ReadReg(R0)
T7BD8 003:036.220 - 0.003ms returns 0x00000000
T7BD8 003:036.581 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:036.589   Data:  D1 CF 7F BF 2C C3 16 BD 98 D3 7F BF B8 7B 10 BD ...
T7BD8 003:036.598   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:039.230 - 2.648ms returns 0x27C
T7BD8 003:039.247 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:039.251   Data:  2B 95 AC 3D E4 16 7F BF 80 B6 AF 3D 58 0E 7F BF ...
T7BD8 003:039.261   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:041.132 - 1.885ms returns 0x184
T7BD8 003:041.138 JLINK_HasError()
T7BD8 003:041.144 JLINK_WriteReg(R0, 0x0800C400)
T7BD8 003:041.149 - 0.005ms returns 0
T7BD8 003:041.153 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:041.156 - 0.003ms returns 0
T7BD8 003:041.161 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:041.164 - 0.003ms returns 0
T7BD8 003:041.168 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:041.171 - 0.003ms returns 0
T7BD8 003:041.175 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:041.179 - 0.003ms returns 0
T7BD8 003:041.183 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:041.186 - 0.003ms returns 0
T7BD8 003:041.190 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:041.194 - 0.003ms returns 0
T7BD8 003:041.198 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:041.201 - 0.003ms returns 0
T7BD8 003:041.205 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:041.209 - 0.003ms returns 0
T7BD8 003:041.213 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:041.216 - 0.003ms returns 0
T7BD8 003:041.220 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:041.223 - 0.003ms returns 0
T7BD8 003:041.227 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:041.231 - 0.003ms returns 0
T7BD8 003:041.235 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:041.238 - 0.003ms returns 0
T7BD8 003:041.242 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:041.246 - 0.003ms returns 0
T7BD8 003:041.250 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:041.253 - 0.003ms returns 0
T7BD8 003:041.258 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:041.261 - 0.003ms returns 0
T7BD8 003:041.265 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:041.272 - 0.006ms returns 0
T7BD8 003:041.276 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:041.279 - 0.003ms returns 0
T7BD8 003:041.283 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:041.287 - 0.003ms returns 0
T7BD8 003:041.291 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:041.294 - 0.003ms returns 0
T7BD8 003:041.299 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:041.303 - 0.004ms returns 0x0000003D
T7BD8 003:041.307 JLINK_Go()
T7BD8 003:041.314   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:044.028 - 2.720ms 
T7BD8 003:044.034 JLINK_IsHalted()
T7BD8 003:044.485 - 0.451ms returns FALSE
T7BD8 003:044.491 JLINK_HasError()
T7BD8 003:046.300 JLINK_IsHalted()
T7BD8 003:046.757 - 0.456ms returns FALSE
T7BD8 003:046.762 JLINK_HasError()
T7BD8 003:048.304 JLINK_IsHalted()
T7BD8 003:050.653   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:051.112 - 2.808ms returns TRUE
T7BD8 003:051.118 JLINK_ReadReg(R15 (PC))
T7BD8 003:051.123 - 0.005ms returns 0x20000000
T7BD8 003:051.127 JLINK_ClrBPEx(BPHandle = 0x0000003D)
T7BD8 003:051.131 - 0.003ms returns 0x00
T7BD8 003:051.135 JLINK_ReadReg(R0)
T7BD8 003:051.139 - 0.004ms returns 0x00000000
T7BD8 003:051.893 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:051.905   Data:  AE CF 7C BF B6 AB 22 3E C9 BF 7C BF AD 38 24 3E ...
T7BD8 003:051.917   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:054.534 - 2.641ms returns 0x27C
T7BD8 003:054.540 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:054.544   Data:  FC D8 8D 3E B3 FA 75 BF 22 9A 8E 3E C6 DE 75 BF ...
T7BD8 003:054.551   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:056.433 - 1.892ms returns 0x184
T7BD8 003:056.438 JLINK_HasError()
T7BD8 003:056.443 JLINK_WriteReg(R0, 0x0800C800)
T7BD8 003:056.448 - 0.004ms returns 0
T7BD8 003:056.452 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:056.455 - 0.003ms returns 0
T7BD8 003:056.459 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:056.463 - 0.003ms returns 0
T7BD8 003:056.467 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:056.471 - 0.003ms returns 0
T7BD8 003:056.475 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:056.478 - 0.003ms returns 0
T7BD8 003:056.482 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:056.485 - 0.003ms returns 0
T7BD8 003:056.490 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:056.493 - 0.003ms returns 0
T7BD8 003:056.497 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:056.500 - 0.003ms returns 0
T7BD8 003:056.505 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:056.508 - 0.003ms returns 0
T7BD8 003:056.512 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:056.516 - 0.003ms returns 0
T7BD8 003:056.520 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:056.523 - 0.003ms returns 0
T7BD8 003:056.527 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:056.531 - 0.003ms returns 0
T7BD8 003:056.534 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:056.538 - 0.003ms returns 0
T7BD8 003:056.542 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:056.546 - 0.003ms returns 0
T7BD8 003:056.550 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:056.553 - 0.003ms returns 0
T7BD8 003:056.557 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:056.561 - 0.003ms returns 0
T7BD8 003:056.565 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:056.568 - 0.003ms returns 0
T7BD8 003:056.572 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:056.576 - 0.003ms returns 0
T7BD8 003:056.580 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:056.583 - 0.003ms returns 0
T7BD8 003:056.587 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:056.591 - 0.003ms returns 0
T7BD8 003:056.595 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:056.599 - 0.004ms returns 0x0000003E
T7BD8 003:056.603 JLINK_Go()
T7BD8 003:056.610   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:059.617 - 3.012ms 
T7BD8 003:059.633 JLINK_IsHalted()
T7BD8 003:060.110 - 0.476ms returns FALSE
T7BD8 003:060.115 JLINK_HasError()
T7BD8 003:061.300 JLINK_IsHalted()
T7BD8 003:061.756 - 0.455ms returns FALSE
T7BD8 003:061.762 JLINK_HasError()
T7BD8 003:063.300 JLINK_IsHalted()
T7BD8 003:063.758 - 0.457ms returns FALSE
T7BD8 003:063.767 JLINK_HasError()
T7BD8 003:065.300 JLINK_IsHalted()
T7BD8 003:067.634   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:068.152 - 2.851ms returns TRUE
T7BD8 003:068.162 JLINK_ReadReg(R15 (PC))
T7BD8 003:068.167 - 0.005ms returns 0x20000000
T7BD8 003:068.172 JLINK_ClrBPEx(BPHandle = 0x0000003E)
T7BD8 003:068.176 - 0.003ms returns 0x00
T7BD8 003:068.190 JLINK_ReadReg(R0)
T7BD8 003:068.194 - 0.004ms returns 0x00000000
T7BD8 003:068.545 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:068.553   Data:  67 18 70 BF EF 63 B2 3E 73 F5 6F BF 58 20 B3 3E ...
T7BD8 003:068.564   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:071.187 - 2.642ms returns 0x27C
T7BD8 003:071.200 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:071.204   Data:  30 19 EB 3E 95 6A 63 BF BB CB EB 3E 5A 3C 63 BF ...
T7BD8 003:071.213   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:073.098 - 1.898ms returns 0x184
T7BD8 003:073.109 JLINK_HasError()
T7BD8 003:073.142 JLINK_WriteReg(R0, 0x0800CC00)
T7BD8 003:073.147 - 0.005ms returns 0
T7BD8 003:073.151 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:073.154 - 0.003ms returns 0
T7BD8 003:073.159 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:073.162 - 0.003ms returns 0
T7BD8 003:073.166 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:073.170 - 0.003ms returns 0
T7BD8 003:073.174 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:073.177 - 0.003ms returns 0
T7BD8 003:073.181 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:073.185 - 0.003ms returns 0
T7BD8 003:073.189 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:073.192 - 0.003ms returns 0
T7BD8 003:073.196 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:073.200 - 0.003ms returns 0
T7BD8 003:073.204 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:073.207 - 0.003ms returns 0
T7BD8 003:073.212 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:073.215 - 0.003ms returns 0
T7BD8 003:073.219 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:073.222 - 0.003ms returns 0
T7BD8 003:073.226 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:073.230 - 0.003ms returns 0
T7BD8 003:073.233 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:073.237 - 0.003ms returns 0
T7BD8 003:073.241 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:073.245 - 0.003ms returns 0
T7BD8 003:073.249 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:073.252 - 0.003ms returns 0
T7BD8 003:073.256 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:073.260 - 0.003ms returns 0
T7BD8 003:073.264 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:073.267 - 0.003ms returns 0
T7BD8 003:073.271 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:073.275 - 0.003ms returns 0
T7BD8 003:073.279 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:073.282 - 0.003ms returns 0
T7BD8 003:073.286 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:073.290 - 0.003ms returns 0
T7BD8 003:073.294 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:073.298 - 0.004ms returns 0x0000003F
T7BD8 003:073.303 JLINK_Go()
T7BD8 003:073.310   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:075.985 - 2.682ms 
T7BD8 003:075.991 JLINK_IsHalted()
T7BD8 003:076.441 - 0.450ms returns FALSE
T7BD8 003:076.447 JLINK_HasError()
T7BD8 003:078.305 JLINK_IsHalted()
T7BD8 003:078.908 - 0.602ms returns FALSE
T7BD8 003:078.922 JLINK_HasError()
T7BD8 003:080.303 JLINK_IsHalted()
T7BD8 003:082.612   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:083.108 - 2.804ms returns TRUE
T7BD8 003:083.114 JLINK_ReadReg(R15 (PC))
T7BD8 003:083.120 - 0.005ms returns 0x20000000
T7BD8 003:083.125 JLINK_ClrBPEx(BPHandle = 0x0000003F)
T7BD8 003:083.129 - 0.004ms returns 0x00
T7BD8 003:083.134 JLINK_ReadReg(R0)
T7BD8 003:083.138 - 0.004ms returns 0x00000000
T7BD8 003:083.473 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:083.481   Data:  16 27 5A BF 82 4B 06 3F 6A F2 59 BF 0E A1 06 3F ...
T7BD8 003:083.491   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:086.091 - 2.618ms returns 0x27C
T7BD8 003:086.098 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:086.102   Data:  41 A8 1F 3F 27 1D 48 BF CB F6 1F 3F 65 DE 47 BF ...
T7BD8 003:086.109   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:087.983 - 1.884ms returns 0x184
T7BD8 003:088.000 JLINK_HasError()
T7BD8 003:088.006 JLINK_WriteReg(R0, 0x0800D000)
T7BD8 003:088.010 - 0.004ms returns 0
T7BD8 003:088.014 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:088.018 - 0.003ms returns 0
T7BD8 003:088.022 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:088.025 - 0.003ms returns 0
T7BD8 003:088.030 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:088.033 - 0.003ms returns 0
T7BD8 003:088.037 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:088.040 - 0.003ms returns 0
T7BD8 003:088.044 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:088.048 - 0.003ms returns 0
T7BD8 003:088.052 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:088.056 - 0.003ms returns 0
T7BD8 003:088.060 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:088.064 - 0.003ms returns 0
T7BD8 003:088.068 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:088.071 - 0.003ms returns 0
T7BD8 003:088.075 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:088.079 - 0.003ms returns 0
T7BD8 003:088.083 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:088.086 - 0.003ms returns 0
T7BD8 003:088.090 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:088.094 - 0.003ms returns 0
T7BD8 003:088.098 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:088.101 - 0.003ms returns 0
T7BD8 003:088.105 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:088.109 - 0.003ms returns 0
T7BD8 003:088.113 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:088.117 - 0.003ms returns 0
T7BD8 003:088.121 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:088.124 - 0.003ms returns 0
T7BD8 003:088.129 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:088.132 - 0.004ms returns 0
T7BD8 003:088.136 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:088.140 - 0.003ms returns 0
T7BD8 003:088.144 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:088.147 - 0.003ms returns 0
T7BD8 003:088.151 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:088.155 - 0.003ms returns 0
T7BD8 003:088.160 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:088.164 - 0.004ms returns 0x00000040
T7BD8 003:088.168 JLINK_Go()
T7BD8 003:088.176   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:090.882 - 2.714ms 
T7BD8 003:090.890 JLINK_IsHalted()
T7BD8 003:091.346 - 0.456ms returns FALSE
T7BD8 003:091.352 JLINK_HasError()
T7BD8 003:092.806 JLINK_IsHalted()
T7BD8 003:093.296 - 0.489ms returns FALSE
T7BD8 003:093.301 JLINK_HasError()
T7BD8 003:094.811 JLINK_IsHalted()
T7BD8 003:097.147   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:097.688 - 2.876ms returns TRUE
T7BD8 003:097.702 JLINK_ReadReg(R15 (PC))
T7BD8 003:097.707 - 0.005ms returns 0x20000000
T7BD8 003:097.711 JLINK_ClrBPEx(BPHandle = 0x00000040)
T7BD8 003:097.715 - 0.004ms returns 0x00
T7BD8 003:097.719 JLINK_ReadReg(R0)
T7BD8 003:097.723 - 0.003ms returns 0x00000000
T7BD8 003:098.061 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:098.068   Data:  98 D3 3B BF DE 3B 2E 3F 3B 8F 3B BF 78 85 2E 3F ...
T7BD8 003:098.079   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:100.776 - 2.714ms returns 0x27C
T7BD8 003:100.790 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:100.794   Data:  38 A1 43 3F 04 1F 25 BF 00 E2 43 3F 25 D2 24 BF ...
T7BD8 003:100.803   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:102.680 - 1.890ms returns 0x184
T7BD8 003:102.687 JLINK_HasError()
T7BD8 003:102.692 JLINK_WriteReg(R0, 0x0800D400)
T7BD8 003:102.697 - 0.004ms returns 0
T7BD8 003:102.701 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:102.704 - 0.003ms returns 0
T7BD8 003:102.708 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:102.712 - 0.003ms returns 0
T7BD8 003:102.716 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:102.719 - 0.003ms returns 0
T7BD8 003:102.724 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:102.727 - 0.003ms returns 0
T7BD8 003:102.731 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:102.734 - 0.003ms returns 0
T7BD8 003:102.738 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:102.742 - 0.003ms returns 0
T7BD8 003:102.746 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:102.749 - 0.003ms returns 0
T7BD8 003:102.753 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:102.757 - 0.003ms returns 0
T7BD8 003:102.761 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:102.768 - 0.007ms returns 0
T7BD8 003:102.772 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:102.775 - 0.003ms returns 0
T7BD8 003:102.779 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:102.783 - 0.003ms returns 0
T7BD8 003:102.787 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:102.790 - 0.003ms returns 0
T7BD8 003:102.794 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:102.798 - 0.003ms returns 0
T7BD8 003:102.802 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:102.806 - 0.003ms returns 0
T7BD8 003:102.810 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:102.813 - 0.003ms returns 0
T7BD8 003:102.817 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:102.821 - 0.003ms returns 0
T7BD8 003:102.825 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:102.828 - 0.003ms returns 0
T7BD8 003:102.832 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:102.836 - 0.003ms returns 0
T7BD8 003:102.840 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:102.843 - 0.003ms returns 0
T7BD8 003:102.848 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:102.852 - 0.004ms returns 0x00000041
T7BD8 003:102.856 JLINK_Go()
T7BD8 003:102.867   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:105.537 - 2.681ms 
T7BD8 003:105.544 JLINK_IsHalted()
T7BD8 003:106.023 - 0.479ms returns FALSE
T7BD8 003:106.029 JLINK_HasError()
T7BD8 003:107.809 JLINK_IsHalted()
T7BD8 003:108.361 - 0.551ms returns FALSE
T7BD8 003:108.371 JLINK_HasError()
T7BD8 003:109.807 JLINK_IsHalted()
T7BD8 003:112.127   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:112.590 - 2.783ms returns TRUE
T7BD8 003:112.596 JLINK_ReadReg(R15 (PC))
T7BD8 003:112.600 - 0.004ms returns 0x20000000
T7BD8 003:112.605 JLINK_ClrBPEx(BPHandle = 0x00000041)
T7BD8 003:112.608 - 0.003ms returns 0x00
T7BD8 003:112.613 JLINK_ReadReg(R0)
T7BD8 003:112.616 - 0.003ms returns 0x00000000
T7BD8 003:113.081 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:113.095   Data:  47 48 16 BF 1F 7A 4F 3F D9 F6 15 BF F4 B4 4F 3F ...
T7BD8 003:113.108   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:115.766 - 2.685ms returns 0x27C
T7BD8 003:115.778 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:115.782   Data:  96 15 60 3F DC 90 F7 BE 21 46 60 3F CB E0 F6 BE ...
T7BD8 003:115.797   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:117.646 - 1.868ms returns 0x184
T7BD8 003:117.661 JLINK_HasError()
T7BD8 003:117.666 JLINK_WriteReg(R0, 0x0800D800)
T7BD8 003:117.671 - 0.004ms returns 0
T7BD8 003:117.682 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:117.685 - 0.003ms returns 0
T7BD8 003:117.689 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:117.693 - 0.003ms returns 0
T7BD8 003:117.697 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:117.700 - 0.003ms returns 0
T7BD8 003:117.704 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:117.707 - 0.003ms returns 0
T7BD8 003:117.711 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:117.715 - 0.003ms returns 0
T7BD8 003:117.719 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:117.722 - 0.003ms returns 0
T7BD8 003:117.726 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:117.729 - 0.003ms returns 0
T7BD8 003:117.733 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:117.737 - 0.003ms returns 0
T7BD8 003:117.741 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:117.744 - 0.003ms returns 0
T7BD8 003:117.748 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:117.752 - 0.003ms returns 0
T7BD8 003:117.756 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:117.760 - 0.003ms returns 0
T7BD8 003:117.764 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:117.767 - 0.003ms returns 0
T7BD8 003:117.772 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:117.776 - 0.004ms returns 0
T7BD8 003:117.780 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:117.783 - 0.003ms returns 0
T7BD8 003:117.787 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:117.791 - 0.003ms returns 0
T7BD8 003:117.795 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:117.798 - 0.003ms returns 0
T7BD8 003:117.802 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:117.806 - 0.003ms returns 0
T7BD8 003:117.810 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:117.813 - 0.003ms returns 0
T7BD8 003:117.817 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:117.825 - 0.007ms returns 0
T7BD8 003:117.829 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:117.833 - 0.004ms returns 0x00000042
T7BD8 003:117.838 JLINK_Go()
T7BD8 003:117.846   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:120.589 - 2.750ms 
T7BD8 003:120.601 JLINK_IsHalted()
T7BD8 003:121.066 - 0.465ms returns FALSE
T7BD8 003:121.072 JLINK_HasError()
T7BD8 003:122.806 JLINK_IsHalted()
T7BD8 003:123.296 - 0.489ms returns FALSE
T7BD8 003:123.301 JLINK_HasError()
T7BD8 003:124.806 JLINK_IsHalted()
T7BD8 003:127.101   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:127.897 - 3.090ms returns TRUE
T7BD8 003:127.911 JLINK_ReadReg(R15 (PC))
T7BD8 003:127.916 - 0.005ms returns 0x20000000
T7BD8 003:127.921 JLINK_ClrBPEx(BPHandle = 0x00000042)
T7BD8 003:127.925 - 0.003ms returns 0x00
T7BD8 003:127.929 JLINK_ReadReg(R0)
T7BD8 003:127.933 - 0.003ms returns 0x00000000
T7BD8 003:128.252 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:128.259   Data:  FD EC D5 BE 3C BF 68 3F 41 36 D5 BE 07 E9 68 3F ...
T7BD8 003:128.269   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:130.895 - 2.642ms returns 0x27C
T7BD8 003:130.907 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:130.912   Data:  6E ED 73 3F 27 60 9B BE DD 0B 74 3F 86 A0 9A BE ...
T7BD8 003:130.922   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:132.802 - 1.895ms returns 0x184
T7BD8 003:132.810 JLINK_HasError()
T7BD8 003:132.815 JLINK_WriteReg(R0, 0x0800DC00)
T7BD8 003:132.820 - 0.004ms returns 0
T7BD8 003:132.824 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:132.827 - 0.003ms returns 0
T7BD8 003:132.831 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:132.835 - 0.003ms returns 0
T7BD8 003:132.839 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:132.842 - 0.003ms returns 0
T7BD8 003:132.846 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:132.850 - 0.003ms returns 0
T7BD8 003:132.854 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:132.857 - 0.003ms returns 0
T7BD8 003:132.861 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:132.865 - 0.003ms returns 0
T7BD8 003:132.869 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:132.872 - 0.003ms returns 0
T7BD8 003:132.876 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:132.880 - 0.003ms returns 0
T7BD8 003:132.884 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:132.887 - 0.003ms returns 0
T7BD8 003:132.891 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:132.894 - 0.003ms returns 0
T7BD8 003:132.899 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:132.902 - 0.003ms returns 0
T7BD8 003:132.906 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:132.909 - 0.003ms returns 0
T7BD8 003:132.913 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:132.917 - 0.003ms returns 0
T7BD8 003:132.921 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:132.924 - 0.003ms returns 0
T7BD8 003:132.929 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:132.932 - 0.003ms returns 0
T7BD8 003:132.936 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:132.940 - 0.003ms returns 0
T7BD8 003:132.944 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:132.948 - 0.003ms returns 0
T7BD8 003:132.952 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:132.955 - 0.003ms returns 0
T7BD8 003:132.959 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:132.962 - 0.003ms returns 0
T7BD8 003:132.967 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:132.971 - 0.004ms returns 0x00000043
T7BD8 003:132.975 JLINK_Go()
T7BD8 003:132.983   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:135.771 - 2.795ms 
T7BD8 003:135.785 JLINK_IsHalted()
T7BD8 003:136.293 - 0.508ms returns FALSE
T7BD8 003:136.302 JLINK_HasError()
T7BD8 003:137.808 JLINK_IsHalted()
T7BD8 003:138.362 - 0.553ms returns FALSE
T7BD8 003:138.373 JLINK_HasError()
T7BD8 003:139.809 JLINK_IsHalted()
T7BD8 003:142.268   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:142.760 - 2.950ms returns TRUE
T7BD8 003:142.767 JLINK_ReadReg(R15 (PC))
T7BD8 003:142.773 - 0.005ms returns 0x20000000
T7BD8 003:142.778 JLINK_ClrBPEx(BPHandle = 0x00000043)
T7BD8 003:142.782 - 0.004ms returns 0x00
T7BD8 003:142.786 JLINK_ReadReg(R0)
T7BD8 003:142.793 - 0.006ms returns 0x00000000
T7BD8 003:143.173 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:143.180   Data:  AB 21 6E BE 98 12 79 3F 7F 9A 6C BE BF 29 79 3F ...
T7BD8 003:143.190   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:145.836 - 2.663ms returns 0x27C
T7BD8 003:145.849 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:145.853   Data:  88 65 7E 3F 76 DB E4 BD B0 70 7E 3F 2E BC E1 BD ...
T7BD8 003:145.865   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:147.776 - 1.925ms returns 0x184
T7BD8 003:147.790 JLINK_HasError()
T7BD8 003:147.796 JLINK_WriteReg(R0, 0x0800E000)
T7BD8 003:147.801 - 0.005ms returns 0
T7BD8 003:147.805 JLINK_WriteReg(R1, 0x00000400)
T7BD8 003:147.809 - 0.003ms returns 0
T7BD8 003:147.813 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:147.816 - 0.003ms returns 0
T7BD8 003:147.820 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:147.824 - 0.003ms returns 0
T7BD8 003:147.828 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:147.832 - 0.003ms returns 0
T7BD8 003:147.836 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:147.839 - 0.003ms returns 0
T7BD8 003:147.843 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:147.846 - 0.003ms returns 0
T7BD8 003:147.850 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:147.854 - 0.003ms returns 0
T7BD8 003:147.858 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:147.861 - 0.003ms returns 0
T7BD8 003:147.865 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:147.868 - 0.003ms returns 0
T7BD8 003:147.872 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:147.877 - 0.004ms returns 0
T7BD8 003:147.881 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:147.884 - 0.003ms returns 0
T7BD8 003:147.888 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:147.891 - 0.003ms returns 0
T7BD8 003:147.896 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:147.899 - 0.004ms returns 0
T7BD8 003:147.904 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:147.907 - 0.003ms returns 0
T7BD8 003:147.911 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:147.915 - 0.003ms returns 0
T7BD8 003:147.919 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:147.922 - 0.003ms returns 0
T7BD8 003:147.927 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:147.930 - 0.003ms returns 0
T7BD8 003:147.934 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:147.937 - 0.003ms returns 0
T7BD8 003:147.941 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:147.945 - 0.003ms returns 0
T7BD8 003:147.950 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:147.954 - 0.004ms returns 0x00000044
T7BD8 003:147.958 JLINK_Go()
T7BD8 003:147.967   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:150.758 - 2.799ms 
T7BD8 003:150.778 JLINK_IsHalted()
T7BD8 003:151.237 - 0.458ms returns FALSE
T7BD8 003:151.246 JLINK_HasError()
T7BD8 003:152.807 JLINK_IsHalted()
T7BD8 003:153.312 - 0.504ms returns FALSE
T7BD8 003:153.319 JLINK_HasError()
T7BD8 003:154.811 JLINK_IsHalted()
T7BD8 003:157.157   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:157.641 - 2.829ms returns TRUE
T7BD8 003:157.654 JLINK_ReadReg(R15 (PC))
T7BD8 003:157.659 - 0.005ms returns 0x20000000
T7BD8 003:157.664 JLINK_ClrBPEx(BPHandle = 0x00000044)
T7BD8 003:157.668 - 0.004ms returns 0x00
T7BD8 003:157.672 JLINK_ReadReg(R0)
T7BD8 003:157.676 - 0.003ms returns 0x00000000
T7BD8 003:158.139 JLINK_WriteMem(0x20000184, 0x27C Bytes, ...)
T7BD8 003:158.146   Data:  88 0A 1D BD 98 D3 7F 3F 2C C3 16 BD 37 D7 7F 3F ...
T7BD8 003:158.157   CPU_WriteMem(636 bytes @ 0x20000184)
T7BD8 003:160.764 - 2.624ms returns 0x27C
T7BD8 003:160.776 JLINK_WriteMem(0x20000400, 0x184 Bytes, ...)
T7BD8 003:160.780   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
T7BD8 003:160.789   CPU_WriteMem(388 bytes @ 0x20000400)
T7BD8 003:162.639 - 1.863ms returns 0x184
T7BD8 003:162.648 JLINK_HasError()
T7BD8 003:162.653 JLINK_WriteReg(R0, 0x0800E400)
T7BD8 003:162.658 - 0.005ms returns 0
T7BD8 003:162.663 JLINK_WriteReg(R1, 0x00000110)
T7BD8 003:162.666 - 0.003ms returns 0
T7BD8 003:162.670 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:162.674 - 0.004ms returns 0
T7BD8 003:162.678 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:162.684 - 0.006ms returns 0
T7BD8 003:162.689 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:162.692 - 0.003ms returns 0
T7BD8 003:162.696 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:162.700 - 0.003ms returns 0
T7BD8 003:162.704 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:162.707 - 0.003ms returns 0
T7BD8 003:162.711 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:162.714 - 0.003ms returns 0
T7BD8 003:162.719 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:162.722 - 0.003ms returns 0
T7BD8 003:162.726 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:162.729 - 0.003ms returns 0
T7BD8 003:162.734 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:162.737 - 0.003ms returns 0
T7BD8 003:162.741 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:162.745 - 0.003ms returns 0
T7BD8 003:162.749 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:162.752 - 0.003ms returns 0
T7BD8 003:162.756 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:162.760 - 0.004ms returns 0
T7BD8 003:162.764 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:162.768 - 0.003ms returns 0
T7BD8 003:162.772 JLINK_WriteReg(R15 (PC), 0x2000010C)
T7BD8 003:162.776 - 0.003ms returns 0
T7BD8 003:162.780 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:162.783 - 0.003ms returns 0
T7BD8 003:162.787 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:162.791 - 0.003ms returns 0
T7BD8 003:162.795 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:162.798 - 0.003ms returns 0
T7BD8 003:162.802 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:162.806 - 0.003ms returns 0
T7BD8 003:162.810 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:162.815 - 0.004ms returns 0x00000045
T7BD8 003:162.819 JLINK_Go()
T7BD8 003:162.827   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:165.550 - 2.730ms 
T7BD8 003:165.555 JLINK_IsHalted()
T7BD8 003:166.023 - 0.467ms returns FALSE
T7BD8 003:166.029 JLINK_HasError()
T7BD8 003:167.809 JLINK_IsHalted()
T7BD8 003:170.184   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:170.687 - 2.877ms returns TRUE
T7BD8 003:170.694 JLINK_ReadReg(R15 (PC))
T7BD8 003:170.699 - 0.005ms returns 0x20000000
T7BD8 003:170.704 JLINK_ClrBPEx(BPHandle = 0x00000045)
T7BD8 003:170.708 - 0.003ms returns 0x00
T7BD8 003:170.712 JLINK_ReadReg(R0)
T7BD8 003:170.716 - 0.003ms returns 0x00000000
T7BD8 003:170.721 JLINK_HasError()
T7BD8 003:170.725 JLINK_WriteReg(R0, 0x00000002)
T7BD8 003:170.729 - 0.004ms returns 0
T7BD8 003:170.734 JLINK_WriteReg(R1, 0x00000110)
T7BD8 003:170.737 - 0.003ms returns 0
T7BD8 003:170.741 JLINK_WriteReg(R2, 0x20000184)
T7BD8 003:170.744 - 0.003ms returns 0
T7BD8 003:170.748 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:170.752 - 0.003ms returns 0
T7BD8 003:170.756 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:170.759 - 0.003ms returns 0
T7BD8 003:170.763 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:170.767 - 0.003ms returns 0
T7BD8 003:170.771 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:170.774 - 0.003ms returns 0
T7BD8 003:170.778 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:170.782 - 0.003ms returns 0
T7BD8 003:170.786 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:170.789 - 0.003ms returns 0
T7BD8 003:170.793 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:170.797 - 0.003ms returns 0
T7BD8 003:170.801 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:170.804 - 0.003ms returns 0
T7BD8 003:170.808 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:170.812 - 0.003ms returns 0
T7BD8 003:170.816 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:170.819 - 0.003ms returns 0
T7BD8 003:170.823 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:170.827 - 0.003ms returns 0
T7BD8 003:170.831 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:170.834 - 0.003ms returns 0
T7BD8 003:170.838 JLINK_WriteReg(R15 (PC), 0x20000086)
T7BD8 003:170.842 - 0.003ms returns 0
T7BD8 003:170.846 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:170.849 - 0.003ms returns 0
T7BD8 003:170.853 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:170.856 - 0.003ms returns 0
T7BD8 003:170.860 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:170.864 - 0.003ms returns 0
T7BD8 003:170.868 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:170.872 - 0.003ms returns 0
T7BD8 003:170.876 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:170.883 - 0.006ms returns 0x00000046
T7BD8 003:170.887 JLINK_Go()
T7BD8 003:170.894   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:173.610 - 2.723ms 
T7BD8 003:173.616 JLINK_IsHalted()
T7BD8 003:175.945   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:176.405 - 2.788ms returns TRUE
T7BD8 003:176.423 JLINK_ReadReg(R15 (PC))
T7BD8 003:176.428 - 0.005ms returns 0x20000000
T7BD8 003:176.466 JLINK_ClrBPEx(BPHandle = 0x00000046)
T7BD8 003:176.472 - 0.006ms returns 0x00
T7BD8 003:176.476 JLINK_ReadReg(R0)
T7BD8 003:176.480 - 0.003ms returns 0x00000000
T7BD8 003:230.533 JLINK_WriteMem(0x20000000, 0x184 Bytes, ...)
T7BD8 003:230.550   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T7BD8 003:230.567   CPU_WriteMem(388 bytes @ 0x20000000)
T7BD8 003:232.455 - 1.921ms returns 0x184
T7BD8 003:232.474 JLINK_HasError()
T7BD8 003:232.480 JLINK_WriteReg(R0, 0x08000000)
T7BD8 003:232.485 - 0.004ms returns 0
T7BD8 003:232.489 JLINK_WriteReg(R1, 0x0ABA9500)
T7BD8 003:232.492 - 0.003ms returns 0
T7BD8 003:232.496 JLINK_WriteReg(R2, 0x00000003)
T7BD8 003:232.500 - 0.003ms returns 0
T7BD8 003:232.504 JLINK_WriteReg(R3, 0x00000000)
T7BD8 003:232.507 - 0.003ms returns 0
T7BD8 003:232.511 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:232.515 - 0.003ms returns 0
T7BD8 003:232.519 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:232.522 - 0.003ms returns 0
T7BD8 003:232.526 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:232.530 - 0.003ms returns 0
T7BD8 003:232.534 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:232.537 - 0.003ms returns 0
T7BD8 003:232.541 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:232.544 - 0.003ms returns 0
T7BD8 003:232.549 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:232.552 - 0.003ms returns 0
T7BD8 003:232.556 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:232.560 - 0.003ms returns 0
T7BD8 003:232.564 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:232.567 - 0.003ms returns 0
T7BD8 003:232.571 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:232.574 - 0.003ms returns 0
T7BD8 003:232.579 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:232.582 - 0.004ms returns 0
T7BD8 003:232.586 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:232.590 - 0.003ms returns 0
T7BD8 003:232.594 JLINK_WriteReg(R15 (PC), 0x20000054)
T7BD8 003:232.597 - 0.003ms returns 0
T7BD8 003:232.602 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:232.605 - 0.003ms returns 0
T7BD8 003:232.609 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:232.612 - 0.003ms returns 0
T7BD8 003:232.616 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:232.620 - 0.003ms returns 0
T7BD8 003:232.624 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:232.627 - 0.003ms returns 0
T7BD8 003:232.632 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:232.638   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:233.109 - 0.477ms returns 0x00000047
T7BD8 003:233.115 JLINK_Go()
T7BD8 003:233.120   CPU_WriteMem(2 bytes @ 0x20000000)
T7BD8 003:233.614   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:236.316 - 3.201ms 
T7BD8 003:236.322 JLINK_IsHalted()
T7BD8 003:238.712   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:239.222 - 2.898ms returns TRUE
T7BD8 003:239.237 JLINK_ReadReg(R15 (PC))
T7BD8 003:239.242 - 0.005ms returns 0x20000000
T7BD8 003:239.247 JLINK_ClrBPEx(BPHandle = 0x00000047)
T7BD8 003:239.251 - 0.004ms returns 0x00
T7BD8 003:239.255 JLINK_ReadReg(R0)
T7BD8 003:239.258 - 0.003ms returns 0x00000000
T7BD8 003:239.263 JLINK_HasError()
T7BD8 003:239.268 JLINK_WriteReg(R0, 0xFFFFFFFF)
T7BD8 003:239.272 - 0.003ms returns 0
T7BD8 003:239.276 JLINK_WriteReg(R1, 0x08000000)
T7BD8 003:239.279 - 0.003ms returns 0
T7BD8 003:239.283 JLINK_WriteReg(R2, 0x0000E510)
T7BD8 003:239.287 - 0.003ms returns 0
T7BD8 003:239.291 JLINK_WriteReg(R3, 0x04C11DB7)
T7BD8 003:239.294 - 0.003ms returns 0
T7BD8 003:239.299 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:239.302 - 0.003ms returns 0
T7BD8 003:239.306 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:239.309 - 0.003ms returns 0
T7BD8 003:239.313 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:239.317 - 0.003ms returns 0
T7BD8 003:239.325 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:239.329 - 0.003ms returns 0
T7BD8 003:239.333 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:239.336 - 0.003ms returns 0
T7BD8 003:239.340 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:239.343 - 0.003ms returns 0
T7BD8 003:239.348 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:239.351 - 0.003ms returns 0
T7BD8 003:239.355 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:239.358 - 0.003ms returns 0
T7BD8 003:239.362 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:239.366 - 0.003ms returns 0
T7BD8 003:239.370 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:239.374 - 0.004ms returns 0
T7BD8 003:239.378 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:239.381 - 0.003ms returns 0
T7BD8 003:239.385 JLINK_WriteReg(R15 (PC), 0x20000002)
T7BD8 003:239.389 - 0.003ms returns 0
T7BD8 003:239.393 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:239.396 - 0.003ms returns 0
T7BD8 003:239.400 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:239.404 - 0.003ms returns 0
T7BD8 003:239.408 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:239.411 - 0.003ms returns 0
T7BD8 003:239.415 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:239.419 - 0.003ms returns 0
T7BD8 003:239.423 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:239.427 - 0.004ms returns 0x00000048
T7BD8 003:239.431 JLINK_Go()
T7BD8 003:239.439   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:242.110 - 2.678ms 
T7BD8 003:242.116 JLINK_IsHalted()
T7BD8 003:242.606 - 0.489ms returns FALSE
T7BD8 003:242.611 JLINK_HasError()
T7BD8 003:245.012 JLINK_IsHalted()
T7BD8 003:245.554 - 0.541ms returns FALSE
T7BD8 003:245.560 JLINK_HasError()
T7BD8 003:247.014 JLINK_IsHalted()
T7BD8 003:247.614 - 0.599ms returns FALSE
T7BD8 003:247.626 JLINK_HasError()
T7BD8 003:249.013 JLINK_IsHalted()
T7BD8 003:249.509 - 0.496ms returns FALSE
T7BD8 003:249.515 JLINK_HasError()
T7BD8 003:251.010 JLINK_IsHalted()
T7BD8 003:251.464 - 0.453ms returns FALSE
T7BD8 003:251.470 JLINK_HasError()
T7BD8 003:253.012 JLINK_IsHalted()
T7BD8 003:253.522 - 0.509ms returns FALSE
T7BD8 003:253.537 JLINK_HasError()
T7BD8 003:255.015 JLINK_IsHalted()
T7BD8 003:255.494 - 0.478ms returns FALSE
T7BD8 003:255.500 JLINK_HasError()
T7BD8 003:257.012 JLINK_IsHalted()
T7BD8 003:257.657 - 0.644ms returns FALSE
T7BD8 003:257.672 JLINK_HasError()
T7BD8 003:259.012 JLINK_IsHalted()
T7BD8 003:259.484 - 0.471ms returns FALSE
T7BD8 003:259.489 JLINK_HasError()
T7BD8 003:261.016 JLINK_IsHalted()
T7BD8 003:261.488 - 0.472ms returns FALSE
T7BD8 003:261.495 JLINK_HasError()
T7BD8 003:263.010 JLINK_IsHalted()
T7BD8 003:263.502 - 0.492ms returns FALSE
T7BD8 003:263.508 JLINK_HasError()
T7BD8 003:265.010 JLINK_IsHalted()
T7BD8 003:265.463 - 0.453ms returns FALSE
T7BD8 003:265.469 JLINK_HasError()
T7BD8 003:267.011 JLINK_IsHalted()
T7BD8 003:267.660 - 0.647ms returns FALSE
T7BD8 003:267.672 JLINK_HasError()
T7BD8 003:269.013 JLINK_IsHalted()
T7BD8 003:269.532 - 0.519ms returns FALSE
T7BD8 003:269.539 JLINK_HasError()
T7BD8 003:271.013 JLINK_IsHalted()
T7BD8 003:271.483 - 0.470ms returns FALSE
T7BD8 003:271.490 JLINK_HasError()
T7BD8 003:273.018 JLINK_IsHalted()
T7BD8 003:273.512 - 0.494ms returns FALSE
T7BD8 003:273.519 JLINK_HasError()
T7BD8 003:275.016 JLINK_IsHalted()
T7BD8 003:275.511 - 0.493ms returns FALSE
T7BD8 003:275.520 JLINK_HasError()
T7BD8 003:277.016 JLINK_IsHalted()
T7BD8 003:277.800 - 0.783ms returns FALSE
T7BD8 003:277.822 JLINK_HasError()
T7BD8 003:279.016 JLINK_IsHalted()
T7BD8 003:279.557 - 0.540ms returns FALSE
T7BD8 003:279.573 JLINK_HasError()
T7BD8 003:281.015 JLINK_IsHalted()
T7BD8 003:281.526 - 0.511ms returns FALSE
T7BD8 003:281.534 JLINK_HasError()
T7BD8 003:283.015 JLINK_IsHalted()
T7BD8 003:283.539 - 0.523ms returns FALSE
T7BD8 003:283.558 JLINK_HasError()
T7BD8 003:286.023 JLINK_IsHalted()
T7BD8 003:286.562 - 0.538ms returns FALSE
T7BD8 003:286.571 JLINK_HasError()
T7BD8 003:288.022 JLINK_IsHalted()
T7BD8 003:288.564 - 0.541ms returns FALSE
T7BD8 003:288.574 JLINK_HasError()
T7BD8 003:290.522 JLINK_IsHalted()
T7BD8 003:291.055 - 0.532ms returns FALSE
T7BD8 003:291.062 JLINK_HasError()
T7BD8 003:292.523 JLINK_IsHalted()
T7BD8 003:293.029 - 0.506ms returns FALSE
T7BD8 003:293.040 JLINK_HasError()
T7BD8 003:294.520 JLINK_IsHalted()
T7BD8 003:295.026 - 0.506ms returns FALSE
T7BD8 003:295.036 JLINK_HasError()
T7BD8 003:296.525 JLINK_IsHalted()
T7BD8 003:297.036 - 0.510ms returns FALSE
T7BD8 003:297.066 JLINK_HasError()
T7BD8 003:298.524 JLINK_IsHalted()
T7BD8 003:299.073 - 0.548ms returns FALSE
T7BD8 003:299.084 JLINK_HasError()
T7BD8 003:300.520 JLINK_IsHalted()
T7BD8 003:301.011 - 0.491ms returns FALSE
T7BD8 003:301.019 JLINK_HasError()
T7BD8 003:302.531 JLINK_IsHalted()
T7BD8 003:303.026 - 0.495ms returns FALSE
T7BD8 003:303.033 JLINK_HasError()
T7BD8 003:304.520 JLINK_IsHalted()
T7BD8 003:305.008 - 0.487ms returns FALSE
T7BD8 003:305.014 JLINK_HasError()
T7BD8 003:306.525 JLINK_IsHalted()
T7BD8 003:307.063 - 0.537ms returns FALSE
T7BD8 003:307.080 JLINK_HasError()
T7BD8 003:308.524 JLINK_IsHalted()
T7BD8 003:309.069 - 0.544ms returns FALSE
T7BD8 003:309.084 JLINK_HasError()
T7BD8 003:310.525 JLINK_IsHalted()
T7BD8 003:311.026 - 0.500ms returns FALSE
T7BD8 003:311.034 JLINK_HasError()
T7BD8 003:312.520 JLINK_IsHalted()
T7BD8 003:312.985 - 0.465ms returns FALSE
T7BD8 003:312.993 JLINK_HasError()
T7BD8 003:314.523 JLINK_IsHalted()
T7BD8 003:315.027 - 0.503ms returns FALSE
T7BD8 003:315.037 JLINK_HasError()
T7BD8 003:316.521 JLINK_IsHalted()
T7BD8 003:317.062 - 0.540ms returns FALSE
T7BD8 003:317.079 JLINK_HasError()
T7BD8 003:318.526 JLINK_IsHalted()
T7BD8 003:319.060 - 0.534ms returns FALSE
T7BD8 003:319.067 JLINK_HasError()
T7BD8 003:320.523 JLINK_IsHalted()
T7BD8 003:321.058 - 0.534ms returns FALSE
T7BD8 003:321.072 JLINK_HasError()
T7BD8 003:322.521 JLINK_IsHalted()
T7BD8 003:323.014 - 0.493ms returns FALSE
T7BD8 003:323.022 JLINK_HasError()
T7BD8 003:324.529 JLINK_IsHalted()
T7BD8 003:325.026 - 0.497ms returns FALSE
T7BD8 003:325.034 JLINK_HasError()
T7BD8 003:326.520 JLINK_IsHalted()
T7BD8 003:327.019 - 0.499ms returns FALSE
T7BD8 003:327.038 JLINK_HasError()
T7BD8 003:328.525 JLINK_IsHalted()
T7BD8 003:329.070 - 0.544ms returns FALSE
T7BD8 003:329.083 JLINK_HasError()
T7BD8 003:330.521 JLINK_IsHalted()
T7BD8 003:331.013 - 0.492ms returns FALSE
T7BD8 003:331.026 JLINK_HasError()
T7BD8 003:332.525 JLINK_IsHalted()
T7BD8 003:333.018 - 0.493ms returns FALSE
T7BD8 003:333.026 JLINK_HasError()
T7BD8 003:334.522 JLINK_IsHalted()
T7BD8 003:335.018 - 0.496ms returns FALSE
T7BD8 003:335.024 JLINK_HasError()
T7BD8 003:336.521 JLINK_IsHalted()
T7BD8 003:337.105 - 0.583ms returns FALSE
T7BD8 003:337.127 JLINK_HasError()
T7BD8 003:338.523 JLINK_IsHalted()
T7BD8 003:339.057 - 0.533ms returns FALSE
T7BD8 003:339.069 JLINK_HasError()
T7BD8 003:340.524 JLINK_IsHalted()
T7BD8 003:341.055 - 0.530ms returns FALSE
T7BD8 003:341.062 JLINK_HasError()
T7BD8 003:342.524 JLINK_IsHalted()
T7BD8 003:343.018 - 0.494ms returns FALSE
T7BD8 003:343.028 JLINK_HasError()
T7BD8 003:344.523 JLINK_IsHalted()
T7BD8 003:345.009 - 0.486ms returns FALSE
T7BD8 003:345.016 JLINK_HasError()
T7BD8 003:346.527 JLINK_IsHalted()
T7BD8 003:347.075 - 0.547ms returns FALSE
T7BD8 003:347.089 JLINK_HasError()
T7BD8 003:349.526 JLINK_IsHalted()
T7BD8 003:350.077 - 0.550ms returns FALSE
T7BD8 003:350.093 JLINK_HasError()
T7BD8 003:351.525 JLINK_IsHalted()
T7BD8 003:352.026 - 0.501ms returns FALSE
T7BD8 003:352.034 JLINK_HasError()
T7BD8 003:353.522 JLINK_IsHalted()
T7BD8 003:354.013 - 0.490ms returns FALSE
T7BD8 003:354.022 JLINK_HasError()
T7BD8 003:355.521 JLINK_IsHalted()
T7BD8 003:356.010 - 0.488ms returns FALSE
T7BD8 003:356.017 JLINK_HasError()
T7BD8 003:357.528 JLINK_IsHalted()
T7BD8 003:358.054 - 0.526ms returns FALSE
T7BD8 003:358.061 JLINK_HasError()
T7BD8 003:359.520 JLINK_IsHalted()
T7BD8 003:360.017 - 0.496ms returns FALSE
T7BD8 003:360.025 JLINK_HasError()
T7BD8 003:361.524 JLINK_IsHalted()
T7BD8 003:362.024 - 0.498ms returns FALSE
T7BD8 003:362.042 JLINK_HasError()
T7BD8 003:363.522 JLINK_IsHalted()
T7BD8 003:364.018 - 0.496ms returns FALSE
T7BD8 003:364.024 JLINK_HasError()
T7BD8 003:365.528 JLINK_IsHalted()
T7BD8 003:366.028 - 0.500ms returns FALSE
T7BD8 003:366.039 JLINK_HasError()
T7BD8 003:367.526 JLINK_IsHalted()
T7BD8 003:368.119 - 0.592ms returns FALSE
T7BD8 003:368.132 JLINK_HasError()
T7BD8 003:369.521 JLINK_IsHalted()
T7BD8 003:370.062 - 0.540ms returns FALSE
T7BD8 003:370.081 JLINK_HasError()
T7BD8 003:371.523 JLINK_IsHalted()
T7BD8 003:372.027 - 0.504ms returns FALSE
T7BD8 003:372.034 JLINK_HasError()
T7BD8 003:373.522 JLINK_IsHalted()
T7BD8 003:374.019 - 0.496ms returns FALSE
T7BD8 003:374.027 JLINK_HasError()
T7BD8 003:375.528 JLINK_IsHalted()
T7BD8 003:376.027 - 0.499ms returns FALSE
T7BD8 003:376.036 JLINK_HasError()
T7BD8 003:377.527 JLINK_IsHalted()
T7BD8 003:378.068 - 0.540ms returns FALSE
T7BD8 003:378.083 JLINK_HasError()
T7BD8 003:379.528 JLINK_IsHalted()
T7BD8 003:380.074 - 0.545ms returns FALSE
T7BD8 003:380.096 JLINK_HasError()
T7BD8 003:381.525 JLINK_IsHalted()
T7BD8 003:382.016 - 0.490ms returns FALSE
T7BD8 003:382.022 JLINK_HasError()
T7BD8 003:383.527 JLINK_IsHalted()
T7BD8 003:384.019 - 0.492ms returns FALSE
T7BD8 003:384.028 JLINK_HasError()
T7BD8 003:385.526 JLINK_IsHalted()
T7BD8 003:386.027 - 0.500ms returns FALSE
T7BD8 003:386.033 JLINK_HasError()
T7BD8 003:387.533 JLINK_IsHalted()
T7BD8 003:388.071 - 0.537ms returns FALSE
T7BD8 003:388.078 JLINK_HasError()
T7BD8 003:389.529 JLINK_IsHalted()
T7BD8 003:390.026 - 0.496ms returns FALSE
T7BD8 003:390.032 JLINK_HasError()
T7BD8 003:392.030 JLINK_IsHalted()
T7BD8 003:392.554 - 0.523ms returns FALSE
T7BD8 003:392.561 JLINK_HasError()
T7BD8 003:395.035 JLINK_IsHalted()
T7BD8 003:395.550 - 0.515ms returns FALSE
T7BD8 003:395.558 JLINK_HasError()
T7BD8 003:397.032 JLINK_IsHalted()
T7BD8 003:397.567 - 0.535ms returns FALSE
T7BD8 003:397.578 JLINK_HasError()
T7BD8 003:399.032 JLINK_IsHalted()
T7BD8 003:399.573 - 0.541ms returns FALSE
T7BD8 003:399.581 JLINK_HasError()
T7BD8 003:401.031 JLINK_IsHalted()
T7BD8 003:401.533 - 0.501ms returns FALSE
T7BD8 003:401.540 JLINK_HasError()
T7BD8 003:403.030 JLINK_IsHalted()
T7BD8 003:403.559 - 0.528ms returns FALSE
T7BD8 003:403.571 JLINK_HasError()
T7BD8 003:405.032 JLINK_IsHalted()
T7BD8 003:405.512 - 0.479ms returns FALSE
T7BD8 003:405.525 JLINK_HasError()
T7BD8 003:407.032 JLINK_IsHalted()
T7BD8 003:407.551 - 0.519ms returns FALSE
T7BD8 003:407.561 JLINK_HasError()
T7BD8 003:409.034 JLINK_IsHalted()
T7BD8 003:409.543 - 0.508ms returns FALSE
T7BD8 003:409.558 JLINK_HasError()
T7BD8 003:411.032 JLINK_IsHalted()
T7BD8 003:411.557 - 0.524ms returns FALSE
T7BD8 003:411.566 JLINK_HasError()
T7BD8 003:413.030 JLINK_IsHalted()
T7BD8 003:413.540 - 0.509ms returns FALSE
T7BD8 003:413.550 JLINK_HasError()
T7BD8 003:415.034 JLINK_IsHalted()
T7BD8 003:415.510 - 0.476ms returns FALSE
T7BD8 003:415.518 JLINK_HasError()
T7BD8 003:417.028 JLINK_IsHalted()
T7BD8 003:417.556 - 0.526ms returns FALSE
T7BD8 003:417.564 JLINK_HasError()
T7BD8 003:419.027 JLINK_IsHalted()
T7BD8 003:419.515 - 0.487ms returns FALSE
T7BD8 003:419.521 JLINK_HasError()
T7BD8 003:421.027 JLINK_IsHalted()
T7BD8 003:421.530 - 0.502ms returns FALSE
T7BD8 003:421.536 JLINK_HasError()
T7BD8 003:423.031 JLINK_IsHalted()
T7BD8 003:423.556 - 0.525ms returns FALSE
T7BD8 003:423.568 JLINK_HasError()
T7BD8 003:425.030 JLINK_IsHalted()
T7BD8 003:425.508 - 0.478ms returns FALSE
T7BD8 003:425.515 JLINK_HasError()
T7BD8 003:427.034 JLINK_IsHalted()
T7BD8 003:427.552 - 0.517ms returns FALSE
T7BD8 003:427.566 JLINK_HasError()
T7BD8 003:429.035 JLINK_IsHalted()
T7BD8 003:429.576 - 0.540ms returns FALSE
T7BD8 003:429.583 JLINK_HasError()
T7BD8 003:431.033 JLINK_IsHalted()
T7BD8 003:431.571 - 0.538ms returns FALSE
T7BD8 003:431.585 JLINK_HasError()
T7BD8 003:433.030 JLINK_IsHalted()
T7BD8 003:433.512 - 0.482ms returns FALSE
T7BD8 003:433.524 JLINK_HasError()
T7BD8 003:435.034 JLINK_IsHalted()
T7BD8 003:435.585 - 0.550ms returns FALSE
T7BD8 003:435.598 JLINK_HasError()
T7BD8 003:437.034 JLINK_IsHalted()
T7BD8 003:437.577 - 0.543ms returns FALSE
T7BD8 003:437.596 JLINK_HasError()
T7BD8 003:439.032 JLINK_IsHalted()
T7BD8 003:439.544 - 0.511ms returns FALSE
T7BD8 003:439.559 JLINK_HasError()
T7BD8 003:441.032 JLINK_IsHalted()
T7BD8 003:441.540 - 0.507ms returns FALSE
T7BD8 003:441.547 JLINK_HasError()
T7BD8 003:443.032 JLINK_IsHalted()
T7BD8 003:443.558 - 0.525ms returns FALSE
T7BD8 003:443.567 JLINK_HasError()
T7BD8 003:445.030 JLINK_IsHalted()
T7BD8 003:445.532 - 0.501ms returns FALSE
T7BD8 003:445.539 JLINK_HasError()
T7BD8 003:447.039 JLINK_IsHalted()
T7BD8 003:447.576 - 0.543ms returns FALSE
T7BD8 003:447.588 JLINK_HasError()
T7BD8 003:449.034 JLINK_IsHalted()
T7BD8 003:449.541 - 0.506ms returns FALSE
T7BD8 003:449.548 JLINK_HasError()
T7BD8 003:451.032 JLINK_IsHalted()
T7BD8 003:451.563 - 0.530ms returns FALSE
T7BD8 003:451.571 JLINK_HasError()
T7BD8 003:453.030 JLINK_IsHalted()
T7BD8 003:453.551 - 0.520ms returns FALSE
T7BD8 003:453.560 JLINK_HasError()
T7BD8 003:455.031 JLINK_IsHalted()
T7BD8 003:455.552 - 0.521ms returns FALSE
T7BD8 003:455.560 JLINK_HasError()
T7BD8 003:457.032 JLINK_IsHalted()
T7BD8 003:457.576 - 0.543ms returns FALSE
T7BD8 003:457.590 JLINK_HasError()
T7BD8 003:459.030 JLINK_IsHalted()
T7BD8 003:459.563 - 0.530ms returns FALSE
T7BD8 003:459.574 JLINK_HasError()
T7BD8 003:461.030 JLINK_IsHalted()
T7BD8 003:461.538 - 0.508ms returns FALSE
T7BD8 003:461.545 JLINK_HasError()
T7BD8 003:463.028 JLINK_IsHalted()
T7BD8 003:463.496 - 0.467ms returns FALSE
T7BD8 003:463.502 JLINK_HasError()
T7BD8 003:465.032 JLINK_IsHalted()
T7BD8 003:465.556 - 0.523ms returns FALSE
T7BD8 003:465.563 JLINK_HasError()
T7BD8 003:467.031 JLINK_IsHalted()
T7BD8 003:467.576 - 0.545ms returns FALSE
T7BD8 003:467.589 JLINK_HasError()
T7BD8 003:469.035 JLINK_IsHalted()
T7BD8 003:469.570 - 0.535ms returns FALSE
T7BD8 003:469.578 JLINK_HasError()
T7BD8 003:471.030 JLINK_IsHalted()
T7BD8 003:471.556 - 0.526ms returns FALSE
T7BD8 003:471.566 JLINK_HasError()
T7BD8 003:473.041 JLINK_IsHalted()
T7BD8 003:473.540 - 0.499ms returns FALSE
T7BD8 003:473.548 JLINK_HasError()
T7BD8 003:475.030 JLINK_IsHalted()
T7BD8 003:475.538 - 0.508ms returns FALSE
T7BD8 003:475.547 JLINK_HasError()
T7BD8 003:477.036 JLINK_IsHalted()
T7BD8 003:477.578 - 0.541ms returns FALSE
T7BD8 003:477.596 JLINK_HasError()
T7BD8 003:479.033 JLINK_IsHalted()
T7BD8 003:479.572 - 0.539ms returns FALSE
T7BD8 003:479.580 JLINK_HasError()
T7BD8 003:481.038 JLINK_IsHalted()
T7BD8 003:481.538 - 0.499ms returns FALSE
T7BD8 003:481.583 JLINK_HasError()
T7BD8 003:483.032 JLINK_IsHalted()
T7BD8 003:483.506 - 0.473ms returns FALSE
T7BD8 003:483.514 JLINK_HasError()
T7BD8 003:485.031 JLINK_IsHalted()
T7BD8 003:485.534 - 0.502ms returns FALSE
T7BD8 003:485.542 JLINK_HasError()
T7BD8 003:487.035 JLINK_IsHalted()
T7BD8 003:487.579 - 0.543ms returns FALSE
T7BD8 003:487.594 JLINK_HasError()
T7BD8 003:489.539 JLINK_IsHalted()
T7BD8 003:490.055 - 0.516ms returns FALSE
T7BD8 003:490.062 JLINK_HasError()
T7BD8 003:491.540 JLINK_IsHalted()
T7BD8 003:492.055 - 0.515ms returns FALSE
T7BD8 003:492.064 JLINK_HasError()
T7BD8 003:493.542 JLINK_IsHalted()
T7BD8 003:494.028 - 0.486ms returns FALSE
T7BD8 003:494.035 JLINK_HasError()
T7BD8 003:495.538 JLINK_IsHalted()
T7BD8 003:496.026 - 0.487ms returns FALSE
T7BD8 003:496.032 JLINK_HasError()
T7BD8 003:497.540 JLINK_IsHalted()
T7BD8 003:498.076 - 0.534ms returns FALSE
T7BD8 003:498.090 JLINK_HasError()
T7BD8 003:499.539 JLINK_IsHalted()
T7BD8 003:500.053 - 0.513ms returns FALSE
T7BD8 003:500.060 JLINK_HasError()
T7BD8 003:501.540 JLINK_IsHalted()
T7BD8 003:502.068 - 0.527ms returns FALSE
T7BD8 003:502.085 JLINK_HasError()
T7BD8 003:504.542 JLINK_IsHalted()
T7BD8 003:505.078 - 0.535ms returns FALSE
T7BD8 003:505.097 JLINK_HasError()
T7BD8 003:506.539 JLINK_IsHalted()
T7BD8 003:507.041 - 0.501ms returns FALSE
T7BD8 003:507.064 JLINK_HasError()
T7BD8 003:508.539 JLINK_IsHalted()
T7BD8 003:509.056 - 0.516ms returns FALSE
T7BD8 003:509.064 JLINK_HasError()
T7BD8 003:510.537 JLINK_IsHalted()
T7BD8 003:511.028 - 0.491ms returns FALSE
T7BD8 003:511.038 JLINK_HasError()
T7BD8 003:512.540 JLINK_IsHalted()
T7BD8 003:513.028 - 0.487ms returns FALSE
T7BD8 003:513.035 JLINK_HasError()
T7BD8 003:514.541 JLINK_IsHalted()
T7BD8 003:515.025 - 0.484ms returns FALSE
T7BD8 003:515.032 JLINK_HasError()
T7BD8 003:516.537 JLINK_IsHalted()
T7BD8 003:517.077 - 0.539ms returns FALSE
T7BD8 003:517.095 JLINK_HasError()
T7BD8 003:518.542 JLINK_IsHalted()
T7BD8 003:519.056 - 0.514ms returns FALSE
T7BD8 003:519.064 JLINK_HasError()
T7BD8 003:520.537 JLINK_IsHalted()
T7BD8 003:521.071 - 0.533ms returns FALSE
T7BD8 003:521.080 JLINK_HasError()
T7BD8 003:522.539 JLINK_IsHalted()
T7BD8 003:523.056 - 0.517ms returns FALSE
T7BD8 003:523.066 JLINK_HasError()
T7BD8 003:524.539 JLINK_IsHalted()
T7BD8 003:525.010 - 0.471ms returns FALSE
T7BD8 003:525.018 JLINK_HasError()
T7BD8 003:526.539 JLINK_IsHalted()
T7BD8 003:527.078 - 0.538ms returns FALSE
T7BD8 003:527.094 JLINK_HasError()
T7BD8 003:528.540 JLINK_IsHalted()
T7BD8 003:529.061 - 0.521ms returns FALSE
T7BD8 003:529.068 JLINK_HasError()
T7BD8 003:530.537 JLINK_IsHalted()
T7BD8 003:531.028 - 0.491ms returns FALSE
T7BD8 003:531.036 JLINK_HasError()
T7BD8 003:532.536 JLINK_IsHalted()
T7BD8 003:533.016 - 0.479ms returns FALSE
T7BD8 003:533.022 JLINK_HasError()
T7BD8 003:534.538 JLINK_IsHalted()
T7BD8 003:535.057 - 0.518ms returns FALSE
T7BD8 003:535.064 JLINK_HasError()
T7BD8 003:537.543 JLINK_IsHalted()
T7BD8 003:538.070 - 0.527ms returns FALSE
T7BD8 003:538.085 JLINK_HasError()
T7BD8 003:539.541 JLINK_IsHalted()
T7BD8 003:540.059 - 0.517ms returns FALSE
T7BD8 003:540.065 JLINK_HasError()
T7BD8 003:541.542 JLINK_IsHalted()
T7BD8 003:542.072 - 0.530ms returns FALSE
T7BD8 003:542.079 JLINK_HasError()
T7BD8 003:543.539 JLINK_IsHalted()
T7BD8 003:544.055 - 0.515ms returns FALSE
T7BD8 003:544.064 JLINK_HasError()
T7BD8 003:545.545 JLINK_IsHalted()
T7BD8 003:546.062 - 0.517ms returns FALSE
T7BD8 003:546.070 JLINK_HasError()
T7BD8 003:547.540 JLINK_IsHalted()
T7BD8 003:548.075 - 0.534ms returns FALSE
T7BD8 003:548.087 JLINK_HasError()
T7BD8 003:549.540 JLINK_IsHalted()
T7BD8 003:550.073 - 0.533ms returns FALSE
T7BD8 003:550.082 JLINK_HasError()
T7BD8 003:551.538 JLINK_IsHalted()
T7BD8 003:552.054 - 0.516ms returns FALSE
T7BD8 003:552.063 JLINK_HasError()
T7BD8 003:553.541 JLINK_IsHalted()
T7BD8 003:554.054 - 0.513ms returns FALSE
T7BD8 003:554.061 JLINK_HasError()
T7BD8 003:555.537 JLINK_IsHalted()
T7BD8 003:556.027 - 0.489ms returns FALSE
T7BD8 003:556.033 JLINK_HasError()
T7BD8 003:557.571 JLINK_IsHalted()
T7BD8 003:558.100 - 0.528ms returns FALSE
T7BD8 003:558.112 JLINK_HasError()
T7BD8 003:559.536 JLINK_IsHalted()
T7BD8 003:560.031 - 0.494ms returns FALSE
T7BD8 003:560.044 JLINK_HasError()
T7BD8 003:561.540 JLINK_IsHalted()
T7BD8 003:562.062 - 0.521ms returns FALSE
T7BD8 003:562.068 JLINK_HasError()
T7BD8 003:563.540 JLINK_IsHalted()
T7BD8 003:565.922   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:566.432 - 2.890ms returns TRUE
T7BD8 003:566.440 JLINK_ReadReg(R15 (PC))
T7BD8 003:566.447 - 0.006ms returns 0x20000000
T7BD8 003:566.451 JLINK_ClrBPEx(BPHandle = 0x00000048)
T7BD8 003:566.455 - 0.004ms returns 0x00
T7BD8 003:566.460 JLINK_ReadReg(R0)
T7BD8 003:566.464 - 0.004ms returns 0x95B5393C
T7BD8 003:568.284 JLINK_HasError()
T7BD8 003:568.300 JLINK_WriteReg(R0, 0x00000003)
T7BD8 003:568.306 - 0.006ms returns 0
T7BD8 003:568.311 JLINK_WriteReg(R1, 0x08000000)
T7BD8 003:568.314 - 0.003ms returns 0
T7BD8 003:568.318 JLINK_WriteReg(R2, 0x0000E510)
T7BD8 003:568.322 - 0.003ms returns 0
T7BD8 003:568.326 JLINK_WriteReg(R3, 0x04C11DB7)
T7BD8 003:568.329 - 0.003ms returns 0
T7BD8 003:568.334 JLINK_WriteReg(R4, 0x00000000)
T7BD8 003:568.337 - 0.003ms returns 0
T7BD8 003:568.341 JLINK_WriteReg(R5, 0x00000000)
T7BD8 003:568.344 - 0.003ms returns 0
T7BD8 003:568.348 JLINK_WriteReg(R6, 0x00000000)
T7BD8 003:568.356 - 0.008ms returns 0
T7BD8 003:568.360 JLINK_WriteReg(R7, 0x00000000)
T7BD8 003:568.364 - 0.003ms returns 0
T7BD8 003:568.368 JLINK_WriteReg(R8, 0x00000000)
T7BD8 003:568.372 - 0.003ms returns 0
T7BD8 003:568.376 JLINK_WriteReg(R9, 0x20000180)
T7BD8 003:568.380 - 0.003ms returns 0
T7BD8 003:568.384 JLINK_WriteReg(R10, 0x00000000)
T7BD8 003:568.387 - 0.003ms returns 0
T7BD8 003:568.392 JLINK_WriteReg(R11, 0x00000000)
T7BD8 003:568.395 - 0.003ms returns 0
T7BD8 003:568.399 JLINK_WriteReg(R12, 0x00000000)
T7BD8 003:568.403 - 0.003ms returns 0
T7BD8 003:568.407 JLINK_WriteReg(R13 (SP), 0x20001000)
T7BD8 003:568.411 - 0.004ms returns 0
T7BD8 003:568.415 JLINK_WriteReg(R14, 0x20000001)
T7BD8 003:568.419 - 0.003ms returns 0
T7BD8 003:568.423 JLINK_WriteReg(R15 (PC), 0x20000086)
T7BD8 003:568.426 - 0.003ms returns 0
T7BD8 003:568.430 JLINK_WriteReg(XPSR, 0x01000000)
T7BD8 003:568.434 - 0.003ms returns 0
T7BD8 003:568.438 JLINK_WriteReg(MSP, 0x20001000)
T7BD8 003:568.441 - 0.003ms returns 0
T7BD8 003:568.445 JLINK_WriteReg(PSP, 0x20001000)
T7BD8 003:568.448 - 0.003ms returns 0
T7BD8 003:568.453 JLINK_WriteReg(CFBP, 0x00000000)
T7BD8 003:568.456 - 0.003ms returns 0
T7BD8 003:568.461 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T7BD8 003:568.465 - 0.004ms returns 0x00000049
T7BD8 003:568.470 JLINK_Go()
T7BD8 003:568.480   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:571.210 - 2.739ms 
T7BD8 003:571.226 JLINK_IsHalted()
T7BD8 003:573.575   CPU_ReadMem(2 bytes @ 0x20000000)
T7BD8 003:574.071 - 2.844ms returns TRUE
T7BD8 003:574.079 JLINK_ReadReg(R15 (PC))
T7BD8 003:574.084 - 0.005ms returns 0x20000000
T7BD8 003:574.089 JLINK_ClrBPEx(BPHandle = 0x00000049)
T7BD8 003:574.093 - 0.004ms returns 0x00
T7BD8 003:574.097 JLINK_ReadReg(R0)
T7BD8 003:574.101 - 0.003ms returns 0x00000000
T7BD8 003:627.050 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T7BD8 003:627.082   Data:  FE E7
T7BD8 003:627.103   CPU_WriteMem(2 bytes @ 0x20000000)
T7BD8 003:627.652 - 0.602ms returns 0x2
T7BD8 003:627.671 JLINK_HasError()
T7BD8 003:629.180 JLINK_Close()
T7BD8 003:630.797   OnDisconnectTarget() start
T7BD8 003:630.820    J-Link Script File: Executing OnDisconnectTarget()
T7BD8 003:630.831   CPU_WriteMem(4 bytes @ 0xE0042004)
T7BD8 003:631.352   CPU_WriteMem(4 bytes @ 0xE0042008)
T7BD8 003:632.875   OnDisconnectTarget() end - Took 1.01ms
T7BD8 003:632.896   CPU_ReadMem(4 bytes @ 0xE0001000)
T7BD8 003:653.213 - 24.032ms
T7BD8 003:653.243   
T7BD8 003:653.247   Closed
